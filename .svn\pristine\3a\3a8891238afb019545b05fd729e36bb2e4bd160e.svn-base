<template>
  <div>
    <div v-show="cxxs">
      <div class="header">
        <div class="header-left" id="echart" @click="$router.push('/yjgzsy?activeName=second')"></div>
        <div class="header-center" v-if="normalShow">本次检测，未发现问题</div>
        <div class="header-center" v-else>本次检测，发现<span style="color:#E6A23C">{{ problemCount }}</span>项问题</div>
        <el-button type="primary" round style="margin-left:35px" @click="$router.push('/yjgzsy?activeName=second')">查看结果
        </el-button>
      </div>
      <div class="center">
        <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline" style="float:left">
          <el-form-item style="font-weight: 700;">
            <el-input v-model="formInline.name" placeholder="请输入查询内容" style="height:54px; width: 684px; position: relative;top: 11px;"></el-input>
          </el-form-item>
        </el-form>
        <div class="center-right" @click="onSubmit">
          <img src="./img/ztqk_12.png" alt="">
        </div>
      </div>
      <div class="bottom">
        <div class="bottom-wai" @click="toIndex(1)">
          <div class="bottom-top">
            <img src="./img/ztqk_2.png" alt="">
          </div>
          <div class="bottom-size">保密制度<span> {{ bmzd }} </span>项</div>
        </div>
        <div class="bottom-wai" @click="toIndex(2)">
          <div class="bottom-top">
            <img src="./img/ztqk_3.png" alt="">
          </div>
          <div class="bottom-size">组织机构<span> {{ zzjg }} </span>个</div>
        </div>
        <div class="bottom-wai" @click="toIndex(3)">
          <div class="bottom-top">
            <img src="./img/ztqk_4.png" alt="">
          </div>
          <div class="bottom-size">涉密岗位<span> {{ smgw }} </span>个</div>
        </div>
        <div class="bottom-wai" @click="toIndex(4)">
          <div class="bottom-top">
            <img src="./img/ztqk_5.png" alt="">
          </div>
          <div class="bottom-size">涉密人员<span> {{ smry }} </span>人</div>
        </div>
        <div class="bottom-wai" style="margin-top: 0;" @click="toIndex(5)">
          <div class="bottom-top">
            <img src="./img/ztqk_6.png" alt="">
          </div>
          <div class="bottom-size">涉密场所<span> {{ smcs }} </span>个</div>
        </div>
        <div class="bottom-wai" style="margin-top: 0;" @click="toIndex(6)">
          <div class="bottom-top">
            <img src="./img/ztqk_7.png" alt="">
          </div>
          <div class="bottom-size">涉密设备<span> {{ smsb }} </span>台</div>
        </div>
        <div class="bottom-wai" style="margin-top: 0;" @click="toIndex(7)">
          <div class="bottom-top">
            <img src="./img/ztqk_8.png" alt="">
          </div>
          <div class="bottom-size">教育培训<span> {{ jypx }} </span>次</div>
        </div>
        <div class="bottom-wai" style="margin-top: 0;" @click="toIndex(8)">
          <div class="bottom-top">
            <img src="./img/ztqk_9.png" alt="">
          </div>
          <div class="bottom-size">涉密载体<span> {{ smzt }} </span>个</div>
        </div>
      </div>
    </div>
    <div v-show="!cxxs">
      <div class="xmlb-title" style=" cursor: pointer;">
        <span style="font-size: 24px; cursor: pointer;">搜索结果</span>
        <span style="" @click="gbxs" class="fhsy">返回</span>
      </div>
      <div v-for="(item, index) in cxjg" :key="index" style="width: 500px;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        margin-top:10px;
        align-items: center;">
        <div>{{ item.name }}</div>
        <el-button type="primary" size="small" @click="searchLook(item.path)">查 看</el-button>
      </div>
    </div>
  </div>

</template>
<script>
import {
  getSysj
} from '../../../db/sysjdb'
export default {
  data() {
    return {
      bmzd: '',
      zzjg: '',
      smgw: '',
      smry: '',
      smcs: '',
      smsb: '',
      jypx: '',
      smzt: '',
      input: '',
      cxjd: [],
      cxjg: [],
      cxxs: true,
      formInline: {
        input: '',
        name: ""
      },
      score: 0,
      problemCount: 0,
      normalShow: true
    }
  },
  computed: {},
  methods: {
    toIndex() {

    },
    sj() {
      let sj = getSysj()
      console.log(sj);
      this.bmzd = sj.bmzd
      this.zzjg = sj.zzjg
      this.smgw = sj.smgw
      this.smry = sj.smry
      this.smcs = sj.smcs
      this.smsb = sj.smsb
      this.jypx = sj.jypx
      this.smzt = sj.smzt
    },
    gbxs() {
      this.cxxs = true
    },
    getLoadEcharts() {
      // var myChart = this.$echarts.init(
      //   document.getElementById("echart")
      // );
      const domMap = document.getElementById("echart");
      // 清除Echarts默认添加的属性
      domMap.removeAttribute("_echarts_instance_");
      let myChart = this.$echarts.init(domMap);
      var option = {
        series: [{
          type: 'liquidFill',
          radius: '90%',
          data: [this.score / 100, this.score / 100 - 0.1, this.score / 100 - 0.2],
          label: {
            normal: {
              color: '#fff',
              insideColor: 'transparent',
              formatter: this.score + "分", //显示文本
              textStyle: {
                fontSize: 24,
                fontWeight: 'bold',
                fontFamily: 'Microsoft YaHei'
              }
            }
          },
          outline: {
            show: true,
            borderDistance: 5,
            itemStyle: {
              borderColor: 'rgba(0,0,0,0)',
              borderWidth: 2
            }
          },
          backgroundStyle: {
            // color: 'rgba(67,209,100,1)'
          }
        }]
      };
      myChart.setOption(option);
    },
    onSubmit() {
      //  form是查询条件
      console.log(this.formInline);
      // 备份了一下数据
      let arr = this.cxjd
      // 通过遍历key值来循环处理
      Object.keys(this.formInline).forEach(e => {
        // 调用自己定义好的筛选方法
        console.log(this.formInline[e]);
        arr = this.filterFunc(this.formInline[e], e, arr)
      })
      this.cxjg = arr
      if (arr == '') {
        this.$message({
          message: '未找到相关内容',
          type: 'warning'
        });
      } else if (this.formInline.name == '') {
        this.$message({
          message: '请输入查询内容',
          type: 'warning'
        });
      } else {
        this.cxxs = false
      }
    },
    filterFunc(val, target, filterArr) {
      // 参数不存在或为空时，就相当于查询全部
      if (val == undefined || val == '') {
        console.log(2);
        return filterArr
      }
      return filterArr.filter(p => {
        return p[target].indexOf(val) > -1
        // return bool
      }) // 可以自己加一个.toLowerCase()来兼容一下大小
    },
    // 搜索列表点击查看跳页方法
    searchLook(path){
      this.$router.push('/tzglsy?activeName='+path)
    },
    toIndex(i) {
      this.$router.push('/tzglsy')
      if (i == 1) {
        this.$router.push('/tzglsy?activeName=/bmzd')
      } else if (i == 2) {
        this.$router.push('/tzglsy?activeName=/zzjg')
      } else if (i == 3) {
        this.$router.push('/tzglsy?activeName=/smgwgl')
      } else if (i == 4) {
        this.$router.push('/tzglsy?activeName=/smry')
      } else if (i == 5) {
        this.$router.push('/tzglsy?activeName=/csgl')
      } else if (i == 6) {
        this.$router.push('/tzglsy?activeName=/smjsj')
      } else if (i == 7) {
        this.$router.push('/tzglsy?activeName=/pxqd')
      } else if (i == 8) {
        this.$router.push('/tzglsy?activeName=/smzttz')
      }
    }

  },
  watch: {},
  mounted() {
    this.score = localStorage.getItem('allCounts') != null ? localStorage.getItem('allCounts') : 0
    this.problemCount = localStorage.getItem('proNumber') != null ? localStorage.getItem('proNumber') : 0
    if (this.score != 100) {
      this.normalShow = false
    }
    this.sj()
    this.$router.options.routes.forEach(item => {
      if (item.path != '/' && item.path != '*') {
        this.cxjd.push({
          path: item.path,
          name: item.meta.name
        })
      }
    });
    if (this.score < 0) {
      this.score = 0
    }
    this.getLoadEcharts()
  }
};
</script>
<style scoped>
.header,
.center {
  display: flex;
  align-items: center;
  width: 683px;
  margin: 0 auto;

}


.header-center {
  font-size: 32px;

  color: #333333;
  letter-spacing: 0;
  line-height: 28px;
  font-weight: 400;
  margin-left: 11px;
}

.header-left {
  width: 150px;
  height: 150px;
  /* line-height: 150px!important;
    background: url(./img/zjpf_2.png) no-repeat center;
    font-family: Helvetica;
    font-size: 40px;
    color: #21A566;
    letter-spacing: 0;
    text-align: center;
    line-height: 40px;
    font-weight: 400; */
}

.center {
  margin-top: 28px;
  position: relative;
}

.center-right {
  width: 54px;
  height: 54px;
  border-radius: 4px;
  background: #307EEC;
  position: absolute;
  right: 0;
}

.center-right img {
  position: relative;
  top: 16px;
  left: 16px;
}

.bottom {
  width: 1370px;
  margin: 0 auto;
  height: 464px;
  margin-top: 20px;
  display: flex;
  flex-wrap: wrap
}

.bottom-wai {
  margin: 50px 70px 0 70px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.bottom-wai:hover {
  cursor: pointer
}

.bottom-top {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background: #307EEC;
  margin-bottom: 10px;

}

.bottom-size {
  width: 200px;
  font-size: 24px;
  color: #333333;
  letter-spacing: 0;
  text-align: center;
  line-height: 39.2px;
}

.bottom-size span {
  color: #307EEC;
}

.bottom-top img {
  position: relative;
  top: 15px;
  left: 15px;

}

.fhsy {
  display: inline-block;
  width: 120px;
  margin-top: 10px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-left: 30px;
  padding-top: 4px;
  float: right;
  background: url(../../assets/icons/zzjg_icon1.png) no-repeat center;
  background-size: 100% 100%;
  position: relative;
  left: -10px;
}

.xmlb-title {
  line-height: 60px;
  width: 100%;
  padding-left: 10px;
  height: 60px;
  background: url(../../assets/background/bg-02.png) no-repeat left;
  background-size: 100% 100%;
  text-indent: 10px;
  /* margin: 0 20px; */
  color: #0646BF;
  font-weight: 700;
}

:deep(.el-input__inner) {
  height: 54px;
}
</style>