<template>
  <div class="bg_con bgHeight" v-loading="pageLoading">
    <div class="maxWidth maxHeight positionR overflowHidden">
      <div class="dabg maxHeight">
        <div class="content maxHeight">
          <div class="table maxHeight">
            <!-- 查询以及操作start -->
            <div class="mhcx">
              <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline">
                <el-form-item>
                  <el-select v-model="formInline.wdlx" clearable placeholder="请选择类型" class="widthx fl">
                    <el-option v-for="item in wdlx" :label="item.lxmc" :value="item.lxmc" :key="item.lxid"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
                </el-form-item>
              </el-form>
              <el-form :inline="true" size="medium" class="demo-form-inline fr">
                <el-form-item class="fr">
                  <el-button size="medium" @click="returnSy" icon="el-icon-back">
                    返回
                  </el-button>
                </el-form-item>
                <el-form-item class="fr">
                  <el-button type="danger" size="medium" @click="deleteFiles" icon="el-icon-delete-solid">
                    删除
                  </el-button>
                </el-form-item>
                <el-form-item class="fr">
                  <input type="file" ref="upload" class="uploadClass hidden positionA opacity-0 cursorClick"
                    accept=".docx,.pdf">
                  <el-button type="primary" icon="el-icon-upload2" size="medium" @click="dr_dialog = true">
                    上传
                  </el-button>
                </el-form-item>
              </el-form>
            </div>
            <!-- 查询以及操作end -->
            <!-- 文档列表start -->
            <div class="table_content_padding maxHeight">
              <div class="table_content maxHeight">
                <el-table class="eltableClass maxWidth" :data="wdList" border @selection-change="selectRow"
                  :header-cell-style="headerCellStyle" :height="tableHeight" stripe>
                  <el-table-column type="selection" width="55" align="center"> </el-table-column>
                  <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                  <el-table-column prop="wdmc" label="文档名称"></el-table-column>
                  <el-table-column prop="wdlx" label="文档类型"></el-table-column>
                  <el-table-column prop="wdys" label="页数"></el-table-column>
                  <el-table-column prop="wdscsj" label="上传时间"></el-table-column>
                  <el-table-column label="操作" width="120">
                    <template slot-scope="scoped">
                      <el-button size="medium" type="text" @click="downloadFileItem(scoped.row)">下载
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <!-- <div style="border: 1px solid #ebeef5;">
                  <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange"
                    :pager-count="5" :current-page="page" :page-sizes="[5, 10, 20, 30]" :page-size="pageSize"
                    layout="total, prev, pager, sizes,next, jumper" :total="total">
                  </el-pagination>
                </div> -->
              </div>
            </div>
          </div>
        </div>
        <!-- 文档列表end -->
        <!-- 上传start -->
        <el-dialog title="文档上传" class="scbg-dialog" top="5vh" width="600px" @close="closeDialog('formName')" :visible.sync="dr_dialog"
          show-close>
          <el-form ref="formName" :model="tjlist" :rules="rules" label-width="120px" size="mini">
            <el-form-item label="文档类型" prop="wdlx">
              <el-select v-model="tjlist.wdlx" placeholder="请选择文档类型" class="maxWidthCalc">
                <el-option v-for="item in wdlx" :label="item.lxmc" :value="item.lxmc" :key="item.lxid">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="文档名称" prop="wdmc" class="one-line">
              <el-input class="fl inputWidth" disabled placeholder="文档名称" v-model="tjlist.wdmc" clearable></el-input>
              <div class="uploadButton fr cursorClick" @click="chooseFile">上传</div>
            </el-form-item>
            <el-form-item label="文档页数" prop="wdys" class="one-line">
              <el-input placeholder="文档页数" v-model="tjlist.wdys" clearable></el-input>
            </el-form-item>
            <el-form-item label="文档页缩略图" prop="wdslt" class="one-line picHeight positionR">
              <el-upload class="avatar-uploader" action="#" :disabled="uploadDisable" :show-file-list="false" :before-upload="beforeAvatarUpload"
                :http-request="httpRequest">
                <img v-if="sltshow" :src="sltshow" class="avatar">
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                <p v-if="uploadDisable" class="fr zyposition">注：需先上传文件</p>
              </el-upload>
            </el-form-item>
            <el-form-item label="文档摘要" prop="wdzy" class="one-line textareaHeight">
              <el-input type="textarea" :autosize="{ minRows: 6, maxRows: 6}" maxlength="140" show-word-limit placeholder="文档摘要" v-model="tjlist.wdzy" clearable></el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitTj('formName')">保 存</el-button>
            <el-button type="warning" @click="closeDialog('formName')">关 闭</el-button>
          </span>
        </el-dialog>
        <!-- 上传end -->
      </div>
    </div>
  </div>
</template>
<script>
import {
  // 文件上传
  addFileInfo,
  getWdInfoDatas,
  deleteFileItem
} from "../../../db/bgwdgldb.js";
import { getDeportConfigPathDev,getPdfNormal } from '../../../utils/pathUtil' // 获取文件路径
import { encryptAes,decryptAes } from '../../../utils/aesUtils' // 文件加密
export default {
  components: {},
  props: {},
  data() {
    return {
      pageLoading: false, // loading
      wdList: [], // 文档列表数据
      tableDataCopy: [], // 备份数据
      // 改变了首行样式
      headerCellStyle: {
        background: '#EEF7FF', color: '#4D91F8'
      },
      // eltable高度
      tableHeight: 'calc(100% - 34px - 41px - 3px)',
      // 文档类型
      wdlx: [
        {
          lxmc: '通用',
          lxid: '1'
        },
        {
          lxmc: '涉密信息系统集成资质',
          lxid: '2'
        },
        {
          lxmc: '武器装备科研生产单位资格',
          lxid: '3'
        },
        {
          lxmc: '国家秘密载体印刷资质',
          lxid: '4'
        }
      ],
      sltshow: '', // 文档的缩略图显示
      sltFileData: {},
      // 上传文档信息
      tjlist: {
        wdlx: '',
        wdmc: '',
        wdys: '',
        wdslt: '',
        wdscsj: '',
        wdzy: '',
        wdBuffer: null
      },
      page: 1, // 当前页
      pageSize: 10, // 每页条数
      formInline: {}, // 查询条件
      uploadDisable: true,
      //表单验证
      rules: {
        wdlx: [{
          required: true,
          message: '请输入文档类型',
          trigger: 'blur'
        }],
        wdmc: [{
          required: true,
          message: '请上传文件',
          trigger: 'blur'
        }],
        wdys: [{
          required: true,
          message: '请输入文档总页数',
          trigger: 'blur'
        }],
        wdslt: [{
          required: true,
          message: '请上传文档缩略图',
          trigger: 'blur'
        }]
      },
      // total: 0,
      selectlistRow: [], //列表的值
      dr_dialog: false, // 上传弹框
    }
  },
  computed: {},
  mounted() {
    //列表初始化
    this.getWdList()
    //绑定监听表格导入事件
    this.$refs.upload.addEventListener('change', e => {
      this.readExcel(e);
    })
  },
  // 监听
  watch:{
    'tjlist.wdmc':{ //监听的对象
        deep:true, //深度监听设置为 true
        handler:function(newV,oldV){
          if(newV == ''){
            this.uploadDisable = true
          }
        }
    }
  },
  methods: {
    // 根据文件名称生成文件id
    toCode(str) {
      var key = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"
      var len = key.length
      var a = key.split("")
      var s = "", b, b1, b2, b3
      for (var i = 0; i < str.length; i++) {
        b = str.charCodeAt(i)
        b1 = b % len
        b = (b - b1) / len
        b2 = b % len
        b = (b - b2) / len
        b3 = b % len
        s += a[b3] + a[b2] + a[b1]
      }
      return s
    },
    // 上传的缩略图格式限定jpg/png
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg';
      const isPNG = file.type === 'image/png';
      // const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isJPG && !isPNG) {
        this.$message.error('上传缩略图只能是 JPG/PNG 格式!');
      }
      return isJPG || isPNG;
    },
    // 不用action 
    httpRequest(data) {
      this.sltshow = URL.createObjectURL(data.file);
      this.sltFileData.file = data.file
      this.sltFileData.type = data.file.type
      this.tjlist.wdslt = '1'
    },
    //导入
    chooseFile() {
      this.$refs.upload.click()
    },
    // 上传文件确定
    submitTj(formName) {
      this.pageLoading = true
      let nowkeyStr = 'hsoftBanner' + this.toCode(this.tjlist.wdmc)
      if (this.wdList.length > 0) {
        var findItem = this.wdList.find((item) => {
          return item.id == nowkeyStr
        })
      }
      this.$refs[formName].validate(async (valid) => {
        if (valid && findItem == undefined) {
          const fs = require('fs')
          let filePath = getDeportConfigPathDev(this.tjlist.wdmc)
          let id = this.toCode(this.tjlist.wdmc)
          let keyStr = 'hsoftBanner' + id
          var buf = this.tjlist.wdBuffer;
          let encryptStr
          encryptStr = await encryptAes(JSON.stringify(buf), keyStr)
          fs.writeFileSync(filePath, encryptStr, { encoding: 'utf8' })
          // 向文件夹中添加缩略图
          const base = new FileReader()
          base.readAsArrayBuffer(this.sltFileData.file)
          let result = getDeportConfigPathDev('all') // 获取文件路径
          let wdmc = this.tjlist.wdmc
          let hz
          if(this.sltFileData.type == 'image/png'){
            hz = '.png'
          }else if(this.sltFileData.type == 'image/jpeg'){
            hz = '.jpg'
          }
          base.onload = () => {
            fs.writeFileSync(result + wdmc + hz, Buffer.from(base.result))
          }
          // 向文档列表中添加文档信息
          var date = new Date(new Date().getTime());
          let Y = date.getFullYear() + '-';
          let M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
          let D = date.getDate() + ' ';
          let h = date.getHours() + ':';
          let m = date.getMinutes() + ':';
          let s = date.getSeconds();
          let params = {
            id: keyStr,
            wdlx: this.tjlist.wdlx,
            wdmc: this.tjlist.wdmc,
            wdys: this.tjlist.wdys,
            wdslt: this.tjlist.wdslt,
            wdzy: this.tjlist.wdzy,
            wdsltType: hz,
            wdscsj: Y + M + D + h + m + s,
          }
          addFileInfo(params)
          this.dr_dialog = false
          this.$message({
            message: '添加成功',
            type: 'success'
          });
          this.resetForm()
          this.getWdList()
        } else if (valid && findItem != undefined) {
          this.$message.error('此文件已经存在，请勿重复上传！');
          this.resetForm()
          this.dr_dialog = false
          this.pageLoading = false
          return false;
        } else if (!valid) {
          this.$message.error('请将文件信息填写完整');
          this.resetForm()
          this.dr_dialog = false
          this.pageLoading = false
          return false;
        }
        this.pageLoading = false
      });
    },
    //----表格导入方法
    readExcel(e) {
      const file = e.target.files;
      const fr = new FileReader();
      this.tjlist.wdmc = file[0].name
      fr.readAsArrayBuffer(file[0])
      fr.onload = (e) => {
        // 文件的ArrayBuffer结果
        const buffer = Buffer.from(e.target.result)
        // let jsonBuffer = JSON.stringify(buffer, null, 2)
        this.tjlist.wdBuffer = buffer
        this.uploadDisable = false
      }
    },
    // 获取文档列表数据
    getWdList() {
      let params = {
        page: this.page,
        pageSize: this.pageSize
      }
      Object.assign(params, this.formInline)
      let resList = getWdInfoDatas(params)
      this.wdList = resList.list_total
      this.tableDataCopy = resList.list_total
      // this.total = resList.total
    },
    // 下载文件
    downloadFileItem(row) {
      let fromPath = getDeportConfigPathDev(row.wdmc) // 获取文件路径 
      let wjm = row.wdmc // 获取文件名
      // 获取文件流
      const FS = require('fs')
      const {
        dialog
      } = require('electron').remote
      let options = {
        title: '请选择文件保存路径',
        properties: ['openDirectory'],
        defaultPath: wjm
      }
      dialog.showSaveDialog(options, async result => {
        try {
          let buffer = FS.readFileSync(fromPath)
          let jmkey = row.id
          // console.log('jmkey', jmkey)
          let encryptStr = buffer
          let jmdatas = await decryptAes(encryptStr.toString(), jmkey)
          if (buffer) {
            // 保存文件到新路径下
            FS.writeFileSync(result, new Buffer(JSON.parse(jmdatas)))
          }
          this.$message.success('文件[' + wjm + ']下载成功')
        } catch (error) {
          error = errorProcessor(error)
          let errObj = JSON.parse(error.message)
          this.$notify({
            title: '系统异常',
            message: '[' + errObj.mark + ']\n',
            type: 'error',
            offset: 100,
            duration: 0
          })
          return
        }
      })
    },
    //查询
    onSubmit() {
      // 备份了一下数据
      let arr = this.tableDataCopy
      // 通过遍历key值来循环处理
      Object.keys(this.formInline).forEach(e => {
        arr = this.filterFunc(this.formInline[e], e, arr)
      })
      // 为表格赋值
      this.wdList = arr
    },
    filterFunc(val, target, filterArr) {
      // 参数不存在或为空时，就相当于查询全部
      if (val == undefined || val == '') {
        return filterArr
      }
      return filterArr.filter(p => {
        return p[target].indexOf(val) > -1
      })
    },
    // 返回报告管理列表页 
    returnSy() {
      this.$router.push("/bgglsy");
    },
    //删除
    deleteFiles(id) {
      if (this.selectlistRow != '') {
        this.$confirm('是否继续删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let valArr = this.selectlistRow
          let _that = this
          valArr.forEach(function (item) {
            const fs = require('fs')
            console.log('.'+item.wdmc.split('.').pop())
            let fromPath = getDeportConfigPathDev(item.wdmc) // 获取文件路径 
            let formImgPath = getDeportConfigPathDev(item.wdmc + item.wdsltType)
            if('.'+item.wdmc.split('.').pop() == '.pdf'){
              console.log('我是删除pdf文件')
              let formPdfPath = getPdfNormal(item.wdmc)
              fs.unlink(formPdfPath, function (error) {
                if (error) {
                  console.log(error);
                  return false;
                }
              })
            }
            // let formPdfPath = getDeportConfigPathDev(item.wdmc + item.wdsltType)
            fs.unlink(fromPath, function (error) {
              if (error) {
                console.log(error);
                return false;
              }
              deleteFileItem(item)
              _that.getWdList()
              _that.$message({
                message: '文件删除成功',
                type: 'success'
              });
            })
            fs.unlink(formImgPath, function (error) {
              if (error) {
                console.log(error);
                return false;
              }
            })
          })
        }).catch(() => {
          this.$message('已取消删除')
        })
      } else {
        this.$message({
          message: '未选择删除记录，请选择下列列表',
          type: 'warning'
        });
      }
    },
    // 复选框
    selectRow(val) {
      this.selectlistRow = val;
    },
    closeDialog(formName){
      this.dr_dialog = false
      this.$refs[formName].resetFields();
      this.resetForm()
    },
    //列表分页--跳转页数
    // handleCurrentChange(val) {
    //   this.page = val
    //   this.getWdList()
    // },
    //列表分页--更改每页显示个数
    // handleSizeChange(val) {
    //   this.page = 1
    //   this.pageSize = val
    //   this.getWdList()
    // },
    //重置
    resetForm() {
      this.tjlist.wdlx = ''
      this.tjlist.wdmc = ''
      this.tjlist.wdys = ''
      this.tjlist.wdzy = ''
      this.sltshow = ''
      this.tjlist.wdscsj = ''
      this.tjlist.wdBuffer = null
      this.sltFileData = {}
    }
  }
}
</script>

<style scoped>
.bgHeight {
  height: calc(100% - 38px);
}
.uploadClass {
  top: 10px;
  right: 0;
  height: 32px;
  width: 56px;
  z-index: 1;
}

.eltableClass {
  border: 1px solid #EBEEF5;
}

.uploadButton {
  background: #409EFF;
  border-radius: 5px;
  padding: 0px 10px;
  color: #ffffff;
}

.inputWidth {
  width: 85%;
}

.maxWidthCalc {
  width: calc(100% - 120px);
}

.bg_con /deep/ .el-dialog__body .el-form>div .el-form-item__label {
  border: 0px;
  border-right: 1px solid rgba(235, 235, 235, 1);
}

.bg_con /deep/ .el-form-item__content {
  width: 100% !important;
}
.picHeight {
  height: 210px !important;
}
.textareaHeight {
  height: 150px !important;
}
.bg_con /deep/.avatar-uploader-icon {
  border: 2px solid #EBEBEB;
}
/deep/ .el-dialog__body .el-form .el-form-item__error {
  bottom: -10px;
}
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
.bg_con {
  width: 100%;
}
.dabg {
  /* margin-top: 10px; */
  box-shadow: 0px 2px 12px 0px rgba(9, 70, 255, 0.13);
  border-radius: 8px;
  width: 100%;
}
.item_button {
  height: 100%;
  float: left;
  padding-left: 10px;
  line-height: 50px;
}
.widthx {
  width: 8vw;
}
/deep/.mhcx .el-form-item {
  /* margin-top: 5px; */
  margin-bottom: 5px;
}
.dialog-footer {
  display: block;
  margin-top: 10px;
}
.zyposition {
  color: gray!important;
  color: gray!important;
  position: absolute;
  bottom: 10px;
  left: 200px;
}
</style>