import {
    getYz
} from "../../../db/syszjb";
import {
    getJgxx
} from "../../../db/zzjgdb";
import {
    //内容管理初始化成员列表
    getAllBmzd,
    getSmgwglList,
    getZgsmrylList,
    getRyxzhzList,
    getGwbgList,
    getLglzList,
    getJypxList,
    getCsglList,
    getCsbgList,
    getSmjsjList,
    getFsmjsjList,
    getSmydccjzList,
    getSmbgzdhsbList,
    getFsmbgzdhsbList,
    getSmwlsbList,
    getFmwlsbList,
    getAqcpList,
    getSmzttzList,
    getZczpRiskDatas,
    getDwxxDatas
} from "../../../db/yjjcdb";
export default {
    data() {
        return {
            score: 0,
            progressShow: false,
            // 一键检测评分
            allCounts: 100,
            proNumber: 0,
            checkCount: {
                proJurisdictionOptimizeCount: 0,// 保密制度--优化个数
                proJurisdictionriskCount: 0,// 保密制度--风险个数
                bmwtzs: 0,
                organizationOptimizeCount: 0,
                organizationriskCount: 0,
                zzwtzs: 0,
                secretSitesriskCount: 0, // 涉密场所--风险个数
                secretSitesOptimizeCount: 0, // 涉密场所--优化个数
                smcswtzs: 0,
                secretPersonnelOptimizeCount: 0,
                secretPersonnelriskCount: 0,
                smrywtzs: 0,
                eduTrainOptimizeCount: 0, // 教育培训--优化个数
                eduTrainriskCount: 0,// 教育培训--风险个数
                jypxwtzs: 0,
                deviceOptimizeCount: 0,
                deviceriskCount: 0,
                sbxxwtzs: 0,
                carrierOptimizeCount: 0, // 涉密载体--优化个数
                carrierriskCount: 0,// 涉密载体--风险个数
                smztwtzs: 0,
                selfEvaluationriskCount: 0,// 自查自评--风险个数
                zczpwtzs: 0,
            },
            // 自检评分
            statusArr: {
                // 保密制度
                proJurisdictionShow1: false,// 保密制度--没问题显示提示文字
                proJurisdictionShow2: false,// 保密制度--优化项显示文字
                proJurisdictionShow3: false,// 保密制度--风险项显示文字
                proJurisdictionLoadingShow: false,// 保密制度--Loading
                proJurisdictionAllRight1: false,// 保密制度--未发现问题--查看场所管理
                proJurisdictionShowOptimize1: false,// 保密制度--优化1
                proJurisdictionShowRisk1: false,// 保密制度--风险1
                // 组织机构
                organizationShow1: false,// 组织机构--没问题显示提示文字
                organizationShow2: false,// 组织机构--优化项显示文字
                organizationShow3: false,// 组织机构--风险项显示文字
                organizationLoadingShow: false,// 组织机构--Loading
                organizationAllRight1: false,// 组织机构--未发现问题
                organizationShowOptimize1: false,// 组织机构--优化1
                organizationShowRisk1: false,// 组织机构--风险1
                organizationShowRisk2: false,// 组织机构--风险2
                organizationShowRisk3: false,// 组织机构--风险3
                organizationShowRisk4: false,// 组织机构--风险4
                // 涉密人员
                secretPersonnelShow1: false,
                secretPersonnelShow2: false,
                secretPersonnelShow3: false,
                secretPersonnelLoadingShow: false,
                secretPersonnelAllRight1: false,
                secretPersonnelAllRight2: false,
                secretPersonnelAllRight3: false,
                secretPersonnelAllRight4: false,
                secretPersonnelAllRight5: false,
                secretPersonnelShowRisk1: false,
                secretPersonnelShowRisk2: false,
                secretPersonnelShowRisk3: false,
                secretPersonnelShowOptimize1: false,
                secretPersonnelShowOptimize2: false,
                secretPersonnelShowOptimize3: false,
                secretPersonnelShowOptimize4: false,
                secretPersonnelShowOptimize5: false,
                // 教育培训
                eduTrainShow1: false,
                eduTrainShow2: false,
                eduTrainShow3: false,
                eduTrainLoadingShow: false,
                eduTrainAllRight1: false,
                eduTrainShowOptimize1: false,
                eduTrainShowRisk1: false,
                // 涉密场所
                secretSitesShow1: false,// 涉密场所--没问题显示提示文字
                secretSitesShow2: false,// 涉密场所--优化项显示文字
                secretSitesShow3: false,// 涉密场所--风险项显示文字
                secretSitesAllRight1: false, // 涉密场所--未发现问题--查看场所管理
                secretSitesAllRight2: false, // 涉密场所--未发现问题--查看场所变更
                secretSitesShowRisk1: false, // 涉密场所--风险1
                secretSitesShowOptimize1: false, // 涉密场所--优化1
                secretSitesShowOptimize2: false, // 涉密场所--优化2
                secretSitesLoadingShow: false, // 涉密场所--Loading
                // 设备信息
                deviceShow1: false,
                deviceShow2: false,
                deviceShow3: false,
                deviceLoadingShow: false,
                deviceAllRight1: false,
                deviceAllRight2: false,
                deviceAllRight3: false,
                deviceAllRight4: false,
                deviceAllRight5: false,
                deviceAllRight6: false,
                deviceAllRight7: false,
                deviceAllRight8: false,
                deviceShowOptimize1: false,
                deviceShowOptimize2: false,
                deviceShowOptimize3: false,
                deviceShowOptimize4: false,
                deviceShowOptimize5: false,
                deviceShowOptimize6: false,
                deviceShowOptimize7: false,
                deviceShowOptimize8: false,
                deviceShowRisk1: false,
                deviceShowRisk2: false,
                deviceShowRisk3: false,
                deviceShowRisk4: false,
                deviceShowRisk5: false,
                deviceShowRisk6: false,
                deviceShowRisk7: false,
                deviceShowRisk8: false,
                // 涉密载体
                carrierShow1: false,
                carrierShow2: false,
                carrierShow3: false,
                carrierLoadingShow: false,
                carrierAllRight1: false,
                carrierShowOptimize1: false,
                carrierShowRisk1: false,
                // 自查自评
                selfEvaluationShow1: false,
                selfEvaluationShow2: false,
                selfEvaluationShow3: false,
                selfEvaluationAllRight1: false,
                selfEvaluationLoadingShow: false,
                selfEvaluationAShowRisk1: false,
                selfEvaluationAShowRisk2: false,
                selfEvaluationAShowRisk3: false,
                selfEvaluationAShowRisk4: false,
            }
        }
    },
    watch: {
        // allCounts(newVal){
        //     if(newVal<0){
        //         this.allCounts = 0
        //         this.$nextTick(() => {
        //             this.getLoadEcharts()
        //         })
        //     }
        // }
    },
    methods: {
        // 一键检测----检测保密制度检测信息
        getConfidentialitySystem() {
            this.score = 1
            this.statusArr.proJurisdictionLoadingShow = true
            let that = this
            setTimeout(function () {
                // 需要执行的代码
                let resListArr = getAllBmzd()
                let resYz = getYz('bmzdgxyz')[0]
                // 统计优化和风险个数
                let optimizeCounts = []
                let riskCounts = []
                // 检查是否全部都没问题
                let lock = 0
                // 此模块应扣分数
                let allkfs = 0
                // 获取所有时间并筛选最后一次更新时间
                let bmzdTimeArr = []
                resListArr.forEach((item) => {
                    let { gxsj } = item
                    let time = new Date().getTime() - gxsj
                    bmzdTimeArr.push(time)
                });
                let diffDay = parseInt(Math.min(...bmzdTimeArr) / (1000 * 60 * 60 * 24))
                // 优化1---长时间未对保密制度进行更新
                if (resListArr.length > 0 && diffDay > resYz.csz) {
                    that.statusArr.proJurisdictionShowOptimize1 = true
                    that.statusArr.proJurisdictionShow2 = true
                    // that.allCounts = that.allCounts - 5
                    optimizeCounts.push('1')
                    lock = lock + 1
                    allkfs = allkfs + 5
                }
                // 风险-未建立保密制度清单
                if (resListArr.length == 0) {
                    that.statusArr.proJurisdictionShowRisk1 = true
                    that.statusArr.proJurisdictionShow3 = true
                    // that.allCounts = that.allCounts - 10// 扣分
                    riskCounts.push('1')
                    lock = lock + 1
                    allkfs = allkfs + 12
                }
                // 更新总分
                that.allCounts = allkfs > 12 ? that.allCounts - 12 : that.allCounts - allkfs
                // 更新echarts分数图
                that.getLoadEcharts()
                // 全部ok没有问题
                // && resListArr.length != 0
                if (lock == 0) {
                    that.statusArr.proJurisdictionAllRight1 = true
                    that.statusArr.proJurisdictionAllRight2 = true
                    that.statusArr.proJurisdictionShow1 = true
                    that.statusArr.proJurisdictionLoadingShow = false
                }
                // 获取优化个数以及风险个数
                that.checkCount.proJurisdictionOptimizeCount = optimizeCounts.length > 0 ? optimizeCounts.length : 0
                that.checkCount.proJurisdictionriskCount = riskCounts.length > 0 ? riskCounts.length : 0
                that.checkCount.bmwtzs = that.checkCount.proJurisdictionOptimizeCount + that.checkCount.proJurisdictionriskCount
                console.log(that.checkCount.bmwtzs)
                that.statusArr.proJurisdictionLoadingShow = false
                // 更新进度条
                that.score = 12.5
                // 更新状态
                localStorage.setItem('checkCount', JSON.stringify(that.checkCount))
                localStorage.setItem('allShowState', JSON.stringify(that.statusArr))
                localStorage.setItem('allCounts', that.allCounts)
                // that.activeNames = ['2', '3', '4', '5', '6', '7', '8']
                // 继续检查
                that.getOrganizationDatas() // 一键检测---获取组织机构检测信息
            }, 1000);
        },
        // 一键检测----检测组织机构检测信息
        getOrganizationDatas() {
            this.statusArr.organizationLoadingShow = true
            let that = this
            setTimeout(function () {
                // 需要执行的代码
                let resListArr = getJgxx()
                let resYz = getYz('zzjggxyz')[0]
                let resDwxx = getDwxxDatas()
                // 统计优化和风险个数
                let optimizeCounts = []
                let riskCounts = []
                // 检查是否全部都没问题
                let lock = 0
                // 此模块应扣分数
                let allkfs = 0
                // 获取所有时间并筛选最后一次更新时间
                let zzjgTimeArr = []
                resListArr.forEach((item) => {
                    let { gxsj } = item
                    // var date = new Date(cjsj).getTime()
                    let time = new Date().getTime() - gxsj
                    zzjgTimeArr.push(time)
                });
                let kfItem = resListArr.some((item) => {
                    let { bmflag } = item
                    if (bmflag == 0) {
                        return item
                    }
                });
                console.log(resListArr)
                // 风险1---未按要求设置负责的保密机构
                if (resListArr.length > 0 && kfItem) {
                    that.statusArr.organizationShowRisk2 = true
                    that.statusArr.organizationShow3 = true
                    // that.allCounts = that.allCounts - 5
                    allkfs = allkfs + 5
                    riskCounts.push('1')
                    lock = lock + 1
                }
                let diffDay = parseInt(Math.min(...zzjgTimeArr) / (1000 * 60 * 60 * 24))
                // 优化---长时间未对组织机构进行更新
                if (resListArr.length > 0 && diffDay > resYz.csz) {
                    that.statusArr.organizationShowOptimize1 = true
                    that.statusArr.organizationShow2 = true
                    // that.allCounts = that.allCounts - 2
                    allkfs = allkfs + 2
                    optimizeCounts.push('1')
                    lock = lock + 1
                }
                // 风险---未建立组织机构清单
                if (resListArr.length == 0 || resListArr.length == 1) {
                    that.statusArr.organizationShowRisk1 = true
                    that.statusArr.organizationShow3 = true
                    // that.allCounts = that.allCounts - 10
                    allkfs = allkfs + 10
                    riskCounts.push('2')
                    lock = lock + 1
                }
                let zgsmryListArr = getZgsmrylList() // 在岗涉密人员数据
                // 在岗涉密人员数据
                let bhgItem = zgsmryListArr.some((item) => {
                    let { gwmc } = item
                    if (gwmc == '保密总监' && resDwxx[0].sscc == 12) {
                        return item
                    }
                });
                // 风险---未按要求设置专职保密总监岗位（甲级资质单位）
                if (zgsmryListArr.length > 0 && bhgItem) {
                    that.statusArr.organizationShowRisk3 = true
                    that.statusArr.organizationShow3 = true
                    allkfs = allkfs + 5
                    riskCounts.push('3')
                    lock = lock + 1
                }
                if (optimizeCounts.length > 0) {
                    that.checkCount.organizationOptimizeCount = optimizeCounts.length
                }
                if (riskCounts.length > 0) {
                    that.checkCount.organizationriskCount = riskCounts.length
                }
                that.checkCount.zzwtzs = that.checkCount.organizationOptimizeCount + that.checkCount.organizationriskCount
                // 全部ok没有问题
                // && resListArr.length != 0
                if (lock == 0) {
                    that.statusArr.organizationAllRight1 = true
                    that.statusArr.organizationShow1 = true
                    that.statusArr.organizationLoadingShow = false
                }
                that.statusArr.organizationLoadingShow = false
                that.score = 25

                that.allCounts = allkfs > 10 ? that.allCounts - 10 : that.allCounts - allkfs
                that.getLoadEcharts()

                localStorage.setItem('checkCount', JSON.stringify(that.checkCount))
                localStorage.setItem('allShowState', JSON.stringify(that.statusArr))
                localStorage.setItem('allCounts', that.allCounts)
                // that.activeNames = ['3', '4', '5', '6', '7', '8']
                that.getSecretPersonnelDatas()
            }, 1000);
        },
        // 一键检测----检测涉密人员检测信息
        getSecretPersonnelDatas() {
            this.statusArr.secretPersonnelLoadingShow = true
            let that = this
            setTimeout(function () {
                // 获取各个表中数据
                let smgwListArr = getSmgwglList() // 涉密岗位数据
                let zgsmryListArr = getZgsmrylList() // 在岗涉密人员数据
                let ryxzhzListArr = getRyxzhzList() // 人员新增汇总数据
                let gwbgListArr = getGwbgList() // 岗位变更数据
                let lglzListArr = getLglzList() // 离岗离职数据
                // 获取各个模块相应阈值
                let smgwgxYz = getYz('smgwgxyz')[0]
                let zgsmryYz = getYz('zgsmzrgxyz')[0]
                let ryxzhzYz = getYz('ryxzhzgxyz')[0]
                let gwbgYz = getYz('gwbggxyz')[0]
                let lglzYz = getYz('lglzgxyz')[0]
                // 统计优化和风险个数
                let optimizeCounts = []
                let riskCounts = []
                // 检查是否全部都没问题
                let lock = 0
                // 此模块应扣分数
                let allkfs = 0
                // 获取所有时间并筛选最后一次更新时间
                let smgwTimeArr = []
                let zgsmryTimeArr = []
                let ryxzhzTimeArr = []
                let gwbgTimeArr = []
                let lglzTimeArr = []
                // 涉密岗位数据
                smgwListArr.forEach((item) => {
                    let { gxsj } = item
                    // var date = new Date(gxsj).getTime()
                    let time = new Date().getTime() - gxsj
                    smgwTimeArr.push(time)
                });
                // 在岗涉密人员数据
                zgsmryListArr.forEach((item) => {
                    let { gxsj } = item
                    // var date = new Date(gxsj).getTime()
                    let time = new Date().getTime() - gxsj
                    zgsmryTimeArr.push(time)
                });
                let kfItem = zgsmryListArr.some((item) => {
                    let { sfsc } = item
                    if (sfsc == '否') {
                        return item
                    }
                });
                // 风险1---未按要求对涉密人员进行审查
                if (kfItem) {
                    that.statusArr.secretPersonnelShowRisk1 = true
                    that.statusArr.secretPersonnelShow3 = true
                    // that.allCounts = that.allCounts - 5
                    allkfs = allkfs + 5
                    riskCounts.push('1')
                    lock = lock + 1
                }
                // 人员新增汇总数据
                ryxzhzListArr.forEach((item) => {
                    let { gxsj } = item
                    // var date = new Date(gxsj).getTime()
                    let time = new Date().getTime() - gxsj
                    ryxzhzTimeArr.push(time)
                });
                // 岗位变更数据
                gwbgListArr.forEach((item) => {
                    let { gxsj } = item
                    // var date = new Date(gxsj).getTime()
                    let time = new Date().getTime() - gxsj
                    gwbgTimeArr.push(time)
                });
                // 离岗离职数据
                lglzListArr.forEach((item) => {
                    let { gxsj } = item
                    // var date = new Date(gxsj).getTime()
                    let time = new Date().getTime() - gxsj
                    lglzTimeArr.push(time)
                });
                // 最近一次更新时间转换成时间戳
                let diffDay = parseInt(Math.min(...smgwTimeArr) / (1000 * 60 * 60 * 24))
                let diffDay1 = parseInt(Math.min(...zgsmryTimeArr) / (1000 * 60 * 60 * 24))
                let diffDay2 = parseInt(Math.min(...ryxzhzTimeArr) / (1000 * 60 * 60 * 24))
                let diffDay3 = parseInt(Math.min(...gwbgTimeArr) / (1000 * 60 * 60 * 24))
                let diffDay4 = parseInt(Math.min(...lglzTimeArr) / (1000 * 60 * 60 * 24))
                // 优化---长时间未对涉密岗位进行更新
                if (smgwListArr.length > 0 && diffDay > smgwgxYz.csz) {
                    that.statusArr.secretPersonnelShowOptimize1 = true
                    that.statusArr.secretPersonnelShow2 = true
                    // that.allCounts = that.allCounts - 2
                    allkfs = allkfs + 2
                    optimizeCounts.push('1')
                    lock = lock + 1
                }
                // 优化---长时间未对在岗涉密人员进行更新
                if (zgsmryListArr.length > 0 && diffDay1 > zgsmryYz.csz) {
                    that.statusArr.secretPersonnelShowOptimize2 = true
                    that.statusArr.secretPersonnelShow2 = true
                    // that.allCounts = that.allCounts - 2
                    allkfs = allkfs + 2
                    optimizeCounts.push('2')
                    lock = lock + 1
                }
                // 优化---未建立或长时间未对人员新增汇总进行更新
                if (ryxzhzListArr.length > 0 && diffDay2 > ryxzhzYz.csz) {
                    that.statusArr.secretPersonnelShowOptimize3 = true
                    that.statusArr.secretPersonnelShow2 = true
                    // that.allCounts = that.allCounts - 5
                    allkfs = allkfs + 5
                    optimizeCounts.push('3')
                    lock = lock + 1
                }
                // 优化---未建立或长时间未对岗位变更进行更新
                if (gwbgListArr.length > 0 && diffDay3 > gwbgYz.csz) {
                    that.statusArr.secretPersonnelShowOptimize4 = true
                    that.statusArr.secretPersonnelShow2 = true
                    // that.allCounts = that.allCounts - 5
                    allkfs = allkfs + 5
                    optimizeCounts.push('4')
                    lock = lock + 1
                }
                // 优化---未建立或长时间未对离岗离职进行更新
                if (lglzListArr.length > 0 && diffDay4 > lglzYz.csz) {
                    that.statusArr.secretPersonnelShowOptimize5 = true
                    that.statusArr.secretPersonnelShow2 = true
                    // that.allCounts = that.allCounts - 5
                    allkfs = allkfs + 5
                    optimizeCounts.push('5')
                    lock = lock + 1
                }
                // 风险---未建立涉密岗位清单
                if (smgwListArr.length == 0) {
                    that.statusArr.secretPersonnelShowRisk2 = true
                    that.statusArr.secretPersonnelShow3 = true
                    // that.allCounts = that.allCounts - 10
                    allkfs = allkfs + 10
                    riskCounts.push('2')
                    lock = lock + 1

                }
                // 风险---未建立在岗涉密人员台账
                if (zgsmryListArr.length == 0) {
                    that.statusArr.secretPersonnelShowRisk3 = true
                    that.statusArr.secretPersonnelShow3 = true
                    // that.allCounts = that.allCounts - 10
                    allkfs = allkfs + 10
                    riskCounts.push('2')
                    lock = lock + 1
                }
                if (optimizeCounts.length > 0) {
                    that.checkCount.secretPersonnelOptimizeCount = optimizeCounts.length
                }
                if (riskCounts.length > 0) {
                    that.checkCount.secretPersonnelriskCount = riskCounts.length
                }
                that.checkCount.smrywtzs = that.checkCount.secretPersonnelOptimizeCount + that.checkCount.secretPersonnelriskCount
                // 全部ok没有问题
                // && smgwListArr.length != 0 && zgsmryListArr.length != 0
                if (lock == 0) {
                    that.statusArr.secretPersonnelAllRight1 = true
                    that.statusArr.secretPersonnelAllRight2 = true
                    that.statusArr.secretPersonnelAllRight3 = true
                    that.statusArr.secretPersonnelAllRight4 = true
                    that.statusArr.secretPersonnelAllRight5 = true
                    that.statusArr.secretPersonnelShow1 = true
                    that.statusArr.secretPersonnelLoadingShow = false
                }
                that.statusArr.secretPersonnelLoadingShow = false
                that.score = 37.5

                that.allCounts = allkfs > 10 ? that.allCounts - 10 : that.allCounts - allkfs
                that.getLoadEcharts()

                // that.activeNames = ['4', '5', '6', '7', '8']
                localStorage.setItem('checkCount', JSON.stringify(that.checkCount))
                localStorage.setItem('allShowState', JSON.stringify(that.statusArr))
                localStorage.setItem('allCounts', that.allCounts)
                that.getEduTrainDatas()
            }, 1000);
        },
        // 一键检测---检测教育培训信息
        getEduTrainDatas() {
            // this.score = 43
            this.statusArr.eduTrainLoadingShow = true
            let that = this
            setTimeout(function () {
                // 获取各个表中数据
                let jypxListArr = getJypxList() // 获取教育培训数据
                let zgsmryListArr = getZgsmrylList() // 在岗涉密人员数据
                // 获取各个模块相应阈值
                let jypxqdYz = getYz('pxqdgxyz')[0]
                // 统计优化和风险个数
                let optimizeCounts = []
                let riskCounts = []
                // 检查是否全部都没问题
                let lock = 0
                // 此模块应扣分数
                let allkfs = 0
                // 获取所有时间并筛选最后一次更新时间
                let jypxqdTimeArr = []
                // 涉密岗位数据
                jypxListArr.forEach((item) => {
                    let { gxsj } = item
                    let time = new Date().getTime() - gxsj
                    jypxqdTimeArr.push(time)
                });
                console.log(jypxqdTimeArr)
                // 最近一次更新时间转换成时间戳
                let diffDay = parseInt(Math.min(...jypxqdTimeArr) / (1000 * 60 * 60 * 24))
                // 风险---教育培训学识未达到要求
                let newArrId = []
                let xsjc = 'normal'
                // 去在岗涉密人员查每一个人的培训学时 目前是如果有小于10学识的 就扣分
                if (zgsmryListArr.length > 0 && jypxListArr.length > 0) {
                    zgsmryListArr.forEach((item) => {
                        let { smryid } = item
                        jypxListArr.forEach((i) => {
                            let { ry } = i
                            ry.forEach((r) => {
                                r.ks = i.ks
                                r.pxrq = i.pxrq
                                if (smryid == r.smryid) {
                                    newArrId.push(r)
                                }
                            })
                        })
                    })
                    zgsmryListArr.forEach((item) => {
                        let { smryid } = item
                        let ksArr = newArrId.filter((i) => {
                            // && new Date(time).getFullYear() == new Date().getFullYear()
                            return smryid == i.smryid
                        })
                        item.arr = ksArr
                        item.zks = []
                        let sum = 0;
                        if (item.arr && item.arr.length > 0) {
                            item.arr.forEach((l) => {
                                item.zks.push(l.ks)
                            })
                            if (item.zks.length > 0) {
                                item.zks.forEach((k) => {
                                    item.zkeshi = sum += parseInt(k)
                                })
                            }
                        }
                        if (item.zkeshi < 10) {
                            xsjc = 'sksStatus'
                        }
                    })
                    if (xsjc == 'sksStatus') {
                        that.statusArr.eduTrainShowRisk1 = true
                        that.statusArr.eduTrainShow3 = true
                        allkfs = allkfs + 9
                        riskCounts.push('1')
                        lock = lock + 1
                    }
                }else if(jypxListArr.length == 0){
                    that.statusArr.eduTrainShowRisk1 = true
                    that.statusArr.eduTrainShow3 = true
                    allkfs = allkfs + 9
                    riskCounts.push('1')
                    lock = lock + 1
                }
                // 优化---长时间未对培训清单进行更新
                if (jypxListArr.length > 0 && diffDay > jypxqdYz.csz) {
                    that.statusArr.eduTrainShowOptimize1 = true
                    that.statusArr.eduTrainShow2 = true
                    // that.allCounts = that.allCounts - 2
                    allkfs = allkfs + 5
                    optimizeCounts.push('1')
                    lock = lock + 1
                }

                if (optimizeCounts.length > 0) {
                    that.checkCount.eduTrainOptimizeCount = optimizeCounts.length
                }
                if (riskCounts.length > 0) {
                    that.checkCount.eduTrainriskCount = riskCounts.length
                }
                that.checkCount.jypxwtzs = that.checkCount.eduTrainOptimizeCount + that.checkCount.eduTrainriskCount
                // 全部ok没有问题
                if (lock == 0) {
                    that.statusArr.eduTrainAllRight1 = true
                    that.statusArr.eduTrainShow1 = true
                    that.statusArr.eduTrainLoadingShow = false
                }
                that.statusArr.eduTrainLoadingShow = false
                that.score = 50

                that.allCounts = allkfs > 9 ? that.allCounts - 9 : that.allCounts - allkfs
                that.getLoadEcharts()

                // that.activeNames = ['5', '6', '7', '8']
                localStorage.setItem('checkCount', JSON.stringify(that.checkCount))
                localStorage.setItem('allShowState', JSON.stringify(that.statusArr))
                localStorage.setItem('allCounts', that.allCounts)
                that.getSecretSitesDatas() // 一键检测---获取涉密场所检测信息
            }, 1000);
        },
        // 一键检测---获取涉密场所检测信息
        getSecretSitesDatas() {
            this.statusArr.secretSitesLoadingShow = true
            let that = this
            setTimeout(function () {
                // 需要执行的代码
                let resListArr = getCsglList() // 场所管理
                let csbgListArr = getCsbgList()
                let resYz = getYz('smcsgxyz')[0]
                let resYz2 = getYz('smcsgbgxyz')[0]
                // 统计优化和风险个数
                let optimizeCounts = []
                let riskCounts = []
                // 检查是否全部都没问题
                let lock = 0
                // 此模块应扣分数
                let allkfs = 0
                let smcsTimeArr = []
                let csbgTimeArr = []
                // 优化
                resListArr.forEach((item) => {
                    let { gxsj } = item
                    let time = new Date().getTime() - gxsj
                    smcsTimeArr.push(time)
                });
                csbgListArr.forEach((item) => {
                    let { gxsj } = item
                    let time = new Date().getTime() - gxsj
                    csbgTimeArr.push(time)
                });
                let diffDay = parseInt(Math.min(...smcsTimeArr) / (1000 * 60 * 60 * 24))
                let diffDay2 = parseInt(Math.min(...csbgTimeArr) / (1000 * 60 * 60 * 24))
                // 优化1---长时间未对场所管理进行更新 ok
                if (resListArr.length > 0 && diffDay > resYz.csz) {
                    that.statusArr.secretSitesShowOptimize1 = true
                    that.statusArr.secretSitesShow2 = true
                    // that.allCounts = that.allCounts - 1
                    allkfs = allkfs + 1
                    optimizeCounts.push('1')
                    lock = lock + 1
                }
                // 优化2---未建立或长时间未对场所变更进行更新
                if (csbgListArr.length > 0 && diffDay2 > resYz2.csz) {
                    // that.allCounts = that.allCounts - 1
                    allkfs = allkfs + 1
                    that.statusArr.secretSitesShow2 = true
                    that.statusArr.secretSitesShowOptimize2 = true
                    optimizeCounts.push('2')
                    lock = lock + 1
                }
                // 风险-未建立涉密场所台账
                if (resListArr.length == 0) {
                    that.statusArr.secretSitesShowRisk1 = true
                    that.statusArr.secretSitesShow3 = true
                    // that.allCounts = that.allCounts - 12
                    allkfs = allkfs + 12
                    riskCounts.push('1')
                    lock = lock + 1
                }
                if (optimizeCounts.length > 0) {
                    that.checkCount.secretSitesOptimizeCount = optimizeCounts.length
                }
                if (riskCounts.length > 0) {
                    that.checkCount.secretSitesriskCount = riskCounts.length
                }
                that.checkCount.smcswtzs = that.checkCount.secretSitesOptimizeCount + that.checkCount.secretSitesriskCount
                // 全部ok没有问题
                // && resListArr.length != 0
                if (lock == 0) {
                    that.statusArr.secretSitesAllRight1 = true
                    that.statusArr.secretSitesAllRight2 = true
                    that.statusArr.secretSitesShow1 = true
                    that.statusArr.secretSitesLoadingShow = false
                }
                that.statusArr.secretSitesLoadingShow = false
                that.score = 62.5

                that.allCounts = allkfs > 12 ? that.allCounts - 12 : that.allCounts - allkfs
                that.getLoadEcharts()

                // that.activeNames = ['6', '7', '8']
                localStorage.setItem('checkCount', JSON.stringify(that.checkCount))
                localStorage.setItem('allShowState', JSON.stringify(that.statusArr))
                localStorage.setItem('allCounts', that.allCounts)
                that.getDeviceDatas()
            }, 1000);
        },
        // 一键检测----检测设备信息信息
        getDeviceDatas() {
            this.statusArr.deviceLoadingShow = true
            let that = this
            setTimeout(function () {
                // 获取各个表中数据
                let smjsjListArr = getSmjsjList()
                let fsmjsjListArr = getFsmjsjList() // 
                let smydccjzListArr = getSmydccjzList() // 
                let smbgzdhsbListArr = getSmbgzdhsbList() // 
                let fsmbgzdhsbListArr = getFsmbgzdhsbList() // 
                let smwlsbListArr = getSmwlsbList() // 
                let fmwlsbListArr = getFmwlsbList() // 
                let aqcpListArr = getAqcpList() // 
                // 获取各个模块相应阈值
                let smjsjYz = getYz('smjsjgxyz')[0]
                let smjsjYz1 = getYz('fsmjsjgxyz')[0]
                let smjsjYz2 = getYz('smydccjzgxyz')[0]
                let smjsjYz3 = getYz('smbgzdhsbgxyz')[0]
                let smjsjYz4 = getYz('fmbgzdhsbgxyz')[0]
                let smjsjYz5 = getYz('smwlsbgxyz')[0]
                let smjsjYz6 = getYz('fmwlsbgxyz')[0]
                let smjsjYz7 = getYz('aqcpgxyz')[0]
                // 统计优化和风险个数
                let optimizeCounts = []
                let riskCounts = []
                // 检查是否全部都没问题
                let lock = 0
                // 此模块应扣分数
                let allkfs = 0
                // 获取所有时间并筛选最后一次更新时间
                let smjsjTimeArr = []
                let fsmjsjTimeArr = []
                let smydccjzTimeArr = []
                let smbgzdhsbTimeArr = []
                let fsmbgzdhsbTimeArr = []
                let smwlsbTimeArr = []
                let fmwlsbTimeArr = []
                let aqcpTimeArr = []
                // 涉密计算机
                smjsjListArr.forEach((item) => {
                    let { gxsj } = item
                    // var date = new Date(gxsj).getTime()
                    let time = new Date().getTime() - gxsj
                    smjsjTimeArr.push(time)
                });
                // 非涉密计算机
                fsmjsjListArr.forEach((item) => {
                    let { gxsj } = item
                    // var date = new Date(gxsj).getTime()
                    let time = new Date().getTime() - gxsj
                    fsmjsjTimeArr.push(time)
                });
                // 涉密移动存储介质
                smydccjzListArr.forEach((item) => {
                    let { gxsj } = item
                    // var date = new Date(gxsj).getTime()
                    let time = new Date().getTime() - gxsj
                    smydccjzTimeArr.push(time)
                });
                // 涉密办公自动化设备
                smbgzdhsbListArr.forEach((item) => {
                    let { gxsj } = item
                    // var date = new Date(gxsj).getTime()
                    let time = new Date().getTime() - gxsj
                    smbgzdhsbTimeArr.push(time)
                });
                // 非密办公自动化设备
                fsmbgzdhsbListArr.forEach((item) => {
                    let { gxsj } = item
                    // var date = new Date(gxsj).getTime()
                    let time = new Date().getTime() - gxsj
                    fsmbgzdhsbTimeArr.push(time)
                });
                // 涉密网络设备
                smwlsbListArr.forEach((item) => {
                    let { gxsj } = item
                    // var date = new Date(gxsj).getTime()
                    let time = new Date().getTime() - gxsj
                    smwlsbTimeArr.push(time)
                });
                // 非密网络设备
                fmwlsbListArr.forEach((item) => {
                    let { gxsj } = item
                    // var date = new Date(gxsj).getTime()
                    let time = new Date().getTime() - gxsj
                    fmwlsbTimeArr.push(time)
                });
                // 安全产品
                aqcpListArr.forEach((item) => {
                    let { gxsj } = item
                    // var date = new Date(gxsj).getTime()
                    let time = new Date().getTime() - gxsj
                    aqcpTimeArr.push(time)
                });

                // 最近一次更新时间转换成时间戳
                let diffDay = parseInt(Math.min(...smjsjTimeArr) / (1000 * 60 * 60 * 24))
                let diffDay1 = parseInt(Math.min(...fsmjsjTimeArr) / (1000 * 60 * 60 * 24))
                let diffDay2 = parseInt(Math.min(...smydccjzTimeArr) / (1000 * 60 * 60 * 24))
                let diffDay3 = parseInt(Math.min(...smbgzdhsbTimeArr) / (1000 * 60 * 60 * 24))
                let diffDay4 = parseInt(Math.min(...fsmbgzdhsbTimeArr) / (1000 * 60 * 60 * 24))
                let diffDay5 = parseInt(Math.min(...smwlsbTimeArr) / (1000 * 60 * 60 * 24))
                let diffDay6 = parseInt(Math.min(...fmwlsbTimeArr) / (1000 * 60 * 60 * 24))
                let diffDay7 = parseInt(Math.min(...aqcpTimeArr) / (1000 * 60 * 60 * 24))
                // 优化---长时间未对涉密计算机进行更新
                if (smjsjListArr.length > 0 && diffDay > smjsjYz.csz) {
                    that.statusArr.deviceShowOptimize1 = true
                    that.statusArr.deviceShow2 = true
                    // that.allCounts = that.allCounts - 1
                    allkfs = allkfs + 1
                    optimizeCounts.push('1')
                    lock = lock + 1
                }
                // 优化---长时间未对非涉密计算机进行更新
                if (fsmjsjListArr.length > 0 && diffDay1 > smjsjYz1.csz) {
                    that.statusArr.deviceShowOptimize2 = true
                    that.statusArr.deviceShow2 = true
                    // that.allCounts = that.allCounts - 1
                    allkfs = allkfs + 1
                    optimizeCounts.push('2')
                    lock = lock + 1
                }
                // 优化---长时间未对涉密移动存储介质进行更新
                if (smydccjzListArr.length > 0 && diffDay2 > smjsjYz2.csz) {
                    that.statusArr.deviceShowOptimize3 = true
                    that.statusArr.deviceShow2 = true
                    // that.allCounts = that.allCounts - 1
                    allkfs = allkfs + 1
                    optimizeCounts.push('3')
                    lock = lock + 1
                }
                // // 优化---长时间未对涉密办公自动化设备进行更新 
                if (smbgzdhsbListArr.length > 0 && diffDay3 > smjsjYz3.csz) {
                    that.statusArr.deviceShowOptimize4 = true
                    that.statusArr.deviceShow2 = true
                    // that.allCounts = that.allCounts - 1
                    allkfs = allkfs + 1

                    optimizeCounts.push('4')
                    lock = lock + 1
                }
                // 优化---长时间未对非密办公自动化设备进行更新
                if (fsmbgzdhsbListArr.length > 0 && diffDay4 > smjsjYz4.csz) {
                    that.statusArr.deviceShowOptimize5 = true
                    that.statusArr.deviceShow2 = true
                    // that.allCounts = that.allCounts - 1
                    allkfs = allkfs + 1
                    optimizeCounts.push('5')
                    lock = lock + 1
                }
                // 优化---长时间未对涉密网络设备进行更新
                if (smwlsbListArr.length > 0 && diffDay5 > smjsjYz5.csz) {
                    that.statusArr.deviceShowOptimize6 = true
                    that.statusArr.deviceShow2 = true
                    // that.allCounts = that.allCounts - 1
                    allkfs = allkfs + 1
                    optimizeCounts.push('6')
                    lock = lock + 1
                }
                // 优化---长时间未对非密网络设备进行更新
                if (fmwlsbListArr.length > 0 && diffDay6 > smjsjYz6.csz) {
                    that.statusArr.deviceShowOptimize7 = true
                    that.statusArr.deviceShow2 = true
                    // that.allCounts = that.allCounts - 1
                    allkfs = allkfs + 1
                    optimizeCounts.push('7')
                    lock = lock + 1
                }
                // 优化---长时间未对安全产品进行更新
                if (aqcpListArr.length > 0 && diffDay7 > smjsjYz7.csz) {
                    that.statusArr.deviceShowOptimize8 = true
                    that.statusArr.deviceShow2 = true
                    // that.allCounts = that.allCounts - 1
                    allkfs = allkfs + 1
                    optimizeCounts.push('8')
                    lock = lock + 1
                }
                // 风险-未建立涉密计算机台账
                if (smjsjListArr.length == 0) {
                    that.statusArr.deviceShowRisk1 = true
                    that.statusArr.deviceShow3 = true
                    // that.allCounts = that.allCounts - 10
                    allkfs = allkfs + 10
                    riskCounts.push('2')
                    lock = lock + 1

                }
                // 风险-未建立非涉密计算机台账
                if (fsmjsjListArr.length == 0) {
                    that.statusArr.deviceShowRisk2 = true
                    that.statusArr.deviceShow3 = true
                    // that.allCounts = that.allCounts - 10
                    allkfs = allkfs + 10
                    riskCounts.push('3')
                    lock = lock + 1

                }
                // 风险-未建立涉密移动存储介质台账
                if (smydccjzListArr.length == 0) {
                    that.statusArr.deviceShowRisk3 = true
                    that.statusArr.deviceShow3 = true
                    // that.allCounts = that.allCounts - 1
                    allkfs = allkfs + 1
                    riskCounts.push('4')
                    lock = lock + 1

                }
                // 风险-未建立涉密办公自动化设备台账
                if (smbgzdhsbListArr.length == 0) {
                    that.statusArr.deviceShowRisk4 = true
                    that.statusArr.deviceShow3 = true
                    // that.allCounts = that.allCounts - 1
                    allkfs = allkfs + 1
                    riskCounts.push('5')
                    lock = lock + 1

                }
                // 风险-未建立非密办公自动化设备台账
                if (fsmbgzdhsbListArr.length == 0) {
                    that.statusArr.deviceShowRisk5 = true
                    that.statusArr.deviceShow3 = true
                    // that.allCounts = that.allCounts - 1
                    allkfs = allkfs + 1
                    riskCounts.push('6')
                    lock = lock + 1

                }
                // 风险-未建立涉密网络设备台账
                if (smwlsbListArr.length == 0) {
                    that.statusArr.deviceShowRisk6 = true
                    that.statusArr.deviceShow3 = true
                    // that.allCounts = that.allCounts - 1
                    allkfs = allkfs + 1
                    riskCounts.push('7')
                    lock = lock + 1

                }
                // 风险-未建立非密网络设备台账
                if (fmwlsbListArr.length == 0) {
                    that.statusArr.deviceShowRisk7 = true
                    that.statusArr.deviceShow3 = true
                    // that.allCounts = that.allCounts - 1
                    allkfs = allkfs + 1
                    riskCounts.push('8')
                    lock = lock + 1

                }
                // 风险-未建立安全产品台账
                if (aqcpListArr.length == 0) {
                    that.statusArr.deviceShowRisk8 = true
                    that.statusArr.deviceShow3 = true
                    // that.allCounts = that.allCounts - 10
                    allkfs = allkfs + 10
                    riskCounts.push('9')
                    lock = lock + 1

                }
                if (optimizeCounts.length > 0) {
                    that.checkCount.deviceOptimizeCount = optimizeCounts.length
                }
                if (riskCounts.length > 0) {
                    that.checkCount.deviceriskCount = riskCounts.length
                }
                that.checkCount.sbxxwtzs = that.checkCount.deviceOptimizeCount + that.checkCount.deviceriskCount
                // 全部ok没有问题
                // && smjsjListArr.length != 0 && fsmjsjListArr.length != 0&& smydccjzListArr.length != 0&& smbgzdhsbListArr.length != 0&& fsmbgzdhsbListArr.length != 0&& smwlsbListArr.length != 0&& fmwlsbListArr.length != 0&& aqcpListArr.length != 0
                if (lock == 0) {
                    that.statusArr.deviceAllRight1 = true
                    that.statusArr.deviceAllRight2 = true
                    that.statusArr.deviceAllRight3 = true
                    that.statusArr.deviceAllRight4 = true
                    that.statusArr.deviceAllRight5 = true
                    that.statusArr.deviceAllRight6 = true
                    that.statusArr.deviceAllRight7 = true
                    that.statusArr.deviceAllRight8 = true
                    that.statusArr.deviceShow1 = true
                    that.statusArr.deviceLoadingShow = false
                }
                that.statusArr.deviceLoadingShow = false
                that.score = 75

                that.allCounts = allkfs > 23 ? that.allCounts - 23 : that.allCounts - allkfs
                that.getLoadEcharts()

                // that.activeNames = ['7', '8']
                localStorage.setItem('checkCount', JSON.stringify(that.checkCount))
                localStorage.setItem('allShowState', JSON.stringify(that.statusArr))
                localStorage.setItem('allCounts', that.allCounts)
                that.getCarrierDatas()
            }, 1000);
        },
        // 一键检测---载体信息
        getCarrierDatas() {
            this.statusArr.carrierLoadingShow = true
            let that = this
            setTimeout(function () {
                let resListArr = getSmzttzList()
                let resYz = getYz('ztxxgxyz')[0]
                // let smjsjYz8 = getYz('ztxxgxyz')[0]
                // 统计优化和风险个数
                let optimizeCounts = []
                let riskCounts = []
                // 检查是否全部都没问题
                let lock = 0
                // 此模块应扣分数
                let allkfs = 0
                // 获取所有时间并筛选最后一次更新时间
                let bmzdTimeArr = []
                resListArr.forEach((item) => {
                    let { gxsj } = item
                    let time = new Date().getTime() - gxsj
                    bmzdTimeArr.push(time)
                });
                let diffDay = parseInt(Math.min(...bmzdTimeArr) / (1000 * 60 * 60 * 24))
                // 优化1---长时间未对载体管理进行更新
                if (resListArr.length > 0 && diffDay > resYz.csz) {
                    that.statusArr.carrierShowOptimize1 = true
                    that.statusArr.carrierShow2 = true
                    // that.allCounts = that.allCounts - 5
                    allkfs = allkfs + 5
                    optimizeCounts.push('1')
                    lock = lock + 1
                }
                // 风险-未建立涉密载体台账
                if (resListArr.length == 0) {
                    that.statusArr.carrierShowRisk1 = true
                    that.statusArr.carrierShow3 = true
                    // that.allCounts = that.allCounts - 12
                    allkfs = allkfs + 12
                    riskCounts.push('1')
                    lock = lock + 1
                }
                if (optimizeCounts.length > 0) {
                    that.checkCount.carrierOptimizeCount = optimizeCounts.length
                }
                if (riskCounts.length > 0) {
                    that.checkCount.carrierriskCount = riskCounts.length
                }
                that.checkCount.smztwtzs = that.checkCount.carrierOptimizeCount + that.checkCount.carrierriskCount
                // 全部ok没有问题
                // && resListArr.length != 0
                if (lock == 0) {
                    that.statusArr.carrierAllRight1 = true
                    that.statusArr.carrierAllRight2 = true
                    that.statusArr.carrierShow1 = true
                    that.statusArr.carrierLoadingShow = false
                }
                that.statusArr.carrierLoadingShow = false
                that.score = 87.5
                that.allCounts = allkfs > 12 ? that.allCounts - 12 : that.allCounts - allkfs
                that.getLoadEcharts()
                // that.activeNames = ['8']
                localStorage.setItem('checkCount', JSON.stringify(that.checkCount))
                localStorage.setItem('allShowState', JSON.stringify(that.statusArr))
                localStorage.setItem('allCounts', that.allCounts)
                that.getSelfEvaluationDatas()
            }, 1000);
        },
        // 一键检测---自查自评
        getSelfEvaluationDatas() {
            this.statusArr.selfEvaluationLoadingShow = true
            let that = this
            setTimeout(function () {
                // 统计风险个数
                let riskCounts = []
                // 检查是否全部都没问题
                let lock = 0
                // 此模块应扣分数
                let allkfs = 0
                // 获取所有时间并筛选最后一次更新时间
                // 第一季度
                let resRiskDatas = getZczpRiskDatas(1)
                let bhgItem = resRiskDatas.some((item) => {
                    let { zt } = item
                    if (zt != 7) {
                        return item
                    }
                });
                if (resRiskDatas.length == 0  && new Date().getMonth() + 1 > 9 && new Date().getMonth() + 1 < 13 || bhgItem && new Date().getMonth() + 1 > 9 && new Date().getMonth() + 1 < 13) {
                    that.statusArr.selfEvaluationAShowRisk1 = true
                    that.statusArr.selfEvaluationShow3 = true
                    // that.allCounts = that.allCounts - 3
                    allkfs = allkfs + 3
                    riskCounts.push('1')
                    lock = lock + 1
                }
                // 第二季度
                let resRiskDatas2 = getZczpRiskDatas(2)
                let bhgItem2 = resRiskDatas2.some((item) => {
                    let { zt } = item
                    if (zt != 7) {
                        return item
                    }
                });
                if (resRiskDatas2.length == 0 && new Date().getMonth() + 1 > 3 && new Date().getMonth() + 1 < 13 || bhgItem2 && new Date().getMonth() + 1 > 3 && new Date().getMonth() + 1 < 13) {
                    that.statusArr.selfEvaluationAShowRisk2 = true
                    that.statusArr.selfEvaluationShow3 = true
                    // that.allCounts = that.allCounts - 3
                    allkfs = allkfs + 3
                    riskCounts.push('2')
                    lock = lock + 1
                }
                // 第三季度
                let resRiskDatas3 = getZczpRiskDatas(3)
                let bhgItem3 = resRiskDatas3.some((item) => {
                    let { zt } = item
                    if (zt != 7) {
                        return item
                    }
                });
                if (resRiskDatas3.length == 0 && new Date().getMonth() + 1 > 6 && new Date().getMonth() + 1 < 13 || bhgItem3 && new Date().getMonth() + 1 > 6 && new Date().getMonth() + 1 < 13) {
                    that.statusArr.selfEvaluationAShowRisk3 = true
                    that.statusArr.selfEvaluationShow3 = true
                    // that.allCounts = that.allCounts - 3
                    allkfs = allkfs + 3
                    riskCounts.push('3')
                    lock = lock + 1
                }
                // 第四季度
                let resRiskDatas4 = getZczpRiskDatas(4)
                let bhgItem4 = resRiskDatas4.some((item) => {
                    let { zt } = item
                    if (zt != 7) {
                        return item
                    }
                });
                if (resRiskDatas4.length == 0 && new Date().getMonth() + 1 > 9 && new Date().getMonth() + 1 < 13 || bhgItem4 && new Date().getMonth() + 1 > 9 && new Date().getMonth() + 1 < 13) {
                    that.statusArr.selfEvaluationAShowRisk4 = true
                    that.statusArr.selfEvaluationShow3 = true
                    // that.allCounts = that.allCounts - 3
                    allkfs = allkfs + 3
                    riskCounts.push('4')
                    lock = lock + 1
                }
                // 全部ok没有问题
                // && resRiskDatas&& resRiskDatas1&& resRiskDatas2&& resRiskDatas3 && resRiskDatas.zt!=7 && resRiskDatas1.zt!=7&& resRiskDatas2.zt!=7&& resRiskDatas3.zt!=7
                
                if (lock == 0) {
                    that.statusArr.selfEvaluationAllRight1 = true
                    that.statusArr.selfEvaluationShow1 = true
                    that.statusArr.selfEvaluationLoadingShow = false
                }
                if (riskCounts.length > 0) {
                    that.checkCount.selfEvaluationriskCount = riskCounts.length
                }
                that.checkCount.zczpwtzs = that.checkCount.selfEvaluationriskCount
                that.statusArr.selfEvaluationLoadingShow = false
                that.allCounts = allkfs > 12 ? that.allCounts - 12 : that.allCounts - allkfs
                that.getLoadEcharts()
                that.score = 100
                that.progressShow = false
                // that.activeNames = ['1', '2', '3', '4', '5', '6', '7', '8']
                that.proNumber = that.checkCount.proJurisdictionOptimizeCount + that.checkCount.proJurisdictionriskCount + that.checkCount.organizationOptimizeCount + that.checkCount.organizationriskCount + that.checkCount.secretSitesriskCount + that.checkCount.secretSitesOptimizeCount + that.checkCount.secretPersonnelOptimizeCount + that.checkCount.secretPersonnelriskCount + that.checkCount.eduTrainOptimizeCount + that.checkCount.eduTrainriskCount + that.checkCount.deviceOptimizeCount + that.checkCount.deviceriskCount + that.checkCount.carrierOptimizeCount + that.checkCount.carrierriskCount + that.checkCount.selfEvaluationriskCount

                localStorage.setItem('checkCount', JSON.stringify(that.checkCount))
                localStorage.setItem('allShowState', JSON.stringify(that.statusArr))
                localStorage.setItem('allCounts', that.allCounts)
                localStorage.setItem('proNumber', that.proNumber)
                that.isshow2 = false
                this.sbisAdd = 0
                this.sbrisk = 0
                this.addCounts = 0
                if (that.allCounts < 100) {
                    that.isshow4 = true
                } else if (that.allCounts == 100) {
                    that.isshow3 = true
                }
                that.$message({ message: '扫描完成' });
            }, 1000);
        },
    }
}