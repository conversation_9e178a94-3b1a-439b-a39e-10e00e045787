import db from './adapter/zczpAdaptor'

//涉密等级-------------------------------------------------------------------
export const getsmdj = () => {
	let smdj = db.get('smdj_xz').cloneDeep().value()
	return smdj
}
//三类用人形式涉密等级-------------------------------------------------------------------
export const getyrxs = () => {
	let yrxs = db.get('yrxs_xz').cloneDeep().value()
	return yrxs
}
//被追责情况-------------------------------------------------------------------
export const getbzzqk = () => {
	let yrxs = db.get('bzzqk_xz').cloneDeep().value()
	return yrxs
}
//岗位确定依据-------------------------------------------------------------------
export const getgwqdyj = () => {
	let gwqdyj = db.get('gwqdyj_xz').cloneDeep().value()
	return gwqdyj
}
//级别职称-------------------------------------------------------------------
export const getjbzc = () => {
	let jbzc = db.get('jbzc_xz').cloneDeep().value()
	return jbzc
}
//最高学历-------------------------------------------------------------------
export const getzgxl = () => {
	let zgxl = db.get('zgxl_xz').cloneDeep().value()
	return zgxl
}
//身份类型-------------------------------------------------------------------
export const getsflx = () => {
	let sflx = db.get('sflx_xz').cloneDeep().value()
	return sflx
}
//用人形式-------------------------------------------------------------------
export const getyrxx = () => {
	let yrxx = db.get('yrxx_xz').cloneDeep().value()
	return yrxx
}
//培训类型-------------------------------------------------------------------
export const getpxlx = () => {
	let pxlx = db.get('pxlx_xz').cloneDeep().value()
	return pxlx
}
//培训类型--内外类型----------------------------------------------------------
export const getpxlxnw = () => {
	let pxlx = db.get('pxlx_lx').cloneDeep().value()
	return pxlx
}
//离职离岗-------------------------------------------------------------------
export const getlzlglx = () => {
	let lzlglx = db.get('lzlglx_xz').cloneDeep().value()
	return lzlglx
}
//设备密级
export const getsbmj = () => {
	let sbmj = db.get('sbmj_xz').cloneDeep().value()
	return sbmj
}
//设备类型
export const getsblx = () => {
	let sblx = db.get('sblx_xz').cloneDeep().value()
	return sblx
}
//设备使用情况
export const getsbsyqk = () => {
	let sbsyqk = db.get('sbsyqk_xz').cloneDeep().value()
	return sbsyqk
}
//设备办公自动化类型
export const getsbbgzdhlx = () => {
	let sbbgzdhlx = db.get('sbbgzdhlx_xz').cloneDeep().value()
	return sbbgzdhlx
}
//设备办公自动化类型
export const getfsbbgzdhlx = () => {
	let sbbgzdhlx = db.get('fsbbgzdhlx_xz').cloneDeep().value()
	return sbbgzdhlx
}
//设备网络类型
export const getsbsmwllx = () => {
	let sbsmwllx = db.get('sbsmwllx_xz').cloneDeep().value()
	return sbsmwllx
}
//设备安全产品
export const getaqcplx = () => {
	let aqcplx = db.get('aqcplx_xz').cloneDeep().value()
	return aqcplx
}
//载体类型
export const getztlx = () => {
	let ztlx = db.get('ztlx_xz').cloneDeep().value()
	return ztlx
}
//载体生成原因
export const getztscyy = () => {
	let ztscyy = db.get('ztscyy_xz').cloneDeep().value()
	return ztscyy
}
//载体状态
export const getztzt = () => {
	let ztzt = db.get('ztzt_xz').cloneDeep().value()
	return ztzt
}
//定密权限类型
export const getdmqxlx = () => {
	let dmqxlx = db.get('dmqxlx_xz').cloneDeep().value()
	return dmqxlx
}
//定密类别
export const getdmlb = () => {
	let dmlb = db.get('dmlb_xz').cloneDeep().value()
	return dmlb
}
//定密类别
export const getdmmj = () => {
	let dmmj = db.get('dmmj_xz').cloneDeep().value()
	return dmmj
}
//项目种类
export const getxmzl = () => {
	let xmzl = db.get('xmzl_list').cloneDeep().value()
	return xmzl
}
