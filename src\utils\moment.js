import moment from "moment";

export const dateFormat = (date, format = "YYYY-MM-DD HH:mm:ss") => {
    // console.log(arguments)
    return moment(date)
        .format(format)
        .toString();
}

export const dateFormatChinese = (date, format = "YYYY年MM月DD日 HH时mm分ss秒") => {
    // console.log(arguments)
    return moment(date)
        .format(format)
        .toString();
}

export const dateFormatNYR = (date, format = "YYYY-MM-DD") => {
    // console.log(arguments)
    return moment(date)
        .format(format)
        .toString();
}

export const dateFormatNY = (date, format = "YYYY-MM") => {
    // console.log(arguments)
    return moment(date)
        .format(format)
        .toString();
}

export const dateFormatNYRChinese = (date, format = "YYYY年MM月DD日") => {
    // console.log(arguments)
    return moment(date)
        .format(format)
        .toString();
}