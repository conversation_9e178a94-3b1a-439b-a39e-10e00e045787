// const LOW = require('lowdb')
// const FileSyncDataMigration = require('lowdb/adapters/FileSync')

// import { getDatabaseDataMigrationSavePath } from '../../utils/pathUtil'

// const adapterDataMigration = new FileSyncDataMigration(getDatabaseDataMigrationSavePath())
// // const adapterDataMigration = new FileSyncDataMigration('dataMigration.json')

// const dbDataMigration = LOW(adapterDataMigration)

// dbDataMigration.defaults({}).write()

// export default dbDataMigration
