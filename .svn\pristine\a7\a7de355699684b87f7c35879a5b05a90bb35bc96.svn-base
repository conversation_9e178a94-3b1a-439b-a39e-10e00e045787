import db from './adapter/zczpAdaptor'
// 获取所有sm岗位信息(上报数据自选模式专用)
export const getAllSmgwZxms = () => {
	return db.get('Smgwgl_list').cloneDeep().value()
}
//
export const getsmgw = (params) => {
	let smry = db.get('Smgwgl_list').filter({
		'bm': params
	}).cloneDeep().value()
	return smry
}
export const getsmgw1 = () => {
	let smry = db.get('Smgwgl_list').cloneDeep().value()
	return smry
}
export const getbmmc = () => {
	let smry = db.get('zzjg_list').cloneDeep().value()
	return smry
}
export const getsmgw2 = (smgw1) => {
	console.log(smgw1);
	/**
	 * 这里的参数需要拷贝一份来进行查询
	 * 因为：当对象不变的情况下，如果只是值变了，lowDB会认为这是同一个对象，此时会提取之前的查询结果，可以认为是缓存，而没有真正的去查询
	 */
	const smgw = smgw1;
	return db.get('Smgwgl_list')
		.find({
			'smgw': smgw
		})
		.cloneDeep()
		.value();
}
//涉密岗位管理-----------------------------------涉密岗位管理初始化列表********
export const getSmgwgl = (params) => {
	let page = params.page
	let pagesize = params.pageSize
	let gwmc = params.gwmc;
	let bm;
	let rs = []
	if (typeof(params.bm) == 'object') {
    console.log(1);
    bm = params.bm.join('/')
  }
	let list_total = db.get('Smgwgl_list').sortBy('cjsj').filter(function (item) {
		if ((gwmc === undefined || gwmc == "") && (bm === undefined || bm == "")) {
			return item;
			console.log("查空", item);
		} else if (gwmc && (bm === undefined || bm == "")) {
			if (item.gwmc) {
				if (item.gwmc.indexOf(gwmc) != -1) {
					console.log("名称", item);
					return item;
				}
			} else {
				console.log("item.gwmc", item.gwmc);
			}
		} else if ((gwmc === undefined || gwmc == "") && bm) {
			if (item.bm) {
				if (item.bm.indexOf(bm) != -1) {
					console.log("名称", item);
					return item;
				}
			} else {
				console.log("item.bm", item.bm);
			}
		} else if (gwmc && bm) {
			if (item.gwmc && item.bm) {
				if (item.gwmc.indexOf(gwmc) != -1 && item.bm.indexOf(bm) != -1) {
					console.log("名称", item);
					return item;
				}
			} else {
				console.log("item.gwmc", item.gwmc, "item.bm", item.bm);
			}
		}
	}).cloneDeep().value()
	list_total.forEach((item,lable) =>{
		let bm = db.read().get("Smry_list").filter({ bm: item.bm }).cloneDeep().value()
		bm.forEach(item1 =>{
			if (item.gwmc == item1.gwmc) {
				rs.push(item1)
			}
		})
		item.rs = rs.length
		rs = []
	})
	// 手动分页
	console.log(pagesize * (page - 1), pagesize * (page - 1) + pagesize)
	let pageList = list_total.slice(pagesize * (page - 1), pagesize * (page - 1) + pagesize)

	let resList = {
		"list": pageList,
		"list_total": list_total,
		"total": list_total.length
	}
	console.log('涉密岗位管理', resList)
	return resList
}
//涉密岗位管理-----------------------------------涉密岗位管理添加成员********
export const addSmgwgl = (params) => {
	console.log(typeof(params.bm));
	if(typeof(params.bm) == 'object'){
		let bm = params.bm.join('/')
		params.bm = bm
	}
	let sjc = new Date().getTime()
	params.cjsj = sjc
	params.gxsj = sjc
	db.read().get('Smgwgl_list').push(params).write()
}

export const addDrSmgwgl = (params) => {
	let sjc = new Date().getTime()
	params.cjsj = sjc
	params.gxsj = sjc
	db.read().get('Smgwgl_list').push(params).write()
}
export const jxbmgw = (params) => {
  console.log('jxbmgw', params)
	let gwmc = params.gwmc
	let smdj = params.smdj
	let message = 0
	if (params.bm != '') {
		let bm = params.bm.join('/')
    // 部门+岗位名称+涉密登记进行联合验证
		let pd = db.read().get("Smgwgl_list").filter({ bm: bm }).cloneDeep().value()
		if (pd != undefined) {
			for (let i = 0; i < pd.length; i++) {
        console.log(gwmc, pd[i].gwmc, gwmc == pd[i].gwmc, smdj, pd[i].smdj, smdj == pd[i].smdj, gwmc == pd[i].gwmc && smdj == pd[i].smdj)
				if (gwmc == pd[i].gwmc && smdj == pd[i].smdj) {
					message = 1
					break
				}
			}
		}
		console.log(message);
	}


	return message
}
//涉密岗位管理-----------------------------------涉密岗位管理删除成员********
export const deleteSmgwgltsxx = (params) => {
	let bm = params.bm
	let gwmc = params.gwmc
	let tsxx = '是否继续删除'
	let pdbm = db.read().get("Smry_list").filter({ bm: bm }).cloneDeep().value()
	// let pdgwmc = 
	if (pdbm != []) {
		pdbm.forEach(item => {
			if (item.gwmc == gwmc) {
				console.log(item);
				tsxx = '在岗位里已有人员，是否继续删除该岗位'
			}
		});
	}
	return tsxx

}
export const deleteSmgwgl = (params) => {
	delete params.rs
	db.read().get('Smgwgl_list').remove(params).write()
}
export const deleteSmgwgl1 = () => {
	
    db.get("Smgwgl_list").remove().write()
}
//涉密岗位管理-----------------------------------涉密岗位管理删除成员********
export const reviseSmgwgl = (params) => {
	let bm = params.bm.join('/')
	params.bm = bm
	let smgwid = params.smgwid
	console.log('smgwid', smgwid)
	if (!smgwid || smgwid == '') {
		return
	}
	params.gxsj = new Date().getTime()
	// 全字段更新方法(传进来的字段都会更新)
	db.read().get('Smgwgl_list').find({ smgwid: smgwid }).assign(params).write()
}
export const getsmry = (params) => {
	let smry = db.get('Smry_list').filter({
		'bm': params
	}).cloneDeep().value()
	console.log(smry)
	return smry
}