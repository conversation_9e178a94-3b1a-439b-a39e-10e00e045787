<template>
	<div class="index" style="padding:1vw 1vw">
		<!-- <div class="top-font" style="text-align:center">注册单位基本信息</div> -->
		<div>
			<div class="bt-xx" style="font-weight: 700;">注册信息</div>
			<div style="">
				<el-form ref="form" :model="form" label-width="80px" size="mini" :label-position="labelPosition">
					<el-form-item label="单位注册号" prop="dwzch" style="position: relative;" >
						<el-input v-model="form.dwzch" disabled></el-input>
					</el-form-item>
					<el-form-item label="单位名称" prop="dwmc">
						<el-input v-model="form.dwmc"></el-input>
					</el-form-item>
					<el-form-item label="用户名" prop="yhm">
						<el-input v-model="form.yhm" disabled></el-input>
					</el-form-item>
					<el-form-item label="登录密码" prop="dlmm">
						<el-input v-model="form.dlmm" show-password disabled></el-input>
					</el-form-item>
					<el-form-item label="确认密码" prop="qrmm">
						<el-input v-model="form.qrmm" @blur="qrma" show-password disabled></el-input>
					</el-form-item>
					<!-- <div style="width:100%;height:1px;background:#d8d8d8;position: absolute; left: 0px;"></div>
					<div class="bt-xx bt-dw" style="font-weight: 700;">单位信息</div> -->
					<!-- <div class="dwxx-font">
						说明：单位向保密行政管理部门上报数据需要完整的单位信息
					</div> -->
					<el-form-item label="单位类型" style="margin-top:5px;position: relative;" prop="dwlx">
						<el-select v-model="form.dwlx" placeholder="请选择单位类型" style="width:110%">
							<el-option v-for="(item,index) in dwlx" :label="item.dwlxmc" :value="item.dwlxid" :key="index">
							</el-option>
						</el-select>
						<el-popover placement="left" width="200" trigger="hover">
							<div>
								<div style="display:flex;margin-bottom:10px">
									<i class="el-icon-info" style="color:#409eef;    position: relative;
    top: 2px;"></i>
									<div class="tszt">提示</div>
								</div>
								<div class="smzt">
									从下拉栏中选择：党政机关主要包括党的机关、人大机关、行政机关、政协机关、监察机关、审判机关、检察机关、民主党派机关；人民团体主要包括工会组织、共青团组织、妇女联合会组织等人民团体，也包括学会、协会、研究会等社团组织；参公事业单位指参照公务员法管理事业单位；事业单位指接受政府领导，主要提供教育、科技、文化、卫生等活动非物质生产和劳务服务的社会公共组织或机构；国有企业指由中央管理或者地方政府监管的国有企业，包括国有独资企业、国有控股企业（不包括国有参股企业）；民营保密资质（资格）企业指民间资本作为投资主体，具有印制、集成或者军工保密资质（资格）的非公有制企业；其他指上述类型之外的单位。
								</div>
							</div>
							<i class="el-icon-info" style="color:#409eef;position: absolute;    right: -5px;top: 7px;"
								slot="reference"></i>

						</el-popover>
					</el-form-item>
					<el-form-item label="所属领域" style="margin-top:5px;position: relative;" prop="ssly">
						<el-select v-model="form.ssly" placeholder="请选择所属领域" style="width:110%">
							<el-option v-for="(item,index) in ssly" :label="item.sslymc" :value="item.sslyid" :key="index">
							</el-option>
						</el-select>
						<el-popover placement="right" width="200" trigger="hover">
							<div>
								<div style="display:flex;margin-bottom:10px">
									<i class="el-icon-info" style="color:#409eef;    position: relative;
    top: 2px;"></i>
									<div class="tszt">提示</div>
								</div>
								<div class="smzt">
									从下拉栏中选择：国防军工、外交外事、安全、政法、经济金融、科技、教育、能源、测绘、其他。如涉及多个领域，选择主要领域。
								</div>
							</div>
							<i class="el-icon-info" style="color:#409eef;position: absolute;    right: -5px;top: 7px;"
								slot="reference"></i>

						</el-popover>
					</el-form-item>
					<el-form-item label="所属层次" style="margin-top:5px;position: relative;" prop="sscc">
						<el-select v-model="form.sscc" placeholder="请选择所属层次" style="width:110%">
							<el-option v-for="(item,index) in sscc" :label="item.ssccmc" :value="item.ssccid" :key="index">
							</el-option>
						</el-select>
						<el-popover placement="right" width="200" trigger="hover">
							<div>
								<div style="display:flex;margin-bottom:10px">
									<i class="el-icon-info" style="color:#409eef;    position: relative;
    top: 2px;"></i>
									<div class="tszt">提示</div>
								</div>
								<div class="smzt">
									从下拉栏中选择：中央机关、企业本级/中央机关、企业下属驻京单位/中央机关、企业京外单位/省直机关、企业本级/省直机关、企业下属单位/市直机关、企业本级/市直机关、企业下属单位/县直和乡镇机关、企业本级/县直和乡镇机关、企业下属单位/私营企业保密资质单位（甲级）/私营企业保密资质单位（乙级）/私营企业保密资格单位（一级）/私营企业保密资格单位（二级）/私营企业保密资格单位（三级）。
								</div>
							</div>
							<i class="el-icon-info" style="color:#409eef;position: absolute;    right: -5px;top: 7px;"
								slot="reference"></i>

						</el-popover>
					</el-form-item>
					<el-form-item label="统一社会信用代码" prop="xydm">
						<el-input v-model="form.xydm"></el-input>
					</el-form-item>
					<el-form-item label="单位所在行政区域" style="margin-top:5px" prop="district">
						<div>
							<el-select v-model="form.province" disabled placeholder="黑龙江省">
								<el-option label="黑龙江省" value="黑龙江省">
								</el-option>
							</el-select>
							<!-- <div style="width: 88px;">黑龙江省</div> -->
							<el-select v-model="form.city" placeholder="请选择市" @change="changeCity">
								<el-option v-for="item in cityData" :key="item.code" :label="item.name" :value="item.name">
								</el-option>
							</el-select>
							<el-select v-model="form.district" placeholder="请选择区" @change="changeArea">
								<el-option v-for="item in areaData" :key="item.code" :label="item.name" :value="item.name">
								</el-option>
							</el-select>
						</div>

					</el-form-item>
					<el-form-item label="单位联系人" prop="dwlxr">
						<el-input v-model="form.dwlxr"></el-input>
					</el-form-item>
					<el-form-item label="单位联系电话" prop="dwlxdh">
						<el-input v-model="form.dwlxdh"></el-input>
					</el-form-item>
				</el-form>
				<div style="display:flex;align-items: center;justify-content:flex-end;">
					<!-- <input type="button" value="保存"
						style="width:130px;height:40px;background-image: linear-gradient(159deg, #06A8FF 0%, #0660FF 100%);border:0;color: #FFFFFF;"
						@click='baocun'> -->
					<el-button type="primary" @click='baocun("form")'>保 存</el-button>
					<el-button type="warning" @click='quxiao("form")'>返 回</el-button>
					<!-- <input type="button" value="取消"
						style="width:130px;height:40px;background: #FE9239;border:0;color: #FFFFFF;" @click="quxiao"> -->
				</div>
			</div>
		</div>
	</div>
</template>
<script>
	import address from "../../../utils/address.json"

	import {
		getDmbDwlxDB
	} from '../../../db/dmbDwlxDb'
	import {
		getDmbSslyDB
	} from '../../../db/dmbSslyDb'
	import {
		getDmbSsccDB
	} from '../../../db/dmbSsccDb'

	import {
		insertDwxxList,
		deleteDwxx,
		addDwxx,
	} from '../../../db/dwxxDb'

	import {
		checkObjIsBlank
	} from '../../../utils/utils'
import {getDwxx} from "../../../db/dwxxDb"
	export default {
		name: 'dwzc',
		data() {
			return {
				form: {
					dwmc: '', // 单位名称
					dwzch: '', // 单位注册号
					dwlx: '', // 单位类型
					ssly: '', // 所属领域
					sscc: '', // 所属层次
					xydm: '', // 统一社会信用代码
					dwlxr: '', // 单位联系人
					dwlxdh: '', // 单位联系电话
					province: "黑龙江省", // 省
					city: "哈尔滨市", // 市
					district: "南岗区", // 区
				},
				oldform:{},
				// 单位类型
				dwlx: [],
				// 所属领域
				ssly: [],
				// 所属层次
				sscc: [],
				// 表单label对齐方式
				labelPosition: 'left',
				//  省数据
				addressData: [],
				//  市数据
				cityData: [],
				// 区数据
				areaData: [],
				// rules:{
				// 	dwzch: [{
				// 		required: true,
				// 		message: '请输入单位注册号',
				// 		trigger: 'blur'
				// 	}, ],
				// 	dwmc: [{
				// 		required: true,
				// 		message: '请输入单位名称',
				// 		trigger: 'blur'
				// 	}, ],
				// 	yhm: [{
				// 		required: true,
				// 		message: '请输入用户名',
				// 		trigger: 'blur'
				// 	}, ],
				// 	dlmm: [{
				// 		required: true,
				// 		message: '请输入登录密码',
				// 		trigger: 'blur'
				// 	}, ],
				// 	qrmm: [{
				// 		required: true,
				// 		message: '请输入确认密码',
				// 		trigger: 'blur'
				// 	}, ],
				// 	dwlx: [{
				// 		required: true,
				// 		message: '请选择单位类型',
				// 		trigger: 'blur'
				// 	}, ],
				// 	ssly: [{
				// 		required: true,
				// 		message: '请选择所属领域',
				// 		trigger: 'blur'
				// 	}, ],
				// 	sscc: [{
				// 		required: true,
				// 		message: '请选择所属层次',
				// 		trigger: 'blur'
				// 	}, ],
				// 	xydm: [{
				// 		required: true,
				// 		message: '请输入统一社会信用代码',
				// 		trigger: 'blur'
				// 	}, ],
				// 	district: [{
				// 		required: true,
				// 		message: '请选择市区',
				// 		trigger: 'blur'
				// 	}, ],
				// 	dwlxr: [{
				// 		required: true,
				// 		message: '请输入单位联系人',
				// 		trigger: 'blur'
				// 	}, ],
				// 	dwlxdh: [{
				// 		required: true,
				// 		message: '请输入单位联系电话',
				// 		trigger: 'blur'
				// 	}, ],
				// }
			}
		},
		created() {
			// 省份数据初始化
			this.addressData = address;
			// console.log(this.addressData);
			this.cityData = this.addressData.filter((item) => {
				// console.log(item);
				return item.name == "黑龙江省";
			})[0].children;
			this.areaData = this.cityData.filter((item) => {
				return item.name == "哈尔滨市"
			})[0].children;
			// console.log(this.cityData);
		},
		methods: {
			dwxx(){
				let list = getDwxx()
				console.log(list);
				this.oldform = list[0]
				console.log(this.oldform);
				this.form = list[0]
			},
			// 获取所属层次信息
			getDmbSsccList() {
				this.sscc = getDmbSsccDB().list
			},
			// 获取单位类型信息
			getDmbDwlxList() {
				this.dwlx = getDmbDwlxDB().list
			},
			// 获取所属领域信息
			getDmbSslyList() {
				this.ssly = getDmbSslyDB().list
			},
			qrma() {
				if (this.form.dlmm !== this.form.qrmm) {
					this.$message.error('两次密码不一致');
				}
			},
			async baocun() {
				// 获取单位注册信息并校验
				let params = this.form
				try {
					checkObjIsBlank(params, [{
							field: 'dwmc',
							fieldCH: '单位名称'
						},
						{
							field: 'dwzch',
							fieldCH: '单位注册号'
						},
						{
							field: 'yhm',
							fieldCH: '用户名'
						},
						{
							field: 'dlmm',
							fieldCH: '登录密码'
						},
						{
							field: 'dwlx',
							fieldCH: '单位类型'
						}, {
							field: 'ssly',
							fieldCH: '所属领域'
						}, {
							field: 'sscc',
							fieldCH: '所属层次'
						}, {
							field: 'xydm',
							fieldCH: '统一社会信用代码'
						}, {
							field: 'dwlxr',
							fieldCH: '单位联系人'
						}, {
							field: 'dwlxdh',
							fieldCH: '单位联系电话'
						}, {
							field: 'city',
							fieldCH: '市'
						}, {
							field: 'district',
							fieldCH: '区'
						}
					])
				} catch (error) {
					this.$message.warning(error.message)
					return
				}
				// 删除旧的
						deleteDwxx(this.oldform)
						console.log('删除成功',this.oldform);
						// 插入新的
						addDwxx(this.form)
						console.log('添加成功',this.form);
						// 刷新页面表格数据
						this.dwxx()
						// 关闭dialog
						this.$message.success('修改成功')
						this.xgdialogVisible = false
				// // 写入单位信息表
				// const bool = await insertDwxxList(params)
				// if (bool) {
				// 	console.log('插入成功')
				// 	this.$message.success('单位注册成功')
				// 	this.$emit("baocun", true)
				// 	return
				// }
				// console.log('插入失败')
			},
			quxiao() {
				// this.$emit("quxiao", true);
				this.$router.push('/systemSetting')
			},
			// // 省份更改
			// changePro(e) {
			// 	// 从省中过滤出市的数据
			// 	// console.log(e);
			// 	this.cityData = this.addressData.filter((item) => {
			// 		// console.log(item);
			// 		return item.name == e;
			// 	})[0].children;
			// 	// 省发生改变的时候 清空输入框市区街道的内容
			// 	this.form.district = "";
			// 	this.form.city = "";
			// 	// 省发生更改时 该表空区街道数据的内容
			// 	this.areaData = [];
			// },
			// 市更改
			changeCity(e) {
				// 获取到区的数据
				this.areaData = this.cityData.filter(
					(item) => item.name == e
				)[0].children;
				// 清空数据后面对应数组的数据
				this.form.district = "";
			},
			// 区更改
			changeArea(e) {
				let temp = this.areaData.filter((item) => item.name == e);
				// 获取到区的code码
				this.form.regionalNumber = temp[0].code;
				// 获取到街道的数据
				this.jdData = this.areaData.filter((item) => item.name == e)[0].children;
			},

		},
		mounted() {
			// 获取单位类型list
			this.getDmbDwlxList()
			// 获取所属领域list
			this.getDmbSslyList()
			// 获取所属层次list
			this.getDmbSsccList()
			this.dwxx()
		}
	}
</script>
<style scoped>
	.index {
		width: 94%;
		/* height: 62%; */
		/* margin: 6% 13% 0 0; */
		/* float: right; */
		position: absolute;
		top: 11%;
		right: 2%;
		left: 2%;
		/* background-image: linear-gradient(180deg, #FFFFFF 0%, rgba(255, 255, 255, 0.40) 50%); */
		box-shadow: 0px 2px 20px 0px rgba(45, 93, 194, 0.5);
		/* border-radius: 8px; */
		background: #fff;
		z-index: 99;
	}

	.top-font {
		font-family: Helvetica;
		font-size: 1vw;
		color: #737C90;
		letter-spacing: 0;
		text-align: center;
		line-height: 1vw;
		font-weight: 400;
	}

	.bt-xx {
		font-family: Helvetica;
		font-size: 0.85vw;
		color: #333333;
		letter-spacing: 0;
		line-height: 1vw;
		font-weight: 400;
		margin-top: 1.9%;
		margin-bottom: 1.7%;
	}

	.bt-xx::before {
		content: "";
		position: absolute;
		left: 8px;
		top: 40px;
		width: 5px;
		height: 20px;
		border-radius: 2px;
		background: #409eef;
	}

	.bt-dw {
		padding-top: 1.2%;
		margin-bottom: 1.2%;
	}

	.dwxx-font {
		font-family: Helvetica;
		font-size: 14px;
		color: #737C90;
		line-height: 32px;
		background: #F4F5F8;
		margin-top: 1.2%;
		margin-bottom: 1.7%;
		padding: 0.7% 0px 0.7% 3%;
	}
		.tszt {
		font-family: KaiTi;
	}

	.smzt {
		font-size: 12px;
	}

	/deep/ .el-form-item {
		/* height: 30px; */
	}

	/deep/ .el-form-item__content {
		margin-left: 7.7vw !important;
	}

	/deep/ .el-form--label-left .el-form-item__label {
		width: 7.6vw !important;
		font-size: 0.76vw;
		
		text-align: right;
	}

	/deep/ .el-select>.el-input {
		width: 90%;
	}

	/deep/ .el-input--mini .el-input__inner {
		height: 1.5vw;
		line-height: 1.5vw;
	}

	/deep/ .el-form-item--mini.el-form-item,
	.el-form-item--small.el-form-item {
		margin-bottom: 0.7vw !important;
	}

	/deep/.el-form--label-left .el-form-item__label::before {
		content: '*';
		color: #F56C6C;
		margin-right: 4px;
	}
	/deep/.el-input{
		    width: 99%;
	}
</style>