import db from "./adapter/zczpAdaptor";

//保密制度-----------------------------------保密制度初始化列表********
export const getAqcp = (params) => {
  let page = params.page;
  let pagesize = params.pageSize;
  //
  let zcbh = params.zcbh
  let qyrq = params.qyrq
  //
  let list_total = db.get("Aqcp_list").sortBy("cjsj").cloneDeep().value();

  // 模糊查询过滤
  if (zcbh) {
    list_total = list_total.filter((item) => {
      if (item.zcbh.toString().indexOf(zcbh) != -1) {
        return item
      }
    })
  }
  if (qyrq) {
    list_total = list_total.filter((item) => {
      if (item.qyrq >= qyrq[0] && item.qyrq <= qyrq[1]) {
        return item
      }
    })
  }
 
  // 手动分页
  console.log(pagesize * (page - 1), pagesize * (page - 1) + pagesize);
  let pageList = list_total.slice(
    pagesize * (page - 1),
    pagesize * (page - 1) + pagesize
  );

  let resList = {
    list: pageList,
    list_total: list_total,
    total: list_total.length,
  };
  console.log("保密制度", resList);
  return resList;
};
//保密制度-----------------------------------保密制度添加成员********
export const addAqcp = (params) => {
  let sjc = new Date().getTime();
  params.cjsj = sjc;
  params.gxsj = sjc;
  db.read().get("Aqcp_list").push(params).write();
};
//添加校验
export const jyaddAqcp = (params) => {
  let zcbh = params.zcbh;
  let message = 0;
  let pdzcbh = db
    .read()
    .get("Aqcp_list")
    .find({ zcbh: zcbh })
    .cloneDeep()
    .value();
  if (pdzcbh) {
    message = 2;
  }

  return message;
};

//修改
export const reviseAqcp = (params) => {
  let aqcpid = params.aqcpid;
  console.log("aqcpid", aqcpid);
  if (!aqcpid || aqcpid == "") {
    return;
  }
  params.gxsj = new Date().getTime();
  // 全字段更新方法(传进来的字段都会更新)
  db.read().get("Aqcp_list").find({ aqcpid: aqcpid }).assign(params).write();
};
//保密制度-----------------------------------保密制度删除成员********
export const deleteAqcp = (params) => {
  db.read().get("Aqcp_list").remove(params).write();
};
export const getPpxh = () => {
	let ppxh = db.get('Aqcp_list').cloneDeep().value()
	console.log()
	return ppxh
}
