<template>
  <div class="login clearfix loginStyle">
    <!---->
    <div style="height: 100%; display: flex; flex-flow: column">
      <div style="flex: 1">
        <div class="indexLogo">
          <div class="bg-bm">
            <div class="guohui">
              <!-- <img src="./images/logo.png" alt="" /> -->
              <img src="../assets/logo/logo.png" alt="" />
              <!-- <span>保密管家</span> -->
              &nbsp;&nbsp;&nbsp;&nbsp;<img src="./images/logo2.png" style="width:127px;height:43px" alt="">
            </div>
          </div>
          <div class="indexR" v-if="zc">
            <div style="font-size: 1vw; font-weight: 700; margin: 5vw 4vw 1vw">
              账号密码登录
            </div>
            <div class="content">
              <!-- <div style="display: flex;align-items: center;justify-content: center;">
								<el-radio v-model="radio" label="1">现场审查评分</el-radio>
								<el-radio v-model="radio" label="2">自查评分</el-radio>
							</div> -->
              <div class="regform-use" id="userId">
                <div class="zsdlinput">
                  <span class="fl"><img src="./images/person.png" align="absmiddle"></span>
                  <!-- <span class="fl"><i class="el-icon-user-solid i-icon"></i></span> -->
                  <span class="fl se_wai">
                    <el-input type="text" v-model="username" @keyup.enter.native="loginIt" placeholder="请输入账号"></el-input>
                    <!-- <input type="text" v-model="username" name="username" id="username" maxlength="20" class="dlinput" placeholder="请输入用户名" /> -->
                  </span>
                </div>
                <div class="clearkit"></div>
                <div class="zsdlinput">
                  <span class="fl"><img src="./images/suo.png" align="absmiddle"></span>
                  <!-- <span class="fl"><i class="el-icon-lock i-icon"></i></span> -->
                  <span class="fl se_wai">
                    <el-input type="password" v-model="password" @keyup.enter.native="loginIt" show-password
                      placeholder="请输入密码"></el-input>
                    <!-- <input type="password" v-model="password" name="password" maxlength="20" id="password" class="dlinput" placeholder="请输入密码" autocomplete="off" @keyup.enter="loginIt" /> -->
                  </span>
                </div>
                <div style="margin-top: 10%">
                  <div class="dlinputd">
                    <input ref="aaa" type="button" value="登       录" @click="loginIt" />
                  </div>
                  <!--  -->
                  <div class="zcinputd" v-if="dwxx2.length == 0">
                    <!-- v-if="dwxx2.length ==0" -->
                    <input ref="aaa" type="button" value="单位注册" @click="loginZc" />
                  </div>
                  <div class="zcinputd">
                    <input type="button" value="退  出  系  统" @click="quitSystem" style="background:#F56C6C;" />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <dwzc v-else @baocun="baocun" @quxiao="quxiao"></dwzc>
          <div class="footer" style="
              position: absolute;
              bottom: 0;
              left: 50%;
              transform: translate(-50%, -50%);
              font-size:14px;
            ">
            技术支持：哈尔滨思和信息技术股份有限公司
          </div>
          <el-dialog :visible.sync="dialogVisible" width="46%">
            <div class="dialogAlert">
              <img src="./images/hsoftLogo.png" alt="">
              <div class="alertFontDiv">
                <p class="p1">尊敬的用户，您的保密管家已到一个月用期限，如需继续使用，请联系我们获取口令登录系统！</p>
                <el-button type="success" round class="klBtn text-center" @click="kldialog">输入口令</el-button>
                <p class="p2">联系人：张先生</p>
                <p class="p2">手机号码：18545181691</p>
                <p class="p2">联系电话：0451-88070960</p>
                <p class="p2">联系地址：哈尔滨市松北区创新路1599号</p>
              </div>
              <div class="alertFooter">
                <p class="p1">版权所有 哈尔滨思和信息技术股份有限公司</p>
                <div class="footerErweima">
                  <img src="./images/hsoftMa.png" alt="">
                  <p>工大软件</p>
                  <p>微信服务公众号</p>
                </div>
              </div>
            </div>
          </el-dialog>
          <!-- 输入口令弹框 -->
          <el-dialog title="" :close-on-click-modal="false" :visible.sync="kldialogVisible" width="30%" class="xg"
            :before-close="klhandleClose">
            <el-form ref="form" label-width="120px" size="mini">
              <el-form-item label="口令" prop="kl" class="one-line">
                <el-input placeholder="口令" v-model="kl" clearable></el-input>
              </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
              <el-button type="primary" @click="updataDialog">保 存</el-button>
              <el-button type="warning" @click="gbkl">关 闭</el-button>
            </span>
          </el-dialog>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import dwzc from "./components/dwzc.vue";

import { writeLoginLog } from '../../utils/logUtils'


// 操作浏览器本地数据
import {
  setWindowLocation,
  getWindowLocation,
} from "../../utils/windowLocation.js";

import {
  getDwxxDB
} from "../../db/dwxxDb"

import {
  getlogin,
  getloginyh
} from "../../db/loginyhdb";

import { selectYhByYhmPassword } from '../../db/yhdb'

import { checkArr } from '../../utils/utils'
import { getMacAddress } from '../../utils/detectZoom'

import { getHykl, reviseHyKl } from "../../db/bgwdgldb";
import MD5 from "md5";
import { decryptAesCBCHy, encryptAesCBCHy } from "../../utils/aesUtils";

export default {
  components: {
    dwzc,
  },
  data() {
    return {
      username: "",
      password: "",
      radio: "1",
      yhmc: "",
      zc: true,
      dwxx2: [],
      kl: '',
      dialogVisible: false,
      kldialogVisible: false
    };
  },
  methods: {
    // 退出系统
    quitSystem() {
      this.$confirm('是否执行此操作，点击将退出系统？', '是否退出系统？', {
        cancelButtonClass: "btn-custom-cancel",
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        // center: true
      }).then(() => {
        this.$electron.ipcRenderer.send("close")
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消退出系统'
        })
      })
    },
    dwxx() {
      let dwxx1 = getDwxxDB()
      console.log(dwxx1)
      this.dwxx2 = dwxx1.list_total
      console.log(this.dwxx2.length)
    },
    baocun(value) {
      this.dwxx()
      this.zc = value;
    },
    quxiao(value) {
      this.zc = value;
    },
    // 输入口令方法
    kldialog() {
      this.kldialogVisible = true
    },
    // 输入会员口令弹框关闭
    klhandleClose() {
      this.kldialogVisible = false
    },
    async loginIt() {
      // this.sysj()
      // 数据校验
      if (this.username.trim() == "") {
        this.$message.warning("请输入用户名");
        return;
      }
      if (this.password.trim() == "") {
        this.$message.warning("请输入密码");
        return;
      }
      //
      let dataLogin = getlogin()
      //
      let params = {
        yhm: this.username,
        password: this.password
      }
      let yhList = selectYhByYhmPassword(params)
      if (!yhList || (checkArr(yhList) && yhList.length < 1)) {
        this.$message({
          message: "用户名或密码错误！！！",
          type: "warning"
        })
        return
      }
      
      // 登录结果信息放入缓存
      Object.assign(yhList[0], dataLogin[0])
      setWindowLocation(yhList[0])
      // 写入状态机顶部菜单哪些需要显示(这里使用hsoft-button-img2中的imgType进行标记)
      let showElHeaderMenuList = ['ztqk', 'dbgz', 'yjgz', 'zczp', 'tzgl', 'lstz', 'bggl']
      if (params.yhm == 'root') {
        console.log('超级管理员')
        showElHeaderMenuList.push('sjrz')
      }
      if (params.yhm == 'sysadmin') {
        console.log('系统管理员')
        showElHeaderMenuList = ['']
      }
      if (params.yhm == 'secadmin') {
        console.log('安全管理员')
        showElHeaderMenuList = ['', '']
      }
      if (params.yhm == 'audadmin') {
        console.log('审计员')
        showElHeaderMenuList = ['sjrz']
      }

      this.$router.app.$options.store.commit('changeElHeaderMenuList', showElHeaderMenuList)
      // 写入登录日志
      writeLoginLog(0)

      // 无试用期限制
      // if (params.yhm == 'audadmin') {
      //   this.$router.push("/sjrzsy")
      // } else if (params.yhm == 'sysadmin') {
      //   this.$router.push("/systemSetting")
      // } else if (params.yhm == 'secadmin') {
      //   this.$router.push("/gywmSetting")
      // } else {
      //   this.$router.push("/ztqksy")
      // }


      let nowdateChuo = new Date().getTime(); //转换成时间戳
      // 数据库中是否有数据
      let hyklDatas = getHykl()
      console.log(hyklDatas);
      // 单位注册号
      let dwzch = getlogin()[0].dwzch
      let keyStr = MD5(dwzch).substring(8, 24)
      console.log('数据库里没有key，非会员----带锁')

      console.log(yhList[0].cjsj + 30 * 86399915)
      console.log(nowdateChuo)
      // 带试用期
      // 如果没有密钥或者密钥不匹配，提示相关信息
      if (nowdateChuo > yhList[0].cjsj + 30 * 86399915 && hyklDatas[0].syqzt == 0) {
        this.dialogVisible = true
        this.$notify({
          title: '提示',
          message: '该口令试用期已过',
          type: 'warning',
          offset: 100
        })
      } else if (nowdateChuo < yhList[0].cjsj + 30 * 86399915 || hyklDatas[0].syqzt == 1) {
        if (params.yhm == 'audadmin') {
          this.$router.push("/sjrzsy")
        } else if (params.yhm == 'sysadmin') {
          this.$router.push("/systemSetting")
        } else if (params.yhm == 'secadmin') {
          this.$router.push("/gywmSetting")
        } else {
          this.$router.push("/ztqksy")
        }
      }
    },
    //关闭口令弹框
    gbkl() {
      this.kldialogVisible = false
      this.kl = ''
    },
    // 验证口令
    async updataDialog() {
      // 数据库中是否有数据
      // let hyklDatas = getHykl()
      // 单位注册号
      let dwzch = getlogin()[0].dwzch
      let keyStr = MD5(dwzch).substring(8, 24)
      let nowdateChuo = new Date().getTime(); //转换成时间戳
      //
      // 数据库中无会员口令提示输入密钥串
      if (!this.kl || this.kl == '') {
        console.log('数据库里没有key，非会员----带锁')
        this.$notify({
          title: '提示',
          message: '请输入口令',
          type: 'warning',
          offset: 100
        })
        // 如果没有密钥或者密钥不匹配，提示相关信息
        this.dialogVisible = true
      } else if (this.kl != '') { // 数据库中存在密钥串 验证库中密钥串是否有效
        console.log('数据库里有key------待验证')
        // 如果有，则验证密钥是否有效或是否在有限期内
        let backStr = decryptAesCBCHy(this.kl, keyStr)
        console.log('backStr', backStr)
        if (backStr.dwzch) {
          // 获取口令里的单位注册号
          let mDwzch = backStr.dwzch
          // 验证本地单位注册号与解密单位注册号是否相同
          if (mDwzch == dwzch) {
            console.log('验证此key中单位注册号与本地单位注册号一致')
            // console.log("222222222222222222222222222222222222222222222222", backStr.isZcStatus);
            // if (!backStr.isZcStatus) {
            // 未注册
            // console.log('未注册')
            // 当前系统时间是否在签发日期+注册阈值内
            // backStr.isZcStatus = 0
            // backStr.zcDate = Date.now()
            // encryptAesCBCHy(backStr, keyStr)
            let params = {
              "id": "1",
              "kl": this.kl,
              "syqzt": "1",
            }
            reviseHyKl(params)
            let data = getHykl()
            console.log("注册成功可访问全部！", data);
            if (data[0].kl != '' && data[0].syqzt == 1) {
              if (this.username == 'audadmin') {
                this.$router.push("/sjrzsy")
              } else if (this.username == 'sysadmin') {
                this.$router.push("/systemSetting")
              } else if (this.username == 'secadmin') {
                this.$router.push("/gywmSetting")
              } else {
                this.$router.push("/ztqksy")
              }
            }

            // } else {

            //   if (this.username == 'audadmin') {
            //     this.$router.push("/sjrzsy")
            //   } else if (this.username == 'sysadmin') {
            //     this.$router.push("/systemSetting")
            //   } else if (this.username == 'secadmin') {
            //     this.$router.push("/gywmSetting")
            //   } else {
            //     this.$router.push("/ztqksy")
            //   }
            // }
          } else { // 提示非法密钥串
            this.$notify({
              title: '提示',
              message: '单位注册号不匹配',
              type: 'warning',
              offset: 100
            })
            this.dialogVisible = true
          }
        }else{
          this.$notify({
              title: '提示',
              message: '口令不匹配',
              type: 'warning',
              offset: 100
            })
        }

      }
    },
    loginZc() {
      this.zc = false
    },
  },
  mounted() {
    // console.log(getMacAddress());
    this.dwxx()
  },
  watch: {
    radio(newval, oldval) {
      if (newval != oldval) {
        this.username = "";
        this.password = "";
      }
    },
  },
};
</script>

<style scoped>
/** i标签图标样式 **/
.i-icon {
  font-size: 24px;
  margin: 0 12px;
}

/***/
.login {
  height: 92%;
  width: 100%;
  margin: 0;
  padding: 0;
}

.loginStyle>>>.el-dialog__body {
  padding: 0px;
}

.loginStyle>>>.el-dialog__title::before {
  background: none !important;
}

.loginStyle>>>.el-dialog__header {
  padding: 0px;
}

.login_left .left_text {
  height: 85px;
  line-height: 85px;
  margin-top: 25px;
  text-align: center;
  padding-left: 90px;
  color: #000;
  font-weight: 500;
  font-size: 16px;
}

.login_left .left_img {
  text-align: center;
  padding-left: 90px;
}

.login_left .left_img img {
  width: 154px;
  height: 154px;
}

.login_content {
  width: 30%;
  /* height: 55%; */
  margin-top: 10%;
  padding-top: 15px;
  border: 1px solid #cfd3e0;
  border-radius: 5px;
  margin-left: 35%;
  /* float: right; */
}

.login_left {
  width: 40%;
  margin-left: 12%;
  height: 50%;
  margin-top: 10%;
  float: left;
}

.login_content .login_title {
  height: 65px;
  line-height: 65px;
  text-align: center;
  color: #4c4949;
  font-weight: 500;
  font-size: 16px;
}

/* .login_content .login_title img {
  height: 100%;
  width: 100%;
} */
.loginTabs {
  height: 40px;
  width: 100%;
  overflow: hidden;
  border-bottom: 1px solid #cfd3e0;
}

.tabsLeft {
  width: 50%;
  float: left;
}

/* 注册 */
.register_container {
  width: 100%;
  height: 100%;
}

.zczh {
  width: 40%;
  margin: auto;
}

.zczh_ts {
  font-size: 14px;
  color: #666;
  height: 65px;
  line-height: 65px;
}

.hqyzmBtn {
  width: 28%;
  float: right;
  margin-top: 4px;
  cursor: pointer;
  /* width: 94px; */
  height: 33px;
  line-height: 33px;
  text-align: center;
  font-size: 14px;
  background: #efefef;
  color: #333;
  border: 1px solid #dddee3;
  border-radius: 3px;
}

.next_xb {
  width: 50%;
  height: 33px;
  line-height: 33px;
  text-align: center;
  font-size: 14px;
  border: 1px solid #bfc0c2;
  background: #efefef;
  color: #2b3642;
  cursor: pointer;
  margin-left: 122px;
}

.auth_text_blue {
  color: #c7cad5;
}

.wc_bord {
  height: 105px;
  width: 470px;
  overflow: hidden;
  margin-top: 135px;
}

.wcb_left {
  float: left;
  width: 105px;
  height: 100%;
}

.wcb_left img {
  width: 100%;
  height: 100%;
}

.wcb_right {
  float: left;
  height: 100%;
  margin-left: 20px;
}

/* ---------------------------*/
a,
a:active,
a:link,
a:visited {}

.indexLogo {
  width: 100%;
  /* min-width: 1000px; */
  height: 100%;
  /* background: url(./images/bgcard.png) no-repeat center;
  background-color: #f5f5f6; */
  background-color: rgba(255, 255, 255, 0);
  background-size: 100% 100%;
  clear: both;
  position: relative;
}

.bg-bm {
  width: 52%;
  /* height: 49.3%; */
  height: 52%;
  position: absolute;
  background: url(./images/card.png) no-repeat center;
  background-size: 100% 100%;
  /* top: 27%; */
  top: 50%;
  margin-top: -215px;
  left: 24%;
}

.indexR {
  width: 25%;
  height: 62%;
  /* margin: 6% 13% 0 0; */
  /* float: right; */
  position: absolute;
  /* top: 20%; */
  top: 19%;
  right: 26%;
  /* background-image: linear-gradient(180deg, #FFFFFF 0%, rgba(255, 255, 255, 0.40) 50%); */
  box-shadow: 0px 2px 20px 0px rgba(45, 93, 194, 0.5);
  /* border-radius: 8px; */
  /* background: #fff; */
  background: rgba(255, 255, 255, 0.92);
  z-index: 99;
}

.top-font {
  font-size: 20px;
  color: #737c90;
  letter-spacing: 0;
  text-align: center;
  line-height: 28px;
  font-weight: 400;
}

.guohui {
  width: 50%;
  height: 20%;
  /* background: #000; */
  display: flex;
  align-items: center;

  margin: 4% 0 0 4%;
  /* justify-content: center; */
}

.guohui img {
  /* width: 3.91vw;
  height: 4.2vw; */
  width: 60px;
}

.guohui span {
  font-family: AlibabaPuHuiTi-Bold;
  font-size: 2vw;
  color: #fff;
  letter-spacing: 0;
  font-weight: 700;
  margin-left: 10;
}

.content {
  width: 100%;
  height: 80%;
  /* background-color: rgba(255,255,255,0.40); */
  z-index: 99;
}

.regform-use {
  width: 78%;
  height: 70%;
  margin: 0 auto;
  /* background: #ffffff; */
  border-radius: 8px;
  padding-top: 2vw;
  /* margin-top: 1.5vw; */
}

.clearkit {
  clear: both;
  font-size: 0px;
  line-height: 0px;
  height: 8%;
}

.zsdlinput {
  /* border: #d3d5d6 1px solid; */
  width: 84%;
  /* height: 10%; */
  height: 40px;
  margin: 0 auto;
  /* background: #fff; */
  display: flex;
  align-items: center;
}

span.fl {
  float: left;
  display: inline-block;
}

span.fl img {
  /* padding: 0 10px 0 10px; */
  padding: 0 10px 0 0px;
  /* width: 16px; */
  width: 20px;
}

.fr {
  float: right;
}

.se_wai {
  border: 0;
  border-left: 0px;
  /* height: 2vw; */
  height: 40px;
  width: 90%;
  overflow: hidden;
  /* background: #fff; */
}

.dlinput {
  border: 0;
  /* padding-top: 2px; */
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  outline: none;
}

.qing269 {
  color: #2695fe;
  font: 12px/1.5 Microsoft YaHei, tahoma, arial, Hiragino Sans GB, \\5b8b\4f53,
    sans-serif;
}

.qing269 a {
  color: #316ba2;
}

.msg a {
  color: #d30b15;
}

.dlinputd {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  /* margin-top: 10%; */
}

.zcinputd {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  /* background: #21A566; */
  margin-top: 5%;
}

.dlinputd input {
  border: 0px;
  background-image: linear-gradient(90deg, #00d1ff 0%, #0088ff 100%);
  border-radius: 4px;
  width: 84%;
  height: 2.5rem;
  line-height: 2.5rem;
  color: #fff;
  cursor: pointer;
}

.footer {
  font-size: 20px;
  color: #666666;
  letter-spacing: 0;
  line-height: 24px;
  font-weight: 400;
}

.zcinputd input {
  border: 0px;
  background: #21a566;
  border-radius: 4px;
  width: 84%;
  height: 2.5rem;
  line-height: 2.5rem;
  color: #fff;
  cursor: pointer;
}

.footer-wrapper {
  border-top: 8px solid #fff;
  background: #fff;
  border-bottom: 1px solid #fff;
  height: 140px;
  padding-top: 20px;
}

.footer-wrapper .f-footer {
  width: 1000px;
  margin: 14px auto;
  margin-top: 10px;
}

.clearfix {
  zoom: 1;
}

.footer-wrapper .f-footer>div {
  text-align: center;
}

.footer-wrapper .f-footer .f-f-icon a:first-child {
  margin-top: 14px;
  margin-left: 40px;
}

.footer-wrapper .f-footer .f-f-icon a {
  display: block;
  float: left;
  margin: 0;
}

.footer-wrapper .f-footer .f-links ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.footer-wrapper .f-footer .f-links ul li {
  list-style: none;
  float: left;
  margin: 0;
  padding: 5px 0;
}

.footer-wrapper .f-footer .f-links ul li a {
  color: #282828;
  text-decoration: none;
  font-weight: 700;
  padding: 0 13px;
  font-size: 14px;
  position: relative;
}

.footer-wrapper .f-footer .f-zhuban {
  font-size: 12px;
  padding: 0px;
}

.footer-wrapper .f-footer .f-zhuban:last-child {
  color: #3c6f99;
}

.footer-wrapper .f-footer .f-zhuban span {
  margin: 0 8px;
  height: 20px;
  line-height: 20px;
}

.footer-wrapper .f-footer .f-zhuban span {
  margin: 0 8px;
  height: 20px;
  line-height: 20px;
}

.dialogAlert {
  height: 470px;
  background-image: url("./images/alert1.png");
  background-size: 100% 100%;
}

.dialogAlert img {
  margin-top: 10px;
}

.alertFontDiv {
  width: 100%;
  height: 280px;
  /*text-align: center;*/
  font-size: 20px;
}

.alertFontDiv .p1 {
  font-size: 20px;
  padding-top: 80px;
  /* padding-left: 130px; */
  padding-bottom: 50px;
  color: #000000;
  width: 80%;
  margin: auto;
}

.alertFontDiv .p2 {
  font-size: 16px;
  padding-left: 500px;
  color: #000000;
}

.alertFooter {
  height: calc(100% - 345px);
  position: relative;
}

.alertFooter .p1 {
  font-size: 12px;
  color: #4472C4;
  font-weight: 600;
  position: absolute;
  bottom: 10px;
  left: 10px;
}

.alertFooter .footerErweima {
  position: absolute;
  right: 10px;
  bottom: 10px;
}

.alertFooter .footerErweima img {
  margin-left: 12px;
  width: 60px;
}

.alertFooter .footerErweima p {
  font-size: 12px;
  text-align: center;
}

.klBtn {
  width: 165px;
  height: 40px;
  background-image: linear-gradient(90deg, rgb(40, 220, 134) 0%, rgb(33, 165, 102) 56%);
  font-size: 20px;
  color: #FFFFFF;
  letter-spacing: 0;
  line-height: 18px;
  font-weight: 400;
  border: none;
  margin-left: 85px !important;
  border-radius: 5px !important;
}

.xg :deep(.el-dialog__body) {
  padding: 30px 20px !important;
}
</style>
<style>
.el-input__inner {
  /* border: 0.5px solid #505050 !important; */
  /* border-radius: 0 !important; */
  color: #000 !important;
}

.el-input__prefix,
.el-input__suffix {
  color: #000 !important;
}

.el-form-item__error {
  margin-left: 15px;
}

.el-step.is-simple .el-step__icon {
  margin-top: 5px;
}

.el-radio .el-radio__label {
  /* font-size: 24px !important; */
  font-weight: 500;
  letter-spacing: 0;
}
</style>
