<template>
  <div style="width: 100%;height: calc(100% - 36px);overflow-y: scroll;">
    <!---->
    <div>
      <div class="button-float-right">
        <el-button type="warning" size="medium" icon="el-icon-document-add" @click="save">
          临时保存
        </el-button>
        <el-button type="primary" size="medium" icon="el-icon-document-add" @click="saveToNext">
          保存至上一步
        </el-button>
        <el-button type="primary" size="medium" icon="el-icon-document-add" @click="submit">
          保存并提交
        </el-button>
      </div>
      <div style="clear: both;"></div>
    </div>
    <!-- 基本信息 -->
    <div class="card-out">
      <div class="card-article-group">
        <div class="card-article">
          <div>
            <p>机关单位</p>
            <div class="div-border-under">{{dwxx.dwmc}}</div>
          </div>
        </div>
      </div>
      <div class="card-article-group">
        <div class="card-article">
          <div>
            <p>检查任务</p>
            <div class="div-border-under">{{dialogObj.rwmc}}</div>
          </div>
          <div>
            <p>检查开始时间</p>
            <div class="div-border-under">{{dateFormatNYRChinese(dialogObj.kssj)}}</div>
          </div>
          <div>
            <p>检查结束时间</p>
            <div class="div-border-under">{{dateFormatNYRChinese(dialogObj.jzsj)}}</div>
          </div>
        </div>
      </div>
      <div class="card-article-group">
        <div class="card-article">
          <div>
            <p>联系人</p>
            <div class="div-border-under">{{dwxx.dwlxr}}</div>
          </div>
          <!-- <div>
            <p>联系人职务</p>
            <div class="div-border-under">{{dwxx.zw}}</div>
          </div> -->
          <div>
            <p>联系人电话</p>
            <div class="div-border-under">{{dwxx.dwlxdh}}</div>
          </div>
          <!--占位div-->
          <div></div>
        </div>
      </div>
    </div>
    <!-- 单位检查结果 -->
    <div class="card-out">
      <div class="out">
        <div class="left">
          <div style="padding-left: 10px;">单位检查结果</div>
        </div>
      </div>
      <div class="card-article-group">
        <div class="card-article">
          <div>
            <p>检查得分</p>
            <div>{{dwjcjg.df}}</div>
          </div>
        </div>
        <div class="card-article">
          <div>
            <p>检查结果</p>
            <div>
              <div v-if="dwjcjg.jcjg.synrList.length > 0">
                <p class="jcjg-title">实有内容</p>
                <div class="jcjg-article">
                  <p v-for="(item,index) in dwjcjg.jcjg.synrList" :key="index" style="color: #695e5e;">{{item}}</p>
                </div>
              </div>
              <div v-if="dwjcjg.jcjg.kfsmList.length > 0">
                <p class="jcjg-title">评分说明</p>
                <div class="jcjg-article">
                  <p v-for="(item,index) in dwjcjg.jcjg.kfsmList" :key="index" style="color: #695e5e;">{{item}}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 内设机构抽查结果 -->
    <div class="card-out">
      <div class="out">
        <div class="left">
          <div style="padding-left: 10px;">内设机构抽查结果</div>
        </div>
      </div>
      <div class="table-div">
        <el-table :data="nsjgList" border :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" style="width: 100%;border:1px solid #EBEEF5;" stripe>
          <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
          <el-table-column prop="label" label="内设机构"></el-table-column>
          <el-table-column prop="df" label="得分"></el-table-column>
          <el-table-column label="登记状态" width="250">
            <template slot-scope="scoped">
              <div>
                <span v-if="scoped.row.zt == 0" style="color:#F56C6C;">待登记</span>
                <span v-if="scoped.row.zt == 1" style="color:#E6A23C;">继续登记</span>
                <span v-if="scoped.row.zt == 2" style="color:#67C23A;">完成登记</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="" label="操作" width="120">
            <template slot-scope="scoped">
              <el-button v-if="scoped.row.zt == 0" size="small" type="text" @click="nsjgDj(scoped.row)">登记</el-button>
              <el-button v-if="scoped.row.zt == 1" size="small" type="text" @click="nsjgDj(scoped.row)">继续登记</el-button>
              <el-button v-if="scoped.row.zt == 2" size="small" type="text" @click="toNsjgXqxx(scoped.row)">详情</el-button>
              <el-button size="small" type="text" @click="ycNsjg(scoped.row)" style="color:#E6A23C;">移除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <!-- 人员抽查结果 -->
    <div class="card-out">
      <div class="out">
        <div class="left">
          <div style="padding-left: 10px;">人员抽查结果</div>
        </div>
      </div>
      <div class="table-div">
        <el-table :data="ryList" border :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" style="width: 100%;border:1px solid #EBEEF5;" stripe>
          <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
          <el-table-column prop="xm" label="姓名"></el-table-column>
          <el-table-column prop="zzjgmc" label="所在部门"></el-table-column>
          <el-table-column prop="zwmc" label="职务"></el-table-column>
          <el-table-column prop="jg" label="结果"></el-table-column>
          <el-table-column label="登记状态" width="250">
            <template slot-scope="scoped">
              <div>
                <span v-if="scoped.row.zt == 0" style="color:#F56C6C;">待登记</span>
                <span v-if="scoped.row.zt == 1" style="color:#E6A23C;">继续登记</span>
                <span v-if="scoped.row.zt == 2" style="color:#67C23A;">完成登记</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="" label="操作" width="120">
            <template slot-scope="scoped">
              <el-button v-if="scoped.row.zt == 0" size="small" type="text" @click="ryDj(scoped.row)">登记</el-button>
              <el-button v-if="scoped.row.zt == 1" size="small" type="text" @click="ryDj(scoped.row)">继续登记</el-button>
              <el-button v-if="scoped.row.zt == 2" size="small" type="text" @click="toRyXqxx(scoped.row)">详情</el-button>
              <el-button size="small" type="text" @click="ycRy(scoped.row)" style="color:#E6A23C;">移除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <!---->
    <!-- 评价 -->
    <div class="card-out">
      <div class="out">
        <div class="left">
          <div style="padding-left: 10px;">评价</div>
        </div>
      </div>
      <div class="card-article-group">
        <div class="card-article">
          <div>
            <p>评价结果</p>
            <div>
              <el-radio-group v-model="pjxx.pjjg">
                <el-radio :label="1">优秀</el-radio>
                <el-radio :label="2">合格</el-radio>
                <el-radio :label="3">基本合格</el-radio>
                <el-radio :label="4">不合格</el-radio>
              </el-radio-group>
            </div>
          </div>
        </div>
      </div>
      <div class="card-article-group">
        <div class="card-article">
          <div>
            <p>评价意见</p>
            <div>
              <el-input type="textarea" v-model="pjxx.pjyj"></el-input>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!---->
    <!---->
    <!---->
    <!---->
  </div>
</template>

<script>

import { dateFormatNYRChinese } from '../../../../utils/moment'

import {
  // 通过任务ID获取单位信息
  selectDwxxByRwid
} from '../../../../db/dwxxDb'

import {
  //
  deleteCcdryListByID,
  // 通过任务ID获取任务信息
  selectScrwByRwid,
  // 通过任务ID获取单位评分记录(非组合)
  selectDwpfjlListByRwidFzh,
  // 通过任务ID获取抽查的内设机构抽查结果
  selectCcdsjgListCcjg,
  // 通过任务ID获取抽查的人员抽查结果
  selectCcdryListCcjg,
  // 更新审查任务(审查报告页使用)
  updateScrwList,
  // 通过抽查的内设机构流水ID移除抽查的内设机构表
  deleteCcdnsjgListByID
} from '../../../../db/zczpdb'

import { getZczpIdsObj, initZczpIdsObj, setZczpIdsObj, removeZczpIdsObjField } from '../../../../utils/windowLocation'

import { writeOptionsLog } from '../../../../utils/logUtils'

export default {
  data () {
    return {
      // 任务信息
      dialogObj: {
        // rwid: '93BC8D4D-2FDE-4BB9-85E5-F6214393B82E'
      },
      // 单位信息
      dwxx: {},
      // 内设机构抽查结果
      nsjgList: [],
      // 人员抽查结果
      ryList: [],
      // 评价信息
      pjxx: {
        pjjg: 1,
        pjyj: ''
      },
      // 单位检查结果
      dwjcjg: {
        df: 100,
        jcjg: {
          synrList: [],
          kfsmList: []
        }
      }
    }
  },
  computed: {},
  components: {
  },
  methods: {
    // 人员登记
    ryDj (row) {
      setZczpIdsObj('ccdryid', row.ccdryid)
      this.$router.push({
        path: '/ccdryDj'
      })
    },
    // 移除，使用[抽查的人员流水ID]移除该记录的[抽查的人员表]和[人员评分记录表]
    ycRy (row) {
      console.log(row)
      removeZczpIdsObjField(row.ccdryid)
      let bool = deleteCcdryListByID(row.ccdryid)
      if (bool) {
        this.$message.success('移除成功')
        // 刷新数据
        this.getData()
      }
    },
    // 调转到人员详情信息页面（不可编辑的页面）
    toRyXqxx (row) {
      setZczpIdsObj('ccdryid', row.ccdryid)
      this.$router.push({
        path: '/ccdryDjXqxx'
      })
    },
    // 内设机构登记
    nsjgDj (row) {
      setZczpIdsObj('ccdnsjgid', row.ccdnsjgid)
      this.$router.push({
        path: '/ccdnsjgDj'
      })
    },
    // 移除，使用[抽查的内设机构流水ID]移除该记录的[抽查的内设机构表]和[内设机构评分记录表]
    ycNsjg (row) {
      console.log(row)
      let bool = deleteCcdnsjgListByID(row.ccdnsjgid)
      if (bool) {
        // local自查自评对象中移除该属性
        removeZczpIdsObjField('ccdnsjgid')
        this.$message.success('移除成功')
        // 刷新数据
        this.getData()
      }
    },
    // 调转到内设机构详情信息页面（不可编辑的页面）
    toNsjgXqxx (row) {
      setZczpIdsObj('ccdnsjgid', row.ccdnsjgid)
      this.$router.push({
        path: '/ccdnsjgDjXqxx'
      })
    },
    // 日期转换
    dateFormatNYRChinese (time) {
      let date = new Date(time)
      if ('Invalid Date' == date) {
        return date
      }
      return dateFormatNYRChinese(date)
    },
    save () {
      // 更新任务
      let updateScrwListParams = {
        rwid: this.dialogObj.rwid,
        zt: 6,
        djr: this.dwxx.dwlxr,
        lxr: this.dwxx.dwlxr,
        lxdh: this.dwxx.dwlxdh,
        jcdf: this.dwjcjg.df,
        jcjg: this.dwjcjg.jcjg,
        pjjg: this.pjxx.pjjg,
        pjyj: this.pjxx.pjyj
      }
      let bool = updateScrwList(updateScrwListParams)
      if (bool) {
        this.$message.success('检查报告临时录入成功')
        // 写入操作日志
        let logParams = JSON.parse(JSON.stringify(this.dialogObj))
        // 加入额外数据
        logParams.df = this.dwjcjg.df
        logParams.jcjg = this.dwjcjg.jcjg
        logParams.pjjg = this.dwjcjg.pjjg
        logParams.pjyj = this.dwjcjg.pjyj
        writeOptionsLog('yybs-jczj', '检查总结临时保存', logParams)
        // this.$router.push({
        //   path: '/xjzczp',
        //   query: {
        //     // 任务ID
        //     rwid: this.dialogObj.rwid
        //   }
        // })
      }
    },
    saveToNext () {
      // 更新任务
      let updateScrwListParams = {
        rwid: this.dialogObj.rwid,
        zt: 5,
        djr: this.dwxx.dwlxr,
        lxr: this.dwxx.dwlxr,
        lxdh: this.dwxx.dwlxdh,
        jcdf: this.dwjcjg.df,
        jcjg: this.dwjcjg.jcjg,
        pjjg: this.pjxx.pjjg,
        pjyj: this.pjxx.pjyj
      }
      let bool = updateScrwList(updateScrwListParams)
      if (bool) {
        // 写入操作日志
        let logParams = JSON.parse(JSON.stringify(this.dialogObj))
        // 加入额外数据
        logParams.df = this.dwjcjg.df
        logParams.jcjg = this.dwjcjg.jcjg
        logParams.pjjg = this.dwjcjg.pjjg
        logParams.pjyj = this.dwjcjg.pjyj
        writeOptionsLog('yybs-jczj', '保存至上一步', logParams)
        // 更新vuex状态机关闭tag值，让tags组件能够监测到需要关闭的tag
        this.$router.app.$options.store.commit('changeCloseTag', { path: this.$route.path })
        this.$message.success('检查报告录入成功，返回上一步')
        this.$router.push({
          path: '/ccdry',
          // query: this.dialogObj
        })
      }
    },
    submit () {
      // 判断是否存在未完成评分的内设机构
      let nsjgNotFinish = false
      this.nsjgList.some(item => {
        if (item.zt != 2) {
          nsjgNotFinish = true
          this.$message.warning('请先完成内设机构[' + item.label + ']的检查结果登记,点击对应操作按钮完成页面的跳转')
          return true
        }
      })
      if (nsjgNotFinish) {
        return
      }
      // 判断是否存在未完成评分的人员
      let ryNotFinish = false
      this.ryList.some(item => {
        if (item.zt != 2) {
          ryNotFinish = true
          this.$message.warning('请先完成人员[' + item.xm + ']的检查结果登记，点击对应操作按钮完成页面的跳转')
          return true
        }
      })
      if (ryNotFinish) {
        return
      }
      // 更新任务
      let updateScrwListParams = {
        rwid: this.dialogObj.rwid,
        zt: 7,
        djr: this.dwxx.dwlxr,
        lxr: this.dwxx.dwlxr,
        lxdh: this.dwxx.dwlxdh,
        jcdf: this.dwjcjg.df,
        jcjg: this.dwjcjg.jcjg,
        pjjg: this.pjxx.pjjg,
        pjyj: this.pjxx.pjyj
      }
      let bool = updateScrwList(updateScrwListParams)
      if (bool) {
        // 写入操作日志
        let logParams = JSON.parse(JSON.stringify(this.dialogObj))
        // 加入额外数据
        logParams.df = this.dwjcjg.df
        logParams.jcjg = this.dwjcjg.jcjg
        logParams.pjjg = this.dwjcjg.pjjg
        logParams.pjyj = this.dwjcjg.pjyj
        writeOptionsLog('yybs-jczj', '审查任务完结', logParams)
        /**
         * 关闭除第一个的其他(想tags组件发送关闭除第一个的其他请求)（allTagWithOutFirst）
         * 更新vuex状态机关闭tag值，让tags组件能够监测到需要关闭的tag
        */
        this.$router.app.$options.store.commit('changeCloseTag', { path: 'allTagWithOutFirst' })
        // 初始化自查自评
        initZczpIdsObj()
        this.$router.push('/zczpls')
      }
    },
    returnSy () {
      this.$router.go(-1)
    },
    // 通过任务ID获取单位信息
    getDwxxByRwid () {
      this.dwxx = selectDwxxByRwid(this.dialogObj.rwid)
    },
    // 通过任务ID获取任务信息
    getRwxxByRwid () {
      this.dialogObj = selectScrwByRwid(this.dialogObj.rwid)
      if (this.dialogObj.pjjg) {
        this.pjxx.pjjg = this.dialogObj.pjjg
      }
      if (this.dialogObj.pjyj) {
        this.pjxx.pjyj = this.dialogObj.pjyj
      }
    },
    // 获取单位检查结果
    getDwpfjlListByRwid () {
      // 通过任务ID获取单位评分记录信息(非组合)
      const dwpfjlList = selectDwpfjlListByRwidFzh(this.dialogObj.rwid)
      dwpfjlList.forEach(item => {
        // console.log(item)
        if (item.ykf) {
          this.dwjcjg.df -= item.ykf
        }
        if (item.sfsynr && item.synr) {
          this.dwjcjg.jcjg.synrList.push((this.dwjcjg.jcjg.synrList.length + 1) + '、' + item.synr)
        }
        if (item.kfsm) {
          this.dwjcjg.jcjg.kfsmList.push((this.dwjcjg.jcjg.kfsmList.length + 1) + '、' + item.kfsm)
        }
      })
      console.log('dwjcjg', this.dwjcjg)
    },
    // 获取内设机构抽查结果
    getNsjgCcjg () {
      // 通过任务ID获取抽查的内设机构抽查结果
      this.nsjgList = selectCcdsjgListCcjg(this.dialogObj.rwid)
    },
    // 获取人员抽查结果
    getRyCcjg () {
      // 通过任务ID获取抽查的人员抽查结果
      this.ryList = selectCcdryListCcjg(this.dialogObj.rwid)
    },
    // 查询数据
    getData () {
      // 通过任务ID获取单位信息
      this.getDwxxByRwid()
      // 通过任务ID获取任务信息
      this.getRwxxByRwid()
      // 获取单位检查结果
      this.getDwpfjlListByRwid()
      // 获取内设机构抽查结果
      this.getNsjgCcjg()
      // 获取人员抽查结果
      this.getRyCcjg()
    }
  },
  watch: {},
  mounted () {
    // let params = this.$route.query
    let params = getZczpIdsObj()
    console.log('检查总结', params)
    if (params && Object.keys(params).length > 0) {
      this.dialogObj.rwid = params.rwid
      // 查询数据
      this.getData()
      return
    }
    this.$message.warning('未能检测到检测任务ID，请关闭页面重新进入')
  }
}
</script>

<style scoped>
.button-float-right {
  float: right;
}
/***/
.out .left {
  color: #0646bf;
  font-weight: 700;
  position: relative;
}
.out .left::before {
  content: "";
  background: #0646bf;
  width: 4px;
  height: 80%;
  position: absolute;
  top: 5%;
}
/***/
.card-out {
  /* background: red; */
  padding: 10px;
  font-size: 15px;
}
.card-out .card-article-group {
  /* background: blue; */
  font-size: 14px;
  padding: 10px 0;
}
.card-out .card-article-group .card-article {
  padding: 10px 0;
  display: flex;
}
.card-out .card-article-group .card-article > div > p {
  /* background: yellow; */
  width: 100px;
  text-align: right;
  padding-right: 1vw;
  box-sizing: border-box;
  font-weight: 600;
  color: #83838c;
}
.card-out .card-article-group .card-article > div {
  /* background: green; */
  flex: 1;
  text-align: left;
  box-sizing: border-box;
  display: flex;
}
.card-out .card-article-group .card-article div > div {
  flex: 1;
  margin: 0 10px;
}
.div-border-under {
  border-bottom: 1px dotted #cec8c8;
}
.card-out .table-div {
  padding: 10px 0;
}
.card-out .card-article-group .card-article .jcjg-title {
  color: #83838c;
  font-weight: 600;
  color: #7878bb;
}
.card-out .card-article-group .card-article .jcjg-article {
  padding: 10px;
}
</style>
