<template>
	<div class="bg_con" style="height: calc(100% - 38px);">
		<div style="width: 100%; position: relative; overflow: hidden; height: 100%;">
			<!-- <div class="xmlb-title" style=" cursor: pointer;">
				<span style="font-size: 24px; cursor: pointer;">汇总情况</span>
				<span style="" @click="returnSy" class="fhsy">返回</span>
			</div> -->

			<div class="dabg" style="height: 100%;">
				<div class="content" style="height: 100%;">
					<div class="table" style="height: 100%;">
						<!-- -----------------操作区域--------------------------- -->
						<div class="mhcx">
							<el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline" style="float:left">
								<el-form-item label="台账时间" style="font-weight: 700;">
									<!-- <el-input v-model="formInline.tzsj" clearable placeholder="台账时间" class="widthw">
									</el-input> -->
									<el-select v-model="formInline.tzsj" placeholder="台账时间">
										<el-option
										v-for="item in yearSelect"
										:key="item.value"
										:label="item.label"
										:value="item.value">
										</el-option>
									</el-select>
								</el-form-item>
								<el-form-item label="部门" style="font-weight: 700;">
									<!-- <el-input v-model="formInline.bm" clearable placeholder="部门" class="widthw">
									</el-input> -->
									<el-cascader v-model="formInline.bm" :options="regionOption" :props="regionParams" filterable
										clearable ref="cascaderArr"></el-cascader>
								</el-form-item>
								<el-form-item label="姓名" style="font-weight: 700;">
									<el-input v-model="formInline.xm" clearable placeholder="姓名" class="widthw">
									</el-input>
								</el-form-item>

								<el-form-item>
									<el-button type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
								</el-form-item>
								<el-form-item>
									<el-button type="warning" icon="el-icon-circle-close" @click="cz">重置</el-button>
								</el-form-item>
							</el-form>
							<el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline" style="float:right">
								<!-- <el-form-item style="float: right;">
									<el-button type="danger" size="medium" @click="deleteTkglBtn" icon="el-icon-delete-solid">删除
									</el-button>
								</el-form-item> -->
								<el-form-item style="float: right;">
									<el-button type="primary" size="medium" icon="el-icon-download" @click="exportList">
										导出
									</el-button>
								</el-form-item>
								<!-- <el-form-item style="float: right;">
									<input type="file" ref="upload"
										style="display: none;position: absolute;top:10px; right:0;opacity:0; cursor: pointer;height: 32px;width: 56px;z-index:1;"
										accept=".xls,.xlsx">
									<el-button type="primary" icon="el-icon-upload2" size="medium" @click="dr_dialog = true">
										导入
									</el-button>
								</el-form-item>
								<el-form-item style="float: right;">
									<el-button type="success" size="medium" @click="xz" icon="el-icon-plus">
										新增
									</el-button>
								</el-form-item> -->
							</el-form>
						</div>


						<!-- -----------------审查组人员列表--------------------------- -->
						<div class="table_content_padding" style="height: 100%;">
							<div class="table_content" style="height: 100%;">
								<el-table :data="smryList" border @selection-change="selectRow"
									:header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
									style="width: 100%;border:1px solid #EBEEF5;" height="calc(100% - 34px - 44px - 10px)" stripe>
									<el-table-column type="selection" width="55" align="center"> </el-table-column>
									<el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
									<el-table-column prop="xm" label="姓名"></el-table-column>
									<el-table-column prop="bm" label="部门"></el-table-column>
									<el-table-column prop="gwmc" label="岗位名称"></el-table-column>
									<el-table-column prop="smdj" label="涉密等级"></el-table-column>
									<el-table-column prop="zw" label="职务"></el-table-column>
									<el-table-column prop="zj" label="职级"></el-table-column>
									<el-table-column prop="zc" label="职称"></el-table-column>
									<el-table-column prop="sfsc" label="是否审查"></el-table-column>
									<el-table-column prop="sgsj" label="上岗时间"></el-table-column>
									<el-table-column prop="tzsj" label="台账时间"></el-table-column>
									<!-- <el-table-column prop="bz" label="备注"></el-table-column> -->
									<el-table-column prop="" label="操作" width="120">
										<template slot-scope="scoped">
											<el-button size="medium" type="text" @click="xqyl(scoped.row)">详情
											</el-button>
											<!-- <el-button size="medium" type="text" @click="updateItem(scoped.row)">修改
											</el-button> -->
										</template>
									</el-table-column>

								</el-table>

								<!-- -------------------------分页区域---------------------------- -->
								<div style="border: 1px solid #ebeef5;">
									<el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange"
										:pager-count="5" :current-page="page" :page-sizes="[5, 10, 20, 30]" :page-size="pageSize"
										layout="total, prev, pager, sizes,next, jumper" :total="total">
									</el-pagination>
								</div>
							</div>
						</div>
					</div>
				</div>
				<!-- -----------------导入-弹窗--------------------------- -->
				<el-dialog width="1000px" height="800px" title="导入涉密人员汇总情况" class="scbg-dialog" :visible.sync="dialogVisible_dr"
					show-close>
					<div style="height: 600px;">
						<el-table :data="dr_cyz_list" ref="multipleTable" @selection-change="handleSelectionChange"
							style="width: 100%;border:1px solid #EBEEF5;" height="100%" stripe>
							<el-table-column type="selection" width="55"> </el-table-column>
							<el-table-column prop="姓名" label="姓名"></el-table-column>
							<el-table-column prop="部门" label="部门"></el-table-column>
							<el-table-column prop="岗位名称" label="岗位名称"></el-table-column>
							<el-table-column prop="涉密等级" label="涉密等级"></el-table-column>
							<el-table-column prop="职务" label="职务"></el-table-column>
							<el-table-column prop="职级" label="职级"></el-table-column>
							<el-table-column prop="级别职称" label="级别职称"></el-table-column>
							<el-table-column prop="是否审查" label="是否审查"></el-table-column>
							<!-- <el-table-column prop="上岗时间" label="上岗时间"></el-table-column> -->
							<el-table-column prop="备注" label="备注">
							</el-table-column>
						</el-table>
					</div>

					<div style="height :30px;display: flex;align-items: center;justify-content: center;margin: 10px 0;">
						<el-button type="primary" @click="drcy" size="mini">导 入</el-button>
						<el-button type="warning" @click="dialogVisible_dr = false" size="mini">关 闭</el-button>
					</div>
				</el-dialog>

				<!-- 模板下载 -->
				<el-dialog title="开始导入" class="scbg-dialog" width="600px" @close="mbxzgb" :visible.sync="dr_dialog" show-close>
					<div style="padding: 20px;">
						<div class="daochu">
							<div>一、请点击“导出模板”，并参照模板填写信息。</div>
							<el-button type="primary" size="mini" @click="mbdc">
								模板导出
							</el-button>
						</div>
						<div class="daochu">
							<div>二、数据导入方式：</div>
							<el-radio-group v-model="sjdrfs" @change="Radio($event)">
								<el-radio label="1">追加（导入时已有的记录信息不变，只添加新的记录）</el-radio>
								<el-radio label="2">覆盖（导入时更新已有的记录信息，并添加新的记录）</el-radio>
							</el-radio-group>
						</div>
						<div class="daochu">
							<div>三、将按模板填写的文件，导入到系统中。</div>
							<el-button type="primary" size="mini" @click="chooseFile">
								上传导入
							</el-button>
						</div>
					</div>

				</el-dialog>

				<!-- -----------------新增涉密人员-弹窗--------------------------- -->
				<el-dialog title="新增涉密人员" :close-on-click-modal="false" :visible.sync="dialogVisible" width="50%" class="xg"
					:before-close="handleClose" @close="close('formName')">
					<el-form ref="formName" :model="tjlist" :rules="rules" label-width="180px" size="mini"
						:label-position="labelPosition">
						<div style="display:flex">
							<el-form-item label="姓名" prop="xm">
								<el-input placeholder="姓名" v-model="tjlist.xm" clearable></el-input>
							</el-form-item>
							<el-form-item label="身份证号码" prop="sfzhm">
								<el-input placeholder="身份证号码" v-model="tjlist.sfzhm" clearable @blur="onInputBlur(1)">
								</el-input>
							</el-form-item>
						</div>

						<el-form-item label="性别" prop="xb">
							<el-radio-group v-model="tjlist.xb">
								<el-radio label="男"></el-radio>
								<el-radio label="女"></el-radio>
							</el-radio-group>
							<!-- <el-select v-model="tjlist.xb" placeholder="请选择性别">
									<el-option label="男" value="男"></el-option>
									<el-option label="女" value="女"></el-option>
								</el-select> -->
						</el-form-item>
						<div style="display:flex">
							<el-form-item label="年龄" prop="nl">
								<el-input oninput="value=value.replace(/[^\d.]/g,'')" @blur="nl = $event.target.value" placeholder="年龄"
									v-model="tjlist.nl" clearable>
								</el-input>
							</el-form-item>
							<el-form-item label="联系电话">
								<el-input placeholder="联系电话" v-model="tjlist.lxdh" clearable oninput="value=value.replace(/[^\d.]/g,'')"
									@blur="lxdh = $event.target.value">
								</el-input>
							</el-form-item>
						</div>
						<div style="display:flex">

							<el-form-item label="部门" prop="bm">
								<!-- <el-input v-model="tjlist.bm" clearable placeholder="部门"></el-input> -->
								<el-cascader v-model="tjlist.bm" :options="regionOption" :props="regionParams" filterable
									ref="cascaderArr" @change="handleChange(1)"></el-cascader>
							</el-form-item>

							<el-form-item label="岗位名称" prop="gwmc">
								<!-- <el-input v-model="tjlist.gwmc" clearable></el-input> -->
								<!-- <el-autocomplete class="inline-inputgw" value-key="gwmc" v-model.trim="tjlist.gwmc"
									:fetch-suggestions="querySearch" placeholder="请输入岗位名称" @select="handleSelect" >
								</el-autocomplete> -->
								<el-select v-model="tjlist.gwmc" placeholder="请选择岗位" multiple @change="handleSelect">
									<el-option v-for="(item, label) in gwmc" :label="item.gwmc" :value="item.gwmc" :key="label">
									</el-option>
								</el-select>
							</el-form-item>

						</div>
						<div style="display:flex">
							<el-form-item label="涉密等级" prop="smdj">
								<el-select v-model="tjlist.smdj" placeholder="请选择涉密等级">
									<el-option v-for="item in smdjxz" :label="item.smdjmc" :value="item.smdjmc" :key="item.smdjid">
									</el-option>
								</el-select>
							</el-form-item>

							<el-form-item label="岗位确定依据" prop="gwqdyj">
								<el-select v-model="tjlist.gwqdyj" placeholder="请选择岗位确定依据">
									<el-option v-for="item in gwqdyjxz" :label="item.gwqdyjmc" :value="item.gwqdyjmc"
										:key="item.gwqdyjid"></el-option>
								</el-select>
							</el-form-item>

						</div>
						<div style="display:flex">
							<el-form-item label="最高学历" prop="zgxl">
								<el-select v-model="tjlist.zgxl" placeholder="请选择最高学历">
									<el-option v-for="item in zgxlxz" :label="item.zgxlmc" :value="item.zgxlmc" :key="item.zgxlid">
									</el-option>
								</el-select>
							</el-form-item>

							<el-form-item label="职务">
								<!-- <el-input v-model="tjlist.zw" clearable placeholder="职务"></el-input> -->
								<el-autocomplete class="inline-input" value-key="zw" v-model.trim="tjlist.zw"
									:fetch-suggestions="querySearchzw" placeholder="请输入职务名称">
								</el-autocomplete>
							</el-form-item>

						</div>
						<div style="display:flex">
							<el-form-item label="职级">
								<el-autocomplete class="inline-input" value-key="zj" v-model.trim="tjlist.zj"
									:fetch-suggestions="querySearchzj" placeholder="请输入职级名称">
								</el-autocomplete>
							</el-form-item>

							<el-form-item label="级别职称">
								<el-select v-model="tjlist.zc" placeholder="请选择级别职称">
									<el-option v-for="item in jbzcxz" :label="item.jbzcmc" :value="item.jbzcmc" :key="item.jbzcid">
									</el-option>
								</el-select>
								<el-popover placement="right" width="200" trigger="hover">
									<div>
										<div style="display:flex;margin-bottom:10px">
											<i class="el-icon-info" style="color:#409eef;position: relative;top: 2px;"></i>
											<div class="tszt">提示</div>
										</div>
										<div class="smzt">
											（1）机关、参公事业单位、事业单位、国有企业管理岗位人员填写行政级别，即省部级、厅局级、县处级、乡科级及以下、试用期；事业单位、国有企业技术岗位人员填写职称，即高级（含正高、副高）、中级、初级及以下、试用期；机关、参公事业单位、事业单位工勤人员填写工勤人员；企业人员无行政级别的，填写企业职员。（2）既有职务、又有职级的，按照对应级别较高的填写；既有级别、又有职称的，机关按照级别填写，事业单位、国有企业按照职称填写。个别机关、单位人员难以明确对应的，根据干部管理权限尽量准确。
										</div>
									</div>
									<i class="el-icon-info" style="color:#409eef;position: absolute;    right: -18px;top: 7px;"
										slot="reference"></i>

								</el-popover>
							</el-form-item>
						</div>
						<div style="display:flex">
							<el-form-item label="身份类型" prop="sflx">
								<el-select v-model="tjlist.sflx" placeholder="请选择身份类型">
									<el-option v-for="item in sflxxz" :label="item.sflxmc" :value="item.sflxmc" :key="item.sflxid">
									</el-option>
								</el-select>
								<el-popover placement="right" width="200" trigger="hover">
									<div>
										<div style="display:flex;margin-bottom:10px">
											<i class="el-icon-info" style="color:#409eef;position: relative;top: 2px;"></i>
											<div class="tszt">提示</div>
										</div>
										<div class="smzt">
											根据涉密人员所在单位类型和人员类型对应填写，从下拉栏中选择：公务员、参公人员、事业人员、企业人员、其他。
										</div>
									</div>
									<i class="el-icon-info" style="color:#409eef;position: absolute;    right: -18px;top: 7px;"
										slot="reference"></i>

								</el-popover>
							</el-form-item>

							<el-form-item label="用人形式" prop="yrxs">
								<el-select v-model="tjlist.yrxs" placeholder="请选择用人形式" style="width:184px">
									<el-option v-for="item in yrxxxz" :label="item.yrxxmc" :value="item.yrxxmc" :key="item.yrxxid">
									</el-option>
								</el-select>
							</el-form-item>
						</div>

						<el-form-item label="是否审查" prop="sfsc">
							<el-radio-group v-model="tjlist.sfsc">
								<el-radio label="是"></el-radio>
								<el-radio label="否"></el-radio>
							</el-radio-group>
							<!-- <el-select v-model="tjlist.sfsc" placeholder="请选择">
									<el-option label="是" value="是"></el-option>
									<el-option label="否" value="否"></el-option>
							</el-select> -->
						</el-form-item>
						<el-form-item label="是否出入境登记备案" prop="crjdjba">
							<el-radio-group v-model="tjlist.crjdjba">
								<el-radio label="是"></el-radio>
								<el-radio label="否"></el-radio>
							</el-radio-group>
							<!-- <el-select v-model="tjlist.crjdjba" placeholder="请选择">
									<el-option label="是" value="是"></el-option>
									<el-option label="否" value="否"></el-option>
							</el-select> -->
						</el-form-item>


						<el-form-item label="是否统一保管出入境证件" prop="tybgcrjzj">
							<el-radio-group v-model="tjlist.tybgcrjzj">
								<el-radio label="是"></el-radio>
								<el-radio label="否"></el-radio>
							</el-radio-group>
							<!-- <el-select v-model="tjlist.tybgcrjzj" placeholder="请选择">
									<el-option label="是" value="是"></el-option>
									<el-option label="否" value="否"></el-option>
							</el-select> -->
						</el-form-item>
						<el-form-item label="上岗时间" prop="sgsj">
							<!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
							<el-date-picker v-model="tjlist.sgsj" class="cd" clearable type="date" placeholder="选择日期"
								format="yyyy年MM月dd日" value-format="yyyy年MM月dd日">
							</el-date-picker>
						</el-form-item>

						<el-form-item label="备注" prop="bz">
							<el-input type="textarea" v-model="tjlist.bz"></el-input>
						</el-form-item>
					</el-form>
					<span slot="footer" class="dialog-footer">
						<el-button type="primary" @click="submitTj('formName')">保 存</el-button>
						<el-button type="warning" @click="handleClose()">关 闭</el-button>
					</span>
				</el-dialog>

				<el-dialog title="修改涉密人员" :close-on-click-modal="false" :visible.sync="xgdialogVisible" width="50%" class="xg"
					@close="close1('form')">
					<el-form ref="form" :model="xglist" :rules="rules" label-width="180px" size="mini"
						:label-position="labelPosition">
						<div style="display:flex">
							<el-form-item label="姓名" prop="xm">
								<el-input placeholder="姓名" v-model="xglist.xm" clearable></el-input>
							</el-form-item>
							<el-form-item label="身份证号码" prop="sfzhm">
								<el-input placeholder="身份证号码" v-model="xglist.sfzhm" clearable @blur="onInputBlur(2)">
								</el-input>
							</el-form-item>
						</div>

						<el-form-item label="性别" prop="xb">
							<el-radio-group v-model="xglist.xb">
								<el-radio label="男"></el-radio>
								<el-radio label="女"></el-radio>
							</el-radio-group>
							<!-- <el-select v-model="tjlist.xb" placeholder="请选择性别">
									<el-option label="男" value="男"></el-option>
									<el-option label="女" value="女"></el-option>
								</el-select> -->
						</el-form-item>
						<div style="display:flex">
							<el-form-item label="年龄" prop="nl">
								<el-input oninput="value=value.replace(/[^\d.]/g,'')" @blur="nl = $event.target.value" placeholder="年龄"
									v-model="xglist.nl" clearable>
								</el-input>
							</el-form-item>
							<el-form-item label="联系电话" >
								<el-input placeholder="联系电话" v-model="xglist.lxdh" clearable oninput="value=value.replace(/[^\d.]/g,'')"
									@blur="lxdh = $event.target.value">
								</el-input>
							</el-form-item>
						</div>
						<div style="display:flex">
							<el-form-item label="部门" prop="bm">
								<!-- <el-input v-model="xglist.bm" clearable></el-input> -->
								<el-cascader v-model="xglist.bm" :options="regionOption" :props="regionParams" filterable
									ref="cascaderArr" @change="handleChange(2)"></el-cascader>
							</el-form-item>

							<el-form-item label="岗位名称" prop="gwmc">
								<!-- <el-input v-model="xglist.gwmc" clearable></el-input> -->
								<!-- <el-autocomplete class="inline-inputgw" value-key="gwmc" v-model.trim="xglist.gwmc"
									:fetch-suggestions="querySearch" placeholder="请输入岗位名称" @select="handleSelect1">
								</el-autocomplete> -->
								<el-select v-model="xglist.gwmc" placeholder="请选择岗位" multiple @change="handleSelect1">
									<el-option v-for="(item, label) in gwmc" :label="item.gwmc" :value="item.gwmc" :key="label">
									</el-option>
								</el-select>
							</el-form-item>

						</div>
						<div style="display:flex">
							<el-form-item label="涉密等级" prop="smdj">
								<el-select v-model="xglist.smdj" placeholder="请选择涉密等级">
									<el-option v-for="item in smdjxz" :label="item.smdjmc" :value="item.smdjmc" :key="item.smdjid">
									</el-option>
								</el-select>
							</el-form-item>

							<el-form-item label="岗位确定依据" prop="gwqdyj">
								<el-select v-model="xglist.gwqdyj" placeholder="请选择岗位确定依据">
									<el-option v-for="item in gwqdyjxz" :label="item.gwqdyjmc" :value="item.gwqdyjmc"
										:key="item.gwqdyjid"></el-option>
								</el-select>
							</el-form-item>

						</div>
						<div style="display:flex">
							<el-form-item label="最高学历" prop="zgxl">
								<el-select v-model="xglist.zgxl" placeholder="请选择最高学历">
									<el-option v-for="item in zgxlxz" :label="item.zgxlmc" :value="item.zgxlmc" :key="item.zgxlid">
									</el-option>
								</el-select>
							</el-form-item>

							<el-form-item label="职务">
								<el-autocomplete class="inline-input" value-key="zw" v-model.trim="xglist.zw"
									:fetch-suggestions="querySearchzw" placeholder="请输入职务名称">
								</el-autocomplete>
							</el-form-item>

						</div>
						<div style="display:flex">
							<el-form-item label="职级">
								<el-autocomplete class="inline-input" value-key="zj" v-model.trim="xglist.zj"
									:fetch-suggestions="querySearchzj" placeholder="请输入职级名称">
								</el-autocomplete>
							</el-form-item>

							<el-form-item label="级别职称">
								<el-select v-model="xglist.zc" placeholder="请选择级别职称">
									<el-option v-for="item in jbzcxz" :label="item.jbzcmc" :value="item.jbzcmc" :key="item.jbzcid">
									</el-option>
								</el-select>

								<el-popover placement="right" width="200" trigger="hover">
									<div>
										<div style="display:flex;margin-bottom:10px">
											<i class="el-icon-info" style="color:#409eef;position: relative;top: 2px;"></i>
											<div class="tszt">提示</div>
										</div>
										<div class="smzt">
											（1）机关、参公事业单位、事业单位、国有企业管理岗位人员填写行政级别，即省部级、厅局级、县处级、乡科级及以下、试用期；事业单位、国有企业技术岗位人员填写职称，即高级（含正高、副高）、中级、初级及以下、试用期；机关、参公事业单位、事业单位工勤人员填写工勤人员；企业人员无行政级别的，填写企业职员。（2）既有职务、又有职级的，按照对应级别较高的填写；既有级别、又有职称的，机关按照级别填写，事业单位、国有企业按照职称填写。个别机关、单位人员难以明确对应的，根据干部管理权限尽量准确。
										</div>
									</div>
									<i class="el-icon-info" style="color:#409eef;position: absolute;    right: -18px;top: 7px;"
										slot="reference"></i>

								</el-popover>
							</el-form-item>
						</div>
						<div style="display:flex">
							<el-form-item label="身份类型" prop="sflx">
								<el-select v-model="xglist.sflx" placeholder="请选择身份类型">
									<el-option v-for="item in sflxxz" :label="item.sflxmc" :value="item.sflxmc" :key="item.sflxid">
									</el-option>
								</el-select>
								<el-popover placement="right" width="200" trigger="hover">
									<div>
										<div style="display:flex;margin-bottom:10px">
											<i class="el-icon-info" style="color:#409eef;position: relative;top: 2px;"></i>
											<div class="tszt">提示</div>
										</div>
										<div class="smzt">
											根据涉密人员所在单位类型和人员类型对应填写，从下拉栏中选择：公务员、参公人员、事业人员、企业人员、其他。
										</div>
									</div>
									<i class="el-icon-info" style="color:#409eef;position: absolute;    right: -18px;top: 7px;"
										slot="reference"></i>

								</el-popover>
							</el-form-item>

							<el-form-item label="用人形式" prop="yrxs">
								<el-select v-model="xglist.yrxs" placeholder="请选择用人形式" style="width:184px">
									<el-option v-for="item in yrxxxz" :label="item.yrxxmc" :value="item.yrxxmc" :key="item.yrxxid">
									</el-option>
								</el-select>
							</el-form-item>
						</div>

						<el-form-item label="是否审查" prop="sfsc">
							<el-radio-group v-model="xglist.sfsc">
								<el-radio label="是"></el-radio>
								<el-radio label="否"></el-radio>
							</el-radio-group>
							<!-- <el-select v-model="tjlist.sfsc" placeholder="请选择">
									<el-option label="是" value="是"></el-option>
									<el-option label="否" value="否"></el-option>
							</el-select> -->
						</el-form-item>
						<el-form-item label="是否出入境登记备案" prop="crjdjba">
							<el-radio-group v-model="xglist.crjdjba">
								<el-radio label="是"></el-radio>
								<el-radio label="否"></el-radio>
							</el-radio-group>
							<!-- <el-select v-model="tjlist.crjdjba" placeholder="请选择">
									<el-option label="是" value="是"></el-option>
									<el-option label="否" value="否"></el-option>
							</el-select> -->
						</el-form-item>


						<el-form-item label="是否统一保管出入境证件" prop="tybgcrjzj">
							<el-radio-group v-model="xglist.tybgcrjzj">
								<el-radio label="是"></el-radio>
								<el-radio label="否"></el-radio>
							</el-radio-group>
							<!-- <el-select v-model="tjlist.tybgcrjzj" placeholder="请选择">
									<el-option label="是" value="是"></el-option>
									<el-option label="否" value="否"></el-option>
							</el-select> -->
						</el-form-item>
						<el-form-item label="上岗时间" prop="sgsj">
							<!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
							<el-date-picker v-model="xglist.sgsj" class="cd" clearable type="date" placeholder="选择日期"
								format="yyyy年MM月dd日" value-format="yyyy年MM月dd日">
							</el-date-picker>
						</el-form-item>

						<el-form-item label="备注" prop="bz">
							<el-input type="textarea" v-model="xglist.bz"></el-input>
						</el-form-item>
					</el-form>
					<span slot="footer" class="dialog-footer">
						<el-button type="primary" @click="updataDialog('form')">修 改</el-button>
						<el-button type="warning" @click="xgdialogVisible = false">关 闭</el-button>
					</span>
				</el-dialog>

				<el-dialog title="涉密人员详情" :close-on-click-modal="false" :visible.sync="xqdialogVisible" width="50%" class="xg">
					<el-form ref="form" :model="xglist" label-width="152px" size="mini" :label-position="labelPosition" disabled>
						<div style="display:flex">
							<el-form-item label="姓名" prop="xm">
								<el-input placeholder="姓名" v-model="xglist.xm" clearable></el-input>
							</el-form-item>
							<el-form-item label="身份证号码" prop="sfzhm">
								<el-input placeholder="身份证号码" v-model="xglist.sfzhm" clearable @blur="onInputBlur(1)"
									style="width:100%">
								</el-input>
							</el-form-item>
						</div>
						<div style="display:flex">
							<el-form-item label="性别" prop="xb">
								<el-radio-group v-model="xglist.xb">
									<el-radio label="男"></el-radio>
									<el-radio label="女"></el-radio>
								</el-radio-group>
							</el-form-item>
							<el-form-item label="年龄" prop="nl">
								<el-input oninput="value=value.replace(/[^\d.]/g,'')" @blur="nl = $event.target.value"
									style="width:100%" placeholder="年龄" v-model="xglist.nl" clearable>
								</el-input>
							</el-form-item>
						</div>
						<div style="display:flex">
							<el-form-item label="联系电话">
								<el-input placeholder="联系电话" v-model="xglist.lxdh" clearable
									oninput="value=value.replace(/[^\d.]/g,'')" @blur="lxdh = $event.target.value">
								</el-input>
							</el-form-item>
							<el-form-item label="部门" prop="bm">
								<!-- <el-input v-model="xglist.bm" clearable placeholder="部门"></el-input> -->
								<el-cascader v-model="xglist.bm" :options="regionOption" :props="regionParams" style="width: 100%;"
									filterable ref="cascaderArr" @change="handleChange(1)"></el-cascader>
							</el-form-item>
						</div>
						<div style="display:flex">
							<el-form-item label="岗位名称" prop="gwmc">
								<el-select v-model="xglist.gwmc" placeholder="请选择岗位" multiple @change="handleSelect" style="height: 32px;width:100%;">
									<el-option v-for="(item, label) in gwmc" :label="item.gwmc" :value="item.gwmc"
										:key="label">
									</el-option>
								</el-select>
							</el-form-item>
							<el-form-item label="涉密等级" prop="smdj">
								<el-select v-model="xglist.smdj" placeholder="请选择涉密等级" style="width: 100%;">
									<el-option v-for="item in smdjxz" :label="item.smdjmc" :value="item.smdjmc"
										:key="item.smdjid">
									</el-option>
								</el-select>
							</el-form-item>
						</div>
						<div style="display:flex">
							<el-form-item label="岗位确定依据" prop="gwqdyj">
								<el-select v-model="xglist.gwqdyj" placeholder="请选择岗位确定依据" style="width: 100%;">
									<el-option v-for="item in gwqdyjxz" :label="item.gwqdyjmc" :value="item.gwqdyjmc"
										:key="item.gwqdyjid"></el-option>
								</el-select>
							</el-form-item>
							<el-form-item label="最高学历" prop="zgxl">
								<el-select v-model="xglist.zgxl" placeholder="请选择最高学历" style="width: 100%;">
									<el-option v-for="item in zgxlxz" :label="item.zgxlmc" :value="item.zgxlmc"
										:key="item.zgxlid">
									</el-option>
								</el-select>
							</el-form-item>
						</div>
						<div style="display:flex">
							<el-form-item label="职务">
								<el-autocomplete class="inline-input" value-key="zw" v-model.trim="xglist.zw"
									style="width:100%" :fetch-suggestions="querySearchzw" placeholder="请输入职务名称">
								</el-autocomplete>
							</el-form-item>
							<el-form-item label="职级">
								<el-autocomplete class="inline-input" value-key="zj" v-model.trim="xglist.zj"
									style="width:100%" :fetch-suggestions="querySearchzj" placeholder="请输入职级名称">
								</el-autocomplete>
							</el-form-item>
						</div>
						<div style="display:flex">
							<el-form-item label="级别职称">
								<el-select v-model="xglist.zc" placeholder="请选择级别职称" style="width:calc(100% - 20px)">
									<el-option v-for="item in jbzcxz" :label="item.jbzcmc" :value="item.jbzcmc"
										:key="item.jbzcid">
									</el-option>
								</el-select>
								<el-popover placement="right" width="200" trigger="hover">
									<div>
										<div style="display:flex;margin-bottom:10px">
											<i class="el-icon-info"
												style="color:#409eef;position: relative;top: 2px;"></i>
											<div class="tszt">提示</div>
										</div>
										<div class="smzt">
											（1）机关、参公事业单位、事业单位、国有企业管理岗位人员填写行政级别，即省部级、厅局级、县处级、乡科级及以下、试用期；事业单位、国有企业技术岗位人员填写职称，即高级（含正高、副高）、中级、初级及以下、试用期；机关、参公事业单位、事业单位工勤人员填写工勤人员；企业人员无行政级别的，填写企业职员。（2）既有职务、又有职级的，按照对应级别较高的填写；既有级别、又有职称的，机关按照级别填写，事业单位、国有企业按照职称填写。个别机关、单位人员难以明确对应的，根据干部管理权限尽量准确。
										</div>
									</div>
									<i class="el-icon-info"
										style="color:#409eef;position: absolute; right: 10px;top: 20px;"
										slot="reference"></i>
								</el-popover>
							</el-form-item>
							<el-form-item label="身份类型" prop="sflx">
								<el-select v-model="xglist.sflx" placeholder="请选择身份类型" style="width:calc(100% - 20px)">
									<el-option v-for="item in sflxxz" :label="item.sflxmc" :value="item.sflxmc"
										:key="item.sflxid">
									</el-option>
								</el-select>
								<el-popover placement="right" width="200" trigger="hover">
									<div>
										<div style="display:flex;margin-bottom:10px">
											<i class="el-icon-info"
												style="color:#409eef;position: relative;top: 2px;"></i>
											<div class="tszt">提示</div>
										</div>
										<div class="smzt">
											根据涉密人员所在单位类型和人员类型对应填写，从下拉栏中选择：公务员、参公人员、事业人员、企业人员、其他。
										</div>
									</div>
									<i class="el-icon-info"
										style="color:#409eef;position: absolute; right: 10px;top: 20px;"
										slot="reference"></i>

								</el-popover>
							</el-form-item>
						</div>
						<div style="display:flex">
							<el-form-item label="用人形式" prop="yrxs">
								<el-select v-model="xglist.yrxs" placeholder="请选择用人形式" style="width: 100%;">
									<el-option v-for="item in yrxxxz" :label="item.yrxxmc" :value="item.yrxxmc"
										:key="item.yrxxid">
									</el-option>
								</el-select>
							</el-form-item>
							<el-form-item label="是否审查" prop="sfsc">
								<el-radio-group v-model="xglist.sfsc">
									<el-radio label="是"></el-radio>
									<el-radio label="否"></el-radio>
								</el-radio-group>
							</el-form-item>
						</div>
						<div style="display:flex">
							<el-form-item label="出入境登记备案" prop="crjdjba">
								<el-radio-group v-model="xglist.crjdjba">
									<el-radio label="是"></el-radio>
									<el-radio label="否"></el-radio>
								</el-radio-group>
							</el-form-item>
							<el-form-item label="统一保管出入境证件" prop="tybgcrjzj">
								<el-radio-group v-model="xglist.tybgcrjzj">
									<el-radio label="是"></el-radio>
									<el-radio label="否"></el-radio>
								</el-radio-group>
							</el-form-item>
						</div>
						<div style="display:flex">
							<el-form-item label="上岗时间" prop="sgsj" class="one-line">
								<!-- <el-input v-model="xglist.sgsj" clearable></el-input> -->
								<el-date-picker v-model="xglist.sgsj" style="width:100%;" clearable type="date"
									placeholder="选择日期" format="yyyy年MM月dd日" value-format="yyyy年MM月dd日">
								</el-date-picker>
							</el-form-item>
						</div>
						<el-form-item label="备注" prop="bz" class="one-line-textarea">
							<el-input type="textarea" v-model="xglist.bz"></el-input>
						</el-form-item>
					</el-form>
					<span slot="footer" class="dialog-footer">
						<el-button type="warning" @click="xqdialogVisible = false">关 闭</el-button>
					</span>
				</el-dialog>
			</div>
		</div>
	</div>

</template>
<script>
	import {
		getsmdj,
		getgwqdyj,
		getjbzc,
		getzgxl,
		getsflx,
		getyrxx
	} from "../../../db/xzdb"
	import {
		getsmry
	} from "../../../db/lstzdb";
	import {
		//涉密人员管理初始化成员列表
		// getsmry,
		//添加涉密人员管理
		addsmry,
		//删除涉密人员管理
		deletesmry,
		//修改涉密人员管理
		updatesmry,
		getZw,
		getZj,
		jxsfzhm
	} from "../../../db/smrydb";
	import {
		getlogin
	} from "../../../db/loginyhdb";
	import {
		//涉密人员管理初始化成员列表
		getsmgw,
		getsmgw1,
		getsmgw2,
		getbmmc
	} from "../../../db/smgwgldb";
	import {
		exportExcel
	} from "../../../utils/exportExcel"; //excel导出工具
	import {
		dateFormatNYRChinese
	} from "../../../utils/moment"

	import {
		getUuid
	} from "../../../utils/getUuid"; //获取uuid
	import XLSX from "xlsx";

	export default {
		components: {},
		props: {},
		data() {
			var isMobileNumber = (rule, value, callback) => {
				if (!value) {
					return new Error('请输入电话号码')
				} else {
					const reg =
						/^((13[0-9])|(14[5-9])|(15([0-3]|[5-9]))|(16[6-7])|(17[1-8])|(18[0-9])|(19[1|3])|(19[5|6])|(19[8|9]))\d{8}$/
					const isPhone = reg.test(value)
					value = Number(value) //转换为数字
					if (typeof value === 'number' && !isNaN(value)) {
						//判断是否为数字
						value = value.toString() //转换成字符串
						if (value.length < 0 || value.length > 12 || !isPhone) {
							//判断是否为11位手机号
							callback(new Error('手机号格式:138xxxx8754'))
						} else {
							callback()
						}
					} else {
						callback(new Error('请输入电话号码'))
					}
				}
			}
			const isCnNewID = (rule, value, callback) => {
				var arrExp = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]; //加权因子
				var arrValid = [1, 0, "X", 9, 8, 7, 6, 5, 4, 3, 2]; //校验码
				if (/^\d{17}\d|x$/i.test(value)) {
					var sum = 0,
						idx;
					for (var i = 0; i < value.length - 1; i++) {
						// 对前17位数字与权值乘积求和
						sum += parseInt(value.substr(i, 1), 10) * arrExp[i];
					}
					// 计算模（固定算法）
					idx = sum % 11;
					// 检验第18为是否与校验码相等
					if (arrValid[idx] == value.substr(17, 1).toUpperCase()) {
						callback()
						if (this.tjlist.sfzhm) {
							var org_birthday = this.tjlist.sfzhm.substring(6, 14);
							var org_gender = this.tjlist.sfzhm.substring(16, 17);
							var sex = org_gender % 2 == 1 ? "男" : "女";
							var birthday =
								org_birthday.substring(0, 4) +
								"-" +
								org_birthday.substring(4, 6) +
								"-" +
								org_birthday.substring(6, 8);
							var birthdays = new Date(birthday.replace(/-/g, "/"));
							let d = new Date();
							let age =
								d.getFullYear() -
								birthdays.getFullYear() -
								(d.getMonth() < birthdays.getMonth() ||
									(d.getMonth() == birthdays.getMonth() &&
										d.getDate() < birthdays.getDate()) ?
									1 :
									0);
							this.tjlist.xb = sex;
							// this.form.birthday = birthdays;
							this.tjlist.nl = age;
						}
						if (this.xglist.sfzhm) {
							var org_birthday = this.xglist.sfzhm.substring(6, 14);
							var org_gender = this.xglist.sfzhm.substring(16, 17);
							var sex = org_gender % 2 == 1 ? "男" : "女";
							var birthday =
								org_birthday.substring(0, 4) +
								"-" +
								org_birthday.substring(4, 6) +
								"-" +
								org_birthday.substring(6, 8);
							var birthdays = new Date(birthday.replace(/-/g, "/"));
							let d = new Date();
							let age =
								d.getFullYear() -
								birthdays.getFullYear() -
								(d.getMonth() < birthdays.getMonth() ||
									(d.getMonth() == birthdays.getMonth() &&
										d.getDate() < birthdays.getDate()) ?
									1 :
									0);
							this.xglist.xb = sex;
							// this.form.birthday = birthdays;
							this.xglist.nl = age;
						}
					} else {
						callback("身份证格式有误")
					}
				} else {
					callback("身份证格式有误")
				}
			}
			return {
				sfzhm: '',
				yearSelect: [],
				pdmsfzhm: 0,
				smdjxz: [],
				gwqdyjxz: [],
				tableDataCopy: [],
				jbzcxz: [],
				zgxlxz: [],
				sflxxz: [],
				yrxxxz: [],
				gwmc: [],
				labelPosition: 'right',
				smryList: [],
				formInline: {
					
				},
				tjlist: {
					xm: '',
					sfzhm: '',
					xb: '',
					nl: '',
					lxdh: '',
					bm: '',
					gwmc: '',
					smdj: '',
					gwqdyj: '',
					zgxl: '',
					zw: '',
					zj: '',
					zc: '',
					gwdyjb: '',
					sflx: '',
					yrxs: '',
					sfsc: '是',
					crjdjba: '是',
					tybgcrjzj: '是',
					sgsj: '',
					bz: '',
				},
				xglist: {},
				updateItemOld: {},
				xgdialogVisible: false,
				xqdialogVisible: false,
				//导入
				dialogVisible_dr: false, //导入成员组弹窗状态
				dr_cyz_list: [], //待选择导入成员组列表
				multipleTable: [], //已选择导入成员组列表
				page: 1,
				pageSize: 10,
				total: 0,
				selectlistRow: [], //列表的值
				dialogVisible: false, //添加弹窗状态
				//表单验证
				rules: {
					xm: [{
						required: true,
						message: '请输入姓名',
						trigger: 'blur'
					}, ],
					sfzhm: [{
							required: true,
							message: "身份证不能为空",
							trigger: "blur"
						},
						{ //调用上面定义的方法校验格式是否正确
							validator: isCnNewID,
							trigger: "blur"
						}
					],
					xb: [{
						required: true,
						message: '请选择性别',
						trigger: 'blur'
					}, ],
					nl: [{
						required: true,
						message: '请输入年龄',
						trigger: 'blur'
					}, ],
					bm: [{
						required: true,
						message: '请输入部门',
						trigger: ['blur', 'change'],
					}, ],
					gwmc: [{
						required: true,
						message: '请输入岗位名称',
						trigger: ['blur', 'change'],
					}, ],
					smdj: [{
						required: true,
						message: '请选择涉密等级',
						trigger: 'blur'
					}, ],
					gwqdyj: [{
						required: true,
						message: '请选择岗位确定依据',
						trigger: 'blur'
					}, ],
					zgxl: [{
						required: true,
						message: '请选择最高学历',
						trigger: 'blur'
					}, ],
					zw: [{
						required: true,
						message: '请输入职务',
						trigger: 'blur'
					}, ],
					zj: [{
						required: true,
						message: '请输入职级',
						trigger: 'blur'
					}, ],
					zc: [{
						required: true,
						message: '请选择级别职称',
						trigger: 'blur'
					}, ],
					// gwdyjb: [{
					// 	required: true,
					// 	message: '请选择岗位对应级别',
					// 	trigger: 'blur'
					// },],
					sflx: [{
						required: true,
						message: '请选择身份类型',
						trigger: 'blur'
					}, ],
					yrxs: [{
						required: true,
						message: '请选择用人形式',
						trigger: 'blur'
					}, ],
					sfsc: [{
						required: true,
						message: '请选择是否审查',
						trigger: 'blur'
					}, ],
					crjdjba: [{
						required: true,
						message: '请选择是否出入境登记备案',
						trigger: 'blur'
					}, ],
					tybgcrjzj: [{
						required: true,
						message: '请选择是否统一保管出入境证件',
						trigger: 'blur'
					}, ],
					sgsj: [{
						required: true,
						message: '请选择上岗时间（现涉密岗位）',
						trigger: 'blur'
					}, ],
					lxdh: [{
						required: true,
						message: '请输入联系电话',
						trigger: 'blur'
					}, {
						validator: isMobileNumber,
						trigger: 'blur'
					}],
					// bz: [{
					// 	required: true,
					// 	message: '请输入文件名',
					// 	trigger: 'blur'
					// },],
				},
				regionOption: [], //地域信息
				regionParams: {
					label: 'label', //这里可以配置你们后端返回的属性
					value: 'label',
					children: 'childrenRegionVo',
					expandTrigger: 'click',
					checkStrictly: true,
				}, //地域信息配置参数
				dwmc: '',
				year: '',
				yue: '',
				ri: '',
				Date: '',
				xh: [],
				dclist: [],
				dr_dialog: false,
				//数据导入方式
				sjdrfs: ''
			}
		},
		computed: {},
		mounted() {
			this.smry()
			this.dwmc = getlogin()[0].dwmc
			let date = new Date()
			this.year = date.getFullYear() + '年'; //获取完整的年份(4位)
			this.yue = (date.getMonth() + 1) + '月'; //获取当前月份(0-11,0代表1月)
			this.ri = date.getDate() + '日'; //获取当前日(1-31)
			this.Date = this.year + this.yue + this.ri
			this.tjlist.sgsj = this.Date
			this.zwmh()
			this.zjmh()
			this.smdjxz = getsmdj()
			console.log(this.smdjxz);
			this.gwqdyjxz = getgwqdyj()
			this.jbzcxz = getjbzc()
			this.zgxlxz = getzgxl()
			this.sflxxz = getsflx()
			this.yrxxxz = getyrxx()
			//获取最近十年的年份
			let yearArr = []
			for (let i = new Date().getFullYear(); i > new Date().getFullYear() - 10; i--) {
				yearArr.push(
					{
						label: i.toString(),
						value: i.toString()
					})
			}
			yearArr.unshift({
				label: "全部",
				value: ""
			})
			this.yearSelect = yearArr
			//列表初始化
			this.smbm()
			let bmmc = getbmmc()
			let shu = []
			// console.log(bmmc);
			bmmc.forEach(item => {
				let childrenRegionVo = []
				bmmc.forEach(item1 => {
					if (item.bmm == item1.fbmm) {
						// console.log(item, item1);
						childrenRegionVo.push(item1)
						// console.log(childrenRegionVo);
						item.childrenRegionVo = childrenRegionVo
					}
				});
				// console.log(item);
				shu.push(item)
			})
			console.log(shu[0]);
			if (shu[0].childrenRegionVo) {
      shu[0].childrenRegionVo.forEach(item => {
      this.regionOption.push(item)
    })
    }
			// this.$refs.upload.addEventListener('change', e => { //绑定监听表格导入事件
			// 	this.readExcel(e);
			// })
			this.morenzhi()
		},
		methods: {
			Radio(val) {
				this.sjdrfs = val
				console.log("当前选中的值", val)
			},

			mbxzgb() {
				this.sjdrfs = ''
			},
			mbdc() {
				console.log("----导出涉密人员----")
				// console.log(this.selectlistRow);
				// if (this.selectlistRow.length > 0) {
				let filename = "涉密人员汇总情况模板" + getUuid() + ".xlsx"

				const {
					dialog
				} = require('electron').remote;
				//弹窗title
				let options = {
					title: "保存文件",
					defaultPath: filename,
				};
				console.log(dialog)
				//导出文件夹选择弹窗
				dialog.showSaveDialog(options, result => {
					console.log('result', result)
					if (result == null || result == "") {
						console.log("取消导出")
						return
					}
					let list = []

					//对导出的数据进行格式，js导出excel要求传入的参数格式为[["列1名称","列2名称","列3名称"],["列11","值21","值31"],["列12","值22","值32"]]
					list.push(["序号", "姓名", "性别", "年龄", "身份证号", "部门", "岗位名称",
						"涉密等级", "最高学历", "级别职称", "职务", "职级", "身份类型", "用人形式",
						"是否审查", "是否出入境登记备案", "是否统一保管出入境证件", "上岗时间", "备注"
					]) //确定列名

					exportExcel(result, list) //list 要求为二维数组
					this.$message('导出成功:' + result)
					this.dr_dialog = false
				})
			},
			morenzhi() {
				let dwxx = getlogin()[0]
				console.log(dwxx);
				if (dwxx.dwlx == 5 || dwxx.dwlx == 6 || dwxx.dwlx == 7) {
					console.log(1);
					this.tjlist.zc = '企业职员'
					this.tjlist.sflx = '企业人员'
					this.tjlist.yrxs = '聘用'
				} else if (dwxx.dwlx == 8) {
					this.tjlist.zc = '其他'
					this.tjlist.sflx = '其他'
					this.tjlist.yrxs = '聘用'
				} else if (dwxx.dwlx == 1 && dwxx.sscc == 1) {
					this.tjlist.zc = '省部级'
					this.tjlist.sflx = '公务员'
					this.tjlist.yrxs = '在编'
				} else if (dwxx.dwlx == 1 && dwxx.sscc == 3 || dwxx.sscc == 9 || dwxx.sscc ==
					5) {
					this.tjlist.zc = '厅局级'
					this.tjlist.sflx = '公务员'
					this.tjlist.yrxs = '在编'
				} else if (dwxx.dwlx == 1 && dwxx.sscc == 7 || dwxx.sscc == 11) {
					this.tjlist.zc = '县处级'
					this.tjlist.sflx = '公务员'
					this.tjlist.yrxs = '在编'
				} else if (dwxx.dwlx == 3) {
					this.tjlist.zc = '高级'
					this.tjlist.sflx = '参公人员'
					this.tjlist.yrxs = '在编'
				} else if (dwxx.dwlx == 4 || dwxx.dwlx == 2) {
					this.tjlist.zc = '高级'
					this.tjlist.sflx = '事业人员'
					this.tjlist.yrxs = '在编'
				}
			},
			xz() {
				this.dialogVisible = true
				this.smry()
				this.smbm()
				this.morenzhi()
			},
			//导入
			chooseFile() {
				if (this.sjdrfs != '') {
					if (this.sjdrfs == 1) {
						this.$refs.upload.click()
					} else if (this.sjdrfs == 2) {
						this.$refs.upload.click()
						let valArr = this.dclist
						valArr.forEach(function (item) {
							deletesmry(item)
						})
					}
				} else {
					this.$message.warning('请选择导入方式')
				}
			},
			//----成员组选择
			handleSelectionChange(val) {
				this.multipleTable = val
				console.log("选中：", this.multipleTable);
			},
			//---确定导入成员组
			drcy() {
				//遍历已选择导入的成员，进行格式化，然后添加到数据库
				for (var i in this.multipleTable) {
					var cy = {
						xm: this.multipleTable[i]["姓名"],
						xb: this.multipleTable[i]["性别"],
						nl: this.multipleTable[i]["年龄"],
						sfzhm: this.multipleTable[i]["身份证号"],
						bm: this.multipleTable[i]["部门"],
						gwmc: this.multipleTable[i]["岗位名称"],
						smdj: this.multipleTable[i]["涉密等级"],
						zgxl: this.multipleTable[i]["最高学历"],
						zc: this.multipleTable[i]["级别职称"],
						zw: this.multipleTable[i]["职务"],
						zj: this.multipleTable[i]["职级"],
						sflx: this.multipleTable[i]["身份类型"],
						yrxs: this.multipleTable[i]["用人形式"],
						sfsc: this.multipleTable[i]["是否审查"],
						crjdjba: this.multipleTable[i]["是否出入境登记备案"],
						tybgcrjzj: this.multipleTable[i]["是否统一保管出入境证件"],
						sgsj: dateFormatNYRChinese(this.multipleTable[i]["上岗时间"]),
						bz: this.multipleTable[i]["备注"],
					}
					addsmry(cy)
				}
				this.dialogVisible_dr = false
				this.smry()
			},
			//----表格导入方法
			readExcel(e) {
				var that = this;
				const files = e.target.files;
				console.log("files", files);
				var vali = /\.(xls|xlsx)$/
				if (files.length <= 0) { //如果没有文件名
					return false;
				} else if (!vali.test(files[0].name.toLowerCase())) {
					this.$Message.error('上传格式不正确，请上传xls或者xlsx格式');
					return false;
				}
				const fileReader = new FileReader();
				fileReader.onload = (e) => {
					try {
						const data = e.target.result;
						const workdata = XLSX.read(data, {
							type: 'binary',
							cellDates: true, //设为true，将天数的时间戳转为时间格式
						});
						console.log("文件的内容：", workdata) // 文件的内容
						//查看打印能看到 workdata的sheetNames里是文件中每个表的有值的集合；
						const wsname = workdata.SheetNames[0]; //取第一张表
						console.log('wsname', wsname)
						const ws = XLSX.utils.sheet_to_json(workdata.Sheets[wsname]); //生成json表格内容

						console.log(ws); //自第二行开始的内容
						this.dialogVisible_dr = true
						this.dr_cyz_list = ws
						console.log("列表的值:", this.dr_cyz_list)
						// 加工excel读取业务类型为数组
						// this.dr_cyz_list.forEach(function(item) {
						// 	console.log(item[0]['业务类型'].splite(','))
						// })
						this.$refs.upload.value = ''; // 处理完成 清空表单值
						this.dr_dialog = false
					} catch (e) {
						return false;
					}
				};
				fileReader.readAsBinaryString(files[0]);
			},
			//查询
			onSubmit() {
				// 备份了一下数据
				let arr = this.tableDataCopy
				// 通过遍历key值来循环处理
				Object.keys(this.formInline).forEach((e, label) => {
					// 调用自己定义好的筛选方法
					if (typeof (this.formInline[e]) == 'object') {
						if (this.formInline[e] == null || this.formInline[e].length == 0) {
							arr = this.filterFunc(this.formInline[e], e, arr)
							return
						}
						if (!(isNaN(this.formInline[e]) && !isNaN(Date.parse(this.formInline[e])))) {
							this.formInline[e] = this.formInline[e].join('/')
							arr = this.filterFunc(this.formInline[e], e, arr)
							this.formInline[e] = this.formInline[e].split('/')
						} else {
							arr = this.filterFunc(this.formInline[e], e, arr)
						}
					} else {
						arr = this.filterFunc(this.formInline[e], e, arr)
					}
				})
				// 为表格赋值
				this.smryList = arr
				// this.smry()
			},
			filterFunc(val, target, filterArr) {
				// 参数不存在或为空时，就相当于查询全部
				if (val == undefined || val == '') {
					return filterArr
				}
				return filterArr.filter(p => {
					return p[target].indexOf(val.toString().replace(',', '/')) > -1
					// return bool
				}) // 可以自己加一个.toLowerCase()来兼容一下大小
			},
			returnSy() {
				this.$router.push("/tzglsy");
			},
			smry() {
				let params = {
					page: this.page,
					pageSize: this.pageSize
				}
				Object.assign(params, this.formInline)
				let resList = getsmry(params)
				console.log("params", params);

				this.smryList = resList.list
				this.tableDataCopy = resList.list
				this.dclist = resList.list_total
				if (resList.list_total.length != 0) {
					this.tjlist.bm = resList.list_total[resList.list_total.length - 1].bm.split('/')
					this.tjlist.gwmc = resList.list_total[resList.list_total.length - 1].gwmc
					this.tjlist.smdj = resList.list_total[resList.list_total.length - 1].smdj
					this.tjlist.gwqdyj = resList.list_total[resList.list_total.length - 1].gwqdyj
					this.tjlist.zgxl = resList.list_total[resList.list_total.length - 1].zgxl
					this.tjlist.zw = resList.list_total[resList.list_total.length - 1].zw
					this.tjlist.zj = resList.list_total[resList.list_total.length - 1].zj
					this.tjlist.zc = resList.list_total[resList.list_total.length - 1].zc
					this.tjlist.sflx = resList.list_total[resList.list_total.length - 1].sflx
					this.tjlist.yrxs = resList.list_total[resList.list_total.length - 1].yrxs
					this.gwmc = getsmgw(this.tjlist.bm.join('/'))
				}
				this.dclist.forEach((item, label) => {
					this.xh.push(label + 1)
				})
				this.total = resList.total
			},
			//删除
			shanchu(id) {
				this.$confirm('是否继续删除?', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(() => {
					let valArr = this.selectlistRow
					// console.log("....", val);
					valArr.forEach(function (item) {
						deletesmry(item)
						console.log("删除：", item);
						console.log("删除：", item);
					})
					let params = valArr
					this.$message({
						message: '删除成功',
						type: 'success'
					});
					this.smry()
				}).catch(() => {
					this.$message('已取消删除')
				})
			},
			//添加
			showDialog() {
				this.dialogVisible = true
			},
			//添加重置
			resetForm() {
				this.tjlist.xm = '',
					this.tjlist.sfzhm = '',
					this.tjlist.xb = '',
					this.tjlist.nl = '',
					this.tjlist.lxdh = '',
					this.tjlist.bm = '',
					this.tjlist.gwmc = '',
					this.tjlist.smdj = '',
					this.tjlist.gwqdyj = '',
					this.tjlist.zgxl = '',
					this.tjlist.zw = '',
					this.tjlist.zj = '',
					this.tjlist.zc = '',
					// this.tjlist.gwdyjb = '',
					this.tjlist.sflx = '',
					this.tjlist.yrxs = '',
					this.tjlist.sfsc = '是',
					this.tjlist.crjdjba = '是',
					this.tjlist.tybgcrjzj = '是',
					this.tjlist.sgsj = this.Date,
					this.tjlist.bz = ''
			},
			//确定添加成员组
			submitTj(formName) {
				this.$refs[formName].validate((valid) => {
					if (valid) {
						let params = {
							xm: this.tjlist.xm,
							sfzhm: this.tjlist.sfzhm,
							xb: this.tjlist.xb,
							nl: this.tjlist.nl,
							lxdh: this.tjlist.lxdh,
							bm: this.tjlist.bm.join('/'),
							gwmc: this.tjlist.gwmc,
							smdj: this.tjlist.smdj,
							gwqdyj: this.tjlist.gwqdyj,
							zgxl: this.tjlist.zgxl,
							zw: this.tjlist.zw,
							zj: this.tjlist.zj,
							zc: this.tjlist.zc,
							// gwdyjb: this.tjlist.gwdyjb,
							sflx: this.tjlist.sflx,
							yrxs: this.tjlist.yrxs,
							sfsc: this.tjlist.sfsc,
							tybgcrjzj: this.tjlist.tybgcrjzj,
							crjdjba: this.tjlist.crjdjba,
							sgsj: this.tjlist.sgsj,
							bz: this.tjlist.bz,
							// smryid :getUuid()
						}
						this.onInputBlur(1)
						if (this.pdmsfzhm == 0) {
							addsmry(params)
							this.dialogVisible = false
							this.$message({
								message: '添加成功',
								type: 'success'
							});
							this.resetForm()
							this.smry()
							this.zwmh()
							this.zjmh()
							this.morenzhi()
						}


					} else {
						console.log('error submit!!');
						return false;
					}
				});

			},

			//修改
			updataDialog(form) {
				this.$refs[form].validate((valid) => {
					if (valid) {
						// 删除旧的
						// deletesmry(this.updateItemOld)
						// 插入新的
						if (this.pdmsfzhm == 0) {
							updatesmry(this.xglist)
							// 刷新页面表格数据
							this.smry()
							this.zwmh()
							this.zjmh()
							// 关闭dialog
							this.$message.success('修改成功')
							this.xgdialogVisible = false
						} else {
							this.$message.error('身份证号码已存在')
						}

					} else {
						console.log('error submit!!');
						return false;
					}
				});

			},
			xqyl(row) {
				this.updateItemOld = JSON.parse(JSON.stringify(row))

				this.xglist = JSON.parse(JSON.stringify(row))
				// this.form1.ywlx = row.ywlx
				console.log('old', row)
				console.log("this.xglist.ywlx", this.xglist);

				this.xglist.bm = this.xglist.bm.split('/')
				this.xqdialogVisible = true
			},

			updateItem(row) {
				this.smbm()
				this.updateItemOld = JSON.parse(JSON.stringify(row))
				this.xglist = JSON.parse(JSON.stringify(row))
				this.sfzhm = this.xglist.sfzhm
				// this.form1.ywlx = row.ywlx
				console.log('old', row)
				console.log("this.xglist.ywlx", this.xglist);
				let resList = getsmgw(this.xglist.bm)
				this.restaurants = resList;
				this.xglist.bm = this.xglist.bm.split('/')
				this.xgdialogVisible = true
			},

			deleteTkglBtn(id) {
				if (this.selectlistRow != '') {
					this.$confirm('是否继续删除?', '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					}).then(() => {
						let valArr = this.selectlistRow
						// console.log("....", val);
						valArr.forEach(function (item) {
							deletesmry(item)
							console.log("删除：", item);
							console.log("删除：", item);
						})
						let params = valArr
						this.$message({
							message: '删除成功',
							type: 'success'
						});
						this.smry()
					}).catch(() => {
						this.$message('已取消删除')
					})
				} else {
					this.$message({
						message: '未选择删除记录，请选择下列列表',
						type: 'warning'
					});
				}

			},
			selectRow(val) {
				console.log(val);
				this.selectlistRow = val;
			},
			//列表分页--跳转页数
			handleCurrentChange(val) {
				this.page = val
				this.smry()
			},
			//列表分页--更改每页显示个数
			handleSizeChange(val) {
				this.page = 1
				this.pageSize = val
				this.smry()
			},

			handleClose(done) {
				// this.resetForm()
				this.dialogVisible = false

			},
			// 弹框关闭触发
			close(formName) {
				// 清空表单校验，避免再次进来会出现上次校验的记录
				this.$refs[formName].resetFields();
			},
			close1(form) {
				// 清空表单校验，避免再次进来会出现上次校验的记录
				this.$refs[form].resetFields();
			},

			//导出
			exportList() {
				let filename = "涉密人员历史台账汇总情况表" + getUuid() + ".xlsx"

				const {
					dialog
				} = require('electron').remote;
				//弹窗title
				let options = {
					title: "保存文件",
					defaultPath: filename,
				};
				console.log(dialog)
				//导出文件夹选择弹窗
				dialog.showSaveDialog(options, result => {
					console.log('result', result)
					if (result == null || result == "") {
						console.log("取消导出")
						return
					}
					let list = []
					list.push(["涉密人员统计情况"])

					list.push(["上报单位:", this.dwmc, "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""])
					list.push(["统计年度:", this.year, "", "", "", "", "", "", "", "",
						"", "", "", "", "", "", "", "", "填报时间:", this.Date
					])
					//对导出的数据进行格式，js导出excel要求传入的参数格式为[["列1名称","列2名称","列3名称"],["列11","值21","值31"],["列12","值22","值32"]]
					list.push(["序号", "台账时间", "姓名", "性别", "年龄", "身份证号", "部门", "岗位名称",
						"涉密等级", "最高学历", "级别职称", "职务", "职级", "身份类型", "用人形式",
						"是否审查", "是否出入境登记备案", "是否统一保管出入境证件", "上岗时间", "备注"
					]) //确定列名

					for (var i in this.dclist) { //每一行的值
						let item = this.dclist[i] //{"name":"XXX","dw":"XXX","zw":"XXX"}

						console.log("导出值:", this.dclist);
						let column = [(parseInt(i) + 1), item["tzsj"], item["xm"], item["xb"], item["nl"], item["sfzhm"],
							item["bm"], item["gwmc"], item["smdj"], item["zgxl"], item["zc"],
							item["zw"], item["zj"], item["sflx"], item["yrxs"],
							item["sfsc"], item["crjdjba"], item["tybgcrjzj"], item["sgsj"], item["bz"]
						]
						list.push(column)
					}

					let merges = [{
						s: { //s为开始
							c: 0, //开始列
							r: 0 //开始取值范围
						},
						e: { //e结束
							c: 19, //结束列
							r: 0 //结束范围
						}
					}]
					let styles = {
						// 列样式
						cols: {
							// 作用sheet页索引（0开始）（-1全sheet页生效）
							scoped: -1,
							style: [{
									wpx: 100
								},
								{
									wpx: 100
								},
								{
									wpx: 100
								},
								{
									wpx: 100
								},
								{
									wpx: 100
								},
								{
									wpx: 100
								},
								{
									wpx: 100
								},
								{
									wpx: 100
								},
								{
									wpx: 100
								},
								{
									wpx: 100
								},
								{
									wpx: 100
								},
								{
									wpx: 100
								},
								{
									wpx: 100
								},
								{
									wpx: 100
								},
								{
									wpx: 100
								},
								{
									wpx: 100
								},
								{
									wpx: 100
								},
								{
									wpx: 100
								},
								{
									wpx: 100
								},
								{
									wpx: 120
								},
							]
						},
						// 全局样式
						all: {
							alignment: {
								horizontal: 'center', // 水平居中
								vertical: 'center' // 垂直居中
							},
							font: {
								sz: 11, // 字号
								name: '宋体' // 字体
							},
							border: { // 边框
								top: {
									style: 'thin'
								},
								bottom: {
									style: 'thin'
								},
								left: {
									style: 'thin'
								},
								right: {
									style: 'thin'
								}
							}
						},
						// 单元格样式
						cell: [{
							// 生效sheet页索引（值为 -1 时所有sheet页都生效）
							scoped: -1,
							// 索引
							index: 'A1',
							style: {
								font: {
									name: '宋体',
									sz: 16, // 字号
									bold: true,
								},
								alignment: {
									horizontal: 'center', // 水平居中
									vertical: 'center' // 垂直居中
								}
							}
						}]
					}
					exportExcel(result, list, merges, styles) //list 要求为二维数组
					this.$message('导出成功:' + result)
				})
			},
			querySearch1(queryString, cb) {
				var restaurants = this.restaurantsbm;
				console.log("restaurants", restaurants);
				var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
				console.log("results", results);
				let results1
				for (var i = 0; i < results.length; i++) {
					for (var j = i + 1; j < results.length; j++) {
						if (results[i].bm === results[j].bm) {
							results.splice(j, 1);
							j--;
						}
					}
				}
				// 调用 callback 返回建议列表的数据
				cb(results);
				console.log("cb(results.dwmc)", results);
			},
			querySearch(queryString, cb) {
				var restaurants = this.restaurants;
				console.log("restaurants", restaurants);
				var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
				console.log("results", results);
				// 调用 callback 返回建议列表的数据
				cb(results);
				console.log("cb(results.dwmc)", results);
			},
			createFilter(queryString) {
				return (restaurant) => {
					return (restaurant.gwmc.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
				};
			},
			smbm() {
				let resList = getsmgw1()
				this.restaurantsbm = resList;
				console.log("this.restaurants", this.restaurantsbm);
				console.log(resList)
			},
			handleSelect(item) {
				let dx = []
				let hx = []
				let zy = []
				let yb = []
				item.forEach(item => {
					this.gwmc.forEach(item1 => {
						if (item == item1.gwmc) {
							dx.push(item1)
						}
					})
				})
				console.log(dx);
				dx.forEach(item=>{
					if(item.smdj == '核心'){
						hx.push(item)
					}else if(item.smdj == '重要'){
						zy.push(item)
					}else{
						yb.push(item)
					}
				})
				console.log(hx);
				console.log(zy);
				console.log(yb);
				if (hx.length>0) {
					this.tjlist.smdj = hx[0].smdj
					this.tjlist.gwqdyj = hx[0].gwqdyj
				}else if(zy.length>0){
					this.tjlist.smdj = zy[0].smdj
					this.tjlist.gwqdyj = zy[0].gwqdyj
				}else if(yb.length>0){
					this.tjlist.smdj = yb[0].smdj
					this.tjlist.gwqdyj = yb[0].gwqdyj
				}
				// var res3 = dx.find(item => {
				// 	if (condition) {
						
				// 	}
				// })
				// console.log(res3);
				// console.log(this.gwmc);
				// this.gwmc.forEach(item1 => {
				// 	if (item == item1.gwmc) {
				// 		this.tjlist.smdj = item1.smdj
				// 		this.tjlist.gwqdyj = item1.gwqdyj
				// 	}
				// })
				// 不能用

				// this.dwxxShow = JSON.parse(JSON.stringify(item))
				// console.log("this.dwxxShow",this.dwxxShow);
				// console.log("item", item);
			},
			handleSelect1(item) {
				// console.log(item);
				// 不能用
				let dx = []
				let hx = []
				let zy = []
				let yb = []
				item.forEach(item => {
					this.gwmc.forEach(item1 => {
						if (item == item1.gwmc) {
							dx.push(item1)
						}
					})
				})
				console.log(dx);
				dx.forEach(item=>{
					if(item.smdj == '核心'){
						hx.push(item)
					}else if(item.smdj == '重要'){
						zy.push(item)
					}else{
						yb.push(item)
					}
				})
				console.log(hx);
				console.log(zy);
				console.log(yb);
				if (hx.length>0) {
					this.xglist.smdj = hx[0].smdj
					this.xglist.gwqdyj = hx[0].gwqdyj
				}else if(zy.length>0){
					this.xglist.smdj = zy[0].smdj
					this.xglist.gwqdyj = zy[0].gwqdyj
				}else if(yb.length>0){
					this.xglist.smdj = yb[0].smdj
					this.xglist.gwqdyj = yb[0].gwqdyj
				}

				// this.dwxxShow = JSON.parse(JSON.stringify(item))
				// console.log("this.dwxxShow",this.dwxxShow);
				console.log("item", item);
			},
			handleChange(index) {
				let resList
				if (index == 1) {
					resList = getsmgw(this.tjlist.bm.join('/'))
				} else if (index == 2)(
					resList = getsmgw(this.xglist.bm.join('/'))
				)
				this.restaurants = resList;
				this.gwmc = resList
				if (this.gwmc.length == 0) {
					this.$message.error('该部门没有添加岗位');
				}
				console.log(this.gwmc);
				this.tjlist.gwmc = ''
				this.tjlist.smdj = ''
				this.tjlist.gwqdyj = ''
				this.xglist.gwmc = ''
				this.xglist.smdj = ''
				this.xglist.gwqdyj = ''
			},


			//模糊匹配职务
			querySearchzw(queryString, cb) {
				var restaurants = this.restaurantszw;
				console.log("restaurants", restaurants);
				var results = queryString ? restaurants.filter(this.createFilterzw(queryString)) : restaurants;
				console.log("results", results);
				// 调用 callback 返回建议列表的数据
				for (var i = 0; i < results.length; i++) {
					for (var j = i + 1; j < results.length; j++) {
						if (results[i].zw === results[j].zw) {
							results.splice(j, 1);
							j--;
						}
					}
				}
				cb(results);
				console.log("cb(results.zw)", results);
			},
			createFilterzw(queryString) {
				return (restaurant) => {
					return (restaurant.zw.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
				};
			},
			zwmh() {
				let resList = getZw()
				this.restaurantszw = resList;
				console.log("this.restaurants", this.restaurantsbm);
				console.log(resList)
			},
			//模糊匹配职级
			querySearchzj(queryString, cb) {
				var restaurants = this.restaurantszj;
				console.log("restaurants", restaurants);
				var results = queryString ? restaurants.filter(this.createFilterzj(queryString)) : restaurants;
				console.log("results", results);
				// 调用 callback 返回建议列表的数据
				for (var i = 0; i < results.length; i++) {
					for (var j = i + 1; j < results.length; j++) {
						if (results[i].zj === results[j].zj) {
							results.splice(j, 1);
							j--;
						}
					}
				}
				cb(results);
				console.log("cb(results.zw)", results);
			},
			createFilterzj(queryString) {
				return (restaurant) => {
					return (restaurant.zj.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
				};
			},
			zjmh() {
				let resList = getZj()
				this.restaurantszj = resList;
				console.log("this.restaurants", this.restaurantsbj);
				console.log(resList)
			},
			onInputBlur(index) {
				if (index == 1) {
					this.pdmsfzhm = jxsfzhm(this.tjlist.sfzhm)
					if (this.pdmsfzhm == 2) {
						this.$message.error('已是在岗人员')
					}
				} else if (index == 2) {
					this.pdmsfzhm = 0
					if (this.sfzhm != this.xglist.sfzhm) {
						this.pdmsfzhm = jxsfzhm(this.xglist.sfzhm)
						if (this.pdmsfzhm == 2) {
							this.$message.error('身份证号码已存在')
						}
					}

				}

			},
			cz() {
				this.formInline = {}
			},
		},
		watch: {

		}
	}
</script>

<style scoped>
	.bg_con {
		width: 100%;
		/* background: url(../../assets/background/table_bg.png) no-repeat center; */
		background-size: cover;
	}

	.dabg {
		/* margin-top: 10px; */
		box-shadow: 0px 2px 12px 0px rgba(9, 70, 255, 0.13);
		border-radius: 8px;
		width: 100%;
	}

	.xmlb-title {
		line-height: 60px;
		width: 100%;
		padding-left: 10px;
		height: 60px;
		background: url(../../assets/background/bg-02.png) no-repeat left;
		background-size: 100% 100%;
		text-indent: 10px;
		/* margin: 0 20px; */
		color: #0646BF;
		font-weight: 700;
	}

	.fhsy {
		display: inline-block;
		width: 120px;
		margin-top: 10px;
		height: 40px;
		display: flex;
		justify-content: center;
		align-items: center;
		padding-left: 30px;
		padding-top: 4px;
		float: right;
		background: url(../../assets/icons/zzjg_icon1.png) no-repeat center;
		background-size: 100% 100%;
	}

	.item_button {
		height: 100%;
		float: left;
		padding-left: 10px;
		line-height: 50px;
	}

	.select_wrap {
		/* //padding: 5px; */

		.select_wrap_content {
			float: left;
			width: 100%;
			line-height: 50px;
			/* // padding-left: 20px; */
			/* // padding-right: 20px; */
			height: 100%;
			background: rgba(255, 255, 255, 0.7);

			.item_label {
				padding-left: 10px;
				height: 100%;
				float: left;
				line-height: 50px;
				font-size: 1em
			}
		}
	}

	.mhcx1 {
		margin-top: 0px;
	}

	.widthw {
		width: 6vw;
	}


	/* /deep/.el-form-item__label {
	text-align: left;
} */
	/deep/.el-date-editor.el-input,
	.el-date-editor.el-input__inner {
		width: 184px
	}

	/deep/.el-radio-group {
		width: 184px;
	}

	/deep/.mhcx .el-form-item {
		margin-top: 5px;
		margin-bottom: 5px;
	}

	/deep/.el-dialog {
		margin-top: 6vh !important;
	}

	/deep/.inline-inputgw {
		width: 105%;
	}

	.daochu {
		display: flex;
		align-items: center;
		margin-bottom: 10px;
	}

	/deep/.el-select .el-select__tags>span {
		display: flex !important;
		flex-wrap: wrap;
	}

	/* /deep/.el-radio {
	display: block;
	margin-top: 10px;
	margin-bottom: 10px;
} */
.bz{
	height: 72px !important;
}
/deep/.el-dialog__body .el-form>div .el-form-item__label {
	width: 155px !important;
}
</style>