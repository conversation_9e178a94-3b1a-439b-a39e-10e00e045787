import DB from "./adapter/zczpAdaptor";

import { getUuid } from "../utils/getUuid";

import { getScrw, getDwpfjlListByDxXx, getDwpfjlId } from "./zczpCommunal";

/*****
 * 组织机构表
 * ******/
//获取所有的组织机构(保密管理部门)
export const selectAllZzjg = () => {
  return DB.get("zzjg_list").filter({ bmflag: "是" }).cloneDeep().value();
};

//通过组织机构ID获取组织机构
export const selectAllZzjgByZzjgid = (zzjgid) => {
  console.log("selectAllZzjgByZzjgid", zzjgid);
  if (!zzjgid) {
    console.log("组织机构ID为空", zzjgid);
    return;
  }
  return DB.get("zzjg_list").find({ bmm: zzjgid }).cloneDeep().value();
};
//通过组织机构名称lable获取组织机构
export const getAllZzjgByLable = (params) => {
  console.log("getAllZzjgByLable", params);
  // if (!lable) {
  //   console.log("组织机构名称为空", lable);
  //   return;
  // }
  return DB.get("zzjg_list")
    .filter(function (item) {
      if (params.label == item.label) {
        return item;
      }
    })
    .cloneDeep()
    .value();
};

//修改
export const revisetztz = (fbmmtz,bmmtz) => {
  let fbmm = fbmmtz
  let bmm = bmmtz
  console.log('fbmm', fbmm)
  console.log('fbmm', bmm)
  if (!fbmm || fbmm == '') {
    return
  }else{
    DB.read().get('zzjg_list').find({ 'fbmm': fbmm }).assign({'fbmm':bmm}).write()
  }
}
// //机构用户-----------------------------------机构用户初始化列表********
// export const getzzjg = (params) => {
//   let page = params.page;
//   let pagesize = params.pageSize;
//   let list_total = DB.get("jgyh_list")
//     .sortBy("cjsj")
//     .filter(function (item) {
//       return item;
//     })
//     .cloneDeep()
//     .value();

//   // 手动分页
//   console.log(pagesize * (page - 1), pagesize * (page - 1) + pagesize);
//   let pageList = list_total.slice(
//     pagesize * (page - 1),
//     pagesize * (page - 1) + pagesize
//   );

//   let resList = {
//     list: pageList,
//     list_total: list_total,
//     total: list_total.length,
//   };
//   console.log("机构用户", resList);
//   return resList;
// };
//机构用户-----------------------------------机构用户添加成员********
export const addzzjg = (params) => {
  DB.read().get("zzjg_list").push(params).write();
};
//机构用户-----------------------------------机构用户删除成员********
export const deletezzjg = (params) => {
  DB.read().get("zzjg_list").remove(params).write();
};

//下属组织机构-----------------------------------下属组织机构初始化列表********
export const getxszzjg = (params) => {
  let page = params.page;
  let pagesize = params.pageSize;
  let bmm = params.bmm;

  let list_total = DB.get("zzjg_list")
    .sortBy("pxh")
    .filter(function (item) {
      if (bmm == item.fbmm) {
        return item;
      }
    })
    .cloneDeep()
    .value();

  // 手动分页
  console.log(pagesize * (page - 1), pagesize * (page - 1) + pagesize);
  let pageList = list_total.slice(
    pagesize * (page - 1),
    pagesize * (page - 1) + pagesize
  );

  let resList = {
    list: pageList,
    list_total: list_total,
    total: list_total.length,
  };
  console.log("下属组织机构", resList);
  return resList;
};
//下属组织机构-----------------------------------下属组织机构添加成员********
export const addxszzjg = (params) => {
  console.log(params,'111111111111111111111111111');
  let sjc = new Date().getTime();
  params.cjsj = sjc;
  params.gxsj = sjc;
  let tempList = DB.get("zzjg_list").cloneDeep().value();
  let a = tempList.filter(item => item.label == params.label)
  if (a.length == 0) {
    console.log(1111);
    params.pxh = tempList.length + 1;
  DB.read().get("zzjg_list").push(params).write();
  }
  // 插入默认排序号
  
};
export const jyaddxszzjg = (params) => {
  console.log(params);
  let fbmm = params.fbmm;
  let label = params.label;
  let message = 0;
  let pd = DB.read()
    .get("zzjg_list")
    .filter({ fbmm: fbmm })
    .cloneDeep()
    .value();
  if (pd != undefined) {
    for (let i = 0; i < pd.length; i++) {
      if (label == pd[i].label) {
        message = 1;
        break;
      }
    }
  }
  return message;
};
//下属组织机构-----------------------------------下属组织机构删除成员********
export const deletexszzjg = (params) => {
  DB.read().get("zzjg_list").remove(params).write();
};

//机构用户-----------------------------------机构用户初始化列表********
export const getJgxx = () => {
  let list_total = DB.get("zzjg_list")
    .sortBy("pxh")
    .filter(function (item) {
      return item;
    })
    .cloneDeep()
    .value();

  console.log("机构用户", list_total);
  return list_total;
};

export const getsmry = (params) => {
  let page = params.page;
  let pagesize = params.pageSize;
  let bm = params.bm;
  let list_total = DB.get("Smry_list")
    .sortBy("cjsj")
    .filter(function (item) {
      console.log("item.bm", item);
      let xdbm = item.bm.split("/");
      console.log("xdbm", xdbm);
      item.bm = xdbm[xdbm.length - 1];
      console.log("item.bm", item.bm);
      if (bm == undefined) {
        return item;
      }
      if (bm == item.bm) {
        console.log("全都没有", item);
        return item;
      }
    })
    .cloneDeep()
    .value();

  // 手动分页
  console.log(pagesize * (page - 1), pagesize * (page - 1) + pagesize);
  let pageList = list_total.slice(
    pagesize * (page - 1),
    pagesize * (page - 1) + pagesize
  );

  let resList = {
    list: pageList,
    list_total: list_total,
    total: list_total.length,
  };
  console.log("保密制度", resList);
  return resList;
};

export const updataZzjg = (params) => {
  // 数据校验
  // 校验ID
  let bmm = params.bmm;
  console.log("bmm", bmm);
  if (!bmm || bmm == "") {
    return;
  }
  params.children = undefined;
  params.gxsj = new Date().getTime();
  // 全字段更新方法(传进来的字段都会更新)
  DB.read().get("zzjg_list").find({ bmm: bmm }).assign(params).write();
  // // 更新部分字段方法
  // db.read().get('Bmzd_list').find({csbgid:csbgid}).assign({
  // 	zdmc: params.zdmc,
  // 	...
  // }).write()
};

export const updataSy = (params) => {
  // 数据校验
  // 校验ID
  let bmm = params.bmm;
  console.log("bmm", bmm);
  if (!bmm || bmm == "") {
    return;
  }
  params.children = undefined;
  if (params.pxh > 0) {
    // params.pxh = params.pxh - 1;
  } else {
    console.log("不可继续上移");
  }

  params.gxsj = new Date().getTime();
  // 全字段更新方法(传进来的字段都会更新)
  DB.read().get("zzjg_list").find({ bmm: bmm }).assign(params).write();
  // // 更新部分字段方法
  // db.read().get('Bmzd_list').find({csbgid:csbgid}).assign({
  // 	zdmc: params.zdmc,
  // 	...
  // }).write()
};
export const updataXy = (params) => {
  // 数据校验
  // 校验ID
  let bmm = params.bmm;
  console.log("bmm", bmm);
  if (!bmm || bmm == "") {
    return;
  }
  params.children = undefined;
  if (params.pxh > 0) {
    // params.pxh = params.pxh + 1;
  } else {
    console.log("不可继续下移");
  }

  params.gxsj = new Date().getTime();
  // 全字段更新方法(传进来的字段都会更新)
  DB.read().get("zzjg_list").find({ bmm: bmm }).assign(params).write();
  // // 更新部分字段方法
  // db.read().get('Bmzd_list').find({csbgid:csbgid}).assign({
  // 	zdmc: params.zdmc,
  // 	...
  // }).write()
};

export const deletezzjgtsxx = (params) => {
  let bm = params.label;
  console.log("bm", bm);
  // return
  let tsxx = "是否继续删除";
  let pdbm = DB.get("Smry_list")
    .sortBy("cjsj")
    .filter(function (item) {
      if (bm == undefined || item.bm.indexOf(bm) != -1) {
        return item;
      }
    })
    .cloneDeep()
    .value();
  // let pdgwmc =
  if (pdbm != []) {
    pdbm.forEach((item) => {
      console.log(item);
      tsxx = "该部门里已有人员，是否继续删除该部门";
    });
  }
  return tsxx;
};

// 删除组织机构（递归）
export const deletexszzjgDg = (params) => {
  let bmm = params.bmm;
  console.log("DB删除", bmm, params.label);
  if (!bmm) {
    return;
  }
  let delFbmObj = DB.get("zzjg_list").find({ bmm: bmm }).cloneDeep().value();
  DB.get("zzjg_list").remove(delFbmObj).write();
  let delChildrenList = DB.get("zzjg_list")
    .filter({ fbmm: bmm })
    .cloneDeep()
    .value();
  delChildrenList.forEach((item) => {
    deletexszzjgDg(item);
  });
};
// 删除组织机构（导入覆盖）
export const deletexszzjgDgdrfg = () => {
  let a = DB.get("zzjg_list").filter().cloneDeep().value();
  a.forEach(item=>{
    if (item.fbmm!=='') {
      DB.get("zzjg_list").remove({fbmm:item.fbmm}).write();
    }
  })
 
};
