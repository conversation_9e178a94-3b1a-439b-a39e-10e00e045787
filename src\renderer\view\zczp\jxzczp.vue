<template>
  <div style="height: calc(100% - 32px);width: 100%;">
    <!-- 表格区域 -->
    <div style="height: calc(100% - 34px - 20px);">
      <el-table :data="scList" border stripe :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" style="width: 100%;border:1px solid #EBEEF5;" height="100%" stripe>
        <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
        <el-table-column prop="rwmc" label="任务名称"></el-table-column>
        <el-table-column prop="jcjdmc" label="检查季度"></el-table-column>
        <el-table-column prop="" label="检查开始时间" width="120">
          <template slot-scope="scoped">
            <span>{{dateFormatNYRChinese(scoped.row.kssj)}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="" label="检查结束时间" width="120">
          <template slot-scope="scoped">
            <span v-if="scoped.row.jssj">{{scoped.row.jssj}}</span>
            <span else>-未结束-</span>
          </template>
        </el-table-column>
        <el-table-column label="检查状态" width="170">
          <template slot-scope="scoped">
            <div>
              <span v-if="scoped.row.zt == -1">新建审查任务</span>
              <span v-if="scoped.row.zt == 0">机关单位基本信息临时保存</span>
              <span v-if="scoped.row.zt == 1">机关单位基本信息保存完成</span>
              <span v-if="scoped.row.zt == 2">抽查的内设机构临时保存</span>
              <span v-if="scoped.row.zt == 3">抽查的内设机构保存完成</span>
              <span v-if="scoped.row.zt == 4">抽查的人员临时保存</span>
              <span v-if="scoped.row.zt == 5">抽查的人员保存完成</span>
              <span v-if="scoped.row.zt == 6">检查总结临时保存</span>
              <span v-if="scoped.row.zt == 7">检查总结保存完成</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="" label="操作" width="120">
          <template slot-scope="scoped">
            <el-button size="small" type="text" @click="jxzczp(scoped.row)">继续自查自评</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 分页组件区域 -->
    <div style="border: 1px solid #ebeef5">
      <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange" :pager-count="5" :current-page="pageInfo.page" :page-sizes="[5, 10, 20, 30]" :page-size="pageInfo.pageSize" layout="total, prev, pager, sizes,next, jumper" :total="pageInfo.total">
      </el-pagination>
    </div>
    <!---->
  </div>
</template>

<script>

import { dateFormatNYRChinese } from '../../../utils/moment'

import { setZczpIdsObj } from '../../../utils/windowLocation'

import { selectScrwJxzczpPage } from '../../../db/zczpdb'

import { writeOptionsLog } from '../../../utils/logUtils'

export default {
  data () {
    return {
      // 查询条件
      formInline: {},
      // 分页信息
      pageInfo: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      // 表格数据
      scList: []
    }
  },
  computed: {},
  components: {
  },
  methods: {
    // 日期转换
    dateFormatNYRChinese (time) {
      let date = new Date(time)
      if ('Invalid Date' == date) {
        return date
      }
      return dateFormatNYRChinese(date)
    },
    // 页码变更
    handleCurrentChange (val) { },
    // 页面大小变更
    handleSizeChange (val) { },
    // 获取继续自查自评信息
    getJxzczpList () {
      let params = {
        zt: 7
      }
      Object.assign(params, this.pageInfo)
      let scrwListPage = selectScrwJxzczpPage(params)
      this.scList = scrwListPage.list
      this.pageInfo.total = scrwListPage.total
    },
    // 继续自查自评，状态7为完结状态，不用管，应该在审查历史页显示
    jxzczp (row) {
      let zt = row.zt
      // 需要跳转的路由
      let path
      switch (zt) {
        case -1:
          path = '/xjzczp'
          break
        case 0:
          path = '/xjzczp'
          break
        case 1:
          path = '/ccdnsjg'
          break
        case 2:
          path = '/ccdnsjg'
          break
        case 3:
          path = '/ccdry'
          break
        case 4:
          path = '/ccdry'
          break
        case 5:
          path = '/jczj'
          break
        case 6:
          path = '/jczj'
          break
      }
      console.log('跳转路由', path)
      if (path) {
        setZczpIdsObj('rwid', row.rwid)
        // 写入操作日志
        writeOptionsLog('yybs-jxzczp', '继续审查任务', row)
        this.$router.push({
          path: path,
          // query: {
          //   rwid: row.rwid
          // }
        })
        return
      }
      console.log('未知路由，请查验')
    }
  },
  watch: {},
  mounted () {
    this.getJxzczpList()
  }
};
</script>

<style scoped></style>
