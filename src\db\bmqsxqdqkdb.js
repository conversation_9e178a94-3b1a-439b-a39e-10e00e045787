import db from './adapter/zczpAdaptor'

// 获取所有不明确事项确定情况信息(上报数据自选模式专用)
export const getAllBmqsxqdqkZxms = () => {
  return db.get('dmqsxqdqk_list').cloneDeep().value()
}

//保密制度-----------------------------------保密制度初始化列表********
export const getDmqsxqdqk = (params) => {
  let page = params.page
  let pagesize = params.pageSize
  //
  let sxmc = params.sxmc
  let mj = params.mj
  let qyrqArr = params.qyrq
  // let bmbh = params.bmbh
  // let lx = params.lx
  // let mj = params.mj
  // let qyrq = params.qyrq
  let list_total = db.get('dmqsxqdqk_list').sortBy('cjsj').cloneDeep().value()

  // 模糊查询过滤
  if (sxmc) {
    list_total = list_total.filter((item) => {
      if (item.sxmc.indexOf(sxmc) != -1) {
        return item
      }
    })
  }
  if (mj) {
    list_total = list_total.filter((item) => {
      if (item.mj == mj) {
        return item
      }
    })
  }
  if (qyrqArr) {
    list_total = list_total.filter((item) => {
      if (item.qyrq >= qyrqArr[0] && item.qyrq <= qyrqArr[1]) {
        return item
      }
    })
  }

  // 手动分页
  console.log(pagesize * (page - 1), pagesize * (page - 1) + pagesize)
  let pageList = list_total.slice(
    pagesize * (page - 1),
    pagesize * (page - 1) + pagesize
  )

  let resList = {
    list: pageList,
    list_total: list_total,
    total: list_total.length,
  }
  console.log('保密制度', resList)
  return resList
}
//保密制度-----------------------------------保密制度添加成员********
export const addDmqsxqdqk = (params) => {
  let sjc = new Date().getTime()
  params.cjsj = sjc
  params.gxsj = sjc
  db.read().get('dmqsxqdqk_list').push(params).write()
}
//保密制度-----------------------------------保密制度删除成员********
//修改
export const reviseDmqsxqdqk = (params) => {
  let bmqsxqdqk = params.bmqsxqdqk
  console.log('Dmqsxqdqkid', bmqsxqdqk)
  if (!bmqsxqdqk || bmqsxqdqk == '') {
    return
  }
  params.gxsj = new Date().getTime()
  // 全字段更新方法(传进来的字段都会更新)
  db.read()
    .get('dmqsxqdqk_list')
    .find({ bmqsxqdqk: bmqsxqdqk })
    .assign(params)
    .write()
}
//保密制度-----------------------------------保密制度删除成员********
export const deleteDmqsxqdqk = (params) => {
  db.read().get('dmqsxqdqk_list').remove(params).write()
}
