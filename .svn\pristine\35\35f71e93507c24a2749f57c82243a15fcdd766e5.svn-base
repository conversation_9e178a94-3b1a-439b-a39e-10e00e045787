<template>
  <div class="bg_con" style="height: calc(100% - 38px);">
    <div style="width: 100%; position: relative; overflow: hidden;height: 100%;">
      <!-- <div class="xmlb-title" style=" cursor: pointer;">
				<span style="font-size: 24px; cursor: pointer;">涉密场所汇总情况</span>
				<span style="" @click="returnSy" class="fhsy">返回</span>
			</div> -->

      <div class="dabg" style="height: 100%;">
        <div class="content" style="height: 100%;">
          <div class="table" style="height: 100%;">
            <!-- -----------------操作区域--------------------------- -->
            <div class="mhcx">
              <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline" style="float:left">
                <el-form-item label="场所名称" style="font-weight: 700;">
                  <el-input v-model="formInline.csmc" clearable placeholder="场所名称" class="widthw">
                  </el-input>
                </el-form-item>
                <el-form-item label="变更日期" style="font-weight: 700;">
                  <el-date-picker v-model="formInline.sqrq" type="daterange" range-separator="至" style="width:266px;" start-placeholder="查询起始时间" end-placeholder="查询结束日期" format="yyyy年MM月dd日" value-format="yyyy年MM月dd日">
                  </el-date-picker>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
                </el-form-item>
                <el-form-item>
                  <el-button type="warning" icon="el-icon-circle-close" @click="cz">重置</el-button>
                </el-form-item>

              </el-form>
              <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline" style="float:right">
                <el-form-item style="float: right;">
                  <el-button type="danger" size="medium" @click="shanchu" icon="el-icon-delete-solid">
                    删除
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <el-button type="primary" size="medium" icon="el-icon-download" @click="exportList()">导出
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <input type="file" ref="upload" style="display: none;position: absolute;top:10px; right:0;opacity:0; cursor: pointer;height: 32px;width: 56px;z-index:1;" accept=".xls,.xlsx">
                  <el-button type="primary" icon="el-icon-upload2" size="medium" @click="dr_dialog = true">
                    导入
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <el-button type="success" size="medium" @click="csbgClick()" icon="el-icon-plus">
                    场所变更
                  </el-button>
                </el-form-item>
              </el-form>
            </div>

            <!-- -----------------审查组人员列表--------------------------- -->
            <div class="table_content_padding" style="height: 100%;">
              <div class="table_content" style="height: 100%;">
                <!-- <el-table :data="csbgList" border @selection-change="selectRow"
									:header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
									style="width: 100%;border:1px solid #EBEEF5;" height="tableHeight" stripe> -->
                <el-table :data="csbgList" border @selection-change="selectRow" :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" style="width: 100%;border:1px solid #EBEEF5;" height="calc(100% - 34px - 44px - 10px)" stripe>
                  <el-table-column type="selection" width="55" align="center"> </el-table-column>
                  <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                  <el-table-column prop="csmc" label="场所名称"></el-table-column>
                  <el-table-column prop="zrbm" label="责任部门"></el-table-column>
                  <el-table-column prop="smcd" label="涉密程度"></el-table-column>
                  <el-table-column prop="zrr" label="责任人"></el-table-column>
                  <el-table-column prop="bzbgyy" label="变更事项">
                    <template slot-scope="scoped">
                      <!-- <div>{{scoped.row.bzbgyy}} {{csmc}}  {{smcd}}  {{sqrq}}  {{zrbm}}  {{zrr}}  {{zrrdh}}  {{szwz}}  {{yt}}  {{bzbgyy}}  {{bz}}</div> -->
                      <div>
                        <!-- {{scoped.row.bzbgyy}} -->
                        <!-- {{ scoped.row.bgls }} -->
                        <p v-for="(item, index) in scoped.row.bgls" :key="index">
                          {{ index + 1 }}.{{ item }}
                        </p>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="sqrq" label="变更日期"></el-table-column>
                  <!-- <el-table-column prop="bz" label="备注"></el-table-column> -->
                  <el-table-column prop="" label="操作" width="100">
                    <template slot-scope="scoped">
                      <el-button size="medium" type="text" @click="xqyl(scoped.row)">详情
                      </el-button>
                    </template>
                  </el-table-column>

                </el-table>

                <!-- -------------------------分页区域---------------------------- -->
                <div style="border: 1px solid #ebeef5;">
                  <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange" :pager-count="5" :current-page="page" :page-sizes="[5, 10, 20, 30]" :page-size="pageSize" layout="total, prev, pager, sizes,next, jumper" :total="total">
                  </el-pagination>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 模板下载 -->
        <el-dialog title="开始导入" class="scbg-dialog" width="600px" @close="mbxzgb" :visible.sync="dr_dialog" show-close>
          <div style="padding: 20px;">
            <div class="daochu">
              <div>一、请点击“导出模板”，并参照模板填写信息。</div>
              <el-button type="primary" size="mini" @click="mbdc">
                模板导出
              </el-button>
            </div>
            <div class="daochu">
              <div>二、数据导入方式：</div>
              <el-radio-group v-model="sjdrfs" @change="Radio($event)">
                <el-radio label="1">追加（导入时已有的记录信息不变，只添加新的记录）</el-radio>
                <el-radio label="2">覆盖（导入时更新已有的记录信息，并添加新的记录）</el-radio>
              </el-radio-group>
            </div>
            <div class="daochu">
              <div>三、将按模板填写的文件，导入到系统中。</div>
              <el-button type="primary" size="mini" @click="chooseFile">
                上传导入
              </el-button>
            </div>
          </div>
        </el-dialog>

        <!-- -----------------导入-弹窗--------------------------- -->
        <el-dialog width="1000px" height="800px" title="导入涉密场所变更汇总情况" class="scbg-dialog" :visible.sync="dialogVisible_dr" show-close>
          <div style="height: 600px;">
            <el-table :data="dr_cyz_list" ref="multipleTable" @selection-change="handleSelectionChange" style="width: 100%;border:1px solid #EBEEF5;" height="100%" stripe>
              <el-table-column type="selection" width="55"> </el-table-column>
              <el-table-column prop="场所名称" label="场所名称"></el-table-column>
              <el-table-column prop="责任部门" label="责任部门"></el-table-column>
              <el-table-column prop="涉密程度" label="涉密程度"></el-table-column>
              <el-table-column prop="责任人" label="责任人"></el-table-column>
              <el-table-column prop="变更事项" label="变更事项"></el-table-column>
              <el-table-column label="变更日期">
                <template slot-scope="scoped">
                  <div>{{ formatTime(scoped.row['变更日期']) }}</div>
                </template>
              </el-table-column>
              <el-table-column prop="备注" label="备注">
              </el-table-column>
            </el-table>
          </div>

          <div style="height :30px;display: flex;align-items: center;justify-content: center;margin: 10px 0;">
            <el-button type="primary" @click="drcy" size="mini">导 入</el-button>
            <el-button type="warning" @click="dialogVisible_dr = false" size="mini">关 闭</el-button>
          </div>
        </el-dialog>

        <!-- -----------------涉密人员离岗离职信息-弹窗--------------------------- -->

        <el-dialog title="新增涉密场所变更" :close-on-click-modal="false" :visible.sync="dialogVisible" width="46%" class="xg" :before-close="handleClose" @close="close('formName')">
          <el-form ref="formName" :model="tjlist" :rules="rules" label-width="150px" size="mini">
            <div style="display:flex">
              <el-form-item label="场所名称" prop="csmc">
                <!-- <el-input placeholder="场所名称" v-model="tjlist.csmc" clearable></el-input> -->
                <el-autocomplete class="inline-input" v-model="tjlist.csmc" value-key="csmc" :fetch-suggestions="querySearch" placeholder="请输入内容" @select="handleSelect">
                </el-autocomplete>
              </el-form-item>
              <el-form-item label="变更日期" prop="sqrq">
                <!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
                <el-date-picker v-model="tjlist.sqrq" class="cd" clearable type="date" placeholder="选择日期" format="yyyy年MM月dd日" value-format="yyyy年MM月dd日" style="width:100%">
                </el-date-picker>
              </el-form-item>
            </div>
            <el-form-item label="授权人" prop="sqr" class="sqrjy">
              <el-autocomplete class="inline-input" v-model="tjlist.sqr" value-key="xm" :fetch-suggestions="querySearchsqr" placeholder="请输入内容">
              </el-autocomplete>
            </el-form-item>
            <el-form-item label="涉密程度" prop="smcd" class="sqrjy">
              <el-radio-group v-model="tjlist.smcd">
                <el-radio v-for="item in sbmjxz" :label="item.sbmjmc" :value="item.sbmjmc" :key="item.sbmjid"></el-radio>
              </el-radio-group>
            </el-form-item>
            <div style="display:flex">
              <el-form-item label="责任部门" prop="zrbm">
                <el-cascader v-model="tjlist.zrbm" :options="regionOption" :props="regionParams" filterable ref="cascaderArr" @change="handleChange(1)"></el-cascader>
              </el-form-item>
              <el-form-item label="责任人" prop="zrr">
                <el-autocomplete class="inline-input" value-key="xm" v-model.trim="tjlist.zrr" :fetch-suggestions="querySearchzrr" placeholder="请输入责任人" style="width:100%">
                </el-autocomplete>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="责任人电话" prop="zrrdh">
                <el-input oninput="value=value.replace(/[^\d.]/g,'')" @blur="zrrdh=$event.target.value" placeholder="责任人电话" v-model="tjlist.zrrdh" clearable></el-input>
              </el-form-item>
              <el-form-item label="所在位置" prop="szwz">
                <el-input placeholder="所在位置" v-model="tjlist.szwz" clearable></el-input>
              </el-form-item>
            </div>
            <el-form-item label="用途" prop="yt" class="bz one-line-textarea">
              <el-input type="textarea" v-model="tjlist.yt"></el-input>
            </el-form-item>
            <el-form-item label="备注变更/撤销原因" prop="bzbgyy" class="bzbg">
              <el-radio-group v-model="tjlist.bzbgyy">
                <div style="display:flex;flex-direction:column">
                  <el-radio label="原任务完成，不再从事或较少从事涉密工作"></el-radio>
                  <el-radio label="原任务完成，承担更低涉密级别工作任务" style="margin-top:10px"></el-radio>
                  <el-radio label="承担更高涉密级别工作任务" style="margin-top:10px"></el-radio>
                  <el-radio label="其他" style="margin-top:10px"></el-radio>
                </div>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="备注" prop="bz" class="bz one-line-textarea">
              <el-input type="textarea" v-model="tjlist.bz"></el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitTj('formName')">保 存</el-button>
            <el-button type="warning" @click="dialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>

        <el-dialog title="涉密场所变更详情" :close-on-click-modal="false" :visible.sync="xgdialogVisible" width="46%" class="xg">
          <el-form ref="form" :model="xglist" label-width="149px" size="mini" disabled>
            <div style="display:flex">
              <el-form-item label="场所名称" prop="csmc">
                <el-input placeholder="场所名称" v-model="xglist.csmc" clearable style="width:100%"></el-input>
              </el-form-item>
              <el-form-item label="变更日期" prop="sqrq">
                <!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
                <el-date-picker v-model="xglist.sqrq" class="cd" clearable type="date" style="width:100%" placeholder="选择日期" format="yyyy年MM月dd日" value-format="yyyy年MM月dd日">
                </el-date-picker>
              </el-form-item>
            </div>
            <el-form-item label="授权人" prop="sqr">
              <el-select v-model="xglist.sqr" placeholder="请选择授权人" style="width:50%">
                <el-option v-for="item in smryList" :label="item.xm" :value="item.xm" :key="item.smryid"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="涉密程度" prop="smcd">
              <el-radio-group v-model="xglist.smcd">
                <el-radio label="绝密"></el-radio>
                <el-radio label="机密"></el-radio>
                <el-radio label="秘密"></el-radio>
              </el-radio-group>
            </el-form-item>
            <div style="display:flex">
              <el-form-item label="责任部门" prop="zrbm">
                <el-cascader v-model="xglist.zrbm" :options="regionOption" :props="regionParams" filterable ref="cascaderArr" @change="handleChange(2)"></el-cascader>
              </el-form-item>
              <el-form-item label="责任人" prop="zrr">
                <el-input placeholder="责任人" v-model="xglist.zrr" clearable></el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="责任人电话" prop="zrrdh">
                <el-input oninput="value=value.replace(/[^\d.]/g,'')" @blur="zrrdh=$event.target.value" placeholder="责任人电话" v-model="xglist.zrrdh" clearable></el-input>
              </el-form-item>
              <el-form-item label="所在位置" prop="szwz">
                <el-input placeholder="所在位置" v-model="xglist.szwz" clearable></el-input>
              </el-form-item>
            </div>
            <el-form-item label="用途" prop="yt" class="bz one-line-textarea">
              <el-input type="textarea" v-model="xglist.yt"></el-input>
            </el-form-item>
            <el-form-item label="备注变更/撤销原因" class="bzbg">
              <el-radio-group v-model="xglist.bzbgyy">
                <div style="display:flex;flex-direction:column">
                  <el-radio label="原任务完成，不再从事或较少从事涉密工作"></el-radio>
                  <el-radio label="原任务完成，承担更低涉密级别工作任务" style="margin-top:10px"></el-radio>
                  <el-radio label="承担更高涉密级别工作任务" style="margin-top:10px"></el-radio>
                  <el-radio label="其他" style="margin-top:10px"></el-radio>
                </div>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="备注" prop="bz" class="bz one-line-textarea">
              <el-input type="textarea" v-model="xglist.bz"></el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="warning" @click="xgdialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>
      </div>
    </div>
  </div>

</template>
<script>
import {
  //内容管理初始化成员列表
  getCsbg,
  //添加内容管理
  addCsbg,
  //删除内容管理
  deleteCsbg,
  getCsgl,
  updataCsgl, getSmryList
} from "../../../db/csbgdb";
import {
  getlogin
} from "../../../db/loginyhdb";
import {
  getbmmc
} from "../../../db/smgwgldb";
import {
  getsmry,
} from "../../../db/csgldb";
import {
  exportExcel
} from "../../../utils/exportExcel"; //excel导出工具

import {
  getUuid
} from "../../../utils/getUuid"; //获取uuid
import XLSX from "xlsx";
import {
  getsbmj,
} from "../../../db/xzdb"
import {
  dateFormatNYRChinese
} from "../../../utils/moment"

import { getDateTime, computeDistanceIndex } from '../../../utils/utils'

export default {
  components: {},
  props: {},
  data () {
    var isMobileNumber = (rule, value, callback) => {
      if (!value) {
        return new Error('请输入电话号码')
      } else {
        const reg = /^((13[0-9])|(14[5-9])|(15([0-3]|[5-9]))|(16[6-7])|(17[1-8])|(18[0-9])|(19[1|3])|(19[5|6])|(19[8|9]))\d{8}$/
        const isPhone = reg.test(value)
        value = Number(value) //转换为数字
        if (typeof value === 'number' && !isNaN(value)) {
          //判断是否为数字
          value = value.toString() //转换成字符串
          if (value.length < 0 || value.length > 12 || !isPhone) {
            //判断是否为11位手机号
            callback(new Error('手机号格式:138xxxx8754'))
          } else {
            callback()
          }
        } else {
          callback(new Error('请输入电话号码'))
        }
      }
    }
    return {

      sbmjxz: [],
      //导入
      dialogVisible_dr: false, //导入成员组弹窗状态
      dr_cyz_list: [], //待选择导入成员组列表
      multipleTable: [], //已选择导入成员组列表
      csbgList: [],
      smryList: [],
      restaurants: [],
      formInline: {

      },
      tjlist: {
        csmc: '',
        sqr: '',
        smcd: '',
        sqrq: '',
        zrbm: '',
        zrr: '',
        zrrdh: '',
        szwz: '',
        yt: '',
        bzbgyy: '',
        bz: '',
        bgls: []
      },
      page: 1,
      pageSize: 10,
      total: 0,
      selectlistRow: [], //列表的值
      dialogVisible: false, //添加弹窗状态
      //表单验证
      rules: {
        csmc: [{
          required: true,
          message: '请输入场所名称',
          trigger: ['blur', 'change']
        },],
        sqr: [{
          required: true,
          message: '请选择授权人',
          trigger: ['blur', 'change'],
        },],
        smcd: [{
          required: true,
          message: '请选择涉密程度',
          trigger: 'blur'
        },],
        sqrq: [{
          required: true,
          message: '请选择确认日期',
          trigger: 'blur'
        },],
        zrbm: [{
          required: true,
          message: '请输入责任部门',
          trigger: ['blur', 'change'],
        },],
        zrr: [{
          required: true,
          message: '请输入责任人',
          trigger: ['blur', 'change'],
        },],
        zrrdh: [{
          required: true,
          message: '请输入责任人电话',
          trigger: 'blur'
        }, {
          validator: isMobileNumber,
          trigger: 'blur'
        }],
        szwz: [{
          required: true,
          message: '请输入所在位置',
          trigger: 'blur'
        },],
        yt: [{
          required: true,
          message: '请输入用途',
          trigger: 'blur'
        },],
        bzbgyy: [{
          required: true,
          message: '请选择备注变更原因',
          trigger: 'blur'
        },],
        // bz: [{
        // 	required: true,
        // 	message: '请输入备注',
        // 	trigger: 'blur'
        // },],
      },
      xglist: {},
      updateItemOld: {},
      xgdialogVisible: false,
      oldtableList: {},
      regionOption: [], //地域信息
      regionParams: {
        label: 'label', //这里可以配置你们后端返回的属性
        value: 'label',
        children: 'childrenRegionVo',
        expandTrigger: 'click',
        checkStrictly: true,
      }, //地域信息配置参数
      dwmc: '',
      year: '',
      yue: '',
      ri: '',
      Date: '',
      xh: [],
      dclist: [],
      dr_dialog: false,
      //数据导入方式
      sjdrfs: ''
    }
  },
  computed: {},
  mounted () {
    this.dwmc = getlogin()[0].dwmc
    let date = new Date()
    this.year = date.getFullYear() + '年'; //获取完整的年份(4位)
    this.yue = (date.getMonth() + 1) + '月'; //获取当前月份(0-11,0代表1月)
    this.ri = date.getDate() + '日'; //获取当前日(1-31)
    this.Date = this.year + this.yue + this.ri
    this.sbmjxz = getsbmj()
    //列表初始化
    this.csbg()
    this.smryList = getSmryList()
    this.$refs.upload.addEventListener('change', e => { //绑定监听表格导入事件
      this.readExcel(e);
    })
    this.Csgl()
    let bmmc = getbmmc()
    let shu = []
    // console.log(bmmc);
    bmmc.forEach(item => {
      let childrenRegionVo = []
      bmmc.forEach(item1 => {
        if (item.bmm == item1.fbmm) {
          // console.log(item, item1);
          childrenRegionVo.push(item1)
          // console.log(childrenRegionVo);
          item.childrenRegionVo = childrenRegionVo
        }
      });
      // console.log(item);
      shu.push(item)
    })
    console.log(shu[0]);
    if (shu[0].childrenRegionVo) {
      shu[0].childrenRegionVo.forEach(item => {
      this.regionOption.push(item)
    })
    }
  },
  methods: {
    // 格式化时间
    formatTime (time) {
      return dateFormatNYRChinese(new Date(time))
    },
    Radio (val) {
      this.sjdrfs = val
      console.log("当前选中的值", val)
    },
    mbxzgb () {
      this.sjdrfs = ''
    },
    mbdc () {
      console.log("----导出涉密人员----")
      // console.log(this.selectlistRow);
      // if (this.selectlistRow.length > 0) {
      let filename = "涉密场所变更汇总模板" + getUuid() + ".xlsx"

      const {
        dialog
      } = require('electron').remote;
      //弹窗title
      let options = {
        title: "保存文件",
        defaultPath: filename,
      };
      console.log(dialog)
      //导出文件夹选择弹窗
      dialog.showSaveDialog(options, result => {
        console.log('result', result)
        if (result == null || result == "") {
          console.log("取消导出")
          return
        }
        let list = []
        //对导出的数据进行格式，js导出excel要求传入的参数格式为[["列1名称","列2名称","列3名称"],["列11","值21","值31"],["列12","值22","值32"]]
        list.push(["序号", "场所名称", "授权人", "责任部门", "涉密程度", "责任人", "变更日期", "所在地点", "变更事项", "备注"]) //确定列名

        let styles = {
          // 列样式
          cols: {
            // 作用sheet页索引（0开始）（-1全sheet页生效）
            scoped: -1,
            style: [
              { wpx: 100 },
              { wpx: 100 },
              { wpx: 100 },
              { wpx: 100 },
              { wpx: 100 },
              { wpx: 100 },
              { wpx: 100 },
              { wpx: 100 },
              { wpx: 100 },
              { wpx: 100 },
              { wpx: 100 },
              { wpx: 100 },
              { wpx: 100 },
              { wpx: 120 },
              { wpx: 100 },
              { wpx: 100 },
              { wpx: 100 },
            ]
          },
          // 全局样式
          all: {
            alignment: {
              horizontal: 'center', // 水平居中
              vertical: 'center' // 垂直居中
            },
            font: {
              sz: 11, // 字号
              name: '宋体' // 字体
            },
            border: { // 边框
              top: {
                style: 'thin'
              },
              bottom: {
                style: 'thin'
              },
              left: {
                style: 'thin'
              },
              right: {
                style: 'thin'
              }
            }
          },
          // 单元格样式
          cell: []
        }

        exportExcel(result, list, undefined, styles) //list 要求为二维数组
        this.dr_dialog = false
        this.$message('导出成功:' + result)
      })
    },
    Csgl () {
      this.restaurants = getCsgl()
      console.log("场所管理初始化数据：", this.restaurants);
    },
    querySearch (queryString, cb) {
      var restaurants = this.restaurants;
      console.log("this.restaurants", this.restaurants);
      var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
      console.log("results", results);
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    createFilter (queryString) {
      return (restaurant) => {
        return (restaurant.csmc.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
      };
    },

    querySearchsqr (queryString, cb) {
      var smryList = this.smryList;
      console.log("this.smryList", this.smryList);
      var results = queryString ? smryList.filter(this.createFiltersqr(queryString)) : smryList;
      console.log("results", results);
      // 调用 callback 返回建议列表的数据
      cb(results);
    },

    createFiltersqr (queryString) {
      return (smryList) => {
        return (smryList.xm.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
      };
    },

    //导入
    chooseFile () {
      if (this.sjdrfs != '') {
        if (this.sjdrfs == 1) {
          this.$refs.upload.click()
        }
        else if (this.sjdrfs == 2) {
          let valArr = this.dclist
          valArr.forEach(function (item) {
            deleteCsbg(item)
          })
          this.$refs.upload.click()
        }
      } else {
        this.$message.warning('请选择导入方式')
      }
    },
    //----成员组选择
    handleSelectionChange (val) {
      this.multipleTable = val
      console.log("选中：", this.multipleTable);
    },
    //---确定导入成员组
    drcy () {
      //遍历已选择导入的成员，进行格式化，然后添加到数据库
      for (var i in this.multipleTable) {
        var cy = {
          csmc: this.multipleTable[i]["场所名称"],
          sqr: this.multipleTable[i]["授权人"],
          zrbm: this.multipleTable[i]["责任部门"],
          smcd: this.multipleTable[i]["涉密程度"],
          zrr: this.multipleTable[i]["责任人"],
          sqrq: dateFormatNYRChinese(this.multipleTable[i]["变更日期"]),
          szwz: this.multipleTable[i]["所在地点"],
          yt: this.multipleTable[i]["用途"],
          bzbgyy: this.multipleTable[i]["变更事项"],
          bz: this.multipleTable[i]["备注"],
          csbgid: getUuid(),
        }
        addCsbg(cy)
      }
      this.dialogVisible_dr = false
      this.csbg()
    },
    //----表格导入方法
    readExcel (e) {
      var that = this;
      const files = e.target.files;
      console.log("files", files);
      var vali = /\.(xls|xlsx)$/
      if (files.length <= 0) { //如果没有文件名
        return false;
      } else if (!vali.test(files[0].name.toLowerCase())) {
        this.$Message.error('上传格式不正确，请上传xls或者xlsx格式');
        return false;
      }
      const fileReader = new FileReader();
      fileReader.onload = (e) => {
        try {
          const data = e.target.result;
          const workdata = XLSX.read(data, {
            type: 'binary',
            cellDates: true,//设为true，将天数的时间戳转为时间格式
          });
          console.log("文件的内容：", workdata) // 文件的内容
          //查看打印能看到 workdata的sheetNames里是文件中每个表的有值的集合；
          const wsname = workdata.SheetNames[0]; //取第一张表
          console.log('wsname', wsname)
          const ws = XLSX.utils.sheet_to_json(workdata.Sheets[wsname]); //生成json表格内容
          console.log(ws); //自第二行开始的内容
          try {
            ws.some(item => {
              console.log('item', item)
              item['变更日期'] = getDateTime(item['变更日期'])
            })
          } catch (error) {
            this.$message.warning('excel解析失败，请填写正确的时间格式，' + error.message)
            // 出现异常，情况表单值
            this.$refs.upload.value = ''
            return
          }
          this.dialogVisible_dr = true
          this.dr_cyz_list = ws
          console.log("列表的值:", this.dr_cyz_list)
          // 加工excel读取业务类型为数组
          // this.dr_cyz_list.forEach(function(item) {
          // 	console.log(item[0]['业务类型'].splite(','))
          // })
          this.$refs.upload.value = ''; // 处理完成 清空表单值
          this.dr_dialog = false
        } catch (e) {
          return false;
        }
      };
      fileReader.readAsBinaryString(files[0]);
    },

    xqyl (row) {
      this.updateItemOld = JSON.parse(JSON.stringify(row))
      this.xglist = JSON.parse(JSON.stringify(row))
      // this.form1.ywlx = row.ywlx
      console.log('old', row)
      console.log("this.xglist.ywlx", this.xglist);
      this.xgdialogVisible = true
    },


    //查询
    onSubmit () {
      this.csbg()
    },

    returnSy () {
      this.$router.push("/tzglsy");
    },
    csbg () {
      let params = {
        page: this.page,
        pageSize: this.pageSize
      }
      Object.assign(params, this.formInline)
      let resList = getCsbg(params)
      console.log("params", resList);

      this.csbgList = resList.list
      this.dclist = resList.list_total
      this.dclist.forEach((item, label) => {
        this.xh.push(label + 1)
      })
      console.log(this.csbgList);
      this.total = resList.total
    },
    //删除
    shanchu (id) {
      if (this.selectlistRow != '') {
        this.$confirm('是否继续删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let valArr = this.selectlistRow
          // console.log("....", val);
          valArr.forEach(function (item) {
            deleteCsbg(item)
            console.log("删除：", item);
            console.log("删除：", item);
          })
          let params = valArr
          this.$message({
            message: '删除成功',
            type: 'success'
          });
          this.csbg()
        }).catch(() => {
          this.$message('已取消删除')
        })
      } else {
        this.$message({
          message: '未选择删除记录，请选择下列列表',
          type: 'warning'
        });
      }
    },
    //添加
    showDialog () {
      this.resetForm()
      this.dialogVisible = true
    },

    //导出
    exportList () {
      console.log("----导出涉密人员----")
      // let str = 'AGQ26'
      // console.log(str.match(/\d+/g))
      // console.log(str.match(/[A-z]+/g))
      // let resObj = computeDistanceIndex('BBC1','BBE1')
      // console.log('resObj', resObj)
      // resObj = computeDistanceIndex('C5','A4')
      // console.log('resObj', resObj)
      // resObj = computeDistanceIndex('C5','A5')
      // console.log('resObj', resObj)
      // resObj = computeDistanceIndex('C5','A6')
      // console.log('resObj', resObj)
      // resObj = computeDistanceIndex('C5','A7')
      // console.log('resObj', resObj)
      // let arr = ['acb','de','f','gh']
      // let arrTemp = []
      // arr.forEach(item => {
      //   item.match(/[A-z]/g).forEach(itemChild => {
      //     arrTemp.push(itemChild)
      //   })
      // })
      // console.log('arrTemp', arrTemp)
      // return
      // console.log(this.selectlistRow);
      // if (this.selectlistRow.length > 0) {
      let filename = "涉密场所变更汇总表" + getUuid() + ".xlsx"

      const {
        dialog
      } = require('electron').remote;
      //弹窗title
      let options = {
        title: "保存文件",
        defaultPath: filename,
      };
      console.log(dialog)
      //导出文件夹选择弹窗
      dialog.showSaveDialog(options, result => {
        console.log('result', result)
        if (result == null || result == "") {
          console.log("取消导出")
          return
        }
        let list = []
        list.push(["涉密场所变更登记表","","","","",""])

        list.push(["上报单位:", this.dwmc, "", "", "", "", "", "", "",])
        list.push(["统计年度:", this.year, "", "", "", "", "", "填报时间:", this.Date])
        //对导出的数据进行格式，js导出excel要求传入的参数格式为[["列1名称","列2名称","列3名称"],["列11","值21","值31"],["列12","值22","值32"]]
        list.push(["序号", "场所名称", "授权人", "责任部门", "涉密程度", "责任人", "变更日期", "所在地点", "备注"]) //确定列名

        for (var i in this.dclist) { //每一行的值
          let item = this.dclist[i] //{"name":"XXX","dw":"XXX","zw":"XXX"}

          console.log("导出值:", this.dclist);
          let column = [(parseInt(i) + 1), item["csmc"], item["sqr"], item["zrbm"], item["smcd"], item["zrr"], item[
            "sqrq"], item["szwz"], item[
          "bz"]]
          list.push(column)
        }
        let merges = [{
          s: { //s为开始
            c: 0, //开始列
            r: 0 //开始取值范围
          },
          e: { //e结束
            c: 8, //结束列
            r: 0 //结束范围
          }
        }]
        let styles = {
          // 列样式
          cols: {
            // 作用sheet页索引（0开始）（-1全sheet页生效）
            scoped: -1,
            style: [
              { wpx: 100 },
              { wpx: 300 },
              { wpx: 100 },
              { wpx: 200 },
              { wpx: 100 },
              { wpx: 100 },
              { wpx: 100 },
              { wpx: 200 },
              { wpx: 300 }
            ]
          },
          // 全局样式
          all: {
            alignment: {
              horizontal: 'center', // 水平居中
              vertical: 'center', // 垂直居中
              wrapText: true, // 文字换行
            },
            font: {
              sz: 11, // 字号
              name: '宋体' // 字体
            },
            border: {  // 边框
              top: {
                style: 'thin'
              },
              bottom: {
                style: 'thin'
              },
              left: {
                style: 'thin'
              },
              right: {
                style: 'thin'
              }
            }
          },
          // 单元格样式
          cell: [
            {
              // 生效sheet页索引（值为 -1 时所有sheet页都生效）
              scoped: -1,
              // 索引
              index: 'A1',
              style: {
                font: {
                  name: '宋体',
                  sz: 16, // 字号
                  bold: true,
                },
                alignment: {
                  horizontal: 'center', // 水平居中
                  vertical: 'center' // 垂直居中
                }
              }
            }
          ]
        }
        // excel导出方法自定义配置
        let config = {
          // 忽略行索引(从1开始)
          rowIndexArrIgnore: [1]
        }
        exportExcel(result, list, merges, styles, config) //list 要求为二维数组
        this.$message('导出成功:' + result)
      })
    },

    handleSelect (item) {
      this.oldtableList = item
      console.log("========this.oldtableList========", this.oldtableList);
      this.tjlist.csmc = item.csmc
      this.tjlist.sqr = item.sqr
      this.tjlist.smcd = item.smcd
      this.tjlist.sqrq = item.sqrq
      this.tjlist.zrbm = item.zrbm.split('/')
      this.tjlist.zrr = item.zrr
      this.tjlist.zrrdh = item.zrrdh
      this.tjlist.szwz = item.szwz
      this.tjlist.yt = item.yt
      this.tjlist.bzbgyy = item.bzbgyy
      this.tjlist.bz = item.bz
      this.restaurantszrr = getsmry(item.zrbm)
      console.log(this.restaurantszrr);
    },
    cz () {
      this.formInline = {}
    },
    csbgClick () {
      this.dialogVisible = true
      this.tjlist.bgls = []
    },
    //确定添加成员组
    submitTj (formName) {

      this.$refs[formName].validate((valid) => {
        if (valid) {
          // this.tjlist.bgls = ''
          let params = {
            csmc: this.tjlist.csmc,
            sqr: this.tjlist.sqr,
            smcd: this.tjlist.smcd,
            sqrq: this.tjlist.sqrq,
            zrbm: this.tjlist.zrbm.join('/'),
            zrr: this.tjlist.zrr,
            zrrdh: this.tjlist.zrrdh,
            szwz: this.tjlist.szwz,
            yt: this.tjlist.yt,
            bzbgyy: this.tjlist.bzbgyy,
            bz: this.tjlist.bz,
            csbgid: getUuid(),
            csglid: this.oldtableList.csglid,
            bgls: this.tjlist.bgls
          }

          // 判断旧数据是否为空
          if (Object.keys(this.oldtableList).length < 0) {
            // 旧数据为空，提示没有变更
            return
          }

          if (this.oldtableList.csmc != params.csmc && params.csmc) {
            let str = '变成' + params.csmc
            if (this.oldtableList.csmc) {
              // 什么也不做
            } else {
              str = '场所名称由' + this.oldtableList.csmc + str
            }
            params.bgls.push(str)
          }
          if (this.oldtableList.sqr != params.sqr && params.sqr) {
            let str = '变成' + params.sqr
            if (this.oldtableList.sqr) {
              // 什么也不做
            } else {
              str = '授权人由' + this.oldtableList.sqr + str
            }
            params.bgls.push(str)
            // params.bgls.push('授权人由' + this.oldtableList.sqr + '变成' + params.sqr)
          }
          if (this.oldtableList.smcd != params.smcd && params.smcd) {
            let str = '变成' + params.smcd
            if (this.oldtableList.smcd) {
              // 什么也不做
            } else {
              str = '涉密程度由' + this.oldtableList.smcd + str
            }
            params.bgls.push(str)
            // params.bgls.push('涉密程度由' + this.oldtableList.smcd + '变成' + params.smcd)
          }
          if (this.oldtableList.sqrq != params.sqrq && params.sqrq) {
            let str = '变成' + params.sqrq
            if (this.oldtableList.sqrq) {
              // 什么也不做
            } else {
              str = '确认日期由' + this.oldtableList.sqrq + str
            }
            params.bgls.push(str)
            // params.bgls.push('确认日期由' + this.oldtableList.sqrq + '变成' + params.sqrq)
          }
          if (this.oldtableList.zrbm != params.zrbm && params.zrbm) {
            let str = '变成' + params.zrbm
            if (this.oldtableList.zrbm) {
              // 什么也不做
            } else {
              str = '责任部门由' + this.oldtableList.zrbm + str
            }
            params.bgls.push(str)
            // params.bgls.push('责任部门由' + this.oldtableList.zrbm + '变成' + params.zrbm)
          }
          if (this.oldtableList.zrr != params.zrr && params.zrr) {
            let str = '变成' + params.zrr
            if (this.oldtableList.zrr) {
              // 什么也不做
            } else {
              str = '责任人由' + this.oldtableList.zrr + str
            }
            params.bgls.push(str)
            // params.bgls.push('责任人由' + this.oldtableList.zrr + '变成' + params.zrr)
          }
          if (this.oldtableList.zrrdh != params.zrrdh && params.zrrdh) {
            let str = '变成' + params.zrrdh
            if (this.oldtableList.zrrdh) {
              // 什么也不做
            } else {
              str = '责任人电话由' + this.oldtableList.zrrdh + str
            }
            params.bgls.push(str)
            // params.bgls.push('责任人电话由' + this.oldtableList.zrrdh + '变成' + params.zrrdh)
          }
          if (this.oldtableList.szwz != params.szwz && params.szwz) {
            let str = '变成' + params.szwz
            if (this.oldtableList.szwz) {
              // 什么也不做
            } else {
              str = '所在位置由' + this.oldtableList.szwz + str
            }
            // params.bgls.push('所在位置由' + this.oldtableList.szwz + '变成' + params.szwz)
          }
          if (this.oldtableList.yt != params.yt && params.yt) {
            let str = '变成' + params.yt
            if (this.oldtableList.yt) {
              // 什么也不做
            } else {
              str = '用途由' + this.oldtableList.yt + str
            } 
            params.bgls.push(str)
            // 那扬 - 个人所得税完税证明
            // params.bgls.push('用途由' + this.oldtableList.yt + '变成' + params.yt)
          }
          if (this.oldtableList.bzbgyy != undefined && this.oldtableList.bzbgyy != params.bzbgyy && params.bzbgyy) {
            let str = '变成' + params.bzbgyy
            if (this.oldtableList.bzbgyy) {
              // 什么也不做
            } else {
              str = '备注变更原因由' + this.oldtableList.bzbgyy + str
            }
            params.bgls.push(str)
            // params.bgls.push('备注变更原因由' + this.oldtableList.bzbgyy + '变成' + params.bzbgyy)
          }
          if (params.bz && this.oldtableList.bz != params.bz) {
            let str = '变成' + params.bz
            if (this.oldtableList.bz) {
              // 什么也不做
            } else {
              str = '备注由' + this.oldtableList.bz + str
            }
            // params.bgls.push('备注由' + this.oldtableList.bz + '变成' + params.bz)
            params.bgls.push(str)
          }
          // let bglsarr = []
          // bglsarr = bglsarr.push(params.bgls)

          // console.log(params)
          addCsbg(params)
          updataCsgl(params)
          this.dialogVisible = false
          this.$message({
            message: '添加成功',
            type: 'success'
          });
          this.resetForm()
          this.csbg()
          this.Csgl()
          // let oldbzbgyy = this.tjlist.bzbgyy + this.csmc + this.smcd + this.sqrq + this.zrbm + this.zrr + this.zrrdh + this.szwz + this.yt + this.bzbgyy + this.bz
          // console.log("oldbzbgyy", oldbzbgyy);
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    deleteTkglBtn () {

    },
    selectRow (val) {
      console.log(val);
      this.selectlistRow = val;
    },
    //列表分页--跳转页数
    handleCurrentChange (val) {
      this.page = val
      this.csbg()
    },
    //列表分页--更改每页显示个数
    handleSizeChange (val) {
      this.page = 1
      this.pageSize = val
      this.csbg()
    },
    //添加重置
    resetForm () {
      this.tjlist.csmc = ''
      this.tjlist.sqr = ''
      this.tjlist.smcd = ''
      this.tjlist.sqrq = ''
      this.tjlist.zrbm = ''
      this.tjlist.zrr = ''
      this.tjlist.zrrdh = ''
      this.tjlist.szwz = ''
      this.tjlist.yt = ''
      this.tjlist.bzbgyy = ''
      this.tjlist.bz = ''
    },
    handleClose (done) {
      this.resetForm()
      this.dialogVisible = false
    },
    // 弹框关闭触发
    close (formName) {
      // 清空表单校验，避免再次进来会出现上次校验的记录
      this.$refs[formName].resetFields();
    },
    querySearchzrr (queryString, cb) {
      var restaurants = this.restaurantszrr;
      console.log("restaurants", restaurants);
      var results = queryString ? restaurants.filter(this.createFilterzrr(queryString)) : restaurants;
      console.log("results", results);
      // 调用 callback 返回建议列表的数据
      cb(results);
      console.log("cb(results.dwmc)", results);
    },
    createFilterzrr (queryString) {
      return (restaurant) => {
        return (restaurant.xm.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
      };
    },
    handleChange (index) {
      let resList
      if (index == 1) {
        resList = getsmry(this.tjlist.zrbm.join('/'))
      } else if (index == 2) (
        resList = getsmry(this.xglist.zrbm.join('/'))
      )
      this.restaurantszrr = resList;
      this.tjlist.zrr = "";
      this.xglist.zrr = "";
    }
  },
  watch: {

  }
}
</script>

<style scoped>
.bg_con {
  width: 100%;
  background-size: 100% 100%;
}

.dabg {
  /* margin-top: 10px; */
  box-shadow: 0px 2px 12px 0px rgba(9, 70, 255, 0.13);
  border-radius: 8px;
  width: 100%;
}

.xmlb-title {
  line-height: 60px;
  width: 100%;
  padding-left: 10px;
  height: 60px;
  background: url(../../assets/background/bg-02.png) no-repeat left;
  background-size: 100% 100%;
  text-indent: 10px;
  /* margin: 0 20px; */
  color: #0646bf;
  font-weight: 700;
}

.fhsy {
  display: inline-block;
  width: 120px;
  margin-top: 10px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-left: 30px;
  padding-top: 4px;
  float: right;
  background: url(../../assets/icons/zzjg_icon1.png) no-repeat center;
  background-size: 100% 100%;
}

.daochu {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

/* /deep/.el-radio {
	display: block;
	margin-top: 10px;
	margin-bottom: 10px;
} */

.item_button {
  height: 100%;
  float: left;
  padding-left: 10px;
  line-height: 50px;
}

.select_wrap {
  /* //padding: 5px; */

  .select_wrap_content {
    float: left;
    width: 100%;
    line-height: 50px;
    /* // padding-left: 20px; */
    /* // padding-right: 20px; */
    height: 100%;
    background: rgba(255, 255, 255, 0.7);

    .item_label {
      padding-left: 10px;
      height: 100%;
      float: left;
      line-height: 50px;
      font-size: 1em;
    }
  }
}

.mhcx1 {
  margin-top: 0px;
}

.widthw {
  width: 8vw;
}

.cd {
  width: 184px;
}

/deep/.mhcx .el-form-item {
  margin-top: 5px;
  margin-bottom: 5px;
}
.bz {
  height: 72px !important;
}
.bzbg {
  height: 120px !important;
}

/deep/.el-dialog__body .el-form > div .el-form-item__label {
  width: 155px !important;
}
/deep/.el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
  margin-bottom: 0px;
}
.dialog-footer {
  display: block;
  margin-top: 10px;
}
</style>
