import { app, BrowserWindow, ipc<PERSON><PERSON>, Menu } from 'electron'
import '../renderer/store'

/**
 * Set `__static` path to static files in production
 * https://simulatedgreg.gitbooks.io/electron-vue/content/en/using-static-assets.html
 */
if (process.env.NODE_ENV !== 'development') {
  global.__static = require('path')
    .join(__dirname, '/static')
    .replace(/\\/g, '\\\\')
}

let mainWindow
const winURL =
  process.env.NODE_ENV === 'development'
    ? `http://localhost:9080`
    : `file://${__dirname}/index.html`

function createWindow() {
  // 隐藏顶部菜单
  Menu.setApplicationMenu(null)
  // 默认全屏
  const { screen } = require('electron')
  const primaryDisplay = screen.getPrimaryDisplay()
  const { width, height } = primaryDisplay.workAreaSize
  /**
   * Initial window options
   */
  mainWindow = new BrowserWindow({
    width: width - 100,
    height: height - 100,
    // width: 1024,
    // height: 768,
    resizable: false,
    // useContentSize: false,
    // backgroundColor: '#eee',
    frame: false,
    // transparent: true,
    // autoHideMenuBar: true,
    center: true,
    fullscreen: false,
    webPreferences: {
      nodeIntegration: true,
      webSecurity: false,
      enableRemoteModule: true,
      plugins: true
    },
  })

  // //打开调试框
  // mainWindow.webContents.openDevTools()

  mainWindow.loadURL(winURL)

  mainWindow.on('closed', () => {
    mainWindow = null
  })
}

app.on('ready', createWindow)

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('activate', () => {
  if (mainWindow === null) {
    createWindow()
  }
})

/**
 * Auto Updater
 *
 * Uncomment the following code below and install `electron-updater` to
 * support auto updating. Code Signing with a valid certificate is required.
 * https://simulatedgreg.gitbooks.io/electron-vue/content/en/using-electron-builder.html#auto-updating
 */

/*
import { autoUpdater } from 'electron-updater'

autoUpdater.on('update-downloaded', () => {
  autoUpdater.quitAndInstall()
})

app.on('ready', () => {
  if (process.env.NODE_ENV === 'production') autoUpdater.checkForUpdates()
})
 */
// 窗口关闭
ipcMain.on('close', (e) => {
  mainWindow.close()
})
// 最小化
ipcMain.on('hide-window', () => {
  mainWindow.minimize()
})
// 最大化
ipcMain.on('max-window', () => {
  console.log('accept max-windows...')
  mainWindow.maximize()
})
// 窗口化
ipcMain.on('unmax-window', () => {
  mainWindow.unmaximize()
})
// 重启
ipcMain.on('reset-window', function () {
  app.relaunch()
  app.exit()
})
// 窗口拖拽移动
ipcMain.on('move-application', (event, pos) => {
  mainWindow && mainWindow.setPosition(pos.posX, pos.posY)
})
