{"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "0.0.1", "author": "哈尔滨工业大学软件工程股份有限公司", "description": "", "license": null, "main": "./dist/electron/main.js", "scripts": {"build": "node .electron-vue/build.js && electron-builder", "build2": "node .electron-vue/build.js", "build3": "electron-builder", "build:dir": "node .electron-vue/build.js && electron-builder --dir", "build:clean": "cross-env BUILD_TARGET=clean node .electron-vue/build.js", "build:web": "cross-env BUILD_TARGET=web node .electron-vue/build.js", "dev": "node .electron-vue/dev-runner.js", "pack": "npm run pack:main && npm run pack:renderer", "pack:main": "cross-env NODE_ENV=production webpack --progress --colors --config .electron-vue/webpack.main.config.js", "pack:renderer": "cross-env NODE_ENV=production webpack --progress --colors --config .electron-vue/webpack.renderer.config.js", "postinstall": "", "package:ep": "electron-packager ./ myapp --out ./OutApp --arch=x64 --electron-version=9.4.4 --overwrite --icon=./build/icons/icon.ico"}, "build": {"productName": "保密管家", "appId": "com.example.SecretKeeper", "nsis": {"oneClick": false, "allowElevation": true, "allowToChangeInstallationDirectory": true, "installerIcon": "./build/icons/icon.ico", "uninstallerIcon": "./build/icons/icon.ico", "installerHeaderIcon": "./build/icons/icon.ico", "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "保密管家"}, "directories": {"output": "build"}, "files": ["dist/electron/**/*"], "dmg": {"contents": [{"x": 410, "y": 150, "type": "link", "path": "/Applications"}, {"x": 130, "y": 150, "type": "file"}]}, "mac": {"icon": "build/icons/icon.icns"}, "win": {"icon": "build/icons/icon.ico"}, "linux": {"icon": "build/icons"}}, "dependencies": {"@babel/core": "^7.19.6", "adm-zip": "^0.5.9", "axios": "^0.18.0", "docxtemplater": "^3.31.5", "echarts": "^5.4.0", "echarts-liquidfill": "^3.1.0", "element-ui": "^2.15.10", "eslint": "^8.26.0", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "lowdb": "1.0.0", "md5": "^2.3.0", "moment": "^2.29.4", "multispinner": "^0.2.1", "mysql": "^2.18.1", "pizzip": "^3.1.3", "polyfill-object.fromentries": "^1.0.1", "run-electron": "^1.0.0", "shortid": "^2.2.16", "vue": "^2.5.16", "vue-electron": "^1.0.6", "vue-router": "^3.0.1", "vuex": "^3.0.1", "vuex-electron": "^1.0.0", "xlsx": "^0.18.5", "xlsx-style": "^0.8.13"}, "devDependencies": {"ajv": "^6.5.0", "babel-core": "^6.26.3", "babel-loader": "^7.1.4", "babel-minify-webpack-plugin": "^0.3.1", "babel-plugin-transform-runtime": "^6.23.0", "babel-preset-env": "^1.7.0", "babel-preset-stage-0": "^6.24.1", "babel-register": "^6.26.0", "cfonts": "^2.1.2", "chalk": "^2.4.1", "copy-webpack-plugin": "^4.5.1", "cross-env": "^5.1.6", "css-loader": "^0.28.11", "del": "^3.0.0", "devtron": "^1.4.0", "electron": "^2.0.18", "electron-builder": "^20.19.2", "electron-debug": "^1.5.0", "electron-devtools-installer": "^2.2.4", "electron-packager": "^16.0.0", "file-loader": "^1.1.11", "html-webpack-plugin": "^3.2.0", "listr": "^0.14.3", "mini-css-extract-plugin": "0.4.0", "node-loader": "^0.6.0", "style-loader": "^0.21.0", "url-loader": "^1.0.1", "vue-devtools": "^5.1.4", "vue-html-loader": "^1.2.4", "vue-loader": "^15.2.4", "vue-style-loader": "^4.1.0", "vue-template-compiler": "^2.5.16", "webpack": "^4.15.1", "webpack-cli": "^3.0.8", "webpack-dev-server": "^3.1.4", "webpack-hot-middleware": "^2.22.2", "webpack-merge": "^4.1.3"}}