<template>
  <div style="height: 100%;width: 100%;overflow-y: scroll;">
    <!---->
    <div class="mhcx">
      <el-form :inline="true" size="medium" class="demo-form-inline" style="float:left">
        <!-- <el-form-item style="float: left;">
          <div>当前审查任务：{{dialogObj.rwmc}}</div>
        </el-form-item> -->
        <el-form-item style="float: left;">
          <div>姓名：{{dialogObj.xm}}</div>
        </el-form-item>
        <el-form-item style="float: left;">
          <div>部门：{{dialogObj.bm}}</div>
        </el-form-item>
        <el-form-item style="float: left;">
          <div>职务：{{dialogObj.zw}}</div>
        </el-form-item>
      </el-form>
      <div style="clear: both;"></div>
    </div>
    <!---->
    <el-table :data="showDxList" :span-method="objectSpanMethod" border>
      <el-table-column label="自查类" width="150">
        <template slot-scope="scope">
          <div>
            <span :id="showDxList[scope.$index].dxMdIndex"></span>{{showDxList[scope.$index].dxmc}}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="自查内容">
        <template slot-scope="scope">
          <div>
            <span :id="showDxList[scope.$index].mdIndex"></span>{{showDxList[scope.$index].nr}}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="是否符合要求" width="150">
        <template slot-scope="scope">
          <el-radio-group v-model="showDxList[scope.$index].sffhyq" disabled>
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </template>
      </el-table-column>
      <el-table-column label="备注说明">
        <template slot-scope="scope">
          <el-input type="textarea" v-model.trim="showDxList[scope.$index].bzsm" :rows="3" disabled></el-input>
        </template>
      </el-table-column>
    </el-table>
    <!---->
  </div>
</template>

<script>

import {
  // 通过抽查的人员流水ID获取抽查的人员信息
  selectCcdryxxByCcdryid,
  // 获取字典
  getRyzcxxjlZD,
  //
  insertUpdateRyjlByCcdnryid,
  //
  selectRypfjlListByCcdryid,
  //
  updateCcdryById
} from '../../../../db/zczpdb'

import { getZczpIdsObj } from '../../../../utils/windowLocation'

export default {
  data () {
    return {
      dialogObj: {
        // rwid: 'D66215EC-1B9A-408A-BEE8-7A9396165EA7',
        // ccdryid: 'F9E1E031-3649-42C4-879E-5297B14B36D6'
      },
      //页面实际操作的评分数据[dx:{scnr:[]}]
      showDxList: [],
      //单元格合并规则
      spanArr: [],
    }
  },
  computed: {},
  components: {
  },
  methods: {
    //人员自查入库
    ryzcRK (dxXxList, zt) {
      //插入或更新人员评分记录表
      let bool = insertUpdateRyjlByCcdnryid(dxXxList, this.dialogObj.ccdryid)
      /*=====部门详细自查记录入库完成=====*/
      // 更新 抽查的内设机构表(ccdnsjg_list) 表状态
      let params = {
        ccdryid: this.dialogObj.ccdryid,
        zt: zt
      }
      bool = updateCcdryById(params)
      if (bool) {
        // 更新数据
        this.getRyzcxxjl()
      }
    },
    /**
     * 获取人员详细自查记录
     */
    getRyzcxxjl () {
      //
      const ryzcxxjlList = selectRypfjlListByCcdryid(this.dialogObj.ccdryid)
      console.log(ryzcxxjlList)
      ryzcxxjlList.forEach(element => {
        if (element.sffhyq === undefined) {
          element.sffhyq = true
        }
      })
      this.spanArr = this.getSpanArr(ryzcxxjlList)
      //
      this.showDxList = ryzcxxjlList
      //
      // this.$message.success('人员自查自评结果登记成功')
    },
    submit () {
      this.ryzcRK(this.showDxList, 2)
    },
    save () {
      this.ryzcRK(this.showDxList, 1)
    },
    returnSy () {
      this.$router.go(-1)
    },
    getCcdryxxByCcdryid () {
      let ccdryxx = selectCcdryxxByCcdryid(this.dialogObj)
      console.log('ccdryxx', ccdryxx)
      Object.assign(this.dialogObj, ccdryxx)
      this.dialogObj = JSON.parse(JSON.stringify(this.dialogObj))
    },
    //----------------------------用来返回this.spanArr数组的，定义每一行的 rowspan-----------
    getSpanArr (list) {
      console.log(list)
      let spanArr = []
      for (var i = 0; i < list.length; i++) {
        if (i === 0) {
          spanArr.push(1)
          this.pos = 0
        } else {
          // 判断当前元素与上一个元素是否相同
          if (list[i].dxid == list[i - 1].dxid) {
            spanArr[this.pos] += 1
            spanArr.push(0)
          } else {
            spanArr.push(1)
            this.pos = i
          }
        }
      }
      return spanArr
    },
    objectSpanMethod ({
      row,
      column,
      rowIndex,
      columnIndex
    }) {
      if (columnIndex === 0) {
        //
        const _row = this.spanArr[row.nrid - 1]
        return {
          rowspan: _row,
          colspan: 1
        }
      }
    },
    /**
     * 获取部门详细自查记录字典
     */
    getZD () {
      console.log("getZD")
      //
      const zdList = getRyzcxxjlZD()
      console.log(zdList)
      zdList.forEach((nr) => {
        if (nr.sffhyq === undefined) {
          nr.sffhyq = true
        }
      });
      this.spanArr = this.getSpanArr(zdList)
      //
      return zdList
    },
  },
  watch: {
    showDxList: {
      handler (newVal, oldVal) {
        console.log("ryzc showDxList changed...");
        const _this = this;
        _this.mdList = [];
        //table导航
        _this.tableDHList = [];
        //
        let dxIdArr = [];
        newVal.forEach((nr, nrIndex) => {
          //
          if (dxIdArr.indexOf(nr.dxid) == -1) {
            dxIdArr.push(nr.dxid);
            nr.dxMdIndex = 'dxMd' + nr.dxid;
            //
            const tableDH = {
              'href': nr.dxMdIndex,
              'content': nr.dxmc
            };
            _this.tableDHList.push(tableDH);
          }
          //
          nr.mdIndex = 'md' + nrIndex;
          if (!nr.sffhyq) {
            //
            const md = {
              'href': '#' + nr.mdIndex,
              'dxmc': nr.dxmc,
              'nr': nr.nr,
              'sffhyqText': nr.sffhyq ? '符合要求' : '不符合要求'
            };
            _this.mdList.push(md);
          }
        });
      },
      deep: true,
    },
  },
  mounted () {
    // let params = this.$route.query.params
    let params = getZczpIdsObj()
    if (params && Object.keys(params).length > 0) {
      console.log('抽查的人员登记详情', params)
      this.dialogObj.rwid = params.rwid
      this.dialogObj.ccdryid = params.ccdryid
      // 获取抽查的人员信息
      this.getCcdryxxByCcdryid()
      // 获取评分信息
      this.getRyzcxxjl()
      return
    }
    this.$message.warning('未能检测到抽查的人员登记ID，请关闭页面重新进入')

    // // 获取字典
    // this.showDxList = this.getZD()
    //
    // this.getRyzcxxjl()
  }
}
</script>

<style scoped></style>
