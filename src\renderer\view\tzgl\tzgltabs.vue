<template>
	<div>
		
	</div>
</template>
<script>
	export default {
		data() {
			return {

			};
		},
		computed: {},
		methods: {
			tabsCode() {
                // 比较结果activeName的值
				console.log(this.$route.query.activeName);
                if (this.$route.query.activeName) {
               		this.$router.push(this.$route.query.activeName)
                } else {
                  this.$router.push('/bmzd')
                }
            },
		},
		watch: {},
		mounted() {
			this.tabsCode()
		}
	};
</script>
<style scoped>

</style>
