<template>
    <div class="bg_con" style="height: calc(100% - 38px);">
        <div style="width: 100%; position: relative; overflow: hidden; height: 100%;">
            <div class="dabg" style="height: 100%;">
                <div class="content" style="height: 100%;">
                    <div class="table" style="height: calc(100% - 0px);">
                        <div class="mhcx">
                            <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline"
                                style="float:left">
                                <el-form-item label="" style="font-weight: 700;">
                                    <!-- <el-input v-model="formInline.tzsj" clearable placeholder="台账时间" class="widths">
                                    </el-input> -->
									<el-select v-model="formInline.tzsj" class="widths" placeholder="台账时间">
										<el-option
										v-for="item in yearSelect"
										:key="item.value"
										:label="item.label"
										:value="item.value">
										</el-option>
									</el-select>
                                </el-form-item>
                                <el-form-item label="" style="font-weight: 700;">
                                    <el-input v-model="formInline.gjmmsxmc" style="width:150px" clearable placeholder="定密事项名称"
                                        class="widths">
                                    </el-input>
                                </el-form-item>
                                <el-form-item label="" style="font-weight: 700;">
                                    <el-select v-model="formInline.mj" clearable placeholder="请选择类型" class="widthx">
                                        <el-option v-for="item in dmgjmj" :label="item.dmmjmc" :value="item.dmmjmc"
                                            :key="item.dmmjid"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item>
                                    <el-button type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
                                </el-form-item>
                                <el-form-item>
									<el-button type="warning" icon="el-icon-circle-close" @click="cz">重置</el-button>
								</el-form-item>

                            </el-form>
                            <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline"
                                style="float:right">
                                <!-- <el-form-item style="float: right;">
                                    <el-button type="danger" size="medium" @click="shanchu" icon="el-icon-delete-solid">
                                        删除
                                    </el-button>
                                </el-form-item> -->
                                <el-form-item style="float: right;">
                                    <el-button type="primary" size="medium" icon="el-icon-download"
                                        @click="exportList()">导出
                                    </el-button>
                                </el-form-item>
                                <!-- <el-form-item style="float: right;">
                                    <input type="file" ref="upload"
                                        style="display: none;position: absolute;top:10px; right:0;opacity:0; cursor: pointer;height: 32px;width: 56px;z-index:1;"
                                        accept=".xls,.xlsx">
                                    <el-button type="primary" icon="el-icon-upload2" size="medium"
                                        @click="dr_dialog = true">
                                        导入
                                    </el-button>
                                </el-form-item>
                                <el-form-item style="float: right;">
                                    <el-button type="success" size="medium" @click="dialogVisible = true"
                                        icon="el-icon-plus">
                                        新增
                                    </el-button>
                                </el-form-item> -->
                            </el-form>
                        </div>
                        <div class="table_content_padding" style="height: calc(100% - 60px);">
                            <div class="table_left" style="height: 100%;">
                                <el-table :data="gjmmsxLeftList" @row-click="haclick"
                                    :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
                                    style="width: 100%;border:1px solid #EBEEF5;" height="70vh">
                                    <el-table-column prop="nd" label="年度"></el-table-column>
                                    <el-table-column label="本单位一览表附件">
                                        <template slot-scope="scoped">

                                            <!-- <div>
                                                <p @click="downloadFj(scoped.row)"
                                                    style="color: #409EFF;cursor: pointer;">{{ scoped.row.wjm }}</p>
                                            </div> -->
                                            <el-button size="medium" type="text" v-if="scoped.row.sjcwjm"
                                                @click="downloadFj(scoped.row)">{{ scoped.row.wjm }}
                                            </el-button>
                                            <el-button size="medium" type="text" v-else @click="uploadSC()">上传
                                            </el-button>

                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>

                            <div class="table_content" style="height: 100%;">
                                <el-table :data="gjmmsxList" border @selection-change="selectRow"
                                    :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
                                    style="width: 100%;border:1px solid #EBEEF5;" height="calc(100% - 34px - 4px)" stripe>
                                    <el-table-column type="selection" width="55" align="center"> </el-table-column>
                                    <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                                    <el-table-column prop="nd" width="60" label="年度"></el-table-column>
                                    <el-table-column prop="gjmmsxmc" width="140px" label="国家秘密事项名称"></el-table-column>
                                    <el-table-column prop="mj" label="密级"></el-table-column>
                                    <el-table-column prop="bmqx" label="保密期限"></el-table-column>
                                    <el-table-column prop="zxfw" label="知悉范围"></el-table-column>
                                    <el-table-column prop="dmyj" label="定密依据"></el-table-column>
                                    <el-table-column prop="tzsj" label="台账时间"></el-table-column>
                                    <el-table-column label="操作" width="120">
                                        <template slot-scope="scoped">
                                            <el-button size="medium" type="text" @click="xqyl(scoped.row)">详情
                                            </el-button>
                                            <!-- <el-button size="medium" type="text" @click="updateItem(scoped.row)">修改
                                            </el-button> -->
                                        </template>
                                    </el-table-column>

                                </el-table>

                                <div style="border: 1px solid #ebeef5;">
                                    <el-pagination background @current-change="handleCurrentChange"
                                        @size-change="handleSizeChange" :pager-count="5" :current-page="page"
                                        :page-sizes="[5, 10, 20, 30]" :page-size="pageSize"
                                        layout="total, prev, pager, sizes,next, jumper" :total="total">
                                    </el-pagination>
                                </div>
                            </div>

                            <!-- 模板下载 -->
                            <el-dialog title="开始导入" class="scbg-dialog" width="600px" @close="mbxzgb"
                                :visible.sync="dr_dialog" show-close>
                                <div style="padding: 20px;">
                                    <div class="daochu">
                                        <div>一、请点击“导出模板”，并参照模板填写信息。</div>
                                        <el-button type="primary" size="mini" @click="mbdc">
                                            模板导出
                                        </el-button>
                                    </div>
                                    <div class="daochu">
                                        <div>二、数据导入方式：</div>
                                        <el-radio-group v-model="sjdrfs" @change="Radio($event)">
                                            <el-radio label="1">追加（导入时已有的记录信息不变，只添加新的记录）</el-radio>
                                            <el-radio label="2">覆盖（导入时更新已有的记录信息，并添加新的记录）</el-radio>
                                        </el-radio-group>
                                    </div>
                                    <div class="daochu">
                                        <div>三、将按模板填写的文件，导入到系统中。</div>
                                        <el-button type="primary" size="mini" @click="chooseFile">
                                            上传导入
                                        </el-button>
                                    </div>
                                </div>
                            </el-dialog>

                            <!-- -----------------导入-弹窗--------------------------- -->
                            <el-dialog width="1000px" height="800px" title="导入国家秘密事项信息" class="scbg-dialog"
                                :visible.sync="dialogVisible_dr" show-close>
                                <div style="height: 600px;">
                                    <el-table :data="dr_cyz_list" ref="multipleTable"
                                        @selection-change="handleSelectionChange"
                                        style="width: 100%;border:1px solid #EBEEF5;" height="100%" stripe>
                                        <el-table-column type="selection" width="55"> </el-table-column>
                                        <el-table-column prop="国家秘密事项名称" label="国家秘密事项名称"></el-table-column>
                                        <el-table-column prop="密级" label="密级"></el-table-column>
                                        <el-table-column prop="保密期限" label="保密期限"></el-table-column>
                                        <el-table-column prop="知悉范围" label="知悉范围"></el-table-column>
                                        <el-table-column prop="定密依据" label="定密依据"></el-table-column>
                                        <el-table-column prop="备注" label="备注"></el-table-column>
                                    </el-table>
                                </div>

                                <div
                                    style="height :30px;display: flex;align-items: center;justify-content: center;margin: 10px 0;">
                                    <el-button type="primary" @click="drcy" size="mini">导 入</el-button>
                                    <el-button type="warning" @click="dialogVisible_dr = false" size="mini">关 闭
                                    </el-button>
                                </div>
                            </el-dialog>

                            <el-dialog title="新增国家秘密事项信息" :close-on-click-modal="false" :visible.sync="dialogVisible"
                                width="47%" class="xg" :before-close="handleClose" @close="close('formName')">
                                <el-form ref="formName" :model="tjlist" :rules="rules" label-width="180px" size="mini">
                                    <div style="display:flex">
                                        <el-form-item label="年度" prop="nd" label-width="80px">
                                            <el-input oninput="value=value.replace(/[^\d.]/g,'')"  @blur="nd=$event.target.value"
                                                placeholder="年度" v-model="tjlist.nd" clearable>
                                            </el-input>
                                        </el-form-item>
                                        <el-form-item label="国家秘密事项名称" prop="gjmmsxmc">
                                            <el-input placeholder="国家秘密事项名称" v-model="tjlist.gjmmsxmc" clearable>
                                            </el-input>
                                        </el-form-item>
                                    </div>
                                    <div style="display:flex">
                                        <el-form-item label="密级" prop="mj" label-width="80px">
                                            <!-- <el-input placeholder="密级" v-model="tjlist.mj" clearable></el-input> -->
                                            <el-select v-model="tjlist.mj" clearable placeholder="请选择类型">
                                                <el-option v-for="item in dmgjmj" :label="item.dmmjmc"
                                                    :value="item.dmmjmc" :key="item.dmmjid"></el-option>
                                            </el-select>
                                        </el-form-item>
                                        <el-form-item label="保密期限" prop="bmqx">
                                            <el-input placeholder="保密期限" v-model.number="tjlist.bmqx" oninput="value=value.replace(/[^\d.]/g,'')"  @blur="bmqx=$event.target.value" clearable></el-input>
                                        </el-form-item>
                                    </div>
                                    <div style="display:flex">
                                        <el-form-item label="知悉范围" prop="zxfw" label-width="80px">
                                            <el-input placeholder="知悉范围" v-model="tjlist.zxfw" clearable></el-input>
                                        </el-form-item>
                                        <el-form-item label="定密依据" prop="dmyj">
                                            <el-input placeholder="定密依据" v-model="tjlist.dmyj" clearable></el-input>
                                        </el-form-item>
                                    </div>
                                    <el-form-item label="备注" prop="bz" label-width="80px">
                                        <el-input type="textarea" v-model="tjlist.bz"></el-input>
                                    </el-form-item>
                                </el-form>
                                <span slot="footer" class="dialog-footer">
                                    <el-button type="primary" @click="submitTj('formName')">保 存</el-button>
                                    <el-button type="warning" @click="dialogVisible = false">关 闭</el-button>
                                </span>
                            </el-dialog>

                            <el-dialog title="修改国家秘密事项信息" :close-on-click-modal="false" :visible.sync="xgdialogVisible"
                                width="50%" class="xg" @close="close1('form')">
                                <el-form ref="form" :model="xglist" :rules="rules" label-width="180px" size="mini">
                                    <div style="display:flex">
                                        <el-form-item label="年度" prop="nd" label-width="80px">
                                            <el-input oninput="value=value.replace(/[^\d.]/g,'')"  @blur="nd=$event.target.value"
                                                placeholder="年度" v-model="xglist.nd" clearable>
                                            </el-input>
                                        </el-form-item>
                                        <el-form-item label="国家秘密事项名称" prop="gjmmsxmc">
                                            <el-input placeholder="国家秘密事项名称" v-model="xglist.gjmmsxmc" clearable>
                                            </el-input>
                                        </el-form-item>
                                    </div>
                                    <div style="display:flex">
                                        <el-form-item label="密级" prop="mj" label-width="80px">
                                            <!-- <el-input placeholder="密级" v-model="xglist.mj" clearable></el-input> -->
                                            <el-select v-model="xglist.mj" clearable placeholder="请选择类型">
                                                <el-option v-for="item in dmgjmj" :label="item.dmmjmc"
                                                    :value="item.dmmjmc" :key="item.dmmjid"></el-option>
                                            </el-select>
                                        </el-form-item>
                                        <el-form-item label="保密期限" prop="bmqx">
                                            <el-input placeholder="保密期限" v-model.number="xglist.bmqx" oninput="value=value.replace(/[^\d.]/g,'')"  @blur="bmqx=$event.target.value" clearable></el-input>
                                        </el-form-item>
                                    </div>
                                    <div style="display:flex">
                                        <el-form-item label="知悉范围" prop="zxfw" label-width="80px">
                                            <el-input placeholder="知悉范围" v-model="xglist.zxfw" clearable></el-input>
                                        </el-form-item>
                                        <el-form-item label="定密依据" prop="dmyj">
                                            <el-input placeholder="定密依据" v-model="xglist.dmyj" clearable></el-input>
                                        </el-form-item>
                                    </div>
                                    <el-form-item label="备注" prop="bz" label-width="80px">
                                        <el-input type="textarea" v-model="xglist.bz"></el-input>
                                    </el-form-item>
                                </el-form>
                                <span slot="footer" class="dialog-footer">
                                    <el-button type="primary" @click="updataDialog('form')">修 改</el-button>
                                    <el-button type="warning" @click="xgdialogVisible = false">关 闭</el-button>
                                </span>
                            </el-dialog>

                            <!-- 详情 -->
                            <el-dialog title="国家秘密事项信息详情" :close-on-click-modal="false" :visible.sync="xqdialogVisible"
                                width="50%" class="xg">
                                <el-form ref="form" :model="xglist" label-width="120px" size="mini" disabled>
                                    <div style="display:flex">
                                        <el-form-item label="年度" prop="nd">
                                            <el-input oninput="value=value.replace(/[^\d.]/g,'')" type="number"
                                                placeholder="年度" v-model="xglist.nd" clearable>
                                            </el-input>
                                        </el-form-item>
                                        <el-form-item label="国家秘密事项名称" prop="gjmmsxmc">
                                            <el-input placeholder="国家秘密事项名称" v-model="xglist.gjmmsxmc" clearable>
                                            </el-input>
                                        </el-form-item>
                                    </div>
                                    <div style="display:flex">
                                        <el-form-item label="密级" prop="mj">
                                            <!-- <el-input placeholder="密级" v-model="xglist.mj" clearable></el-input> -->
                                            <el-select v-model="xglist.mj" clearable placeholder="请选择类型">
                                                <el-option v-for="item in dmgjmj" :label="item.dmmjmc"
                                                    :value="item.dmmjmc" :key="item.dmmjid"></el-option>
                                            </el-select>
                                        </el-form-item>
                                        <el-form-item label="保密期限" prop="bmqx">
                                            <el-input placeholder="保密期限" v-model.number="xglist.bmqx" clearable></el-input>
                                        </el-form-item>
                                    </div>
                                    <div style="display:flex">
                                        <el-form-item label="知悉范围" prop="zxfw">
                                            <el-input placeholder="知悉范围" v-model="xglist.zxfw" clearable></el-input>
                                        </el-form-item>
                                        <el-form-item label="定密依据" prop="dmyj">
                                            <el-input placeholder="定密依据" v-model="xglist.dmyj" clearable></el-input>
                                        </el-form-item>
                                    </div>
                                    <el-form-item label="备注" prop="bz" class="one-line-textarea">
                                        <el-input type="textarea" v-model="xglist.bz"></el-input>
                                    </el-form-item>
                                </el-form>
                                <span slot="footer" class="dialog-footer">
                                    <el-button type="warning" @click="xqdialogVisible = false">关 闭</el-button>
                                </span>
                            </el-dialog>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {
    getlogin
} from "../../../db/loginyhdb";
import {
	getGjmmsx
} from "../../../db/lstzdb";
import {
    //内容管理初始化成员列表
    // getGjmmsx,
    //添加内容管理
    addGjmmsx,
    //删除内容管理
    deleteGjmmsx,
    //修改功能
    reviseGjmmsx,
    //左侧table初始化
    getGjmmsxLeft,
    //添加文件名
    addGjmmLeftsx
} from "../../../db/gjmmsxdb.js";
import {
    getdmmj
} from "../../../db/xzdb"
import {
    exportExcel
} from "../../../utils/exportExcel"; //excel导出工具
import {
    getFileSavePath,
    getFileNameByDirectory
} from "../../../utils/pathUtil"

// 异常处理工具
import { errorProcessor } from '../../../utils/errorProcessor'
import {
    getUuid
} from "../../../utils/getUuid"; //获取uuid
import XLSX from "xlsx";
export default ({
    components: {},
    props: {},
    data() {
        return {
            dmgjmj: [],
            gjmmsxList: [],
            gjmmsxLeftList: [],
            formInline: {},
            xglist: {},
            updateItemOld: {},
            xgdialogVisible: false,
            xqdialogVisible: false,
            tjlist: {
                nd: new Date().getFullYear().toString(),
                gjmmsxmc: '',
                mj: '',
                bmqx: '',
                zxfw: '',
                dmyj: '',
                bz: ''
            },
            page: 1,
            pageSize: 10,
            total: 0,
			yearSelect: [],
            selectlistRow: [], //列表的值
            dialogVisible: false, //添加弹窗状态
            dialogVisible_dr: false, //导入成员组弹窗状态
            dr_cyz_list: [], //待选择导入成员组列表
            multipleTable: [], //已选择导入成员组列表
            tableDataCopy: [],
            regionOption: [], //地域信息
            regionParams: {
                label: 'label', //这里可以配置你们后端返回的属性
                value: 'label',
                children: 'childrenRegionVo',
                expandTrigger: 'click',
                checkStrictly: true,
            }, //地域信息配置参数
            dwmc: '',
            dwdm: '',
            dwlxr: '',
            dwlxdh: '',
            year: '',
            yue: '',
            ri: '',
            Date: '',
            wjm: '',
            xh: [],
            dclist: [],
			tableDataCopy: [],
            //表单验证
            rules: {
                nd: [{
                    required: true,
                    message: '请输入年度',
                    trigger: 'blur'
                },],
                gjmmsxmc: [{
                    required: true,
                    message: '请输入国家秘密事项名称',
                    trigger: 'blur'
                },],
                mj: [{
                    required: true,
                    message: '请选择密级',
                    trigger: 'blur'
                },],
                bmqx: [{
                    required: true,
                    message: '请输入保密期限',
                    type:'number',
                    trigger: 'blur'
                },],
                zxfw: [{
                    required: true,
                    message: '请输入知悉范围',
                    trigger: 'blur'
                },],
                dmyj: [{
                    required: true,
                    message: '请输入定密依据',
                    trigger: 'blur'
                },],
            },
            nd: '',
            dr_dialog: false,
            //数据导入方式
            sjdrfs: ''
        }
    },
    computed: {},
    mounted() {
        this.dmgjmj = getdmmj()
        this.gjleftsx()
        this.gjmmsx()
        this.haclick(this.row = getGjmmsxLeft()[0])

        this.dwmc = getlogin()[0].dwmc
        this.dwdm = getlogin()[0].xydm
        this.dwlxr = getlogin()[0].dwlxr
        this.dwlxdh = getlogin()[0].dwlxdh
        let date = new Date()
        this.year = date.getFullYear() + '年'; //获取完整的年份(4位)
        this.yue = (date.getMonth()+1) + '月'; //获取当前月份(0-11,0代表1月)
        this.ri = date.getDate() + '日'; //获取当前日(1-31)
        this.Date = this.year + this.yue + this.ri
		//获取最近十年的年份
		let yearArr = []
		for (let i = new Date().getFullYear(); i > new Date().getFullYear() - 10; i--) {
			yearArr.push(
				{
					label: i.toString(),
					value: i.toString()
				})
		}
		yearArr.unshift({
			label: "全部",
			value: ""
		})
		this.yearSelect = yearArr
        // this.$refs.upload.addEventListener('change', e => { //绑定监听表格导入事件
        //     this.readExcel(e);
        // })
    },
    methods: {
        Radio(val) {
            this.sjdrfs = val
            console.log("当前选中的值", val)
        },
        mbxzgb() {
            this.sjdrfs = ''
        },
        mbdc() {
            console.log("----导出涉密人员----")
            // console.log(this.selectlistRow);
            // if (this.selectlistRow.length > 0) {
            let filename = "国家秘密事项一览表（细目）模板" + getUuid() + ".xlsx"

            const {
                dialog
            } = require('electron').remote;
            //弹窗title
            let options = {
                title: "保存文件",
                defaultPath: filename,
            };
            console.log(dialog)
            //导出文件夹选择弹窗
            dialog.showSaveDialog(options, result => {
                console.log('result', result)
                if (result == null || result == "") {
                    console.log("取消导出")
                    return
                }
                let list = []

                //对导出的数据进行格式，js导出excel要求传入的参数格式为[["列1名称","列2名称","列3名称"],["列11","值21","值31"],["列12","值22","值32"]]
                list.push(["序号", "国家秘密事项名称", "密级", "保密期限",
                    "知悉范围", "定密依据", "备注",]) //确定列名

                exportExcel(result, list) //list 要求为二维数组
                this.dr_dialog = false
                this.$message('导出成功:' + result)
            })
        },
        //----成员组选择
        handleSelectionChange(val) {
            this.multipleTable = val
            console.log("选中：", this.multipleTable);
        },
        //---确定导入成员组
        drcy() {
            //遍历已选择导入的成员，进行格式化，然后添加到数据库
            for (var i in this.multipleTable) {
                var cy = {
                    gjmmsxmc: this.multipleTable[i]["国家秘密事项名称"],
                    mj: this.multipleTable[i]["密级"],
                    bmqx: this.multipleTable[i]["保密期限"],
                    zxfw: this.multipleTable[i]["知悉范围"],
                    dmyj: this.multipleTable[i]["定密依据"],
                    bz: this.multipleTable[i]["备注"],
                    gjmmsxid: getUuid()
                }
                addGjmmsx(cy)
            }
            this.dialogVisible_dr = false
            this.gjmmsx()
        },
        //----表格导入方法
        readExcel(e) {
            var that = this;
            const files = e.target.files;
            console.log("files", files);
            var vali = /\.(xls|xlsx)$/
            if (files.length <= 0) { //如果没有文件名
                return false;
            } else if (!vali.test(files[0].name.toLowerCase())) {
                this.$Message.error('上传格式不正确，请上传xls或者xlsx格式');
                return false;
            }
            const fileReader = new FileReader();
            fileReader.onload = (e) => {
                try {
                    const data = e.target.result;
                    const workdata = XLSX.read(data, {
                        type: 'binary'
                    });
                    console.log("文件的内容：", workdata) // 文件的内容
                    //查看打印能看到 workdata的sheetNames里是文件中每个表的有值的集合；
                    const wsname = workdata.SheetNames[0]; //取第一张表
                    console.log('wsname', wsname)
                    const ws = XLSX.utils.sheet_to_json(workdata.Sheets[wsname]); //生成json表格内容
                    console.log(ws); //自第二行开始的内容
                    this.dialogVisible_dr = true
                    this.dr_cyz_list = ws
                    console.log("列表的值:", this.dr_cyz_list)
                    // 加工excel读取业务类型为数组
                    // this.dr_cyz_list.forEach(function(item) {
                    // 	console.log(item[0]['业务类型'].splite(','))
                    // })
                    this.$refs.upload.value = ''; // 处理完成 清空表单值
                    this.dr_dialog = false
                } catch (e) {
                    return false;
                }
            };
            fileReader.readAsBinaryString(files[0]);
        },
        //行点击
        haclick(row) {
            if (row != undefined) {
                console.log("点击行", row);
                this.nd = row.nd
                // console.log("this.row", this.row.nd);
                this.gjmmsx()
            }
        },
        //初始化成员列表
        gjmmsx() {
            let params = {
                page: this.page,
                pageSize: this.pageSize,
                nd: this.nd
            }
            Object.assign(params, this.formInline)
            let resList = getGjmmsx(params)
            console.log("params", params);

            this.gjmmsxList = resList.list
			this.tableDataCopy = resList.list
            this.dclist = resList.list_total
            this.dclist.forEach((item, label) => {
                this.xh.push(label + 1)
            })
            // this.gjleftsx()
            this.total = resList.total
        },
        gjleftsx() {
            this.gjmmsxLeftList = getGjmmsxLeft()
            // this.gjmmsx()
            this.nd = this.gjmmsxLeftList[0].nd
            console.log("this.nd",this.nd,"=============================");
        },
        //新增
        submitTj(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    let params = {
                        nd: this.tjlist.nd,
                        gjmmsxmc: this.tjlist.gjmmsxmc,
                        mj: this.tjlist.mj,
                        bmqx: this.tjlist.bmqx,
                        zxfw: this.tjlist.zxfw,
                        dmyj: this.tjlist.dmyj,
                        bz: this.tjlist.bz,
                        gjmmsxid: getUuid()
                    }
                    addGjmmsx(params)
                    this.dialogVisible = false
                    this.$message({
                        message: '添加成功',
                        type: 'success'
                    });
                    // this.resetForm()
                    this.gjleftsx()
                    this.gjmmsx()
                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
        },
        //修改
        updataDialog(form) {
            this.$refs[form].validate((valid) => {
                if (valid) {
                    //删除旧的
                    // deletedmzrr(this.updateItemOld)
                    // 插入新的
                    reviseGjmmsx(this.xglist)
                    // 刷新页面表格数据
                    this.gjmmsx()
                    this.gjleftsx()
                    // 关闭dialog
                    this.$message.success('修改成功')
                    this.xgdialogVisible = false
                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
        },
        cz() {
			this.formInline = {}
		},
        xqyl(row) {
            this.updateItemOld = JSON.parse(JSON.stringify(row))
            this.xglist = JSON.parse(JSON.stringify(row))
            console.log('old', row)
            console.log("this.xglist.ywlx", this.xglist);
            this.xqdialogVisible = true
        },

        updateItem(row) {
            this.updateItemOld = JSON.parse(JSON.stringify(row))
            this.xglist = JSON.parse(JSON.stringify(row))
            console.log('old', row)
            console.log("this.xglist.ywlx", this.xglist);
            this.xgdialogVisible = true
        },
        //导入
        chooseFile() {
            if (this.sjdrfs != '') {
                if (this.sjdrfs == 1) {
                    this.$refs.upload.click()
                }
                else if (this.sjdrfs == 2) {
                    let valArr = this.dclist
                    valArr.forEach(function (item) {
                        deleteGjmmsx(item)
                    })
                    this.$refs.upload.click()
                }
            } else {
                this.$message.warning('请选择导入方式')
            }
        },
        // 导出
        exportList() {
            let filename = "国家秘密事项一览表（细目）历史台账" + getUuid() + ".xlsx"

            const {
                dialog
            } = require('electron').remote;
            //弹窗title
            let options = {
                title: "保存文件",
                defaultPath: filename,
            };
            console.log(dialog)
            //导出文件夹选择弹窗
            dialog.showSaveDialog(options, result => {
                console.log('result', result)
                if (result == null || result == "") {
                    console.log("取消导出")
                    return
                }
                let list = []
                list.push(["国家秘密事项一览表（细目）"])

                list.push(["填报单位:", this.dwmc, "单位代码:", this.dwdm, "","",
                    "年度:", this.nd])
                list.push(["附件名:",this.wjm,"","","","","","",])
                //对导出的数据进行格式，js导出excel要求传入的参数格式为[["列1名称","列2名称","列3名称"],["列11","值21","值31"],["列12","值22","值32"]]
                list.push(["序号", "台账时间", "国家秘密事项名称", "密级", "保密期限",
                    "知悉范围", "定密依据", "备注",]) //确定列名

                for (var i in this.dclist) { //每一行的值
                    let item = this.dclist[i] //{"name":"XXX","dw":"XXX","zw":"XXX"}

                    console.log("导出值:", this.dclist);
                    let column = [(parseInt(i) + 1), item["tzsj"], item["gjmmsxmc"],
                    item["mj"], item["bmqx"],
                    item["zxfw"], item["dmyj"], item["bz"]
                    ]
                    list.push(column)
                }
                list.push(["填报人:", this.dwlxr, "联系方式:", this.dwlxdh, "", "",
                    "填报时间:", this.Date])
                let merges = [{
                    s: { //s为开始
                        c: 0, //开始列
                        r: 0 //开始取值范围
                    },
                    e: { //e结束
                        c: 7, //结束列
                        r: 0 //结束范围
                    }
                }]
                let styles = {
					// 列样式
					cols: {
						// 作用sheet页索引（0开始）（-1全sheet页生效）
						scoped: -1,
						style: [
							{ wpx: 100 },
							{ wpx: 100 },
							{ wpx: 150 },
							{ wpx: 100 },
							{ wpx: 100 },
							{ wpx: 100 },
							{ wpx: 120 },
							{ wpx: 120 },
						]
					},
					// 全局样式
					all: {
						alignment: {
							horizontal: 'center', // 水平居中
							vertical: 'center' // 垂直居中
						},
						font: {
							sz: 11, // 字号
							name: '宋体' // 字体
						},
						border: {  // 边框
							top: {
								style: 'thin'
							},
							bottom: {
								style: 'thin'
							},
							left: {
								style: 'thin'
							},
							right: {
								style: 'thin'
							}
						}
					},
					// 单元格样式
					cell: [
						{
							// 生效sheet页索引（值为 -1 时所有sheet页都生效）
							scoped: -1,
							// 索引
							index: 'A1',
							style: {
								font: {
									name: '宋体',
									sz: 16, // 字号
									bold: true,
								},
								alignment: {
									horizontal: 'center', // 水平居中
									vertical: 'center' // 垂直居中
								}
							}
						}
					]
				}
				exportExcel(result, list, merges, styles) //list 要求为二维数组
                this.$message('导出成功:' + result)
            })
        },
        //删除
        shanchu(id) {
            if (this.selectlistRow != '') {
                this.$confirm('是否继续删除?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    let valArr = this.selectlistRow
                    // console.log("....", val);
                    valArr.forEach(function (item) {
                        deleteGjmmsx(item)
                        console.log("删除：", item);
                        console.log("删除：", item);
                    })
                    let params = valArr
                    this.$message({
                        message: '删除成功',
                        type: 'success'
                    });
                    this.gjmmsx()
                }).catch(() => {
                    this.$message('已取消删除')
                })
            } else {
                this.$message({
                    message: '未选择删除记录，请选择下列列表',
                    type: 'warning'
                });
            }
        },
        //查询
        onSubmit() {
            //  form是查询条件
			console.log(this.formInline);
			// 备份了一下数据
			let arr = this.tableDataCopy
			// 通过遍历key值来循环处理
			Object.keys(this.formInline).forEach(e => {
				// 调用自己定义好的筛选方法
				console.log(this.formInline[e]);
				arr = this.filterFunc(this.formInline[e], e, arr)
			})
			// 为表格赋值
			this.gjmmsxList = arr
            // this.gjmmsx()
        },
		filterFunc(val, target, filterArr) {
			// 参数不存在或为空时，就相当于查询全部
			if (val == undefined || val == '') {
				return filterArr
			}
			return filterArr.filter(p => {
				return p[target].indexOf(val.toString().replace(',', '/')) > -1
				// return bool
			}) // 可以自己加一个.toLowerCase()来兼容一下大小
		},

        selectRow(val) {
            console.log(val);
            this.selectlistRow = val;
        },

        //列表分页--跳转页数
        handleCurrentChange(val) {
            this.page = val
            this.gjmmsx()
        },
        //列表分页--更改每页显示个数
        handleSizeChange(val) {
            this.page = 1
            this.pageSize = val
            this.gjmmsx()
        },

        /**
     * 下载附件
    */
        downloadFj(row) {
            // 获取附件存储路径(sjcwjm字段为真实存储的文件名)
            if (!row) {
                this.$message.warning('下载文件失败，参数为空')
                return
            }
            let sjcwjm = row.sjcwjm
            if (!sjcwjm) {
                this.$message.warning('下载文件失败，附件真实名称为空')
                return
            }
            let wjm = row.wjm
            if (!wjm) {
                this.$message.warning('下载文件失败，附件默认文件名为空')
                return
            }
            let fromPath = getFileSavePath() + sjcwjm
            // 获取文件流
            const FS = require('fs')
            const { dialog } = require('electron').remote
            let options = {
                title: '请选择文件保存路径',
                properties: ['openDirectory'],
                defaultPath: wjm
            }
            console.log('fromPath', fromPath)
            dialog.showSaveDialog(options, result => {
                console.log('savePath', result)
                try {
                    let buffer = FS.readFileSync(fromPath)
                    console.log('buffer', buffer)
                    if (buffer) {
                        // 保存文件到新路径下
                        FS.writeFileSync(result, buffer)
                    }
                    this.$message.success('文件[' + wjm + ']下载成功')
                } catch (error) {
                    error = errorProcessor(error)
                    let errObj = JSON.parse(error.message)
                    this.$notify({
                        title: '系统异常',
                        message: '[' + errObj.mark + ']\n',
                        type: 'error',
                        offset: 100,
                        duration: 0
                    })
                    return
                }
            })
        },

        //上传文件
        uploadSC() {
            console.log("----上传保密制度附件----")
            const fs = require("fs")
            const {
                dialog
            } = require("electron").remote

            let options = {
                title: "请选择保密制度需上传文件",
                properties: ['openFile']
            }

            // dialog.showSaveDialog(options, result => {
            dialog.showOpenDialog(options, result => {
                console.log('result', result)
                // 路径校验，判断是一个路径还是一个文件，这里默认只能是文件路径，不能是目录路径
                if (!result) {
                    this.$message.warning('非法路径:' + result)
                    return
                }
                result = result[0]
                // 判断是否是目录的路径(正则判断路径是否正确)
                // 获取用户上传附件的后缀名
                let suffix = result.substring(result.lastIndexOf('.') + 1)
                console.log('suffix', suffix)
                if (!suffix) {
                    this.$message.warning('请选择正确文件路径:' + result)
                    return
                }
                // 判断文件是否存在
                if (!fs.existsSync(result)) {
                    this.$message.warning('请确认保密制度上传的附件是否已被删除?' + result)
                    return
                }
                // 准备存储文件到指定路径(这里进行了路径的自动创建)
                let saveToPath
                try {
                    saveToPath = getFileSavePath()
                } catch (error) {
                    let errObj = JSON.parse(error.message)
                    this.$notify({
                        title: '系统异常',
                        message: '[' + errObj.mark + ']\n' + '当前用户权限不够\n' + errObj.solvtion,
                        type: 'error',
                        offset: 100,
                        duration: 0
                    })
                    return
                }
                console.log('saveToPath', saveToPath)
                // 校验指定路径是否存在，不存在则新建
                if (!fs.existsSync(saveToPath)) {
                    // 新建存储路径
                    console.log('新建存储路径', saveToPath)
                    fs.mkdirSync(saveToPath)
                    console.log('新建存储路径完成', saveToPath)
                }
                // 存储用户选择的上传文件到系统指定目录下（即saveToPath，备注：不带文件名的）
                // 文件名（这里是源文件的文件名，也是用户期望显示的文件名，用来与数据库里真实存储的文件名区分，下载时使用真实文件名进行下载）
                let fileName = getFileNameByDirectory(result)
                // 拼接上时间戳
                saveToPath += '国家秘密事项上传附件-' + (new Date().getTime()) + '-' + fileName
                //
                let ljwjm = '国家秘密事项上传附件-' + (new Date().getTime()) + '-' + fileName
                console.log('saveToPath', saveToPath)
                console.log('ljwjm', ljwjm)
                this.tjlist.fromFjPath = result
                this.tjlist.saveToPath = saveToPath
                this.wjm = fileName
                console.log("this.wjm", this.wjm);
                let params = {
                    nd: this.nd,
                    wjm: this.wjm,
                    sjcwjm: ljwjm
                }
                addGjmmLeftsx(params)
                console.log("传入文件名", params);
                this.saveFj(this.tjlist.fromFjPath, this.tjlist.saveToPath)
                this.gjleftsx()
            })
        },
        // 文件保存
        saveFj(fromPath, saveToPath) {
            console.log('fromPath', fromPath, 'saveToPath', saveToPath)
            if (!fromPath || fromPath == '') {
                this.$message.warning('原文件路径为空')
                return
            }
            if (!saveToPath || saveToPath == '') {
                this.$message.warning('上传存储路径为空')
                return
            }
            const fs = require("fs")
            // 保存
            try {
                fs.writeFileSync(saveToPath, fs.readFileSync(fromPath))
                this.$message.success('附件上传成功')
            } catch (error) {
                console.log('error', error)
                this.$message.error('附件上传失败，' + error.message)
            }
        },
        handleClose(done) {
            // this.resetForm()
            this.dialogVisible = false
        },

        // 弹框关闭触发
        close(formName) {
            // 清空表单校验，避免再次进来会出现上次校验的记录
            this.$refs[formName].resetFields();
        },
        close1(form) {
            // 清空表单校验，避免再次进来会出现上次校验的记录
            this.$refs[form].resetFields();
        },
    },
})
</script>

<style scoped>
.dabg {
    box-shadow: 0px 2px 12px 0px rgba(9, 70, 255, 0.13);
    border-radius: 8px;
    width: 100%;
}

.mhcx {
    width: 100%;
    height: 6.5vh;
}

.daochu {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.table_content_padding {
    display: flex;
    align-items: center;
}

.table_left {
    width: 29%;
}

.table_content {
    width: 69%;
    margin-left: 2%;
}

.widths {
    width: 6vw;
}

.widthx {
    width: 8vw;
}

.cd {
    width: 184px;
}
.mhcx :deep(.el-form-item) {
    /* margin-top: 5px; */
    margin-bottom: 5px;

}
</style>
