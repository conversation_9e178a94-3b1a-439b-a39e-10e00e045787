<template>
  <div style="height: calc(100% - 32px);width: 100%;">
    <!-- 检索条件区域 -->
    <div class="mhcx">
      <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline" style="float:left">
        <el-form-item label="检查时间" style="font-weight: 700;">
          <el-date-picker v-model="formInline.cxsj" size="" type="daterange" :default-time="['00:00:00', '23:59:59']" value-format="timestamp" :editable="false" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="getZczpLsxx">查询</el-button>
        </el-form-item>
      </el-form>
      <!-- <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline" style="float:right">
        <el-form-item style="float: right;">
          <el-button type="danger" size="medium" @click="deleteTkglBtn" icon="el-icon-delete-solid">删除
          </el-button>
        </el-form-item>
      </el-form> -->
    </div>
    <!-- 表格区域 -->
    <div style="height: calc(100% - 34px - 20px);">
      <el-table :data="scList" border stripe @selection-change="selectRow" :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" style="width: 100%;border:1px solid #EBEEF5;" height="calc(100% - 34px - 10px)" stripe>
        <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
        <el-table-column prop="rwmc" label="任务名称"></el-table-column>
        <el-table-column prop="jcjdmc" label="检查季度"></el-table-column>
        <el-table-column prop="" label="自查自评" width="100">
          <template slot-scope="scoped">
            <el-button size="small" type="text" @click="toXxxx(scoped.row.rwid)">详细信息</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="pjjg" label="评价结果" width="100">
          <template slot-scope="scoped">
            <div>
              <span v-if="scoped.row.pjjg == 1">优秀</span>
              <span v-if="scoped.row.pjjg == 2">合格</span>
              <span v-if="scoped.row.pjjg == 3">基本合格</span>
              <span v-if="scoped.row.pjjg == 4">不合格</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="" label="检查开始时间" width="120">
          <template slot-scope="scoped">
            <span>{{dateFormatNYRChinese(scoped.row.kssj)}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="" label="检查截止时间" width="120">
          <template slot-scope="scoped">
            <span>{{dateFormatNYRChinese(scoped.row.jzsj)}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="djr" label="登记人" width="120"></el-table-column>
        <el-table-column prop="" label="操作" width="120">
          <template slot-scope="scoped">
            <el-button size="small" type="text" @click="downloadJCZip(scoped.row)">下载</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 分页组件区域 -->
    <div style="border: 1px solid #ebeef5">
      <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange" :pager-count="5" :current-page="pageInfo.page" :page-sizes="[5, 10, 20, 30]" :page-size="pageInfo.pageSize" layout="total, prev, pager, sizes,next, jumper" :total="pageInfo.total">
      </el-pagination>
    </div>
    <!---->
  </div>
</template>

<script>

import { dateFormatNYRChinese } from '../../../utils/moment'

import { setZczpIdsObj } from '../../../utils/windowLocation'

import { getDirectory } from '../../../utils/pathUtil'

import {
  selectScrwZczplsPage,
  // 通过任务ID获取单位评分记录
  selectDwpfjlListByRwid,
  // 通过任务ID获取抽查的内设机构集合
  selectCcdnsjgListByRwid,
  // 通过[抽查内设机构流水ID]获取内设机构评分记录历史信息
  selectNsjgpfjlListByCcdnsjgid,
  // 获取抽查的人员
  selectCcdryListPage,
  // 通过抽查的人员流水ID获取抽查的人员评分信息
  selectRypfjlListByCcdryid,
  // 通过任务ID获取任务信息
  selectScrwByRwid,
  // 通过任务ID获取单位评分记录(非组合)
  selectDwpfjlListByRwidFzh,
  // 通过任务ID获取抽查的内设机构抽查结果
  selectCcdsjgListCcjg,
  // 通过任务ID获取抽查的人员抽查结果
  selectCcdryListCcjg
} from '../../../db/zczpdb'

import {
  // 通过任务ID获取单位信息
  selectDwxxByRwid
} from '../../../db/dwxxDb'

import XLSX from "xlsx"

import { workbook2ArrayBuffer, exportExcelWorkbook, exportExcelNumerousSheetWorkbook } from '../../../utils/exportExcel'

import { exportWord } from '../../../utils/exportWord'

import { getDocZczpMbPath } from '../../../utils/pathUtil'

import { writeOptionsLog } from '../../../utils/logUtils'

export default {
  data () {
    return {
      // 查询条件
      formInline: {},
      // 分页信息
      pageInfo: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      // 表格数据
      scList: []
    }
  },
  computed: {},
  components: {
  },
  destroyed () {
    console.log('自查自评销毁了')
  },
  methods: {
    // 日期转换
    dateFormatNYRChinese (time) {
      let date = new Date(time)
      if ('Invalid Date' == date) {
        return date
      }
      return dateFormatNYRChinese(date)
    },
    // 行选择事件
    selectRow () { },
    // 下载
    downloadJCZip (row) {
      console.log('入参row', row)
      // console.log('入参row', row)
      if (!row) {
        this.$message.warning('检测到记录数据为空，请重试或联系管理员处理')
        return
      }
      if (!row.rwid) {
        this.$message.warning('检测到检测任务ID为空，请重试或联系管理员处理')
        return
      }
      //
      const fs = require('fs')
      // zip 文件名称
      let zipFileName = row.rwmc + '-' + row.jcjdmc + '-' + (new Date().getTime()) + '.zip'
      // 弹出dialog选择保存文件的位置
      const { dialog } = require("electron").remote
      let options = {
        title: "下载检查结果文件", //下载文件名称
        defaultPath: zipFileName //下载文件title
      }
      dialog.showSaveDialog(options, (result) => {
        // console.log(result)
        if (!result) {
          this.$message.warning('检查结果下载任务已取消')
          return
        }
        // 判断路径是否存在
        let path = getDirectory(result)
        // console.log('path', path)
        // 判断路径是否存在
        if (!fs.existsSync(path)) {
          this.$message.warning('所选路径不存在，请重新点击下载按钮进行下载')
          return
        }
        // 准备下载zip文件
        this.exportZip(result, row, zipFileName)
        // 写入操作日志
        writeOptionsLog('yybs-zczpls', '审查任务结果下载', row)
      })
    },
    // 导出zip
    async exportZip (savePath, zczpls, zipFileName) {
      // 准备生成zip
      var AdmZip = require("adm-zip")
      var zip = new AdmZip()
      // var content = "inner content of the file"
      // zip.addFile("test111111111.txt", Buffer.from(content, "utf8"), "")
      // 生成单位检查评分记录excel
      let dwJcjlExcelBuffer = this.generateDwJcjlExcel(zczpls)
      if (dwJcjlExcelBuffer) {
        zip.addFile(zczpls.rwmc + '-' + zczpls.dwmc + '-' + zczpls.jcjdmc + ".xlsx", dwJcjlExcelBuffer, "")
      }
      //生成内设机构检查评分记录excel
      let nsjgJcjlExcelBuffer = this.generateNsjgJcjlExcel(zczpls)
      if (nsjgJcjlExcelBuffer) {
        zip.addFile(zczpls.rwmc + '-' + zczpls.dwmc + '-' + zczpls.jcjdmc + '-' + '抽查的内设机构检查记录' + ".xlsx", nsjgJcjlExcelBuffer, "")
      }
      //生成人员检查评分记录excel
      try {
        let ryJcjlExcelBuffer = this.generateRyJcjlExcel(zczpls)
        if (ryJcjlExcelBuffer) {
          zip.addFile(zczpls.rwmc + '-' + zczpls.dwmc + '-' + zczpls.jcjdmc + '-' + '抽查的涉密人员检查记录' + ".xlsx", ryJcjlExcelBuffer, "")
        }
      } catch (error) {
        this.$message.error('[下载异常][生成人员检查记录表异常]' + error.message)
      }
      //生成检查报告word
      let jcbgWord = this.generateJcbgWord(zczpls)
      if (jcbgWord) {
        zip.addFile(zczpls.rwmc + '-' + zczpls.dwmc + '-' + zczpls.jcjdmc + '-' + '检查报告' + ".docx", jcbgWord, "")
      }
      zip.writeZip(savePath, (error) => {
        if (error) {
          this.$message.error('[下载异常]' + error.message)
          return
        }
        this.$message.success('检查结果[' + zipFileName + ']下载已完成')
      })
    },
    // 生成单位检查评分记录excel
    generateDwJcjlExcel (zczpls) {
      if (!zczpls || !zczpls.rwid) {
        this.$message.warning('[单位检查]检查任务信息异常，无法下载检查信息')
        return
      }
      //sheet页数据
      let list = []
      list.push(["检查类", "检查项", "检查内容", "分值", "实有内容", "得分", "扣分标准", "评分说明"])
      // 通过任务ID获取单位评分记录历史信息
      const dwxxzcjlList = selectDwpfjlListByRwid(zczpls.rwid)
      if (!dwxxzcjlList) {
        this.$message.warning('检查任务评分信息异常，无法下载检查信息')
        return
      }
      dwxxzcjlList.forEach(dx => {
        dx.xx.forEach(nr => {
          list.push([
            dx.dxmc, nr.xxmc, nr.nr, nr.fz, nr.synr, nr.fz - nr.ykf, nr.kfbz, nr.kfsm
          ])
        })
      })
      //Excel sheet页的名称
      // var sheet_name = "单位详细检查记录"
      // var sheet = XLSX.utils.aoa_to_sheet(list)
      // //新建book
      // var work_book = XLSX.utils.book_new()
      // //将数据添加到工作薄
      // XLSX.utils.book_append_sheet(work_book, sheet, sheet_name)
      // 单元格合并
      let merges = [
        { s: { c: 0, r: 1 }, e: { c: 0, r: 3 } },
        { s: { c: 0, r: 4 }, e: { c: 0, r: 5 } },
        { s: { c: 0, r: 6 }, e: { c: 0, r: 7 } },
        { s: { c: 0, r: 8 }, e: { c: 0, r: 13 } },
        { s: { c: 0, r: 14 }, e: { c: 0, r: 19 } },
        { s: { c: 0, r: 20 }, e: { c: 0, r: 21 } },
        { s: { c: 0, r: 22 }, e: { c: 0, r: 28 } },
        { s: { c: 0, r: 29 }, e: { c: 0, r: 38 } },
        { s: { c: 0, r: 39 }, e: { c: 0, r: 40 } },
        { s: { c: 0, r: 41 }, e: { c: 0, r: 43 } },
        { s: { c: 0, r: 45 }, e: { c: 0, r: 47 } },
        { s: { c: 0, r: 49 }, e: { c: 0, r: 50 } },
        { s: { c: 0, r: 51 }, e: { c: 0, r: 52 } },
        // col 2
        { s: { c: 1, r: 2 }, e: { c: 1, r: 3 } },
        { s: { c: 1, r: 8 }, e: { c: 1, r: 9 } },
        { s: { c: 1, r: 12 }, e: { c: 1, r: 13 } },
        { s: { c: 1, r: 14 }, e: { c: 1, r: 15 } },
        { s: { c: 1, r: 16 }, e: { c: 1, r: 18 } },
        { s: { c: 1, r: 24 }, e: { c: 1, r: 25 } },
        { s: { c: 1, r: 27 }, e: { c: 1, r: 28 } },
        { s: { c: 1, r: 29 }, e: { c: 1, r: 31 } },
        { s: { c: 1, r: 33 }, e: { c: 1, r: 34 } },
        { s: { c: 1, r: 37 }, e: { c: 1, r: 38 } },
      ]
      // 样式
      let styles = {
					// 列样式
					cols: {
						// 作用sheet页索引（0开始）（-1全sheet页生效）
						scoped: -1,
						style: [
							{ wpx: 200 },
							{ wpx: 200 },
							{ wpx: 350 },
							{ wpx: 100 },
							{ wpx: 320 },
							{ wpx: 100 },
							{ wpx: 320 },
							{ wpx: 250 },
						]
					},
					// 全局样式
					all: {
						alignment: {
							horizontal: 'center', // 水平居中
							vertical: 'center', // 垂直居中
              wrapText: true, // 文字换行
						},
						font: {
							sz: 11, // 字号
							name: '宋体' // 字体
						},
						border: {  // 边框
							top: {
								style: 'thin'
							},
							bottom: {
								style: 'thin'
							},
							left: {
								style: 'thin'
							},
							right: {
								style: 'thin'
							}
						}
					},
					// 单元格样式
					cell: [
						// {
						// 	// 生效sheet页索引（值为 -1 时所有sheet页都生效）
						// 	scoped: -1,
						// 	// 索引
						// 	index: 'A1',
						// 	style: {
						// 		font: {
						// 			name: '宋体',
						// 			sz: 16, // 字号
						// 			bold: true,
						// 		},
						// 		alignment: {
						// 			horizontal: 'center', // 水平居中
						// 			vertical: 'center' // 垂直居中
						// 		}
						// 	}
						// }
					]
				}
      // 配置
      let config
      let work_book = exportExcelWorkbook('单位详细检查记录', list, merges, styles, config)
      // console.log('work_book', work_book)
      const workbookArrayBuffer = workbook2ArrayBuffer(work_book)
      //arrayBuffer转Buffer
      var buf = new Buffer(workbookArrayBuffer.byteLength)
      var view = new Uint8Array(workbookArrayBuffer)
      for (var i = 0; i < buf.length; ++i) {
        buf[i] = view[i]
      }
      return buf
    },
    // 生成内设机构检查评分记录excel
    generateNsjgJcjlExcel (zczpls) {
      if (!zczpls || !zczpls.rwid) {
        this.$message.warning('[内设机构]检查任务信息异常，无法下载检查信息')
        return
      }
      // 获取所有抽查的内设机构
      let params = {
        rwid: zczpls.rwid
      }
      let ccdnsjgListPage = selectCcdnsjgListByRwid(params)
      let ccdnsjgList
      if (ccdnsjgListPage) {
        ccdnsjgList = ccdnsjgListPage.list_total
      }
      if (!ccdnsjgList) {
        this.$message.warning('[内设机构]抽查的内设机构信息异常，无法下载检查信息')
        return
      }
      let list
      let sheet_name
      let exportList = []
      // 单元格合并
      let merges = [
        { s: { c: 0, r: 2 }, e: { c: 0, r: 4 } },
        { s: { c: 0, r: 5 }, e: { c: 0, r: 8 } },
        { s: { c: 0, r: 9 }, e: { c: 0, r: 10 } },
        { s: { c: 0, r: 11 }, e: { c: 0, r: 17 } },
        { s: { c: 0, r: 18 }, e: { c: 0, r: 23 } },
        { s: { c: 0, r: 24 }, e: { c: 0, r: 26 } },
        { s: { c: 0, r: 27 }, e: { c: 0, r: 29 } },
        { s: { c: 0, r: 31 }, e: { c: 0, r: 34 } }
      ]
      // 样式
      let styles = {
					// 列样式
					cols: {
						// 作用sheet页索引（0开始）（-1全sheet页生效）
						scoped: -1,
						style: [
							{ wpx: 200 },
							{ wpx: 350 },
							{ wpx: 100 },
							{ wpx: 320 },
							{ wpx: 100 },
							{ wpx: 320 },
							{ wpx: 320 }
						]
					},
					// 全局样式
					all: {
						alignment: {
							horizontal: 'center', // 水平居中
							vertical: 'center', // 垂直居中
              wrapText: true, // 文字换行
						},
						font: {
							sz: 11, // 字号
							name: '宋体' // 字体
						},
						border: {  // 边框
							top: {
								style: 'thin'
							},
							bottom: {
								style: 'thin'
							},
							left: {
								style: 'thin'
							},
							right: {
								style: 'thin'
							}
						}
					},
					// 单元格样式
					cell: [
						// {
						// 	// 生效sheet页索引（值为 -1 时所有sheet页都生效）
						// 	scoped: -1,
						// 	// 索引
						// 	index: 'A1',
						// 	style: {
						// 		font: {
						// 			name: '宋体',
						// 			sz: 16, // 字号
						// 			bold: true,
						// 		},
						// 		alignment: {
						// 			horizontal: 'center', // 水平居中
						// 			vertical: 'center' // 垂直居中
						// 		}
						// 	}
						// }
					]
			}
      // 配置
      let config
      // let sheet
      // let work_book = XLSX.utils.book_new()
      // 判断是否全部数据都正确获取
      let allCheck = true
      let errorNsjgmc
      let zzjgmc
      ccdnsjgList.some(item => {
        list = []
        list.push(["检查类", "检查内容", "分值", "实有内容", "得分", "扣分标准", "评分说明"])
        zzjgmc = item.zzjgmc
        if (zzjgmc) {
          sheet_name = zzjgmc.replace(/[\\/?*]/g, '-')
          sheet_name = sheet_name.substring(sheet_name.lastIndexOf('-') + 1)
        } else {
          sheet_name = ''
        }
        // 获取内设机构评分记录信息
        let bmxxzcjlList = selectNsjgpfjlListByCcdnsjgid(item.ccdnsjgid)
        if (!bmxxzcjlList) {
          allCheck = false
          errorNsjgmc = item.nsjgmc
          return true
        }
        bmxxzcjlList.forEach(nsjgpfjlItem => {
          list.push([nsjgpfjlItem.dxmc, nsjgpfjlItem.nr, nsjgpfjlItem.fz, nsjgpfjlItem.synr, nsjgpfjlItem.fz - nsjgpfjlItem.ykf, nsjgpfjlItem.kfbz, nsjgpfjlItem.kfsm])
        })
        // // 将数据添加到sheet页
        // sheet = XLSX.utils.aoa_to_sheet(list)
        // //将数据添加到工作薄
        // XLSX.utils.book_append_sheet(work_book, sheet, sheet_name)
        exportList.push({
          sheetName: sheet_name,
          data: list,
          merges: merges
        })
      })
      if (!allCheck) {
        this.$message.error('[内设机构]' + errorNsjgmc + '数据异常，无法下载检查信息')
        return
      }
      ////////////
      // let work_book = exportExcelWorkbook(sheet_name, list, merges, styles, config)
      let work_book = exportExcelNumerousSheetWorkbook(exportList, styles, config)
      const workbookArrayBuffer = workbook2ArrayBuffer(work_book)
      //arrayBuffer转Buffer
      var buf = new Buffer(workbookArrayBuffer.byteLength)
      var view = new Uint8Array(workbookArrayBuffer)
      for (var i = 0; i < buf.length; ++i) {
        buf[i] = view[i]
      }
      return buf
    },
    // 生成人员检查评分记录excel
    generateRyJcjlExcel (zczpls) {
      if (!zczpls || !zczpls.rwid) {
        this.$message.warning('[涉密人员]检查任务信息异常，无法下载检查信息')
        return
      }
      // 获取所有抽查的涉密人员
      let params = {
        rwid: zczpls.rwid
      }
      let ccdnryListPage = selectCcdryListPage(params)
      let ccdryList
      if (ccdnryListPage) {
        ccdryList = ccdnryListPage.list_total
      }
      if (!ccdryList) {
        this.$message.warning('[涉密人员]抽查的涉密人员信息异常，无法下载检查信息')
        return
      }
      let list
      let sheet_name
      let exportList = []
      // 单元格合并(共用)
      let merges = [
        { s: { c: 0, r: 1 }, e: { c: 0, r: 4 } },
        { s: { c: 0, r: 5 }, e: { c: 0, r: 7 } },
        { s: { c: 0, r: 9 }, e: { c: 0, r: 12 } },
        { s: { c: 0, r: 13 }, e: { c: 0, r: 17 } }
      ]
      // 样式
      let styles = {
					// 列样式
					cols: {
						// 作用sheet页索引（0开始）（-1全sheet页生效）
						scoped: -1,
						style: [
							{ wpx: 200 },
							{ wpx: 350 },
							{ wpx: 100 },
							{ wpx: 320 }
						]
					},
					// 全局样式
					all: {
						alignment: {
							horizontal: 'center', // 水平居中
							vertical: 'center', // 垂直居中
              wrapText: true, // 文字换行
						},
						font: {
							sz: 11, // 字号
							name: '宋体' // 字体
						},
						border: {  // 边框
							top: {
								style: 'thin'
							},
							bottom: {
								style: 'thin'
							},
							left: {
								style: 'thin'
							},
							right: {
								style: 'thin'
							}
						}
					},
					// 单元格样式
					cell: [
						// {
						// 	// 生效sheet页索引（值为 -1 时所有sheet页都生效）
						// 	scoped: -1,
						// 	// 索引
						// 	index: 'A1',
						// 	style: {
						// 		font: {
						// 			name: '宋体',
						// 			sz: 16, // 字号
						// 			bold: true,
						// 		},
						// 		alignment: {
						// 			horizontal: 'center', // 水平居中
						// 			vertical: 'center' // 垂直居中
						// 		}
						// 	}
						// }
					]
				}
      // 配置
      let config
      // let sheet
      // let work_book = XLSX.utils.book_new()
      // 判断是否全部数据都正确获取
      let allCheck = true
      let errorRyxm
      //
      ccdryList.some(item => {
        list = []
        list.push(["检查类", "检查内容", "是否符合要求", "备注"])
        // sheet_name = item.xm + '-' + item.bm + '-' + item.zw
        sheet_name = item.xm + '-' + item.zw
        sheet_name = sheet_name.replace(/[\\/?*]/g, '-')
        // 获取人员评分记录信息
        params = {
          ccdryid: item.ccdryid
        }
        let ryzcjlList = selectRypfjlListByCcdryid(item.ccdryid)
        if (!ryzcjlList) {
          allCheck = false
          errorRyxm = item.xm
          return true
        }
        ryzcjlList.forEach(rypfjlItem => {
          // console.log(rypfjlItem, rypfjlItem.sffhyq)
          list.push([rypfjlItem.dxmc, rypfjlItem.nr, rypfjlItem.sffhyq ? '是' : '否', rypfjlItem.bzsm])
        })
        // // 将数据添加到sheet页
        // sheet = XLSX.utils.aoa_to_sheet(list)
        // //将数据添加到工作薄
        // XLSX.utils.book_append_sheet(work_book, sheet, sheet_name)
        exportList.push({
          sheetName: sheet_name,
          data: list,
          merges: merges
        })
      })
      if (!allCheck) {
        this.$message.error('[涉密人员]' + errorRyxm + '数据异常，无法下载检查信息')
        return
      }
      ////////////
      let work_book = exportExcelNumerousSheetWorkbook(exportList, styles, config)
      // console.log('人员 work_book', work_book)
      const workbookArrayBuffer = workbook2ArrayBuffer(work_book)
      //arrayBuffer转Buffer
      var buf = new Buffer(workbookArrayBuffer.byteLength)
      var view = new Uint8Array(workbookArrayBuffer)
      for (var i = 0; i < buf.length; ++i) {
        buf[i] = view[i]
      }
      return buf
    },
    // 生成检查报告word
    generateJcbgWord (zczpls) {
      console.log('检查报告')
      if (!zczpls || !zczpls.rwid) {
        this.$message.warning('[检查报告]检查任务信息异常，无法下载检查信息')
        return
      }
      // 检查报告数据
      let wordData = {
        dwmc: '',
        lxr: '',
        zw: '',
        lxdh: '',
        //
        jcjd: '',
        ksrq: '',
        jsrq: '',
        //
        df: 100,
        synrList: [],
        kfsmList: [],
        //
        nsjgList: [],
        ryList: [],
        jcpj: '',
        pjyj: ''
      }
      // 通过任务ID获取单位信息
      let dwxx = selectDwxxByRwid(zczpls.rwid)
      // console.log('检查报告dwxx', dwxx)
      if (dwxx) {
        wordData.dwmc = dwxx.dwmc
        wordData.lxr = dwxx.dwlxr
        wordData.zw = dwxx.zw
        wordData.lxdh = dwxx.dwlxdh
      }
      // 通过任务ID获取任务信息
      let rwxx = selectScrwByRwid(zczpls.rwid)
      // console.log('检查报告rwxx', rwxx)
      if (rwxx) {
        wordData.jcjd = rwxx.jcjdmc
        wordData.ksrq = dateFormatNYRChinese(rwxx.kssj)
        wordData.jsrq = dateFormatNYRChinese(rwxx.jzsj)
        let pjjg = rwxx.pjjg
        switch (pjjg) {
          case 1:
            pjjg = '优秀'
            break
          case 2:
            pjjg = '合格'
            break
          case 3:
            pjjg = '基本合格'
            break
          case 4:
            pjjg = '不合格'
            break
          default:
            pjjg = '未知'
            break
        }
        wordData.pjjg = pjjg
        wordData.pjyj = rwxx.pjyj
      }
      // 通过任务ID获取单位评分记录信息(非组合)
      const dwpfjlList = selectDwpfjlListByRwidFzh(zczpls.rwid)
      // console.log('检查报告dwpfjlList', dwpfjlList)
      dwpfjlList.forEach(item => {
        // console.log(item)
        if (item.ykf) {
          wordData.df -= item.ykf
        }
        if (item.sfsynr && item.synr) {
          wordData.synrList.push({
            item: (wordData.synrList.length + 1) + '、' + item.synr
          })
        }
        if (item.kfsm) {
          wordData.kfsmList.push({
            item: (wordData.kfsmList.length + 1) + '、' + item.kfsm
          })
        }
      })
      // 获取内设机构抽查结果
      wordData.nsjgList = selectCcdsjgListCcjg(zczpls.rwid)
      // console.log('检查报告nsjgList', wordData.nsjgList)
      // 获取人员抽查结果
      wordData.ryList = selectCcdryListCcjg(zczpls.rwid)
      // console.log('检查报告ryList', wordData.ryList)
      // console.log('检查报告wordData', wordData)
      //////////////
      return exportWord(getDocZczpMbPath(), wordData)
    },
    // 详情信息
    toXxxx (rwid) {
      setZczpIdsObj('rwid', rwid)
      // 跳转到zczp详细信息页面(不可编辑)
      this.$router.push({
        path: '/jczjLsxx',
        // query: { rwid: rwid }
      })
    },
    // 页码变更
    handleCurrentChange (val) {
      this.pageInfo.page = val
      this.getZczpLsxx()
    },
    // 页面大小变更
    handleSizeChange (val) {
      this.pageInfo.pageSize = val
      this.pageInfo.page = 1
      this.getZczpLsxx()
    },
    // 获取自查自评历史信息
    getZczpLsxx () {
      let params = {
        zt: 7
      }
      Object.assign(params, this.pageInfo)
      Object.assign(params, this.formInline)
      let scrwListPage = selectScrwZczplsPage(params)
      this.scList = scrwListPage.list
      this.pageInfo.total = scrwListPage.total
    }
  },
  watch: {},
  mounted () {
    this.getZczpLsxx()
  }
};
</script>

<style scoped>
/deep/.mhcx .el-form-item {
  margin-top: 5px;
  margin-bottom: 5px;
}
</style>
