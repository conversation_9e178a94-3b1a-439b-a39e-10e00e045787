import db from './adapter/zczpAdaptor'

//保密制度-----------------------------------保密制度初始化列表********
export const getBmzd = (params) => {
  let page = params.page
  let pagesize = params.pageSize
  let list_total = db
    .get('Bmzd_list')
    .orderBy('bfrq', 'desc')
    .filter(function (item) {
      return item
    })
    .cloneDeep()
    .value()

  // 手动分页
  console.log(pagesize * (page - 1), pagesize * (page - 1) + pagesize)
  let pageList = list_total.slice(
    pagesize * (page - 1),
    pagesize * (page - 1) + pagesize
  )

  let resList = {
    list: pageList,
    list_total: list_total,
    total: list_total.length,
  }
  console.log('保密制度', resList)
  return resList
}
//保密制度-----------------------------------保密制度添加成员********
export const addBmzd = (params) => {
  let sjc = new Date().getTime()
  params.cjsj = sjc
  params.gxsj = sjc
  db.read().get('Bmzd_list').push(params).write()
}
//保密制度-----------------------------------保密制度删除成员********
export const deleteBmzd = (params) => {
  db.read().get('Bmzd_list').remove(params).write()
}

//保密制度-----------------------------------保密制度修改单条********
export const updateBmzd = (params) => {
  // 数据校验
  // 校验ID
  let bmzdid = params.bmzdid
  console.log('bmzdid', bmzdid)
  if (!bmzdid || bmzdid == '') {
    return
  }
  params.gxsj = new Date().getTime()
  // 全字段更新方法(传进来的字段都会更新)
  db.read().get('Bmzd_list').find({ bmzdid: bmzdid }).assign(params).write()
  // // 更新部分字段方法
  // db.read().get('Bmzd_list').find({bmzdid:bmzdid}).assign({
  // 	zdmc: params.zdmc,
  // 	...
  // }).write()
}
