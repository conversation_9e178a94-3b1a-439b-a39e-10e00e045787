export default [
  {
    name: 'systemSetting',
    path: '/systemSetting',
    component: () => import('../systemSetting.vue'),
    meta: {
      name: '参数设置',
      icon: 'aaa',
      hidden: false,
      showHeaderMenu: true,
      showAsideMenu: false,
    },
  },
  {
    name: 'filePathSetting',
    path: '/filePathSetting',
    component: () => import('../filePathSetting.vue'),
    meta: {
      name: '文件路径设置',
      icon: 'aaa',
      hidden: false,
      showHeaderMenu: true,
      showAsideMenu: false,
    },
  },
  {
    name: 'zcxxSetting',
    path: '/zcxxSetting',
    component: () => import('../zcxxSetting.vue'),
    meta: {
      name: '注册信息',
      icon: 'aaa',
      hidden: false,
      showHeaderMenu: true,
      showAsideMenu: false,
    },
  },
  {
    name: 'xgmmSetting',
    path: '/xgmmSetting',
    component: () => import('../xgmmSetting.vue'),
    meta: {
      name: '修改密码',
      icon: 'aaa',
      hidden: false,
      showHeaderMenu: true,
      showAsideMenu: false,
    },
  },
  {
    name: 'mmczSetting',
    path: '/mmczSetting',
    component: () => import('../mmczSetting.vue'),
    meta: {
      name: '密码重置',
      icon: 'aaa',
      hidden: false,
      showHeaderMenu: true,
      showAsideMenu: false,
    },
  },
  {
    name: 'dmglSetting',
    path: '/dmglSetting',
    component: () => import('../dmglSetting.vue'),
    meta: {
      name: '代码管理',
      icon: 'aaa',
      hidden: false,
      showHeaderMenu: true,
      showAsideMenu: false,
    },
  },
  // {
  //   name: 'sjsbSetting',
  //   path: '/sjsbSetting',
  //   component: () => import('../sjsbSetting.vue'),
  //   meta: {
  //     name: '数据上报',
  //     icon: 'aaa',
  //     hidden: false,
  //     showHeaderMenu: true,
  //     showAsideMenu: false,
  //   },
  // },
  {
    name: 'gywmSetting',
    path: '/gywmSetting',
    component: () => import('../gywmSetting.vue'),
    meta: {
      name: '关于我们',
      icon: 'aaa',
      hidden: false,
      showHeaderMenu: true,
      showAsideMenu: false,
    },
  },
  {
    name: 'toolBox',
    path: '/toolBox',
    component: () => import('../toolBox.vue'),
    meta: {
      name: '工具箱',
      icon: 'aaa',
      hidden: false,
      showHeaderMenu: true,
      showAsideMenu: false,
    },
  },
  {
    name: 'dataMigration',
    path: '/dataMigration',
    component: () => import('../toolBoxPages/dataMigration.vue'),
    meta: {
      name: '数据迁移',
      icon: 'aaa',
      hidden: false,
      showHeaderMenu: true,
      showAsideMenu: false,
    },
  }
]
