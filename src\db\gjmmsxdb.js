import db from "./adapter/zczpAdaptor";

 // 获取所有国家mm事项信息(上报数据自选模式专用)
export const getAllGjmmxsZxms = () => {
  return db.get('Gjmmsx_list').cloneDeep().value()
}

//国家秘密事项-----------------------------------国家秘密事项初始化列表********
export const getGjmmsx = (params) => {
  let page = params.page;
  let pagesize = params.pageSize;
  let nd = params.nd;
  let gjmmsxmc = params.gjmmsxmc;
  let mj = params.mj;
  let cxnd = params.nd;
  let list_total = db
    .get("Gjmmsx_list")
    .sortBy("cjsj")
    .filter(function (item) {
      // if(nd == ""){
      // 	console.log("正常显示", item);
      // 	return item;
      // }
      if (nd == item.nd) {
        if (
          (gjmmsxmc === undefined || gjmmsxmc == "") &&
          (mj === undefined || mj == "")
        ) {
          console.log("全都没有", item);
          return item;
        } else if (gjmmsxmc && (mj === undefined || mj == "")) {
          if (item.gjmmsxmc) {
            if (item.gjmmsxmc.indexOf(gjmmsxmc) != -1) {
              console.log("定密事项内容", item);
              return item;
            }
          } else {
            console.log("item.gjmmsxmc", item.gjmmsxmc);
          }
        } else if ((gjmmsxmc === undefined || gjmmsxmc == "") && mj) {
          if (item.mj) {
            if (item.mj.indexOf(mj) != -1) {
              console.log("密级", item);
              return item;
            }
          } else {
            console.log("item.mj", item.mj);
          }
        } else if (gjmmsxmc && mj) {
          if (item.gjmmsxmc && mj) {
            if (
              item.gjmmsxmc.indexOf(gjmmsxmc) != -1 &&
              item.mj.indexOf(mj) != -1
            ) {
              console.log("定密事项内容和密级", item);
              return item;
            }
          } else {
            console.log("item.gjmmsxmc", item.gjmmsxmc, "item.mj", item.mj);
          }
        }
      }
    })
    .cloneDeep()
    .value();

  // 手动分页
  console.log(pagesize * (page - 1), pagesize * (page - 1) + pagesize);
  let pageList = list_total.slice(
    pagesize * (page - 1),
    pagesize * (page - 1) + pagesize
  );

  let resList = {
    list: pageList,
    list_total: list_total,
    total: list_total.length,
  };
  console.log("国家秘密事项", resList);
  return resList;
};
//国家秘密事项-----------------------------------国家秘密事项添加成员********
export const addGjmmsx = (params) => {
  if (!params) {
    console.log("参数为空");
    return;
  }
  let nd = params.nd;
  let param = {};
  if (!nd) {
    console.log("主键年度为空");
    return;
  }

  let leftObj = db
    .get("GjmmsxLeft_list")
    .sortBy("cjsj")
    .find({ nd: params.nd })
    .cloneDeep()
    .value();
  if (leftObj) {
    // 什么也不用干
    // console.log("11111111111111111111111111111111111111111111");
    // param.nd = params.nd;
    // console.log("添加：", param);
    // db.read().get("GjmmsxLeft_list").push(param).write();
  } else {
    param.nd = params.nd;
    console.log("添加：", param);
    db.read().get("GjmmsxLeft_list").push(param).write();
    // list.forEach((item) => {
    //   console.log("=============item:===========", item);
    //   console.log("=============item:===========", item);
    //   console.log("=============item:===========", item);
    //   if (item.nd != params.nd) {
    //     param.nd = params.nd;
    //     console.log("添加：", param);
    //     db.read().get("GjmmsxLeft_list").push(param).write();
    //   }else{
    // console.log("不添加");
    // }
    // });
  }
  // 最后在插入右表数据
  let sjc = new Date().getTime();
  console.log("添加：", params.nd);
  params.cjsj = sjc;
  params.gxsj = sjc;
  db.read().get("Gjmmsx_list").push(params).write();
};
//国家秘密事项-----------------------------------国家秘密事项删除成员********
export const deleteGjmmsx = (params) => {
  db.read().get("Gjmmsx_list").remove(params).write();
};

//修改
export const reviseGjmmsx = (params) => {
  let gjmmsxid = params.gjmmsxid;
  console.log("Gjmmsxid", gjmmsxid);
  if (!gjmmsxid || gjmmsxid == "") {
    return;
  }
  params.gxsj = new Date().getTime();
  // 全字段更新方法(传进来的字段都会更新)
  db.read()
    .get("Gjmmsx_list")
    .find({
      gjmmsxid: gjmmsxid,
    })
    .assign(params)
    .write();
};

//国家秘密事项-----------------------------------国家秘密事项初始化列表********
export const getGjmmsxLeft = (params) => {
  console.log('params', params)
  if(!params) {
    return
  }
  let nd = params.nd
  let list_total = db
    .get("GjmmsxLeft_list")
    .sortBy("cjsj")
    .filter(function (item) {
      console.log(nd, item.nd,nd == item.nd)
      if(nd) {
        if (nd == item.nd) {
          console.log("查询年度", item);
          return item;
        }
      } else {
        return item
      }
    })
    .cloneDeep()
    .value();

  console.log("国家秘密事项", list_total);
  return list_total;
};

//国家秘密事项-----------------------------------国家秘密事项添加成员********
export const addGjmmLeftsx = (params) => {
  let nd = params.nd;
  console.log("nd", nd);
  if (!nd || nd == "") {
    console.log("主键年度为空");
    return;
  }

  console.log("添加：", params);
  db.read()
    .get("GjmmsxLeft_list")
    .find({
      nd: params.nd,
    })
    .assign(params)
    .write();
};
