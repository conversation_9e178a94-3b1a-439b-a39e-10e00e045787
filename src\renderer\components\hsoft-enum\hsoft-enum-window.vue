<template>
  <div class="out">
    <div
      class="card"
      @mouseleave="userCardMouseLeave"
      ref="userCard"
      style="
        cursor: unset;
        flex: none;
        width: 120px;
        justify-content: start;
        position: relative;
      "
    >
      <input
        type="file"
        ref="upload"
        style="
          display: none;
          position: absolute;
          top: 10px;
          right: 0;
          opacity: 0;
          cursor: pointer;
          height: 32px;
          width: 56px;
          z-index: 1;
        "
        accept=".xls,.xlsx"
      />
      <div
        v-if="loading"
        style="
          background-color: rgba(0, 0, 0, 0.5);
          position: fixed;
          width: 100%;
          height: 100%;
          left: 0;
          top: 0;
          z-index: 111;
          display: flex;
          justify-content: center;
          align-items: center;
          color: #409eff;
        "
      >
        数据导入中，请稍候...
      </div>
      <!-- <img src="../../assets/icons/zxh.png" /> -->
      <!-- <span>最小化</span> -->
      <!-- <i class="el-icon-minus" style="color: #FEC13F;"></i> -->
      <!-- <img src="../../assets/icons/zuxiaohua.png" /> -->
      <i
        @click="showUserCardFlag = !showUserCardFlag"
        class="el-icon-s-custom"
        style="
          background: rgba(255, 255, 255, 0.3);
          border-radius: 50%;
          padding: 5px;
          box-sizing: border-box;
          font-size: 17px;
        "
      ></i>
      <div v-show="showUserCardFlag" class="div-user-card">
        <div style="border-bottom: 1px solid #e4e7ed">
          <!-- <i class="el-icon-caret-top"></i> -->
          <div class="pointer"></div>
          <span>{{ currentXm }}</span>
        </div>
      </div>
      <!-- <span style="font-size: 13px;font-style: italic;color: #e8e8e8;">{{currentXm}}</span> -->
      <!-- <el-popover placement="bottom" width="80" trigger="hover">
        <div class="system-setting-out">
          <span style="font-size: 13px;font-style: italic;color: #e8e8e8;">{{currentXm}}</span>
        </div>
        <i slot="reference" class="el-icon-s-custom" style="background: rgba(255,255,255,0.3);border-radius: 50%;padding: 5px;box-sizing: border-box;font-size: 17px;"></i>
      </el-popover> -->
    </div>
    <div class="card" @click="minimizeSystem()">
      <!-- <img src="../../assets/icons/zxh.png" /> -->
      <!-- <span>最小化</span> -->
      <i class="el-icon-minus"></i>
      <!-- <img src="../../assets/icons/zuxiaohua.png" title="最小化" /> -->
      <!-- <span>最小化</span> -->
    </div>
    <div class="card" @click="maximizeSystem()">
      <i v-show="!windowBtnConfig.isMax" class="el-icon-full-screen"></i>
      <i v-show="windowBtnConfig.isMax" class="el-icon-copy-document"></i>
      <!-- <img v-show="!windowBtnConfig.isMax" src="../../assets/icons/zuxiaohua.png" title="最大化" />
      <img v-show="windowBtnConfig.isMax" src="../../assets/icons/zuxiaohua.png" title="窗口化" /> -->
      <!-- <span v-show="!windowBtnConfig.isMax">最大化</span>
      <span v-show="windowBtnConfig.isMax">窗口化</span> -->
    </div>
    <div class="card">
      <!-- <img src="../../assets/icons/header_icon8.png" /> -->
      <el-popover placement="bottom" width="80" trigger="hover">
        <div class="system-setting-out">
          <!-- <div v-for="(item,index) in systemSettingList" v-if="(item.showRoles.indexOf('all') != -1)||(item.showRoles.indexOf(currentYhm) != -1)" :key="index" @click="toPath(item.path)"><i :class="item.icon"></i>{{item.name}}</div> -->
          <div
            v-for="(item, index) in systemSettingList"
            v-if="
              item.showRoles.indexOf('all') != -1 ||
              (currentYhlx == 1
                ? item.showRoles.indexOf(currentYhm) != -1
                : currentYhlx == 0
                ? true
                : item.showYhlxs.indexOf(3) != -1)
            "
            :key="index"
            @click="toPath(item.path)"
          >
            <i :class="item.icon"></i>{{ item.name }}
          </div>
        </div>
        <!-- <img slot="reference" src="../../assets/icons/header_icon8.png" style="margin-top: 5px;" /> -->
        <!-- <i slot="reference" class="el-icon-setting" style="color: #909399;"></i> -->
        <!-- <i slot="reference" class="el-icon-setting"></i> -->
        <i slot="reference" class="el-icon-s-operation"></i>
        <!-- <div slot="reference">
          <div style="display: flex;">
            <img src="../../assets/icons/shezhi.png" />
            <span>系统设置</span>
          </div>
        </div> -->
      </el-popover>
    </div>
    <div class="card" @click="quitSystem()">
      <!-- <img src="../../assets/icons/header_icon9.png" /> -->
      <!-- <i class="el-icon-close" style="color: #FE645D;"></i> -->
      <i class="el-icon-close"></i>
      <!-- <img src="../../assets/icons/tuichu.png" title="退出系统" /> -->
      <!-- <span>退出</span> -->
    </div>
    <!--数据上报dialog-->
    <el-dialog title="数据上报" :visible.sync="dialogVisibleSjsb" width="35%">
      <div>
        <el-form
          :model="dialogSjsb"
          :label-position="'right'"
          label-width="120px"
          size="mini"
        >
          <div style="display: flex">
            <el-form-item label="上报数据模式" class="one-line">
              <el-radio-group v-model="sbsjlx">
                <el-radio :label="1">本年数据模式</el-radio>
                <!-- <el-radio :label="2">自选数据模式</el-radio> -->
              </el-radio-group>
            </el-form-item>
          </div>
          <div style="display: flex">
            <el-form-item label="上报时间" class="one-line">
              <el-date-picker
                v-model="dialogSjsb.sbsj"
                type="year"
                value-format="yyyy"
                placeholder="选择上报时间"
                style="width: calc(100%)"
              >
              </el-date-picker>
            </el-form-item>
          </div>
          <!-- <div v-if="sbsjlx == 1" style="display: flex">
            <el-form-item label="上报类型" class="one-line">
              <el-checkbox-group v-model="dialogSjsb.sblx">
                <el-checkbox
                  :label="1"
                  :disabled="
                    dialogSjsb.sblx.length == 1 &&
                    dialogSjsb.sblx.indexOf(1) != -1
                  "
                  >涉密人员</el-checkbox
                >
                <el-checkbox
                  :label="2"
                  :disabled="
                    dialogSjsb.sblx.length == 1 &&
                    dialogSjsb.sblx.indexOf(2) != -1
                  "
                  >定密事项</el-checkbox
                >
              </el-checkbox-group>
            </el-form-item>
          </div> -->
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="generateSbsj()">保 存</el-button>
        <el-button type="warning" @click="dialogVisibleSjsb = false"
          >关 闭</el-button
        >
      </span>
    </el-dialog>
    <!--数据上报dialog-->
    <el-dialog title="数据导出" :visible.sync="dialogVisibleSjdc" width="35%">
      <div>
        <div style="display: flex; align-items: center; margin-bottom: 10px">
          <div>一、请点击“导出模板”，并参照模板填写信息。</div>
          <el-button type="primary" size="mini" @click="mbdc">
            模板导出
          </el-button>
        </div>
        <div style="display: flex; align-items: center; margin-bottom: 10px">
          <div style="width: 215px">二、数据导入方式：</div>
          <el-radio-group v-model="sjdrfs">
            <el-radio label="1"
              >追加（导入时已有的记录信息不变，只添加新的记录）</el-radio
            >
            <el-radio label="2"
              >覆盖（导入时更新已有的记录信息，并添加新的记录）</el-radio
            >
          </el-radio-group>
        </div>
        <div style="display: flex; align-items: center; margin-bottom: 10px">
          <div>三、将按模板填写的文件，导入到系统中。</div>
          <el-button type="primary" size="mini" @click="chooseFile">
            上传导入
          </el-button>
        </div>

        <!-- <div v-if="sbsjlx == 1" style="display: flex">
            <el-form-item label="上报类型" class="one-line">
              <el-checkbox-group v-model="dialogSjsb.sblx">
                <el-checkbox
                  :label="1"
                  :disabled="
                    dialogSjsb.sblx.length == 1 &&
                    dialogSjsb.sblx.indexOf(1) != -1
                  "
                  >涉密人员</el-checkbox
                >
                <el-checkbox
                  :label="2"
                  :disabled="
                    dialogSjsb.sblx.length == 1 &&
                    dialogSjsb.sblx.indexOf(2) != -1
                  "
                  >定密事项</el-checkbox
                >
              </el-checkbox-group>
            </el-form-item>
          </div> -->
      </div>
      <!-- <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="generateSbsj()">保 存</el-button>
        <el-button type="warning" @click="dialogVisibleSjdc = false"
          >关 闭</el-button
        >
      </span> -->
    </el-dialog>
    <!---->
    <el-dialog
      title="上报数据自选模式"
      :visible.sync="dialogVisibleSjsbZxms"
      width="90%"
    >
      <div>
        <el-tabs type="card" tab-position="left">
          <el-tab-pane label="涉密岗位" style="height: 403px">
            <el-table
              :data="sbsjDataZxmsOld.Smgwgl_list"
              @select="
                (selection, row) => tableSelected(selection, row, 'Smgwgl_list')
              "
              @select-all="
                (selection) => tableSelectedAll(selection, 'Smgwgl_list')
              "
              border
              :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
              style="width: 100%; border: 1px solid #ebeef5"
              height="calc(100%)"
              stripe
            >
              <el-table-column type="selection" width="55" align="center">
              </el-table-column>
              <el-table-column
                type="index"
                width="60"
                label="序号"
                align="center"
              ></el-table-column>
              <el-table-column prop="bm" label="部门"></el-table-column>
              <el-table-column prop="gwmc" label="岗位名称"></el-table-column>
              <el-table-column prop="smdj" label="涉密等级"></el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="涉密人员" style="height: 403px">
            <el-table
              :data="sbsjDataZxmsOld.Smry_list"
              @select="
                (selection, row) => tableSelected(selection, row, 'Smry_list')
              "
              @select-all="
                (selection) => tableSelectedAll(selection, 'Smry_list')
              "
              border
              :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
              style="width: 100%; border: 1px solid #ebeef5"
              height="100%"
              stripe
            >
              <el-table-column type="selection" width="55" align="center">
              </el-table-column>
              <el-table-column prop="xm" label="姓名"></el-table-column>
              <el-table-column prop="bm" label="部门"></el-table-column>
              <el-table-column prop="gwmc" label="岗位名称"></el-table-column>
              <el-table-column prop="smdj" label="涉密等级"></el-table-column>
              <el-table-column prop="zw" label="职务"></el-table-column>
              <el-table-column prop="sfsc" label="是否审查"></el-table-column>
              <el-table-column prop="sgsj" label="上岗时间"></el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="离岗离职" style="height: 403px">
            <el-table
              :data="sbsjDataZxmsOld.Lglz_list"
              @select="
                (selection, row) => tableSelected(selection, row, 'Lglz_list')
              "
              @select-all="
                (selection) => tableSelectedAll(selection, 'Lglz_list')
              "
              border
              :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
              style="width: 100%; border: 1px solid #ebeef5"
              height="calc(100%)"
              stripe
            >
              <el-table-column type="selection" width="55" align="center">
              </el-table-column>
              <el-table-column
                type="index"
                width="60"
                label="序号"
                align="center"
              ></el-table-column>
              <el-table-column prop="xm" label="姓名"></el-table-column>
              <el-table-column prop="ybm" label="原部门"></el-table-column>
              <el-table-column
                prop="ygwmc"
                label="原岗位名称"
              ></el-table-column>
              <el-table-column
                prop="ysmdj"
                label="原涉密等级"
              ></el-table-column>
              <el-table-column
                prop="tmqkssj"
                label="脱密期开始时间"
              ></el-table-column>
              <el-table-column
                prop="tmqjssj"
                label="脱密期结束时间"
              ></el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="出国出境涉密人员" style="height: 403px">
            <el-table
              :data="sbsjDataZxmsOld.Cgcj_list"
              @select="
                (selection, row) => tableSelected(selection, row, 'Cgcj_list')
              "
              @select-all="
                (selection) => tableSelectedAll(selection, 'Cgcj_list')
              "
              border
              :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
              style="width: 100%; border: 1px solid #ebeef5"
              height="calc(100%)"
              stripe
            >
              <el-table-column type="selection" width="55" align="center">
              </el-table-column>
              <el-table-column
                type="index"
                width="60"
                label="序号"
                align="center"
              ></el-table-column>
              <el-table-column prop="xm" label="姓名"></el-table-column>
              <el-table-column prop="bmmc" label="部门"></el-table-column>
              <el-table-column prop="sfzhm" label="身份证号"></el-table-column>
              <el-table-column prop="smdj" label="涉密等级"></el-table-column>
              <el-table-column prop="cgsj" label="出境时间"></el-table-column>
              <el-table-column
                prop="qwgj"
                label="去往国家（地区）"
              ></el-table-column>
              <el-table-column prop="ggsj" label="返回时间"></el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="三类涉密人员" style="height: 403px">
            <el-table
              :data="sbsjDataZxmsOld.Slsmry_list"
              @select="
                (selection, row) => tableSelected(selection, row, 'Slsmry_list')
              "
              @select-all="
                (selection) => tableSelectedAll(selection, 'Slsmry_list')
              "
              border
              :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
              style="width: 100%; border: 1px solid #ebeef5"
              height="calc(100%)"
              stripe
            >
              <el-table-column type="selection" width="55" align="center">
              </el-table-column>
              <el-table-column
                type="index"
                width="60"
                label="序号"
                align="center"
              ></el-table-column>
              <el-table-column prop="xm" label="姓名"></el-table-column>
              <el-table-column prop="bmmc" label="部门"></el-table-column>
              <el-table-column prop="sfzhm" label="身份证号"></el-table-column>
              <el-table-column prop="smdj" label="涉密等级"></el-table-column>
              <el-table-column prop="yrxs" label="用人形式"></el-table-column>
              <el-table-column
                prop="ydwmc"
                label="原单位名称"
              ></el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="涉密人员被追责" style="height: 403px">
            <el-table
              :data="sbsjDataZxmsOld.Smryzzqk_list"
              @select="
                (selection, row) =>
                  tableSelected(selection, row, 'Smryzzqk_list')
              "
              @select-all="
                (selection) => tableSelectedAll(selection, 'Smryzzqk_list')
              "
              border
              :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
              style="width: 100%; border: 1px solid #ebeef5"
              height="calc(100%)"
              stripe
            >
              <el-table-column type="selection" width="55" align="center">
              </el-table-column>
              <el-table-column
                type="index"
                width="60"
                label="序号"
                align="center"
              ></el-table-column>
              <el-table-column prop="xm" label="姓名"></el-table-column>
              <el-table-column prop="bmmc" label="原工作部门"></el-table-column>
              <el-table-column prop="smdj" label="涉密等级"></el-table-column>
              <el-table-column
                prop="bzzqk"
                label="被追责情况"
              ></el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="保密干部" style="height: 403px">
            <el-table
              :data="sbsjDataZxmsOld.Bmgbqk_list"
              @select="
                (selection, row) => tableSelected(selection, row, 'Bmgbqk_list')
              "
              @select-all="
                (selection) => tableSelectedAll(selection, 'Bmgbqk_list')
              "
              border
              :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
              style="width: 100%; border: 1px solid #ebeef5"
              height="calc(100%)"
              stripe
            >
              <el-table-column type="selection" width="55" align="center">
              </el-table-column>
              <el-table-column
                type="index"
                width="60"
                label="序号"
                align="center"
              ></el-table-column>
              <el-table-column prop="xm" label="姓名"></el-table-column>
              <el-table-column prop="bm" label="部门"></el-table-column>
              <el-table-column prop="gwmc" label="岗位名称"></el-table-column>
              <el-table-column prop="sfzh" label="身份证号"></el-table-column>
              <el-table-column prop="smdj" label="涉密等级"></el-table-column>
              <el-table-column prop="sfzz" label="专职/兼职"></el-table-column>
              <el-table-column
                prop="drbmgbsj"
                label="担任保密时间"
              ></el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="定密责任人" style="height: 403px">
            <el-table
              :data="sbsjDataZxmsOld.dmzrr_list"
              @select="
                (selection, row) => tableSelected(selection, row, 'dmzrr_list')
              "
              @select-all="
                (selection) => tableSelectedAll(selection, 'dmzrr_list')
              "
              border
              :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
              style="width: 100%; border: 1px solid #ebeef5"
              height="calc(100%)"
              stripe
            >
              <el-table-column type="selection" width="55" align="center">
              </el-table-column>
              <el-table-column
                type="index"
                width="60"
                label="序号"
                align="center"
              ></el-table-column>
              <el-table-column prop="nd" label="年度"></el-table-column>
              <el-table-column prop="xm" label="姓名"></el-table-column>
              <el-table-column prop="zw" label="职务"></el-table-column>
              <el-table-column prop="dmqx" label="定密权限"></el-table-column>
              <el-table-column
                prop="dmsx"
                label="定密事项（范围）"
              ></el-table-column>
              <el-table-column prop="lb" label="类别"></el-table-column>
              <el-table-column
                prop="qdsj"
                label="确（指）定时间"
              ></el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="定密授权" style="height: 403px">
            <el-table
              :data="sbsjDataZxmsOld.dmsq_list"
              @select="
                (selection, row) => tableSelected(selection, row, 'dmsq_list')
              "
              @select-all="
                (selection) => tableSelectedAll(selection, 'dmsq_list')
              "
              border
              :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
              style="width: 100%; border: 1px solid #ebeef5"
              height="calc(100%)"
              stripe
            >
              <el-table-column type="selection" width="55" align="center">
              </el-table-column>
              <el-table-column
                type="index"
                width="60"
                label="序号"
                align="center"
              ></el-table-column>
              <el-table-column prop="nd" label="年度"></el-table-column>
              <el-table-column
                prop="bsqjg"
                label="被授权机关、单位名称"
              ></el-table-column>
              <el-table-column
                prop="sqjg"
                label="授权机关/单位名称"
              ></el-table-column>
              <el-table-column prop="dmqx" label="权限"></el-table-column>
              <el-table-column prop="lksj" label="时间"></el-table-column>
              <el-table-column prop="qxnd" label="期限（年）"></el-table-column>
              <el-table-column prop="sx" label="事项"></el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="国家秘密事项" style="height: 403px">
            <el-table
              :data="sbsjDataZxmsOld.Gjmmsx_list"
              @select="
                (selection, row) => tableSelected(selection, row, 'Gjmmsx_list')
              "
              @select-all="
                (selection) => tableSelectedAll(selection, 'Gjmmsx_list')
              "
              border
              :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
              style="width: 100%; border: 1px solid #ebeef5"
              height="calc(100%)"
              stripe
            >
              <el-table-column type="selection" width="55" align="center">
              </el-table-column>
              <el-table-column
                type="index"
                width="60"
                label="序号"
                align="center"
              ></el-table-column>
              <el-table-column
                prop="nd"
                width="60"
                label="年度"
              ></el-table-column>
              <el-table-column
                prop="gjmmsxmc"
                width="140px"
                label="国家秘密事项名称"
              ></el-table-column>
              <el-table-column prop="mj" label="密级"></el-table-column>
              <el-table-column prop="bmqx" label="保密期限"></el-table-column>
              <el-table-column prop="zxfw" label="知悉范围"></el-table-column>
              <el-table-column prop="dmyj" label="定密依据"></el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="定密培训" style="height: 403px">
            <el-table
              :data="sbsjDataZxmsOld.dmpx_list"
              @select="
                (selection, row) => tableSelected(selection, row, 'dmpx_list')
              "
              @select-all="
                (selection) => tableSelectedAll(selection, 'dmpx_list')
              "
              border
              :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
              style="width: 100%; border: 1px solid #ebeef5"
              height="calc(100%)"
              stripe
            >
              <el-table-column type="selection" width="55" align="center">
              </el-table-column>
              <el-table-column
                type="index"
                width="60"
                label="序号"
                align="center"
              ></el-table-column>
              <el-table-column prop="nd" label="年度"></el-table-column>
              <el-table-column prop="pxsj" label="培训时间"></el-table-column>
              <el-table-column prop="xs" label="学时（小时）"></el-table-column>
              <el-table-column
                prop="pxrs"
                label="培训人数（人）"
              ></el-table-column>
              <el-table-column prop="pxdx" label="培训对象"></el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="定密情况年度统计" style="height: 403px">
            <el-table
              :data="sbsjDataZxmsOld.dmqkndtj_list"
              @select="
                (selection, row) =>
                  tableSelected(selection, row, 'dmqkndtj_list')
              "
              @select-all="
                (selection) => tableSelectedAll(selection, 'dmqkndtj_list')
              "
              border
              :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
              style="width: 100%; border: 1px solid #ebeef5"
              height="calc(100%)"
              stripe
            >
              <el-table-column type="selection" width="55" align="center">
              </el-table-column>
              <el-table-column
                type="index"
                width="60"
                label="序号"
                align="center"
              ></el-table-column>
              <el-table-column
                prop="nd"
                label="年度"
                width="60"
                align="center"
              ></el-table-column>
              <el-table-column
                prop="gjmmzshj"
                label="国家秘密数量"
              ></el-table-column>
              <el-table-column
                prop="dmzrrhj"
                label="定密责任人数量"
              ></el-table-column>
              <el-table-column
                prop="dmsqxzshj"
                label="定密授权数量"
              ></el-table-column>
              <el-table-column
                prop="gjmmsxylbxzzsylbs"
                label="国家秘密事项数量"
              ></el-table-column>
              <el-table-column
                prop="dmzdsxczs"
                label="定密制度数量"
              ></el-table-column>
              <el-table-column
                prop="pxcs"
                label="定密培训次数"
              ></el-table-column>
              <el-table-column
                prop="gzmmqds"
                label="工作秘密数量"
              ></el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="不明确事项确定情况" style="height: 403px">
            <el-table
              :data="sbsjDataZxmsOld.dmqsxqdqk_list"
              @select="
                (selection, row) =>
                  tableSelected(selection, row, 'dmqsxqdqk_list')
              "
              @select-all="
                (selection) => tableSelectedAll(selection, 'dmqsxqdqk_list')
              "
              border
              :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
              style="width: 100%; border: 1px solid #ebeef5"
              height="calc(100%)"
              stripe
            >
              <el-table-column type="selection" width="55" align="center">
              </el-table-column>
              <el-table-column
                type="index"
                width="60"
                label="序号"
                align="center"
              ></el-table-column>
              <el-table-column prop="sxmc" label="事项名称"></el-table-column>
              <el-table-column prop="mj" label="密级"></el-table-column>
              <el-table-column prop="bmqx" label="保密期限"></el-table-column>
              <el-table-column prop="qyrq" label="确认时间"></el-table-column>
              <el-table-column prop="qrly" label="确认理由"></el-table-column>
              <el-table-column prop="djsj" label="登记时间"></el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="政府采购项目情况" style="height: 403px">
            <el-table
              :data="sbsjDataZxmsOld.zfcgxmqk_list"
              @select="
                (selection, row) =>
                  tableSelected(selection, row, 'zfcgxmqk_list')
              "
              @select-all="
                (selection) => tableSelectedAll(selection, 'zfcgxmqk_list')
              "
              border
              :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
              style="width: 100%; border: 1px solid #ebeef5"
              height="calc(100%)"
              stripe
            >
              <el-table-column type="selection" width="55" align="center">
              </el-table-column>
              <el-table-column
                type="index"
                width="60"
                label="序号"
                align="center"
              ></el-table-column>
              <el-table-column prop="nd" label="年度"></el-table-column>
              <el-table-column prop="mc" label="项目名称"></el-table-column>
              <el-table-column prop="zl" label="项目种类"></el-table-column>
              <el-table-column prop="mj" label="项目密级"></el-table-column>
              <el-table-column prop="je" label="项目金额"></el-table-column>
              <el-table-column
                prop="gysmc"
                label="供应商名称"
              ></el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogVisibleSjsbZxms = false"
          >自选数据确认</el-button
        >
        <el-button type="warning" @click="dialogVisibleSjsbZxms = false"
          >取 消</el-button
        >
      </span>
    </el-dialog>
    <!---->
  </div>
</template>

<script>
import {
  removeWindowLocation,
  getWindowLocation,
} from "../../../utils/windowLocation";

import { writeLoginLog, writeSystemOptionsLog } from "../../../utils/logUtils";

import { errorProcessor } from "../../../utils/errorProcessor";

import { getZczpPath, getDocsmdwzhtjbMbPath } from "../../../utils/pathUtil";

import { encryptAesCBC, decryptAesCBC } from "../../../utils/aesUtils";

import { dateFormatChinese } from "../../../utils/moment";

import { checkArr } from "../../../utils/utils";
import { dateFormatNYRChinese } from "../../../utils/moment";

//
import { getlogin } from "../../../db/loginyhdb";
import {
  getAllSmgwZxms,
  addDrSmgwgl,
  getbmmc,
  deleteSmgwgl1,
} from "../../../db/smgwgldb";
import { addxszzjg, deletexszzjgDgdrfg } from "../../../db/zzjgdb";
import { getAllCgcjZxms, addCgcj, deleteCgcjall } from "../../../db/cgcjdb";
import {
  getAllSlsmryZxms,
  addSlsmry,
  deleteSlryall,
} from "../../../db/slsmrydb";
import { getAllBmgbqkZxms, addbmgb, deleteBmgball } from "../../../db/bmgbqkxx";
import { getsmry1, deletesmry, addsmry, deletesmry2 } from "../../../db/smrydb";
import { getAllLglzZxms, addLglz, deleteLglzall } from "../../../db/lglzdb";
import { getAllDmzrrZxms } from "../../../db/dmzrrdb";
import {
  getAllSmryzzqkZxms,
  addZzqk,
  deleteSmrybzzall,
} from "../../../db/smrybzzqkdb";
import { getAllDmsqZxms } from "../../../db/dmsqdb";
import { getAllGjmmxsZxms } from "../../../db/gjmmsxdb";
import { getAllDmpxZxms } from "../../../db/dmpxdb";
import { getAllDmqkndtjZxms } from "../../../db/dmqkndtjdb";
import { getAllBmqsxqdqkZxms } from "../../../db/bmqsxqdqkdb";
import { getAllZfcgxmqkZxms } from "../../../db/zfcgxmqkdb";
import { getDwxxListAll, updateDwxx } from "../../../db/dwxxDb";
import { getUuid } from "../../../utils/getUuid"; //获取uuid
import {
  exportExcel,
  exportExcelNumerousSheet1,
} from "../../../utils/exportExcel"; //excel导出工具
import XLSX from "xlsx";
// // 单位信息
// import { updateDwxx } from "../../../db/dwxxDb";
// 单位类型
import { getDmbDwlxDB } from "../../../db/dmbDwlxDb";
// 单位所属领域
import { getDmbSslyDB } from "../../../db/dmbSslyDb";
// 单位所属层次
import { getDmbSsccDB } from "../../../db/dmbSsccDb";
import { log } from "console";
export default {
  data() {
    return {
      sjdrfs: "1",
      loading: false,
      // 上报数据自选模式dialog显隐
      dialogVisibleSjsbZxms: false,
      dwlxList: [],
      sslyList: [],
      ssccList: [],
      // 上报数据自选模式(备选数据)
      sbsjDataZxmsOld: {
        Smgwgl_list: [],
        Smry_list: [],
        Lglz_list: [],
        Cgcj_list: [],
        Slsmry_list: [],
        Smryzzqk_list: [],
        Bmgbqk_list: [],
        dmzrr_list: [],
        dmsq_list: [],
        Gjmmsx_list: [],
        dmpx_list: [],
        dmqkndtj_list: [],
        dmqsxqdqk_list: [],
        zfcgxmqk_list: [],
      },
      // 上报数据(已选数据)
      sbsjDataZxms: {},
      // 人员上报 表数组
      rysbTableArr: [
        "Smgwgl_list",
        "Smry_list",
        "Lglz_list",
        "Cgcj_list",
        "Slsmry_list",
        "Smryzzqk_list",
        "Bmgbqk_list",
      ],
      // 定密上报 表数组
      dmsbTableArr: [
        "dmzrr_list",
        "dmsq_list",
        "Gjmmsx_list",
        "dmpx_list",
        "dmqkndtj_list",
        "dmqsxqdqk_list",
        "zfcgxmqk_list",
      ],
      // 数据上报数据类型默认值
      sbsjlx: 1,
      // 数据上报dialog数据
      dialogSjsb: {
        // 默认值
        sblx: [1, 2],
      },
      // 数据上报dialog显隐
      dialogVisibleSjsb: false,
      // 数据导出dialog显隐
      dialogVisibleSjdc: false,
      // 当前登录用户信息显隐
      showUserCardFlag: false,
      // 当前登录用户的姓名
      currentXm: "",
      // 当前登录用户的用户名(目前系统没有角色概念，故直接使用账号进行按钮显隐的判断)（用户名从登录时放入缓存的数据获取）
      currentYhm: "",
      // 当前登录用户的类型
      currentYhlx: "",
      /**
       * 最大化按钮控制配置
       * 窗口化时显示最大化
       * 最大化时显示窗口化
       */
      windowBtnConfig: {
        isMax: false,
      },
      // 系统设置菜单集合
      systemSettingList: [
        {
          name: "密码重置",
          path: "/mmczSetting",
          icon: "el-icon-edit",
          // 判断按钮是否需要隐藏（目前系统没有角色概念，故直接使用账号进行判断）
          showRoles: ["root"],
          showYhlxs: [0],
        },
        {
          name: "修改密码",
          path: "/xgmmSetting",
          icon: "el-icon-edit",
          showRoles: ["all"],
          showYhlxs: [0, 1, 3],
        },
        {
          name: "参数设置",
          path: "/systemSetting",
          icon: "el-icon-notebook-2",
          showRoles: ["root", "sysadmin"],
          showYhlxs: [0, 1],
        },
        {
          name: "文件路径设置",
          path: "/filePathSetting",
          icon: "el-icon-files",
          showRoles: ["root", "sysadmin"],
          showYhlxs: [0, 1],
        },
        // {
        //   name: '代码管理',
        //   path: '/dmglSetting',
        //   icon: 'el-icon-c-scale-to-original',
        //   showRoles: ['root', 'sysadmin'],
        //   showYhlxs: [0, 1]
        // },
        {
          name: "数据上报",
          path: "/sjsbSetting",
          icon: "el-icon-bank-card",
          showRoles: ["root", "sysadmin"],
          showYhlxs: [0, 1, 3],
        },
        {
          name: "数据导入",
          path: "/sjdaoruSetting",
          icon: "el-icon-upload2",
          showRoles: ["root", "sysadmin"],
          showYhlxs: [0, 1, 3],
        },
        {
          name: "注册信息维护",
          path: "/zcxxSetting",
          icon: "el-icon-office-building",
          showRoles: ["root", "sysadmin"],
          showYhlxs: [0, 1, 3],
        },
        {
          name: "关于我们",
          path: "/gywmSetting",
          icon: "el-icon-message",
          showRoles: ["all"],
          showYhlxs: [0, 1, 3],
        },
        // {
        //   name: '工具箱',
        //   path: '/toolBox',
        //   icon: 'el-icon-message',
        //   showRoles: ['all']
        // },
        {
          name: "退出登录",
          path: "/quit",
          icon: "el-icon-warning-outline",
          showRoles: ["all"],
        },
      ],
    };
  },
  methods: {
    // 通过ID获取所属层次
    getSsccBySsccId(ssccid) {
      console.log(1111, "862");
      if (ssccid === undefined) {
        this.$message.warning("单位所属层次ID为空");
        return;
      }
      let params = {
        ssccid: ssccid,
      };
      console.log(getDmbSsccDB(params));
      return getDmbSsccDB(params).list_total[0];
    },
    // 通过ID获取所属领域
    getSslyBySslyId(sslyid) {
      if (sslyid === undefined) {
        this.$message.warning("单位所属领域ID为空");
        return;
      }
      let params = {
        sslyid: sslyid,
      };
      return getDmbSslyDB(params).list_total[0];
    },
    // 通过ID获取单位类型
    getDwlxByDwlxId(dwlxid) {
      if (dwlxid === undefined) {
        this.$message.warning("单位类型ID为空");
        return;
      }
      let params = {
        dwlxid: dwlxid,
      };
      return getDmbDwlxDB(params).list_total[0];
    },
    // 设置显隐控制标记默认值（防止undefined导致双向绑定失效）
    setFlagDefault(obj) {
      obj.dwmcEdit = 0;
      obj.dwzchEdit = 0;
      obj.xydmEdit = 0;
      obj.dwlxmcEdit = 0;
      obj.sslymcEdit = 0;
      obj.ssccmcEdit = 0;
      obj.dwlxrEdit = 0;
      obj.dwlxdhEdit = 0;
      obj.dwlxyxEdit = 0;
      obj.ssqhEdit = 0;
    },
    // 获取单位信息
    getDwxx() {
      // localstore中获取登录单位的信息
      let dwxx = getDwxxListAll();
      let loginUserDwxx = dwxx[0];
      console.log(
        "loginUserDwxx",
        loginUserDwxx,
        Object.prototype.toString.call(loginUserDwxx)
      );
      if (!loginUserDwxx) {
        // this.$message.error("单位信息获取失败");
        return;
      }
      // try {
      //   if (Object.prototype.toString.call(loginUserDwxx) == '[object Object]') {
      //     // 什么也不用做
      //   } else {
      //     loginUserDwxx = JSON.parse(loginUserDwxx)
      //   }
      // } catch (error) {
      //   this.$message.error('单位信息获取解析失败')
      //   return
      // }
      // 获取单位类型名称
      let dwlxObj = this.getDwlxByDwlxId(loginUserDwxx.dwlx);
      if (dwlxObj) {
        loginUserDwxx.dwlxmc = dwlxObj.dwlxmc;
      }
      // 获取所属领域名称
      let sslyObj = this.getSslyBySslyId(loginUserDwxx.ssly);
      console.log("sslyObj", sslyObj);
      if (sslyObj) {
        loginUserDwxx.sslymc = sslyObj.sslymc;
      }
      // 获取所属层次名称
      let ssccObj = this.getSsccBySsccId(loginUserDwxx.sscc);
      if (ssccObj) {
        loginUserDwxx.ssccmc = ssccObj.ssccmc;
      }
      this.setFlagDefault(loginUserDwxx);
      this.dwxx = loginUserDwxx;
      console.log(this.dwxx, "this.dwxx");
    },
    // 表格选择事件
    tableSelected(selection, row, tableName) {
      console.log(selection, row, tableName);
      this.sbsjDataZxms[tableName] = selection;
    },
    // 表格全选事件
    tableSelectedAll(selection, tableName) {
      console.log(selection, tableName);
      this.sbsjDataZxms[tableName] = selection;
    },
    // 当前登录用户信息鼠标移出事件
    userCardMouseLeave() {
      // console.log('鼠标移出去了')
      this.showUserCardFlag = false;
    },
    // 关闭 el-popover 弹出窗
    closeElPopover() {
      console.log("closeElPopover mouseleave");
      this.elPopoverFlag = false;
    },
    closeElPopover() {
      console.log("closeElPopover mouseleave");
      this.elPopoverFlag = false;
    },
    // 页面跳转方法
    toPath(path) {
      if (path == "/quit") {
        // 写入登录日志
        writeLoginLog(1);
        // 清除缓存
        removeWindowLocation();
        this.$router.push("/");
        return;
      }
      if (path == "/sjsbSetting") {
        // this.generateSbsj()
        // 设置上报数据dialog默认数据
        this.dialogSjsb = {
          sbsj: new Date().getFullYear() + "",
          sblx: [1, 2],
        };
        this.sbsjlx = 1;
        this.dialogVisibleSjsb = true;
        return;
      }
      if (path == "/sjdaoruSetting") {
        this.sjdrfs = "1";
        this.dialogVisibleSjdc = true;
        // this.shujudaoru();
        // 设置上报数据dialog默认数据

        return;
      }
      this.$router.push(path);
    },
    chooseFile() {
      this.shujudaoru();
    },
    mbdc() {
      // console.log(this.selectlistRow);
      // if (this.selectlistRow.length > 0) {
      let filename = "涉密人员情况模板" + this.Date + ".xlsx";

      const { dialog } = require("electron").remote;

      const FS = require("fs");

      //弹窗title
      let options = {
        title: "保存文件",
        defaultPath: filename,
      };
      console.log(dialog);
      //导出文件夹选择弹窗
      dialog.showSaveDialog(options, (result) => {
        console.log("result", result);
        if (result == null || result == "") {
          console.log("取消导出");
          return;
        }
        // 直接读取模板下载
        // FS.copyFileSync(getDocDmsxzhtjbMbPath(), result, FS.constants.COPYFILE_EXCL)
        FS.writeFileSync(result, FS.readFileSync(getDocsmdwzhtjbMbPath()));
        this.dr_dialog = false;
        this.$message("导出成功:" + result);
        this.dialogVisibleSjdc = false;
      });
    },
    // mbdc(){
    //   let filename = "涉密人员情况模板" + this.Date + ".xlsx"
    //   const {
    //     dialog
    //   } = require('electron').remote;
    //   //弹窗title
    //   let options = {
    //     title: "保存文件",
    //     defaultPath: filename,
    //   };
    //   dialog.showSaveDialog(options, (result) => {
    //     console.log("result", result);
    //     if (result == null || result == "") {
    //       console.log("取消导出");
    //       return;
    //     }
    //     /////涉密单位
    //     let listdwxx = [];
    //     listdwxx.push(["附件5-1"]);
    //     listdwxx.push(["基础表1 单位基本信息"]);
    //     let dwmc = "(填报单位名称、印章)" + this.dwmc;
    //     let year = "统计年度：" + this.yearwn;
    //     let date = "填报时间：" + this.Date;
    //     listdwxx.push(["", "", "", "", "", "", "", "", "", ""]);
    //     listdwxx.push([dwmc, "", "", "", "", "", "", "", "", ""]);
    //     listdwxx.push([year, "", "", "", "", "", "", "", "", ""]);
    //     listdwxx.push([
    //       "本单位名称",
    //       "本单位类型",
    //       "本单位所属领域",
    //       "本单位所属层级",
    //       "本单位统一社会信用代码",
    //       "本单位所在行政区域",
    //       "",
    //       "",
    //       "单位联系人",
    //       "单位联系电话",
    //     ]);
    //     listdwxx.push([
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",

    //     ]);

    //     console.log(listdwxx);
    //     let array = [{}];
    //     ////涉密岗位
    //     let listsmgw = [];
    //     listsmgw.push(["附件5-2"]);
    //     listsmgw.push(["基础表2 涉密岗位统计情况"]);
    //     listsmgw.push([dwmc, "", "", "", "", ""]);
    //     listsmgw.push([year, "", "", date, "", ""]);
    //     listsmgw.push([
    //       "序号",
    //       "部门",
    //       "岗位名称",
    //       "涉密等级",
    //       "岗位确定依据",
    //       "备注",
    //     ]); //确定列名
    //     for (var i in array) {
    //       //每一行的值
    //      //{"name":"XXX","dw":"XXX","zw":"XXX"}

    //      let column = [(parseInt(i) + 1), "", "", "", "", "", ""]
    //       listsmgw.push(column);
    //     }
    //     console.log(this.sbsjDataZxmsOld.Smgwgl_list);
    //     ////涉密人员
    //     let listsmry = [];
    //     listsmry.push(["附件5-3"]);
    //     listsmry.push(["基础表3 涉密人员统计情况"]);
    //     listsmry.push([
    //       dwmc,
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //     ]);
    //     listsmry.push([
    //       year,
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       date,
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //     ]);
    //     listsmry.push([
    //       "序号",
    //       "姓名",
    //       "性别",
    //       "身份证号",
    //       "年龄",
    //       "部门",
    //       "岗位名称",
    //       "涉密等级",
    //       "最高学历",
    //       "职务",
    //       "职级",
    //       "级别职称",
    //       "身份类型",
    //       "用人形式",
    //       "是否审查",
    //       "是否出入境登记备案",
    //       "是否统一保管出入境证件",
    //       "上岗时间（现涉密岗位）",
    //       "备注",
    //     ]); //确定列名
    //     for (var i in array) {
    //       //每一行的值
    //       //{"name":"XXX","dw":"XXX","zw":"XXX"}

    //       let column =[(parseInt(i) + 1), "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""]
    //       listsmry.push(column);
    //     }
    //     console.log(this.sbsjDataZxmsOld.Smry_list);
    //     ////离岗离职
    //     let listlglz = [];
    //     listlglz.push(["附件5-4"]);
    //     listlglz.push(["基础表4 涉密人员脱密期管理台帐"]);
    //     listlglz.push([
    //       dwmc,
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //     ]);
    //     listlglz.push([
    //       year,
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       date,
    //       "",
    //     ]);
    //     listlglz.push([
    //       "序号",
    //       "姓名",
    //       "性别",
    //       "身份证号",
    //       "年龄",
    //       "原部门",
    //       "原涉密等级",
    //       "职务",
    //       "职级",
    //       "级别职称",
    //       "原身份类型",
    //       "是否到公安机关出入境备案",
    //       "是否委托管理",
    //       "脱密期开始时间",
    //       "脱密期结束时间",
    //       "手机号码",
    //       "离职离岗类型",
    //       "去向单位名称",
    //       "备注",
    //     ]); //确定列名
    //     for (var i in array) {
    //       //每一行的值
    //       //{"name":"XXX","dw":"XXX","zw":"XXX"}

    //       let column = [(parseInt(i) + 1), "", "", "", "", "", ""];
    //       listlglz.push(column);
    //     }
    //     console.log(this.sbsjDataZxmsOld.Lglz_list);
    //     ////出国出境
    //     let listcgcj = [];
    //     listcgcj.push(["附件5-5"]);
    //     listcgcj.push(["基础表5 因私出国（境）涉密人员统计"]);
    //     listcgcj.push([dwmc, "", "", "", "", "", "", "", "", "", "", ""]);
    //     listcgcj.push([year, "", "", "", "", "", "", date, "", "", "", ""]);
    //     listcgcj.push([
    //       "序号",
    //       "姓名",
    //       "性别",
    //       "身份证号",
    //       "年龄",
    //       "部门",
    //       "涉密等级",
    //       "是否审批",
    //       "去往国家（地区）",
    //       "出境时间",
    //       "返回时间",
    //       "备注",
    //     ]); //确定列名
    //     for (var i in array) {
    //       //每一行的值
    //        //{"name":"XXX","dw":"XXX","zw":"XXX"}

    //        let column = [(parseInt(i) + 1), "", "", "", "", "", "", "", "", "", "", ""];
    //       listcgcj.push(column);
    //     }
    //     console.log(this.sbsjDataZxmsOld.Cgcj_list);
    //     ////三类人员
    //     let listslry = [];
    //     listslry.push(["附件5-6"]);
    //     listslry.push(["基础表6 “三类涉密人员”情况"]);
    //     listslry.push([dwmc, "", "", "", "", "", "", "", "", "", "", ""]);
    //     listslry.push([year, "", "", "", "", "", "", "", date, "", "", ""]);
    //     listslry.push([
    //       "序号",
    //       "姓名",
    //       "性别",
    //       "身份证号",
    //       "年龄",
    //       "部门",
    //       "岗位名称",
    //       "涉密等级",
    //       "用人形式",
    //       "是否审查",
    //       "原单位名称",
    //       "备注",
    //     ]); //确定列名
    //     for (var i in array) {
    //       //每一行的值
    //       //{"name":"XXX","dw":"XXX","zw":"XXX"}

    //       let column = [(parseInt(i) + 1), "", "", "", "", "", "", "", "", "", "", ""];
    //       listslry.push(column);
    //     }
    //     console.log(this.sbsjDataZxmsOld.Slsmry_list);
    //     ////追责情况
    //     let listzzqk = [];
    //     listzzqk.push(["附件5-7"]);
    //     listzzqk.push(["基础表7 涉密人员被追责情况"]);
    //     listzzqk.push([dwmc, "", "", "", "", "", "", "", ""]);
    //     listzzqk.push([year, "", "", "", "", "", date, "", ""]);
    //     listzzqk.push([
    //       "序号",
    //       "姓名",
    //       "性别",
    //       "身份证号",
    //       "年龄",
    //       "(原工作)部门",
    //       "涉密等级",
    //       "被追责情况",
    //       "备注",
    //     ]); //确定列名
    //     for (var i in array) {
    //       //每一行的值
    //        //{"name":"XXX","dw":"XXX","zw":"XXX"}
    //       let column = [
    //         parseInt(i) + 1,
    //         "", "", "", "", "", "", "", ""
    //       ];
    //       listzzqk.push(column);
    //     }
    //     console.log(this.sbsjDataZxmsOld.Smryzzqk_list);
    //     ////保密干部
    //     let listbmgb = [];
    //     listbmgb.push(["附件5-8"]);
    //     listbmgb.push(["基础表8 保密干部情况"]);
    //     listbmgb.push([
    //       dwmc,
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //     ]);
    //     listbmgb.push([
    //       year,
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       date,
    //       "",
    //       "",
    //     ]);
    //     listbmgb.push([
    //       "序号",
    //       "姓名",
    //       "性别",
    //       "身份证号",
    //       "年龄",
    //       "专职/兼职",
    //       "部门",
    //       "岗位名称",
    //       "涉密等级",
    //       "最高学历",
    //       "职务",
    //       "职级",
    //       "级别职称",
    //       "身份类型",
    //       "担任保密干部时间",
    //       "联系方式",
    //       "备注",
    //     ]); //确定列名
    //     for (var i in array) {
    //       //每一行的值
    //       //{"name":"XXX","dw":"XXX","zw":"XXX"}

    //       let column = [
    //         parseInt(i) + 1,
    //         "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       "",
    //       ];
    //       listbmgb.push(column);
    //     }

    //     console.log(this.sbsjDataZxmsOld.Bmgbqk_list);
    //     let sheets = [
    //       {
    //         sheetName: "表1 单位基本信息",
    //         data: listdwxx,
    //         merges: [
    //           {
    //             s: {
    //               //s为开始
    //               c: 0, //开始列
    //               r: 1, //开始取值范围
    //             },
    //             e: {
    //               //e结束
    //               c: 9, //结束列
    //               r: 2, //结束范围
    //             },
    //           },
    //           {
    //             s: {
    //               //s为开始
    //               c: 0, //开始列
    //               r: 3, //开始取值范围
    //             },
    //             e: {
    //               //e结束
    //               c: 1, //结束列
    //               r: 3, //结束范围
    //             },
    //           },
    //           {
    //             s: {
    //               //s为开始
    //               c: 0, //开始列
    //               r: 4, //开始取值范围
    //             },
    //             e: {
    //               //e结束
    //               c: 2, //结束列
    //               r: 4, //结束范围
    //             },
    //           },
    //           {
    //             s: {
    //               //s为开始
    //               c: 5, //开始列
    //               r: 5, //开始取值范围
    //             },
    //             e: {
    //               //e结束
    //               c: 7, //结束列
    //               r: 5, //结束范围
    //             },
    //           },
    //         ],
    //       },
    //       {
    //         sheetName: "表2 涉密岗位统计",
    //         data: listsmgw,
    //         merges: [
    //           {
    //             s: {
    //               //s为开始
    //               c: 0, //开始列
    //               r: 1, //开始取值范围
    //             },
    //             e: {
    //               //e结束
    //               c: 5, //结束列
    //               r: 1, //结束范围
    //             },
    //           },
    //           {
    //             s: {
    //               //s为开始
    //               c: 0, //开始列
    //               r: 2, //开始取值范围
    //             },
    //             e: {
    //               //e结束
    //               c: 1, //结束列
    //               r: 2, //结束范围
    //             },
    //           },
    //           {
    //             s: {
    //               //s为开始
    //               c: 0, //开始列
    //               r: 3, //开始取值范围
    //             },
    //             e: {
    //               //e结束
    //               c: 2, //结束列
    //               r: 3, //结束范围
    //             },
    //           },
    //           {
    //             s: {
    //               //s为开始
    //               c: 3, //开始列
    //               r: 3, //开始取值范围
    //             },
    //             e: {
    //               //e结束
    //               c: 5, //结束列
    //               r: 3, //结束范围
    //             },
    //           },
    //         ],
    //       },
    //       {
    //         sheetName: "表3 涉密人员统计",
    //         data: listsmry,
    //         merges: [
    //           {
    //             s: {
    //               //s为开始
    //               c: 0, //开始列
    //               r: 1, //开始取值范围
    //             },
    //             e: {
    //               //e结束
    //               c: 19, //结束列
    //               r: 1, //结束范围
    //             },
    //           },
    //           {
    //             s: {
    //               //s为开始
    //               c: 0, //开始列
    //               r: 2, //开始取值范围
    //             },
    //             e: {
    //               //e结束
    //               c: 3, //结束列
    //               r: 2, //结束范围
    //             },
    //           },
    //           {
    //             s: {
    //               //s为开始
    //               c: 0, //开始列
    //               r: 3, //开始取值范围
    //             },
    //             e: {
    //               //e结束
    //               c: 2, //结束列
    //               r: 3, //结束范围
    //             },
    //           },
    //           {
    //             s: {
    //               //s为开始
    //               c: 13, //开始列
    //               r: 3, //开始取值范围
    //             },
    //             e: {
    //               //e结束
    //               c: 18, //结束列
    //               r: 3, //结束范围
    //             },
    //           },
    //         ],
    //       },
    //       {
    //         sheetName: "表4 涉密人员脱密期管理台帐",
    //         data: listlglz,
    //         merges: [
    //           {
    //             s: {
    //               //s为开始
    //               c: 0, //开始列
    //               r: 1, //开始取值范围
    //             },
    //             e: {
    //               //e结束
    //               c: 18, //结束列
    //               r: 1, //结束范围
    //             },
    //           },
    //           {
    //             s: {
    //               //s为开始
    //               c: 0, //开始列
    //               r: 2, //开始取值范围
    //             },
    //             e: {
    //               //e结束
    //               c: 1, //结束列
    //               r: 2, //结束范围
    //             },
    //           },
    //           {
    //             s: {
    //               //s为开始
    //               c: 0, //开始列
    //               r: 3, //开始取值范围
    //             },
    //             e: {
    //               //e结束
    //               c: 2, //结束列
    //               r: 3, //结束范围
    //             },
    //           },
    //           {
    //             s: {
    //               //s为开始
    //               c: 16, //开始列
    //               r: 3, //开始取值范围
    //             },
    //             e: {
    //               //e结束
    //               c: 18, //结束列
    //               r: 3, //结束范围
    //             },
    //           },
    //         ],
    //       },
    //       {
    //         sheetName: "表5 因私出国（境）涉密人员统计",
    //         data: listcgcj,
    //         merges: [
    //           {
    //             s: {
    //               //s为开始
    //               c: 0, //开始列
    //               r: 1, //开始取值范围
    //             },
    //             e: {
    //               //e结束
    //               c: 11, //结束列
    //               r: 1, //结束范围
    //             },
    //           },
    //           {
    //             s: {
    //               //s为开始
    //               c: 0, //开始列
    //               r: 2, //开始取值范围
    //             },
    //             e: {
    //               //e结束
    //               c: 3, //结束列
    //               r: 2, //结束范围
    //             },
    //           },
    //           {
    //             s: {
    //               //s为开始
    //               c: 0, //开始列
    //               r: 3, //开始取值范围
    //             },
    //             e: {
    //               //e结束
    //               c: 2, //结束列
    //               r: 3, //结束范围
    //             },
    //           },
    //           {
    //             s: {
    //               //s为开始
    //               c: 7, //开始列
    //               r: 3, //开始取值范围
    //             },
    //             e: {
    //               //e结束
    //               c: 11, //结束列
    //               r: 3, //结束范围
    //             },
    //           },
    //         ],
    //       },
    //       {
    //         sheetName: "表6 “三类涉密人员”情况",
    //         data: listslry,
    //         merges: [
    //           {
    //             s: {
    //               //s为开始
    //               c: 0, //开始列
    //               r: 1, //开始取值范围
    //             },
    //             e: {
    //               //e结束
    //               c: 11, //结束列
    //               r: 1, //结束范围
    //             },
    //           },
    //           {
    //             s: {
    //               //s为开始
    //               c: 0, //开始列
    //               r: 2, //开始取值范围
    //             },
    //             e: {
    //               //e结束
    //               c: 3, //结束列
    //               r: 2, //结束范围
    //             },
    //           },
    //           {
    //             s: {
    //               //s为开始
    //               c: 0, //开始列
    //               r: 3, //开始取值范围
    //             },
    //             e: {
    //               //e结束
    //               c: 2, //结束列
    //               r: 3, //结束范围
    //             },
    //           },
    //           {
    //             s: {
    //               //s为开始
    //               c: 8, //开始列
    //               r: 3, //开始取值范围
    //             },
    //             e: {
    //               //e结束
    //               c: 11, //结束列
    //               r: 3, //结束范围
    //             },
    //           },
    //         ],
    //       },
    //       {
    //         sheetName: "表7 涉密人员被追责情况",
    //         data: listzzqk,
    //         merges: [
    //           {
    //             s: {
    //               //s为开始
    //               c: 0, //开始列
    //               r: 1, //开始取值范围
    //             },
    //             e: {
    //               //e结束
    //               c: 8, //结束列
    //               r: 1, //结束范围
    //             },
    //           },
    //           {
    //             s: {
    //               //s为开始
    //               c: 0, //开始列
    //               r: 2, //开始取值范围
    //             },
    //             e: {
    //               //e结束
    //               c: 3, //结束列
    //               r: 2, //结束范围
    //             },
    //           },
    //           {
    //             s: {
    //               //s为开始
    //               c: 0, //开始列
    //               r: 3, //开始取值范围
    //             },
    //             e: {
    //               //e结束
    //               c: 2, //结束列
    //               r: 3, //结束范围
    //             },
    //           },
    //           {
    //             s: {
    //               //s为开始
    //               c: 6, //开始列
    //               r: 3, //开始取值范围
    //             },
    //             e: {
    //               //e结束
    //               c: 8, //结束列
    //               r: 3, //结束范围
    //             },
    //           },
    //         ],
    //       },
    //       {
    //         sheetName: "表8 保密干部情况",
    //         data: listbmgb,
    //         merges: [
    //           {
    //             s: {
    //               //s为开始
    //               c: 0, //开始列
    //               r: 1, //开始取值范围
    //             },
    //             e: {
    //               //e结束
    //               c: 16, //结束列
    //               r: 1, //结束范围
    //             },
    //           },
    //           {
    //             s: {
    //               //s为开始
    //               c: 0, //开始列
    //               r: 2, //开始取值范围
    //             },
    //             e: {
    //               //e结束
    //               c: 3, //结束列
    //               r: 2, //结束范围
    //             },
    //           },
    //           {
    //             s: {
    //               //s为开始
    //               c: 0, //开始列
    //               r: 3, //开始取值范围
    //             },
    //             e: {
    //               //e结束
    //               c: 2, //结束列
    //               r: 3, //结束范围
    //             },
    //           },
    //           {
    //             s: {
    //               //s为开始
    //               c: 14, //开始列
    //               r: 3, //开始取值范围
    //             },
    //             e: {
    //               //e结束
    //               c: 16, //结束列
    //               r: 3, //结束范围
    //             },
    //           },
    //         ],
    //       },

    //     ];
    //     let styles;
    //     styles = {
    //       cols: {
    //         scoped: -1,
    //         style: [
    //           { wpx: 150 },
    //           { wpx: 150 },
    //           { wpx: 150 },
    //           { wpx: 150 },
    //           { wpx: 150 },
    //           { wpx: 150 },
    //           { wpx: 150 },
    //           { wpx: 150 },
    //           { wpx: 150 },
    //           { wpx: 150 },
    //           { wpx: 150 },
    //           { wpx: 150 },
    //           { wpx: 150 },
    //           { wpx: 150 },
    //           { wpx: 150 },
    //           { wpx: 150 },
    //           { wpx: 150 },
    //           { wpx: 150 },
    //         ],
    //       },
    //       // 全局样式
    //       all: {
    //         alignment: {
    //           horizontal: "center", // 水平居中
    //           vertical: "center", // 垂直居中
    //           wrapText: true, // 文字换行
    //         },
    //         font: {
    //           sz: 11, // 字号
    //           name: "宋体", // 字体
    //           bold: true,
    //         },
    //         border: {
    //           // 边框
    //           top: {
    //             style: "thin",
    //           },
    //           bottom: {
    //             style: "thin",
    //           },
    //           left: {
    //             style: "thin",
    //           },
    //           right: {
    //             style: "thin",
    //           },
    //         },
    //       },
    //       // 单元格样式
    //       cell: [
    //         {
    //           // 生效sheet页索引（值为 -1 时所有sheet页都生效）
    //           scoped: -1,
    //           // 索引
    //           index: "A2",
    //           style: {
    //             font: {
    //               name: "宋体",
    //               sz: 16, // 字号
    //               bold: true,
    //             },
    //             alignment: {
    //               horizontal: "center", // 水平居中
    //               vertical: "center", // 垂直居中
    //             },
    //           },
    //         },
    //       ],
    //     };
    //     let config = { rowIndexArrIgnore: [1] };

    //     exportExcelNumerousSheet1(result, sheets, styles, config);
    //     this.$message("导出成功:" + result);
    //     this.dialogVisibleSjsb = false;
    //   });
    // },
    // // 数据上报方法
    // generateSbsj() {
    //   let FS = require("fs");
    //   let params = this.dialogSjsb;
    //   if (!params) {
    //     this.$message.warning("上报时间或上报类型数据异常");
    //     return;
    //   }
    //   let sbsjlxParams = this.sbsjlx;
    //   let sbYear = params.sbsj;
    //   let sblx = params.sblx;
    //   if (!sbsjlxParams) {
    //     this.$message.warning("上报数据类型未选择");
    //     return;
    //   }
    //   if (!sbYear) {
    //     this.$message.warning("上报时间未选择");
    //     return;
    //   }
    //   if (!sblx || !checkArr(sblx) || sblx.length < 1) {
    //     this.$message.warning("上报类型未选择");
    //     return;
    //   }
    //   // 弹出dialog选择上报数据保存文件位置
    //   let sbsjFileName =
    //     "上报数据-" + sbYear + "-" + new Date().getTime() + ".json";
    //   const { dialog } = require("electron").remote;
    //   let options = {
    //     title: "选择上报数据保存位置",
    //     defaultPath: sbsjFileName,
    //   };
    //   dialog.showSaveDialog(options, (result) => {
    //     let zczpCurrentJsonObj = {};
    //     // 自选模式
    //     if (sbsjlxParams == 2) {
    //       console.log("自选模式...");
    //       zczpCurrentJsonObj = this.handleSbsjZxms(sbYear);
    //     } else {
    //       // 自动筛选模式
    //       console.log("自动筛选模式");
    //       zczpCurrentJsonObj = this.handleSbsjZdsxms(sbYear, sblx, FS);
    //     }
    //     // json对象中加入本次数据所属的年份(共后台区分具体是哪个年份的数据)
    //     zczpCurrentJsonObj["data_mess"] = [
    //       {
    //         ssnf: sbYear,
    //       },
    //     ];
    //     console.log(zczpCurrentJsonObj);
    //     // return
    //     // 密钥年份
    //     let currentYear = new Date().getFullYear();
    //     // 生成密钥
    //     let keyStr = "hsoft3Banner" + currentYear;
    //     // 数据加密
    //     let encryptStr;
    //     try {
    //       encryptStr = encryptAesCBC(
    //         JSON.stringify(zczpCurrentJsonObj),
    //         keyStr
    //       );
    //     } catch (error) {
    //       this.$notify({
    //         title: "系统异常",
    //         message: "[" + "上报数据加密异常" + "]\n" + error.message,
    //         type: "error",
    //         offset: 100,
    //         duration: 0,
    //       });
    //       return;
    //     }
    //     // 测试解密
    //     let decryptJsonObj;
    //     try {
    //       decryptJsonObj = JSON.parse(decryptAesCBC(encryptStr, keyStr));
    //     } catch (error) {
    //       this.$notify({
    //         title: "系统异常",
    //         message: "[" + "上报数据测试解密异常" + "]\n" + error.message,
    //         type: "error",
    //         offset: 100,
    //         duration: 0,
    //       });
    //       return;
    //     }
    //     // 生成上报数据文件
    //     try {
    //       // FS.writeFileSync(result, encryptStr, { encoding: "utf8" });
    //       // 未加密数据(测试对接时使用)
    //       FS.writeFileSync(result.substring(0, result.indexOf('.') + 1) + '(未加密).json', JSON.stringify(zczpCurrentJsonObj), { encoding: 'utf8' })
    //     } catch (error) {
    //       error = errorProcessor(error);
    //       let errObj = JSON.parse(error.message);
    //       this.$notify({
    //         title: "系统异常",
    //         message: "[" + errObj.mark + "]\n",
    //         type: "error",
    //         offset: 100,
    //         duration: 0,
    //       });
    //       return;
    //     }
    //     // 上报数据生成成功
    //     this.$message.success("上报数据生成成功=>" + result);
    //     // 写入日志
    //     let logParams = {
    //       xyybs: "yybs_sjsb",
    //       ymngnmc: "数据上报",
    //       extraParams: params,
    //     };
    //     writeSystemOptionsLog(logParams);
    //     this.dialogVisibleSjsb = false;
    //   });
    // },
    generateSbsj() {
      this.getDwxx();
      this.initTableDataZxms();
      if (getlogin()[0]) {
        this.dwmc = getlogin()[0].dwmc;
      }
      let filename = "涉密人员情况表" + this.Date + ".xlsx";
      const { dialog } = require("electron").remote;
      //弹窗title
      let options = {
        title: "保存文件",
        defaultPath: filename,
      };
      dialog.showSaveDialog(options, (result) => {
        console.log("result", result);
        if (result == null || result == "") {
          console.log("取消导出");
          return;
        }
        /////涉密单位
        let listdwxx = [];
        listdwxx.push(["附件5-1"]);
        listdwxx.push(["基础表1 单位基本信息"]);
        let dwmc = "(填报单位名称、印章)" + this.dwmc;
        let year = "统计年度：" + this.yearwn;
        let date = "填报时间：" + this.Date;
        listdwxx.push(["", "", "", "", "", "", "", "", "", ""]);
        listdwxx.push([dwmc, "", "", "", "", "", "", "", "", ""]);
        listdwxx.push([year, "", "", "", "", "", "", "", "", ""]);
        listdwxx.push([
          "本单位名称",
          "本单位类型",
          "本单位所属领域",
          "本单位所属层级",
          "本单位统一社会信用代码",
          "本单位所在行政区域",
          "",
          "",
          "单位联系人",
          "单位联系电话",
        ]);
        // 添加条件判断并去掉“省”字
        let province = this.dwxx.province;
        if (province && province.includes("省")) {
          province = province.replace("省", "");
        }
        listdwxx.push([
          this.dwxx.dwmc,
          this.dwxx.dwlxmc,
          this.dwxx.sslymc,
          this.dwxx.ssccmc,
          this.dwxx.xydm,
          province,
          this.dwxx.city,
          this.dwxx.district,
          this.dwxx.dwlxr,
          this.dwxx.dwlxdh,
        ]);
        console.log(this.dwxx, "this.dwxx");
        console.log(listdwxx);
        ////涉密岗位
        let listsmgw = [];
        listsmgw.push(["附件5-2"]);
        listsmgw.push(["基础表2 涉密岗位统计情况"]);
        listsmgw.push([dwmc, "", "", "", "", ""]);
        listsmgw.push([year, "", "", date, "", ""]);
        listsmgw.push([
          "序号",
          "部门",
          "岗位名称",
          "涉密等级",
          "岗位确定依据",
          "备注",
        ]); //确定列名
        for (var i in this.sbsjDataZxmsOld.Smgwgl_list) {
          //每一行的值
          let item = this.sbsjDataZxmsOld.Smgwgl_list[i]; //{"name":"XXX","dw":"XXX","zw":"XXX"}

          console.log("导出值:", this.sbsjDataZxmsOld.Smgwgl_list);
          let column = [
            parseInt(i) + 1,
            item["bm"],
            item["gwmc"],
            item["smdj"],
            item["gwqdyj"],
            item["bz"],
          ];
          listsmgw.push(column);
        }
        console.log(this.sbsjDataZxmsOld.Smgwgl_list);
        ////涉密人员
        let listsmry = [];
        listsmry.push(["附件5-3"]);
        listsmry.push(["基础表3 涉密人员统计情况"]);
        listsmry.push([
          dwmc,
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
        ]);
        listsmry.push([
          year,
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          date,
          "",
          "",
          "",
          "",
          "",
        ]);
        listsmry.push([
          "序号",
          "姓名",
          "性别",
          "身份证号",
          "年龄",
          "部门",
          "岗位名称",
          "涉密等级",
          "最高学历",
          "职务",
          "职级",
          "级别职称",
          "身份类型",
          "用人形式",
          "是否审查",
          "是否出入境登记备案",
          "是否统一保管出入境证件",
          "上岗时间（现涉密岗位）",
          "备注",
        ]); //确定列名
        for (var i in this.sbsjDataZxmsOld.Smry_list) {
          //每一行的值
          let item = this.sbsjDataZxmsOld.Smry_list[i]; //{"name":"XXX","dw":"XXX","zw":"XXX"}

          console.log("导出值:", this.sbsjDataZxmsOld.Smry_list);
          let column = [
            parseInt(i) + 1,
            item["xm"],
            item["xb"],
            item["sfzhm"],
            item["nl"],
            item["bm"],
            item["gwmc"].toString(),
            item["smdj"],
            item["zgxl"],
            item["zw"],
            item["zj"],
            item["zc"],
            item["sflx"],
            item["yrxs"],
            item["sfsc"],
            item["crjdjba"],
            item["tybgcrjzj"],
            item["sgsj"],
            item["bz"],
          ];
          listsmry.push(column);
        }
        console.log(this.sbsjDataZxmsOld.Smry_list);
        ////离岗离职
        let listlglz = [];
        listlglz.push(["附件5-4"]);
        listlglz.push(["基础表4 涉密人员脱密期管理台帐"]);
        listlglz.push([
          dwmc,
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
        ]);
        listlglz.push([
          year,
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          date,
          "",
        ]);
        listlglz.push([
          "序号",
          "姓名",
          "性别",
          "身份证号",
          "年龄",
          "原部门",
          "原涉密等级",
          "职务",
          "职级",
          "级别职称",
          "原身份类型",
          "是否到公安机关出入境备案",
          "是否委托管理",
          "脱密期开始时间",
          "脱密期结束时间",
          "手机号码",
          "离职离岗类型",
          "去向单位名称",
          "备注",
        ]); //确定列名
        for (var i in this.sbsjDataZxmsOld.Lglz_list) {
          //每一行的值
          let item = this.sbsjDataZxmsOld.Lglz_list[i]; //{"name":"XXX","dw":"XXX","zw":"XXX"}

          console.log("导出值:", this.sbsjDataZxmsOld.Lglz_list);
          let column = [
            parseInt(i) + 1,
            item["xm"],
            item["xb"],
            item["sfzhm"],
            item["nl"],
            item["ybm"],
            item["ysmdj"],
            item["zw"],
            item["zj"],
            item["zc"],
            item["ysflx"],
            item["gajgcrjba"],
            item["sfwtgl"],
            item["tmqkssj"],
            item["tmqjssj"],
            item["sjhm"],
            item["lzlglx"],
            item["qxdwmc"],
            item["bz"],
          ];
          listlglz.push(column);
        }
        console.log(this.sbsjDataZxmsOld.Lglz_list);
        ////出国出境
        let listcgcj = [];
        listcgcj.push(["附件5-5"]);
        listcgcj.push(["基础表5 因私出国（境）涉密人员统计"]);
        listcgcj.push([dwmc, "", "", "", "", "", "", "", "", "", "", ""]);
        listcgcj.push([year, "", "", "", "", "", "", date, "", "", "", ""]);
        listcgcj.push([
          "序号",
          "姓名",
          "性别",
          "身份证号",
          "年龄",
          "部门",
          "涉密等级",
          "是否审批",
          "去往国家（地区）",
          "出境时间",
          "返回时间",
          "备注",
        ]); //确定列名
        for (var i in this.sbsjDataZxmsOld.Cgcj_list) {
          //每一行的值
          let item = this.sbsjDataZxmsOld.Cgcj_list[i]; //{"name":"XXX","dw":"XXX","zw":"XXX"}

          console.log("导出值:", this.sbsjDataZxmsOld.Cgcj_list);
          let column = [
            parseInt(i) + 1,
            item["xm"],
            item["xb"],
            item["sfzhm"],
            item["nl"],
            item["bmmc"],
            item["smdj"],
            item["sfsp"],
            item["qwgj"],
            item["cgsj"],
            item["ggsj"],
            item["bz"],
          ];
          listcgcj.push(column);
        }
        console.log(this.sbsjDataZxmsOld.Cgcj_list);
        ////三类人员
        let listslry = [];
        listslry.push(["附件5-6"]);
        listslry.push(["基础表6 “三类涉密人员”情况"]);
        listslry.push([dwmc, "", "", "", "", "", "", "", "", "", "", ""]);
        listslry.push([year, "", "", "", "", "", "", "", date, "", "", ""]);
        listslry.push([
          "序号",
          "姓名",
          "性别",
          "身份证号",
          "年龄",
          "部门",
          "岗位名称",
          "涉密等级",
          "用人形式",
          "是否审查",
          "原单位名称",
          "备注",
        ]); //确定列名
        for (var i in this.sbsjDataZxmsOld.Slsmry_list) {
          //每一行的值
          let item = this.sbsjDataZxmsOld.Slsmry_list[i]; //{"name":"XXX","dw":"XXX","zw":"XXX"}

          console.log("导出值:", this.sbsjDataZxmsOld.Slsmry_list);
          let column = [
            parseInt(i) + 1,
            item["xm"],
            item["xb"],
            item["sfzhm"],
            item["nl"],
            item["bmmc"],
            item["gwmc"].toString(),
            item["smdj"],
            item["yrxs"],
            item["sfsc"],
            item["ydwmc"],
            item["bz"],
          ];
          listslry.push(column);
        }
        console.log(this.sbsjDataZxmsOld.Slsmry_list);
        ////追责情况
        let listzzqk = [];
        listzzqk.push(["附件5-7"]);
        listzzqk.push(["基础表7 涉密人员被追责情况"]);
        listzzqk.push([dwmc, "", "", "", "", "", "", "", ""]);
        listzzqk.push([year, "", "", "", "", "", date, "", ""]);
        listzzqk.push([
          "序号",
          "姓名",
          "性别",
          "身份证号",
          "年龄",
          "(原工作)部门",
          "涉密等级",
          "被追责情况",
          "备注",
        ]); //确定列名
        for (var i in this.sbsjDataZxmsOld.Smryzzqk_list) {
          //每一行的值
          let item = this.sbsjDataZxmsOld.Smryzzqk_list[i]; //{"name":"XXX","dw":"XXX","zw":"XXX"}

          console.log("导出值:", this.sbsjDataZxmsOld.Smryzzqk_list);
          let column = [
            parseInt(i) + 1,
            item["xm"],
            item["xb"],
            item["sfzhm"],
            item["nl"],
            item["bmmc"],
            item["smdj"],
            item["bzzqk"],
            item["bz"],
          ];
          listzzqk.push(column);
        }
        console.log(this.sbsjDataZxmsOld.Smryzzqk_list);
        ////保密干部
        let listbmgb = [];
        listbmgb.push(["附件5-8"]);
        listbmgb.push(["基础表8 保密干部情况"]);
        listbmgb.push([
          dwmc,
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
        ]);
        listbmgb.push([
          year,
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          date,
          "",
          "",
        ]);
        listbmgb.push([
          "序号",
          "姓名",
          "性别",
          "身份证号",
          "年龄",
          "专职/兼职",
          "部门",
          "岗位名称",
          "涉密等级",
          "最高学历",
          "职务",
          "职级",
          "级别职称",
          "身份类型",
          "担任保密干部时间",
          "联系方式",
          "备注",
        ]); //确定列名
        for (var i in this.sbsjDataZxmsOld.Bmgbqk_list) {
          //每一行的值
          let item = this.sbsjDataZxmsOld.Bmgbqk_list[i]; //{"name":"XXX","dw":"XXX","zw":"XXX"}

          console.log("导出值:", this.sbsjDataZxmsOld.Bmgbqk_list);
          let column = [
            parseInt(i) + 1,
            item["xm"],
            item["xb"],
            item["sfzh"],
            item["nl"],
            item["sfzz"],
            item["bm"],
            item["gwmc"].toString(),
            item["smdj"],
            item["zgxl"],
            item["zw"],
            item["zj"],
            item["jbzc"],
            item["sflx"],
            item["drbmgbsj"],
            item["lxdh"],
            item["bz"],
          ];
          listbmgb.push(column);
        }
        console.log(this.sbsjDataZxmsOld.Bmgbqk_list);
        let sheets = [
          {
            sheetName: "表1 单位基本信息",
            data: listdwxx,
            merges: [
              {
                s: {
                  //s为开始
                  c: 0, //开始列
                  r: 1, //开始取值范围
                },
                e: {
                  //e结束
                  c: 9, //结束列
                  r: 2, //结束范围
                },
              },
              {
                s: {
                  //s为开始
                  c: 0, //开始列
                  r: 2, //开始取值范围
                },
                e: {
                  //e结束
                  c: 1, //结束列
                  r: 2, //结束范围
                },
              },
              {
                s: {
                  //s为开始
                  c: 0, //开始列
                  r: 3, //开始取值范围
                },
                e: {
                  //e结束
                  c: 2, //结束列
                  r: 3, //结束范围
                },
              },
              {
                s: {
                  //s为开始
                  c: 5, //开始列
                  r: 5, //开始取值范围
                },
                e: {
                  //e结束
                  c: 7, //结束列
                  r: 5, //结束范围
                },
              },
            ],
          },
          {
            sheetName: "表2 涉密岗位统计",
            data: listsmgw,
            merges: [
              {
                s: {
                  //s为开始
                  c: 0, //开始列
                  r: 1, //开始取值范围
                },
                e: {
                  //e结束
                  c: 5, //结束列
                  r: 1, //结束范围
                },
              },
              {
                s: {
                  //s为开始
                  c: 0, //开始列
                  r: 2, //开始取值范围
                },
                e: {
                  //e结束
                  c: 1, //结束列
                  r: 2, //结束范围
                },
              },
              {
                s: {
                  //s为开始
                  c: 0, //开始列
                  r: 3, //开始取值范围
                },
                e: {
                  //e结束
                  c: 2, //结束列
                  r: 3, //结束范围
                },
              },
              {
                s: {
                  //s为开始
                  c: 3, //开始列
                  r: 3, //开始取值范围
                },
                e: {
                  //e结束
                  c: 5, //结束列
                  r: 3, //结束范围
                },
              },
            ],
          },
          {
            sheetName: "表3 涉密人员统计",
            data: listsmry,
            merges: [
              {
                s: {
                  //s为开始
                  c: 0, //开始列
                  r: 1, //开始取值范围
                },
                e: {
                  //e结束
                  c: 19, //结束列
                  r: 1, //结束范围
                },
              },
              {
                s: {
                  //s为开始
                  c: 0, //开始列
                  r: 2, //开始取值范围
                },
                e: {
                  //e结束
                  c: 3, //结束列
                  r: 2, //结束范围
                },
              },
              {
                s: {
                  //s为开始
                  c: 0, //开始列
                  r: 3, //开始取值范围
                },
                e: {
                  //e结束
                  c: 2, //结束列
                  r: 3, //结束范围
                },
              },
              {
                s: {
                  //s为开始
                  c: 13, //开始列
                  r: 3, //开始取值范围
                },
                e: {
                  //e结束
                  c: 18, //结束列
                  r: 3, //结束范围
                },
              },
            ],
          },
          {
            sheetName: "表4 涉密人员脱密期管理台帐",
            data: listlglz,
            merges: [
              {
                s: {
                  //s为开始
                  c: 0, //开始列
                  r: 1, //开始取值范围
                },
                e: {
                  //e结束
                  c: 18, //结束列
                  r: 1, //结束范围
                },
              },
              {
                s: {
                  //s为开始
                  c: 0, //开始列
                  r: 2, //开始取值范围
                },
                e: {
                  //e结束
                  c: 1, //结束列
                  r: 2, //结束范围
                },
              },
              {
                s: {
                  //s为开始
                  c: 0, //开始列
                  r: 3, //开始取值范围
                },
                e: {
                  //e结束
                  c: 2, //结束列
                  r: 3, //结束范围
                },
              },
              {
                s: {
                  //s为开始
                  c: 16, //开始列
                  r: 3, //开始取值范围
                },
                e: {
                  //e结束
                  c: 18, //结束列
                  r: 3, //结束范围
                },
              },
            ],
          },
          {
            sheetName: "表5 因私出国（境）涉密人员统计",
            data: listcgcj,
            merges: [
              {
                s: {
                  //s为开始
                  c: 0, //开始列
                  r: 1, //开始取值范围
                },
                e: {
                  //e结束
                  c: 11, //结束列
                  r: 1, //结束范围
                },
              },
              {
                s: {
                  //s为开始
                  c: 0, //开始列
                  r: 2, //开始取值范围
                },
                e: {
                  //e结束
                  c: 3, //结束列
                  r: 2, //结束范围
                },
              },
              {
                s: {
                  //s为开始
                  c: 0, //开始列
                  r: 3, //开始取值范围
                },
                e: {
                  //e结束
                  c: 2, //结束列
                  r: 3, //结束范围
                },
              },
              {
                s: {
                  //s为开始
                  c: 7, //开始列
                  r: 3, //开始取值范围
                },
                e: {
                  //e结束
                  c: 11, //结束列
                  r: 3, //结束范围
                },
              },
            ],
          },
          {
            sheetName: "表6 “三类涉密人员”情况",
            data: listslry,
            merges: [
              {
                s: {
                  //s为开始
                  c: 0, //开始列
                  r: 1, //开始取值范围
                },
                e: {
                  //e结束
                  c: 11, //结束列
                  r: 1, //结束范围
                },
              },
              {
                s: {
                  //s为开始
                  c: 0, //开始列
                  r: 2, //开始取值范围
                },
                e: {
                  //e结束
                  c: 3, //结束列
                  r: 2, //结束范围
                },
              },
              {
                s: {
                  //s为开始
                  c: 0, //开始列
                  r: 3, //开始取值范围
                },
                e: {
                  //e结束
                  c: 2, //结束列
                  r: 3, //结束范围
                },
              },
              {
                s: {
                  //s为开始
                  c: 8, //开始列
                  r: 3, //开始取值范围
                },
                e: {
                  //e结束
                  c: 11, //结束列
                  r: 3, //结束范围
                },
              },
            ],
          },
          {
            sheetName: "表7 涉密人员被追责情况",
            data: listzzqk,
            merges: [
              {
                s: {
                  //s为开始
                  c: 0, //开始列
                  r: 1, //开始取值范围
                },
                e: {
                  //e结束
                  c: 8, //结束列
                  r: 1, //结束范围
                },
              },
              {
                s: {
                  //s为开始
                  c: 0, //开始列
                  r: 2, //开始取值范围
                },
                e: {
                  //e结束
                  c: 3, //结束列
                  r: 2, //结束范围
                },
              },
              {
                s: {
                  //s为开始
                  c: 0, //开始列
                  r: 3, //开始取值范围
                },
                e: {
                  //e结束
                  c: 2, //结束列
                  r: 3, //结束范围
                },
              },
              {
                s: {
                  //s为开始
                  c: 6, //开始列
                  r: 3, //开始取值范围
                },
                e: {
                  //e结束
                  c: 8, //结束列
                  r: 3, //结束范围
                },
              },
            ],
          },
          {
            sheetName: "表8 保密干部情况",
            data: listbmgb,
            merges: [
              {
                s: {
                  //s为开始
                  c: 0, //开始列
                  r: 1, //开始取值范围
                },
                e: {
                  //e结束
                  c: 16, //结束列
                  r: 1, //结束范围
                },
              },
              {
                s: {
                  //s为开始
                  c: 0, //开始列
                  r: 2, //开始取值范围
                },
                e: {
                  //e结束
                  c: 3, //结束列
                  r: 2, //结束范围
                },
              },
              {
                s: {
                  //s为开始
                  c: 0, //开始列
                  r: 3, //开始取值范围
                },
                e: {
                  //e结束
                  c: 2, //结束列
                  r: 3, //结束范围
                },
              },
              {
                s: {
                  //s为开始
                  c: 14, //开始列
                  r: 3, //开始取值范围
                },
                e: {
                  //e结束
                  c: 16, //结束列
                  r: 3, //结束范围
                },
              },
            ],
          },
        ];
        let styles;
        styles = {
          cols: {
            scoped: -1,
            style: [
              { wpx: 150 },
              { wpx: 150 },
              { wpx: 150 },
              { wpx: 150 },
              { wpx: 150 },
              { wpx: 150 },
              { wpx: 150 },
              { wpx: 150 },
              { wpx: 150 },
              { wpx: 150 },
              { wpx: 150 },
              { wpx: 150 },
              { wpx: 150 },
              { wpx: 150 },
              { wpx: 150 },
              { wpx: 150 },
              { wpx: 150 },
              { wpx: 150 },
            ],
          },
          // 全局样式
          all: {
            alignment: {
              horizontal: "center", // 水平居中
              vertical: "center", // 垂直居中
              wrapText: true, // 文字换行
            },
            font: {
              sz: 11, // 字号
              name: "宋体", // 字体
              bold: true,
            },
            border: {
              // 边框
              top: {
                style: "thin",
              },
              bottom: {
                style: "thin",
              },
              left: {
                style: "thin",
              },
              right: {
                style: "thin",
              },
            },
          },
          // 单元格样式
          cell: [
            {
              // 生效sheet页索引（值为 -1 时所有sheet页都生效）
              scoped: -1,
              // 索引
              index: "A2",
              style: {
                font: {
                  name: "宋体",
                  sz: 16, // 字号
                  bold: true,
                },
                alignment: {
                  horizontal: "center", // 水平居中
                  vertical: "center", // 垂直居中
                },
              },
            },
          ],
        };
        let config = { rowIndexArrIgnore: [1] };

        exportExcelNumerousSheet1(result, sheets, styles, config);
        this.$message("导出成功:" + result);
        this.dialogVisibleSjsb = false;
      });
    },
    shujudaoru() {
      console.log("shujudaoru");
      console.log(this.$refs);
      this.$refs.upload.addEventListener("change", (e) => {
        //绑定监听表格导入事件
        this.readExcel(e);
      });
      this.$refs.upload.click();
    },
    isChineseStandardTime(dateString) {
      // 尝试将字符串解析为日期对象
      const date = new Date(dateString);

      // 检查日期对象是否有效
      if (isNaN(date.getTime())) {
        return false;
      } else {
        return true;
      }
    },
    //----表格导入方法
    readExcel(e) {
      // var that = this;
      this.getDwxx();
      this.zzjg = getbmmc();
      console.log(this.zzjg, "zzjg");
      if (this.zzjg.length !== 0) {
        this.bmm = this.zzjg.filter((item) => item.fbmm == "")[0].bmm;
      }
      console.log(this.bmm, "bmm");
      this.dialogVisibleSjdc = false;
      this.loading = true;
      console.log(1);
      if (this.sjdrfs == "2") {
        deletexszzjgDgdrfg();
        deleteSmgwgl1();
        deletesmry2();
        deleteLglzall();
        deleteCgcjall();
        deleteSlryall();
        deleteSmrybzzall();
        deleteBmgball();
      }
      setTimeout(() => {
        const files = e.target.files;
        console.log("files", files);
        var vali = /\.(xls|xlsx)$/;
        if (files.length <= 0) {
          //如果没有文件名
          return false;
        } else if (!vali.test(files[0].name.toLowerCase())) {
          this.$Message.error("上传格式不正确，请上传xls或者xlsx格式");
          return false;
        }
        console.log("开始读取文件");
        const fileReader = new FileReader();

        fileReader.onload = (e) => {
          console.log("e", e);
          try {
            const data = e.target.result;
            const workdata = XLSX.read(data, {
              type: "binary",
              cellDates: true, //设为true，将天数的时间戳转为时间格式
            });
            console.log("文件的内容：", workdata); // 文件的内容
            //查看打印能看到 workdata的sheetNames里是文件中每个表的有值的集合；
            const dwxxwsname = workdata.SheetNames[0]; //取第一张表
            console.log("wsname", dwxxwsname);
            const dwxxsj = XLSX.utils.sheet_to_json(
              workdata.Sheets[dwxxwsname],
              {
                header: 1,
                range: 6,
              }
            ); //生成json表格内容

            console.log(dwxxsj); //自第二行开始的内容
            // 处理上岗时间为汉字时间（后期建议使用时间戳）
            let dwxx = dwxxsj.map((row) => ({
              dwmc: row[0],
              dwlx: row[1],
              ssly: row[2],
              sscc: row[3],
              xydm: row[4],
              province: row[5],
              city: row[6],
              district: row[7],
              dwlxr: row[8],
              dwlxdh: row[9],
            }));
            console.log(this.dwlxList);
            let dwlx = this.dwlxList.find(
              (item) => item.dwlxmc === dwxx[0].dwlx
            );
            console.log(dwlx);
            console.log(this.sslyList);
            let ssly = this.sslyList.find(
              (item) => item.sslymc === dwxx[0].ssly
            );
            console.log(ssly);
            console.log(this.ssccList);
            let sscc = this.ssccList.find(
              (item) => item.ssccmc === dwxx[0].sscc
            );
            console.log(sscc);
            dwxx[0].dwlx = dwlx ? dwlx.dwlxid : null;
            console.log(222222);
            dwxx[0].ssly = ssly ? ssly.sslyid : null;
            dwxx[0].sscc = sscc ? sscc.ssccid : null;
            console.log(this.dwxx);
            dwxx[0].dwid = this.dwxx.dwid;
            console.log(111111);
            updateDwxx(dwxx[0]);
            console.log(2);
            ////涉密岗位
            const smgwwsname = workdata.SheetNames[1]; //取第一张表
            console.log("wsname", smgwwsname);
            const smgwsj = XLSX.utils.sheet_to_json(
              workdata.Sheets[smgwwsname],
              {
                header: 1,
                range: 5,
              }
            ); //生成json表格内容
            console.log(smgwsj, "smgwsj");
            let smgwxx = smgwsj.map((row) => ({
              bm: row[1],
              gwmc: row[2],
              smdj: row[3],
              gwqdyj: row[4],
              bz: row[5],
            }));
            for (var i in smgwxx) {
              if (smgwxx[i]["bm"]) {
                var params = {
                  bm: smgwxx[i]["bm"],
                  gwmc: smgwxx[i]["gwmc"],
                  smdj: smgwxx[i]["smdj"],
                  gwqdyj: smgwxx[i]["gwqdyj"],
                  // rs: this.multipleTable[i]["__EMPTY_4"],
                  // gwdyjb: this.multipleTable[i]["岗位对应级别"],
                  bz: smgwxx[i]["bz"],
                  smgwid: getUuid(),
                };
                let zzjg = {
                  zzjgh: getUuid(),
                  label: smgwxx[i]["bm"],
                  bmm: getUuid(),
                  bmflag: "否",
                  fbmm: this.bmm,
                };
                console.log(zzjg, "zzzzzzzzzzzzzz");
                addxszzjg(zzjg);
                addDrSmgwgl(params);
              }
            }
            console.log(3);
            ////涉密人员
            const smrywsname = workdata.SheetNames[2]; //取第一张表
            const smrysj = XLSX.utils.sheet_to_json(
              workdata.Sheets[smrywsname],
              {
                header: 1,
                range: 5,
              }
            ); //生成json表格内容
            let smryxx = smrysj.map((row) => ({
              xm: row[1],
              xb: row[2],
              sfzhm: row[3],
              nl: row[4],
              bm: row[5],
              gwmc: row[6],
              smdj: row[7],
              zgxl: row[8],
              zw: row[9],
              zj: row[10],
              zc: row[11],
              sflx: row[12],
              yrxs: row[13],
              sfsc: row[14],
              crjdjba: row[15],
              tybgcrjzj: row[16],
              sgsj: row[17],
              bz: row[18],
            }));
            for (var i in smryxx) {
              if (smryxx[i]["xm"] && smryxx[i]["sfzhm"]) {
                console.log(smryxx[i]["sgsj"]);
                console.log(this.isChineseStandardTime(smryxx[i]["sgsj"]));
                var params = {
                  xm: smryxx[i]["xm"],
                  xb: smryxx[i]["xb"],
                  nl: smryxx[i]["nl"],
                  sfzhm: smryxx[i]["sfzhm"],
                  bm: smryxx[i]["bm"],
                  gwmc: smryxx[i]["gwmc"],
                  smdj: smryxx[i]["smdj"],
                  zgxl: smryxx[i]["zgxl"],
                  zc: smryxx[i]["zc"],
                  zw: smryxx[i]["zw"],
                  zj: smryxx[i]["zj"],
                  sflx: smryxx[i]["sflx"],
                  yrxs: smryxx[i]["yrxs"],
                  sfsc: smryxx[i]["sfsc"],
                  crjdjba: smryxx[i]["crjdjba"],
                  tybgcrjzj: smryxx[i]["tybgcrjzj"],
                  sgsj: this.isChineseStandardTime(smryxx[i]["sgsj"])
                    ? dateFormatNYRChinese(smryxx[i]["sgsj"])
                    : smryxx[i]["sgsj"],
                  gwqdyj: "定性标准确定",
                  // rs: this.multipleTable[i]["__EMPTY_4"],
                  // gwdyjb: this.multipleTable[i]["岗位对应级别"],
                  bz: smryxx[i]["bz"],
                };
                let deleteParams = {};
                deleteParams.sfzhm = params.sfzhm;
                if (this.sjdrfs !== "2") {
                  deletesmry(deleteParams);
                }
                addsmry(params);
              }
            }
            console.log(4);
            ////离岗离职
            const lglzwsname = workdata.SheetNames[3]; //取第一张表
            const lglzsj = XLSX.utils.sheet_to_json(
              workdata.Sheets[lglzwsname],
              {
                header: 1,
                range: 5,
              }
            ); //生成json表格内容
            let lglzxx = lglzsj.map((row) => ({
              xm: row[1],
              xb: row[2],
              sfzhm: row[3],
              nl: row[4],
              ybm: row[5],
              ysmdj: row[6],
              zw: row[7],
              zj: row[8],
              zc: row[9],
              ysflx: row[10],
              gajgcrjba: row[11],
              sfwtgl: row[12],
              tmqkssj: row[13],
              tmqjssj: row[14],
              sjhm: row[15],
              lzlglx: row[16],
              qxdwmc: row[17],
              bz: row[18],
            }));
            for (var i in lglzxx) {
              if (lglzxx[i]["xm"] && lglzxx[i]["sfzhm"]) {
                var params = {
                  xm: lglzxx[i]["xm"],
                  sfzhm: lglzxx[i]["sfzhm"],
                  xb: lglzxx[i]["xb"],
                  nl: lglzxx[i]["nl"],
                  ybm: lglzxx[i]["ybm"],
                  ysmdj: lglzxx[i]["ysmdj"],
                  zw: lglzxx[i]["zw"],
                  zj: lglzxx[i]["zj"],
                  zc: lglzxx[i]["zc"],
                  ysflx: lglzxx[i]["ysflx"],
                  gajgcrjba: lglzxx[i]["gajgcrjba"],
                  sfwtgl: lglzxx[i]["sfwtgl"],
                  tmqkssj: this.isChineseStandardTime(lglzxx[i]["tmqkssj"])
                    ? dateFormatNYRChinese(lglzxx[i]["tmqkssj"])
                    : lglzxx[i]["tmqkssj"],
                  tmqjssj: this.isChineseStandardTime(lglzxx[i]["tmqjssj"])
                    ? dateFormatNYRChinese(lglzxx[i]["tmqjssj"])
                    : lglzxx[i]["tmqjssj"],
                  sjhm: lglzxx[i]["sjhm"],
                  lzlglx: lglzxx[i]["lzlglx"],
                  qxdwmc: lglzxx[i]["qxdwmc"],
                  bz: lglzxx[i]["bz"],
                  lglzid: getUuid(),
                };
                addLglz(params);
              }
            }
            console.log(5);
            ////出国出境
            const cgcjwsname = workdata.SheetNames[4]; //取第一张表
            const cgcjsj = XLSX.utils.sheet_to_json(
              workdata.Sheets[cgcjwsname],
              {
                header: 1,
                range: 5,
              }
            ); //生成json表格内容
            let cgcjxx = cgcjsj.map((row) => ({
              xm: row[1],
              xb: row[2],
              sfzhm: row[3],
              nl: row[4],
              bmmc: row[5],
              smdj: row[6],
              sfsp: row[7],
              qwgj: row[8],
              cgsj: row[9],
              ggsj: row[10],
              bz: row[11],
            }));
            for (var i in cgcjxx) {
              if (cgcjxx[i]["xm"] && cgcjxx[i]["sfzhm"]) {
                var params = {
                  xm: cgcjxx[i]["xm"],
                  xb: cgcjxx[i]["xb"],
                  sfzhm: cgcjxx[i]["sfzhm"],
                  nl: cgcjxx[i]["nl"],
                  bmmc: cgcjxx[i]["bmmc"],
                  smdj: cgcjxx[i]["smdj"],
                  sfsp: cgcjxx[i]["sfsp"],
                  qwgj: cgcjxx[i]["qwgj"],
                  cgsj: this.isChineseStandardTime(cgcjxx[i]["cgsj"])
                    ? dateFormatNYRChinese(cgcjxx[i]["cgsj"])
                    : cgcjxx[i]["cgsj"],
                  ggsj: this.isChineseStandardTime(cgcjxx[i]["ggsj"])
                    ? dateFormatNYRChinese(cgcjxx[i]["ggsj"])
                    : cgcjxx[i]["ggsj"],
                  bz: cgcjxx[i]["bz"],
                  cgcjid: getUuid(),
                };
                addCgcj(params);
              }
            }
            console.log(6);
            ////三类人员
            const slrywsname = workdata.SheetNames[5]; //取第一张表
            const slrysj = XLSX.utils.sheet_to_json(
              workdata.Sheets[slrywsname],
              {
                header: 1,
                range: 5,
              }
            ); //生成json表格内容
            let slryxx = slrysj.map((row) => ({
              xm: row[1],
              xb: row[2],
              sfzhm: row[3],
              nl: row[4],
              bmmc: row[5],
              gwmc: row[6],
              smdj: row[7],
              yrxs: row[8],
              sfsc: row[9],
              ydwmc: row[10],
              bz: row[11],
            }));
            for (var i in slryxx) {
              if (slryxx[i]["xm"] && slryxx[i]["sfzhm"]) {
                var params = {
                  xm: slryxx[i]["xm"],
                  xb: slryxx[i]["xb"],
                  sfzhm: slryxx[i]["sfzhm"],
                  nl: slryxx[i]["nl"],
                  bmmc: slryxx[i]["bmmc"],
                  gwmc: slryxx[i]["gwmc"].split(","),
                  smdj: slryxx[i]["smdj"],
                  yrxs: slryxx[i]["yrxs"],
                  sfsc: slryxx[i]["sfsc"],
                  ydwmc: slryxx[i]["ydwmc"],
                  bz: slryxx[i]["bz"],
                  cgcjid: getUuid(),
                };
                addSlsmry(params);
              }
            }
            console.log(7);
            ////被追责
            const bzzqkwsname = workdata.SheetNames[6]; //取第一张表
            const bzzqksj = XLSX.utils.sheet_to_json(
              workdata.Sheets[bzzqkwsname],
              {
                header: 1,
                range: 5,
              }
            ); //生成json表格内容
            let bzzqkxx = bzzqksj.map((row) => ({
              xm: row[1],
              xb: row[2],
              sfzhm: row[3],
              nl: row[4],
              bmmc: row[5],
              smdj: row[6],
              bzzqk: row[7],
              bz: row[8],
            }));
            for (var i in bzzqkxx) {
              if (bzzqkxx[i]["xm"] && bzzqkxx[i]["sfzhm"]) {
                var params = {
                  xm: bzzqkxx[i]["xm"],
                  xb: bzzqkxx[i]["xb"],
                  sfzhm: bzzqkxx[i]["sfzhm"],
                  nl: bzzqkxx[i]["nl"],
                  bmmc: bzzqkxx[i]["bmmc"],
                  smdj: bzzqkxx[i]["smdj"],
                  bzzqk: bzzqkxx[i]["bzzqk"],
                  bz: bzzqkxx[i]["bz"],
                  cgcjid: getUuid(),
                };
                addZzqk(params);
              }
            }
            console.log(8);
            ////保密干部
            const bmgbwsname = workdata.SheetNames[7]; //取第一张表
            const bmgbsj = XLSX.utils.sheet_to_json(
              workdata.Sheets[bmgbwsname],
              {
                header: 1,
                range: 5,
              }
            ); //生成json表格内容
            let bmgbxx = bmgbsj.map((row) => ({
              xm: row[1],
              xb: row[2],
              sfzh: row[3],
              nl: row[4],
              sfzz: row[5],
              bm: row[6],
              gwmc: row[7],
              smdj: row[8],
              zgxl: row[9],
              zw: row[10],
              zj: row[11],
              jbzc: row[12],
              sflx: row[13],
              drbmgbsj: row[14],
              lxdh: row[15],
              bz: row[16],
            }));
            for (var i in bmgbxx) {
              if (bmgbxx[i]["xm"] && bmgbxx[i]["sfzh"]) {
                var params = {
                  xm: bmgbxx[i]["xm"],
                  xb: bmgbxx[i]["xb"],
                  sfzh: bmgbxx[i]["sfzh"],
                  nl: bmgbxx[i]["nl"],
                  sfzz: bmgbxx[i]["sfzz"],
                  bm: bmgbxx[i]["bm"],
                  gwmc: bmgbxx[i]["gwmc"].split(","),
                  smdj: bmgbxx[i]["smdj"],
                  zgxl: bmgbxx[i]["zgxl"],
                  zw: bmgbxx[i]["zw"],
                  zj: bmgbxx[i]["zj"],
                  jbzc: bmgbxx[i]["jbzc"],
                  sflx: bmgbxx[i]["sflx"],
                  drbmgbsj: this.isChineseStandardTime(bmgbxx[i]["drbmgbsj"])
                    ? dateFormatNYRChinese(bmgbxx[i]["drbmgbsj"])
                    : bmgbxx[i]["drbmgbsj"],
                  lxdh: bmgbxx[i]["lxdh"],
                  bz: bmgbxx[i]["bz"],
                  cgcjid: getUuid(),
                };
                addbmgb(params);
              }
            }
            console.log(9);
            // this.dr_cyz_list = ws.slice(4)
            // this.dr_cyz_list.forEach(item => {
            //   console.log(item)
            //   // item['上岗时间'] = dateFormatNYRChinese(item['上岗时间'].getTime())
            //   item['上岗时间'] = dateFormatNYRChinese(getDateTime(item['上岗时间']))
            // })
            // console.log("列表的值:", this.dr_cyz_list)
            // console.log("列表的值:", this.dr_cyz_list[0]['岗位名称'])
            // this.dialogVisible_dr = true
            // 加工excel读取业务类型为数组
            // this.dr_cyz_list.forEach(function(item) {
            // 	console.log(item[0]['业务类型'].splite(','))
            // })
            this.loading = false;
            this.$refs.upload.value = ""; // 处理完成 清空表单值
            this.$message({
              message: "数据导入成功",
              type: "success",
            });
          } catch (e) {
            this.loading = false;
            return false;
          }
        };
        fileReader.readAsBinaryString(files[0]);
      }, 1000);
    },
    // 自选模式数据处理
    handleSbsjZxms() {
      // 获取单位信息加入到json对象中
      this.sbsjDataZxms["dwxx_List"] = getDwxxListAll();
      return this.sbsjDataZxms;
    },
    // 自动筛选模式数据处理
    handleSbsjZdsxms(currentYear, sblx, FS) {
      // 获取文件路径
      let zczpJsonPath = getZczpPath();
      if (!zczpJsonPath) {
        this.$notify({
          title: "系统异常",
          message: "[" + "上报数据文件路径获取失败" + "]\n",
          type: "error",
          offset: 100,
          duration: 0,
        });
        return;
      }
      // 获取文件内容
      let zczpJsonStr;
      try {
        zczpJsonStr = FS.readFileSync(zczpJsonPath, { encoding: "utf8" });
      } catch (error) {
        console.log(error);
        error = errorProcessor(error);
        let errObj = JSON.parse(error.message);
        this.$notify({
          title: "系统异常",
          message: "[" + errObj.mark + "]\n",
          type: "error",
          offset: 100,
          duration: 0,
        });
        return;
      }
      // 简单校验数据是否完整
      let zczpJsonObj;
      try {
        zczpJsonObj = JSON.parse(zczpJsonStr);
      } catch (error) {
        this.$notify({
          title: "系统异常",
          message: "[" + "上报数据文件内容异常" + "]\n",
          type: "error",
          offset: 100,
          duration: 0,
        });
        return;
      }
      // 筛选今年数据
      // console.log('筛选今年数据')
      let zczpCurrentJsonObj = {};
      let table;
      // 今年一月第一天
      let startTime = new Date(currentYear, 0, 1).getTime();
      // 今年最后月最后一天最后时刻
      let endTime = new Date(
        parseInt(currentYear) + 1,
        0,
        0,
        23,
        59,
        59,
        999
      ).getTime();
      // console.log(startTime, endTime, dateFormatChinese(new Date(startTime)), dateFormatChinese(new Date(endTime)), new Date(), dateFormatChinese(new Date()))
      // 加工上报数据表数组(内置单位信息表)
      let sbTableAllArr = ["dwxx_List"];
      sblx.forEach((item) => {
        if (item == 1) {
          sbTableAllArr = sbTableAllArr.concat(this.rysbTableArr);
        }
        if (item == 2) {
          sbTableAllArr = sbTableAllArr.concat(this.dmsbTableArr);
        }
      });
      if (sbTableAllArr.length < 1) {
        this.$message.warning("数据异常，未检测到正确的上报类型");
        return;
      }
      //
      // console.log('sbTableAllArr', sbTableAllArr)
      Object.keys(zczpJsonObj).forEach((tableName) => {
        // console.log(tableName, sbTableAllArr.indexOf(tableName))
        if (sbTableAllArr.indexOf(tableName) == -1) {
          return;
        }
        table = zczpJsonObj[tableName];
        if (tableName == "dwxx_List") {
          zczpCurrentJsonObj[tableName] = table;
          return;
        }
        // 初始化新json对象对应表数据
        zczpCurrentJsonObj[tableName] = table.filter((item) => {
          // console.log(startTime, item.gxsj, endTime, item.gxsj >= startTime, item.gxsj <= endTime)
          if (item.gxsj >= startTime && item.gxsj <= endTime) {
            return item;
          }
        });
      });
      return zczpCurrentJsonObj;
    },
    // 最小化系统
    minimizeSystem() {
      this.$electron.ipcRenderer.send("hide-window");
    },
    // 最大化系统
    // maximizeSystem() {
    //   let isMax = this.windowBtnConfig.isMax;
    //   console.log("isMax", isMax, "windowBtnConfig", this.windowBtnConfig);
    //   // this.$electron.ipcRenderer.send("max-window")
    //   // return
    //   this.windowBtnConfig.isMax = !isMax;
    //   if (isMax) {
    //     console.log("窗口化");
    //     this.$electron.ipcRenderer.send("unmax-window");
    //   } else {
    //     console.log("最大化");
    //     this.$electron.ipcRenderer.send("max-window");
    //   }
    // },
    maximizeSystem() {
  let isMax = this.windowBtnConfig.isMax;
  console.log("isMax", isMax, "windowBtnConfig", this.windowBtnConfig);

  this.windowBtnConfig.isMax = !isMax;

  const { ipcRenderer } = this.$electron;
  const { remote } = this.$electron; // 使用remote模块获取当前窗口
console.log(process.platform);

  if (isMax) {
    console.log("窗口化");
    if (process.platform === 'linux') {
      // 在Linux系统下，使用remote模块获取当前窗口并取消最大化
      const currentWindow = remote.getCurrentWindow();
      currentWindow.unmaximize();
    } else {
      ipcRenderer.send("unmax-window");
    }
  } else {
    console.log("最大化");
    if (process.platform === 'linux') {
      // 在Linux系统下，使用remote模块获取当前窗口并最大化
      const currentWindow = remote.getCurrentWindow();
      currentWindow.maximize();
    } else {
      ipcRenderer.send("max-window");
    }
  }
},
    // 退出系统
    quitSystem() {
      this.$confirm("是否执行此操作，点击将退出系统？", "是否退出系统？", {
        cancelButtonClass: "btn-custom-cancel",
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        // center: true
      })
        .then(() => {
          this.$emit("quitClicked", true);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "取消退出系统",
          });
        });
    },
    // 初始化表格数据(自选模式)
    initTableDataZxms() {
      //
      this.sbsjDataZxmsOld = {
        Smgwgl_list: [],
        Smry_list: [],
        Lglz_list: [],
        Cgcj_list: [],
        Slsmry_list: [],
        Smryzzqk_list: [],
        Bmgbqk_list: [],
        dmzrr_list: [],
        dmsq_list: [],
        Gjmmsx_list: [],
        dmpx_list: [],
        dmqkndtj_list: [],
        dmqsxqdqk_list: [],
        zfcgxmqk_list: [],
      };
      // 获取sm岗位信息（Smgwgl_list）(所有)
      this.sbsjDataZxmsOld.Smgwgl_list = this.getAllSmgwList();
      // 获取sm人员信息(Smry_list)(所有)
      this.sbsjDataZxmsOld.Smry_list = this.getAllSmryList();
      // 获取离岗离职信息(Lglz_list)(所有)
      this.sbsjDataZxmsOld.Lglz_list = this.getAllLglzList();
      // 获取离岗离职信息(Cgcj_list)(所有)
      this.sbsjDataZxmsOld.Cgcj_list = this.getAllCgcjList();
      // 获取三类涉密信息(Slsmry_list)(所有)
      this.sbsjDataZxmsOld.Slsmry_list = this.getAllSlsmryList();
      // 获取三类涉密信息(Smryzzqk_list)(所有)
      this.sbsjDataZxmsOld.Smryzzqk_list = this.getAllSmryzzqkList();
      // 获取保密干部涉密信息(Bmgbqk_list)(所有)
      this.sbsjDataZxmsOld.Bmgbqk_list = this.getAllSBmgbqkList();
      // 获取dm责任人信息(dmzrr_list)(所有)
      this.sbsjDataZxmsOld.dmzrr_list = this.getAllDmzrrList();
      // 获取dm授权信息(dmsq_list)(所有)
      this.sbsjDataZxmsOld.dmsq_list = this.getAllDmsqList();
      // 获取国家mm事项信息(Gjmmsx_list)(所有)
      this.sbsjDataZxmsOld.Gjmmsx_list = this.getAllGjmmsxList();
      // 获取dm培训信息(dmpx_list)(所有)
      this.sbsjDataZxmsOld.dmpx_list = this.getAllDmpxList();
      // 获取dm情况年度统计信息(dmqkndtj_list)(所有)
      this.sbsjDataZxmsOld.dmqkndtj_list = this.getAllDmqkndtjList();
      // 获取不明确事项确定情况信息(dmqsxqdqk_list)(所有)
      this.sbsjDataZxmsOld.dmqsxqdqk_list = this.getAllBmqsxqdqkList();
      // 获取zf采购项目情况信息(zfcgxmqk_list)(所有)
      this.sbsjDataZxmsOld.zfcgxmqk_list = this.getAllZfcgxmqkList();
      //
    },
    // 获取所有sm岗位信息
    getAllSmgwList() {
      return getAllSmgwZxms();
    },
    // 获取所有sm人员信息
    getAllSmryList() {
      let smryListAll = getsmry1();
      smryListAll.forEach((item) => {
        if (checkArr(item.gwmc)) {
          item.gwmc = item.gwmc.join(",");
        }
      });
      return smryListAll;
    },
    // 获取所有离岗离职信息
    getAllLglzList() {
      let lglzListAll = getAllLglzZxms();
      lglzListAll.forEach((item) => {
        if (checkArr(item.ygwmc)) {
          item.ygwmc = item.ygwmc.join(",");
        }
      });
      return lglzListAll;
    },
    // 获取所有离岗离职信息
    getAllCgcjList() {
      let cgcjListAll = getAllCgcjZxms();
      cgcjListAll.forEach((item) => {
        if (checkArr(item.gwmc)) {
          item.gwmc = item.gwmc.join(",");
        }
      });
      return cgcjListAll;
    },
    // 获取所有离岗离职信息
    getAllSlsmryList() {
      let SlsmryListAll = getAllSlsmryZxms();
      SlsmryListAll.forEach((item) => {
        if (checkArr(item.gwmc)) {
          item.gwmc = item.gwmc.join(",");
        }
      });
      return SlsmryListAll;
    },
    // 获取所有离岗离职信息
    getAllSBmgbqkList() {
      let SlsmryListAll = getAllBmgbqkZxms();
      SlsmryListAll.forEach((item) => {
        if (checkArr(item.gwmc)) {
          item.gwmc = item.gwmc.join(",");
        }
      });
      return SlsmryListAll;
    },
    // 获取所有dm责任人信息
    getAllDmzrrList() {
      return getAllDmzrrZxms();
    },
    // 获取所有dm责任人信息
    getAllSmryzzqkList() {
      return getAllSmryzzqkZxms();
    },
    // 获取所有dm授权信息
    getAllDmsqList() {
      return getAllDmsqZxms();
    },
    // 获取所有国家mm事项信息
    getAllGjmmsxList() {
      return getAllGjmmxsZxms();
    },
    // 获取所有dm培训信息
    getAllDmpxList() {
      return getAllDmpxZxms();
    },
    // 获取所有dm情况年度统计信息
    getAllDmqkndtjList() {
      return getAllDmqkndtjZxms();
    },
    // 获取所有不明确事项确定情况信息
    getAllBmqsxqdqkList() {
      return getAllBmqsxqdqkZxms();
    },
    // 获取所有zf采购项目情况信息
    getAllZfcgxmqkList() {
      return getAllZfcgxmqkZxms();
    },
  },
  mounted() {
    this.dwlxList = getDmbDwlxDB().list_total;
    this.sslyList = getDmbSslyDB().list_total;
    this.ssccList = getDmbSsccDB().list_total;
    this.zzjg = getbmmc();
    console.log(this.zzjg, "zzjg");
    if (this.zzjg.length !== 0) {
      this.bmm = this.zzjg.filter((item) => item.fbmm == "")[0].bmm;
    }
    // console.log(this.bmm, "bmm");
    this.initTableDataZxms();
    if (getlogin()[0]) {
      this.dwmc = getlogin()[0].dwmc;
    }

    console.log(this.dwmc, "this.dwmc");
    let date = new Date();
    this.yearwn = date.getFullYear(); //获取完整的年份(4位)
    this.year = date.getFullYear() + "年"; //获取完整的年份(4位)
    this.yearwn = date.getFullYear(); //获取完整的年份(4位)
    this.yue = date.getMonth() + 1 + "月"; //获取当前月份(0-11,0代表1月)
    this.ri = date.getDate() + "日"; //获取当前日(1-31)
    this.Date = this.year + this.yue + this.ri;
    this.getDwxx();
  },
  watch: {
    // 每次路由变动都去扫一遍缓存中的用户信息（为了处理账号切换时设置里的菜单未正确显隐问题）
    $route(to, form) {
      console.log("路由变更，校验设置里的菜单");
      // 获取当前登录用户信息
      let localObj = getWindowLocation();
      
      if (localObj) {
        this.currentYhm = localObj.yhm;
        this.currentXm = localObj.xm;
        this.currentYhlx = localObj.yhlx;
      }
    },
    // 监听数据上报数据类型变动，如是往年数据，则需要弹框给用户提供选择
    sbsjlx(newVal, oldVal) {
      if (newVal == 1) {
        let now = new Date();
        this.dialogSjsb.sbsj = now.getFullYear() + "";
      } else {
        this.dialogSjsb.sbsj = undefined;
        this.initTableDataZxms();
        this.dialogVisibleSjsbZxms = true;
      }
    },
  },
};
</script>

<style scoped>
.out {
  display: flex;
  height: 100%;
  /* background: red; */
  color: white;
  height: 40px;
  /* background: rgba(255, 255, 255, 0.3); */
  background: url(../../assets/background/head-mall-box.png) no-repeat;
  background-size: cover;
  color: white;
  /* align-self: center; */
  width: 100%;
  /* border-radius: 18px; */
  padding: 0 10px;
}
.out .card {
  flex: 1;
  align-items: center;
  display: flex;
  justify-content: center;
  /* flex-direction: column; */
  cursor: pointer;
}
/* .out .card:hover {
  cursor: pointer;
  background: rgba(255, 255, 255, 0.18);
} */
.out .card:nth-child(odd) {
  /* background: green; */
}
.out .card img {
  /* margin: 5px 10px 5px 0; */
  width: 22px;
  height: 22px;
  align-self: center;
  cursor: pointer;
  /* margin-right: 11.25px; */
  /* background: rgba(255, 255, 255, 0.5); */
  /* padding: 5px; */
  /* box-sizing: border-box; */
  /* border-radius: 50%; */
}
.out .card img:hover {
}
.out .card span {
  font-size: 16px;
  font-weight: 400;
}
.out .card i {
  /* margin: 5px 10px 5px 0; */
  font-size: 20px;
  cursor: pointer;
  margin-right: 5px;
  /* background: rgba(255, 255, 255, 1); */
  /* padding: 5px; */
  /* box-sizing: border-box; */
  /* border-radius: 50%; */
  /* color: red; */
  /* font-weight: bold; */
}
.out .card i:hover {
}
/**系统设置菜单**/
.system-setting-out {
  /* background: red; */
  font-size: 13px;
}
.system-setting-out div {
  border-radius: 6px;
  margin: 3px 0;
  cursor: pointer;
  padding: 3px 0 3px 15px;
}
.system-setting-out div:hover {
  background: #f4f4f5;
  color: #409eff;
}
.system-setting-out > div i {
  margin-right: 10px;
}
/**当前登录用户信息展示卡片样式**/
.div-user-card {
  position: absolute;
  top: calc(27px + 10px);
  background: white;
  color: black;
  padding: 5px 10px;
  border-radius: 5px;
  /* width: 200px; */
  text-align: left;
}
.out .card .div-user-card span {
  font-size: 14px;
}
.out .card .div-user-card .pointer {
  /* background: #3dff00; */
  width: 8px;
  height: 8px;
  display: inline-block;
  border-radius: 50%;
  /* border: 1px solid #909399; */
  background-image: linear-gradient(50deg, white, green);
}
</style>
