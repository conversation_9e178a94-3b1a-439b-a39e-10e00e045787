import db from './adapter/zczpAdaptor'

// 获取所有zf采购项目情况信息(上报数据自选模式专用)
export const getAllZfcgxmqkZxms = () => {
  return db.get('zfcgxmqk_list').cloneDeep().value()
}

//保密制度-----------------------------------保密制度初始化列表********
export const getZfcgxmqk = (params) => {
  let page = params.page
  let pagesize = params.pageSize
  let nd = params.nd
  // let bmbh = params.bmbh
  // let lx = params.lx
  // let mj = params.mj
  // let qyrq = params.qyrq
  let list_total = db.get('zfcgxmqk_list').sortBy('cjsj').cloneDeep().value()

  // 模糊查询过滤
  if (nd) {
    list_total = list_total.filter((item) => {
      if (item.nd == nd) {
        return item
      }
    })
  }

  // 手动分页
  console.log(pagesize * (page - 1), pagesize * (page - 1) + pagesize)
  let pageList = list_total.slice(
    pagesize * (page - 1),
    pagesize * (page - 1) + pagesize
  )

  let resList = {
    list: pageList,
    list_total: list_total,
    total: list_total.length,
  }
  console.log('保密制度', resList)
  return resList
}
//保密制度-----------------------------------保密制度添加成员********
export const addZfcgxmqk = (params) => {
  let sjc = new Date().getTime()
  params.cjsj = sjc
  params.gxsj = sjc
  db.read().get('zfcgxmqk_list').push(params).write()
}
//保密制度-----------------------------------保密制度删除成员********
//修改
export const reviseZfcgxmqk = (params) => {
  let zfcgxmqkid = params.zfcgxmqkid
  console.log('Zfcgxmqkid', zfcgxmqkid)
  if (!zfcgxmqkid || zfcgxmqkid == '') {
    return
  }
  params.gxsj = new Date().getTime()
  // 全字段更新方法(传进来的字段都会更新)
  db.read()
    .get('zfcgxmqk_list')
    .find({ zfcgxmqkid: zfcgxmqkid })
    .assign(params)
    .write()
}
//保密制度-----------------------------------保密制度删除成员********
export const deleteZfcgxmqk = (params) => {
  db.read().get('zfcgxmqk_list').remove(params).write()
}
