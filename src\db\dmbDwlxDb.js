import DB from './adapter/zczpAdaptor'

/**
 * 获取单位类型
 * 如果有id则通过ID查询
 * 如果有分页信息则再次通过分页信息查询
 * 返回结果始终是分页的格式
 */
export const getDmbDwlxDB = (params) => {
  console.log('单位类型入参', params)
  let paramsId = undefined
  let page = undefined
  let pageSize = undefined
  if (params) {
    paramsId = params.dwlxid
    page = params.page
    pageSize = params.pageSize
  }
  let list_total = undefined
  if (paramsId) {
    list_total = DB.get('dmb_dwlx')
      .filter((item) => {
        if (paramsId == item.dwlxid) {
          return item
        }
      })
      .cloneDeep()
      .value()
  } else {
    list_total = DB.get('dmb_dwlx').cloneDeep().value()
  }
  // 手动分页
  let pageList = list_total
  if (page && pageSize && list_total) {
    pageList = list_total.slice(
      pageSize * (page - 1),
      pageSize * (page - 1) + pageSize
    )
  }
  let resList = {
    list: pageList,
    list_total: list_total,
    total: list_total.length,
  }
  console.log('单位类型', resList)
  return resList
}
