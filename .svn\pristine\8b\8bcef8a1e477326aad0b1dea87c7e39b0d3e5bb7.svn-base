import db from "./adapter/zczpAdaptor";
// 日志工具类
import { writeTrajectoryLog } from '../utils/logUtils'

//保密制度-----------------------------------保密制度初始化列表********
export const getGwbg = (params) => {
  let page = params.page;
  let pagesize = params.pageSize;
  let sgsj = params.sgsj;
  let list_total = db
    .get("Gwbg_list")
    .sortBy("cjsj")
    .filter(function (item) {
      //没有时间
      if (sgsj === undefined || sgsj == null) {
        return item;
        console.log("全都没有", item);
      } else if (sgsj) {
        if (item.sgsj) {
          if (
            new Date(item.sgsj) >= new Date(sgsj[0]) &&
            new Date(item.sgsj) <= new Date(sgsj[1])
          ) {
            return item;
          }
        }
      }
    })
    .cloneDeep()
    .value();
  

  // 手动分页
  console.log(pagesize * (page - 1), pagesize * (page - 1) + pagesize);
  let pageList = list_total.slice(
    pagesize * (page - 1),
    pagesize * (page - 1) + pagesize
  );

  let resList = {
    list: pageList,
    list_total: list_total,
    total: list_total.length,
  };
  console.log("保密制度", resList);
  return resList;
};
//保密制度-----------------------------------保密制度添加成员********
export const addGwbg = (params) => {
  let sjc = new Date().getTime();
  params.cjsj = sjc;
  params.gxsj = sjc;
  db.read().get("Gwbg_list").push(params).write();
  // 添加日志
  let paramsLog = {
    xyybs: 'mk_smry',
    id: params.smryid,
    ymngnmc: '变更',
    extraParams: {
      xm: params.xm,
      sfzhm: params.sfzhm,
      bm: params.bgbm,
      gwmc: params.bghgwmc,
      smdj: params.bghsmdj,
      bz: params.bz
    }
  }
  writeTrajectoryLog(paramsLog)
};
//保密制度-----------------------------------保密制度删除成员********
export const deleteGwbg = (params) => {
  db.read().get("Gwbg_list").remove(params).write();
};

//修改
export const reviseGwbg = (params) => {
  let gwbgid = params.gwbgid;
  console.log("gwbgid", gwbgid);
  if (!gwbgid || gwbgid == "") {
    return;
  }
  params.gxsj = new Date().getTime();
  // 全字段更新方法(传进来的字段都会更新)
  db.read()
    .get("Gwbg_list")
    .find({
      gwbgid: gwbgid,
    })
    .assign(params)
    .write();
};
