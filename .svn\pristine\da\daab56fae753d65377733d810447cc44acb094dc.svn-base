<template>
  <div style="height: calc(100% - 32px);width: 100%;">
    <!-- 检索条件区域 -->
    <div class="mhcx">
      <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline" style="float:left">
        <el-form-item label="所属模块" style="font-weight: 700;">
          <el-select v-model="formInline.xyybs" clearable>
            <el-option v-for="(item,index) in ssmkList" :key="index" :label="item.name" :value="item.xyybs"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="时间" style="font-weight: 700;">
          <el-date-picker v-model="formInline.cxsj" size="" type="daterange" :default-time="['00:00:00', '23:59:59']" value-format="timestamp" :editable="false" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="getSjrz">查询</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 表格区域 -->
    <div style="height: calc(100% - 34px - 20px);">
      <el-table :data="scList" border stripe :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" style="width: 100%;border:1px solid #EBEEF5;" height="calc(100% - 34px - 10px)">
        <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
        <el-table-column prop="yhm" label="账号"></el-table-column>
        <el-table-column prop="xm" label="姓名"></el-table-column>
        <el-table-column prop="rwmc" label="任务名称"></el-table-column>
        <el-table-column prop="jcjdmc" label="检查季度"></el-table-column>
        <el-table-column label="操作模块">
          <template slot-scope="scoped">
            <div>{{ translationSsmk(scoped.row.xyybs) }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="ymngnmc" label="操作功能"></el-table-column>
        <el-table-column label="时间">
          <template slot-scope="scoped">
            <div>{{ formatDate(scoped.row.time) }}</div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 分页组件区域 -->
    <div style="border: 1px solid #ebeef5">
      <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange" :pager-count="5" :current-page="pageInfo.page" :page-sizes="[5, 10, 20, 30]" :page-size="pageInfo.pageSize" layout="total, prev, pager, sizes,next, jumper" :total="pageInfo.total">
      </el-pagination>
    </div>
    <!---->
  </div>
</template>

<script>
import { parseOperationLogsSj } from '../../../utils/logUtils'

import { getDateTime } from '../../../utils/utils'

import { dateFormatChinese } from '../../../utils/moment'

export default {
  data () {
    return {
      // 查询条件
      formInline: {},
      // 分页信息
      pageInfo: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      // 表格数据
      scList: [],
      // 所属模块列表集合
      ssmkList: [],
      // 日志数据全集
      logsAllList: []
    }
  },
  methods: {
    // 格式化日期
    formatDate (time) {
      return dateFormatChinese(new Date(time))
    },
    // 获取日志信息集合
    getSjrz () {
      this.pageInfo.page = 1
      this.getLogs()
    },
    // 获取所有的操作日志
    getAllOptionsLogs () {
      parseOperationLogsSj(resArr => {
        console.log('操作日志', resArr)
        // let aaa = resArr.sort((a, b) => {
        //   return a.time < b.time
        // })
        // console.log('aaa', aaa)
        this.logsAllList = resArr
        // 加工获取模块集合
        this.initSsmkList(resArr)
        //
        this.getLogs()
      })
    },
    // 加工获取模块集合
    initSsmkList (resArr) {
      let ssmkTempArr = []
      let tempObj
      resArr.forEach(item => {
        tempObj = {}
        if (ssmkTempArr.indexOf(item.xyybs) == -1) {
          ssmkTempArr.push(item.xyybs)
          tempObj.xyybs = item.xyybs
          tempObj.name = this.translationSsmk(item.xyybs)
          this.ssmkList.push(tempObj)
        }
      })
    },
    // 翻译模块
    translationSsmk (ssmk) {
      switch (ssmk) {
        case 'yybs-xjzczp':
          return '新建自查自评'
        case 'yybs-jxzczp':
          return '继续自查自评'
        case 'yybs-zczpls':
          return '自查自评历史'
        case 'yybs-ccdnsjg':
          return '抽查的内设机构'
        case 'yybs-ccdnsjgDj':
          return '内设机构自查自评结果登记'
        // case 'yybs-ccdnsjgDjXqxx':
        //   return '内设机构抽查详情'
        case 'yybs-ccdry':
          return '抽查的人员'
        case 'yybs-ccdryDj':
          return '人员自查自评'
        // case 'yybs-ccdryDjXqxx':
        //   return '人员抽查详情'
        case 'yybs-jczj':
          return '检查总结'
        // case 'yybs-jczjLsxx':
        //   return '检查总结详情'
        default:
          return ssmk
      }
    },
    // 页码变更
    handleCurrentChange (val) {
      this.pageInfo.page = val
      this.getLogs()
    },
    // 页面大小变更
    handleSizeChange (val) {
      this.pageInfo.pageSize = val
      this.pageInfo.page = 1
      this.getLogs()
    },
    // 总集日志中进行日志的筛选
    getLogs () {
      let cxsj = this.formInline.cxsj
      let xyybs = this.formInline.xyybs
      // 根据查询条件筛选数据
      let logsAllTempList = JSON.parse(JSON.stringify(this.logsAllList))
      if (xyybs) {
        logsAllTempList = this.logsAllList.filter(item => {
          if (item.xyybs == xyybs) {
            return item
          }
        })
      }
      if (cxsj) {
        logsAllTempList = this.logsAllList.filter(item => {
          if (item.time >= cxsj[0] && item.time <= cxsj[1]) {
            return item
          }
        })
      }
      // 分页
      let page = this.pageInfo.page
      let pageSize = this.pageInfo.pageSize
      this.scList = logsAllTempList.slice(pageSize * (page - 1), pageSize * (page - 1) + pageSize)
      this.pageInfo.total = logsAllTempList.length
    }
  },
  mounted () {
    // // 获取所有的操作日志
    this.getAllOptionsLogs()
  }
}
</script>

<style scoped>
.mhcx :deep(.el-form-item) {
  margin-top: 5px;
  margin-bottom: 5px;
}
</style>