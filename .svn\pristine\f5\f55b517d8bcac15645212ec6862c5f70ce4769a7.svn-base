import DB from './adapter/zczpAdaptor'

/**
 * 获取所属层次
 * 如果有id则通过ID查询
 * 如果有分页信息则再次通过分页信息查询
 * 返回结果始终是分页的格式
 */
export const getDmbSsccDB = (params) => {
  console.log('所属层次入参', params)
  let paramsId = undefined
  let page = undefined
  let pageSize = undefined
  if (params) {
    paramsId = params.ssccid
    page = params.page
    pageSize = params.pageSize
  }
  let list_total = undefined
  if (paramsId) {
    list_total = DB.get('dmb_sscc')
      .filter((item) => {
        if (paramsId == item.ssccid) {
          return item
        }
      })
      .cloneDeep()
      .value()
  } else {
    list_total = DB.get('dmb_sscc').cloneDeep().value()
  }
  // 手动分页
  let pageList = list_total
  if (page && pageSize && list_total) {
    pageList = list_total.slice(
      pageSize * (page - 1),
      pageSize * (page - 1) + pageSize
    )
  }
  let resList = {
    list: pageList,
    list_total: list_total,
    total: list_total.length,
  }
  console.log('所属层次', resList)
  return resList
}
