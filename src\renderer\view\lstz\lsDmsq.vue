<template>
	<div class="bg_con" style="height: calc(100% - 38px);">
		<div style="width: 100%; position: relative; overflow: hidden;height: 100%; ">
			<!-- <div class="xmlb-title" style=" cursor: pointer;">
				<span style="font-size: 24px; cursor: pointer;">定密授权信息</span>
				<span style="" @click="returnSy" class="fhsy">返回</span>
			</div> -->

			<div class="dabg" style="height: 100%;">
				<div class="content" style="height: 100%;">
					<div class="table" style="height: 100%;">
						<!-- -----------------操作区域--------------------------- -->
						<div class="mhcx">
							<el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline"
								style="float:left">
								<el-form-item label="" style="font-weight: 700;">
									<!-- <el-input v-model="formInline.tzsj" clearable placeholder="台账时间" class="widths">
									</el-input> -->
									<el-select v-model="formInline.tzsj"  class="widths" placeholder="台账时间">
										<el-option
										v-for="item in yearSelect"
										:key="item.value"
										:label="item.label"
										:value="item.value">
										</el-option>
									</el-select>
								</el-form-item>
								<el-form-item label="" style="font-weight: 700;">
									<el-input v-model="formInline.bsqjg" style="width:200px" clearable placeholder="被授权机关、单位名称"
										class="widths">
									</el-input>
								</el-form-item>
								<el-form-item label="" style="font-weight: 700;">
									<el-select v-model="formInline.dmqx" clearable placeholder="请选择类型" class="widthx">
										<el-option v-for="item in dmqxlxxz" :label="item.dmqxlxmc"
											:value="item.dmqxlxmc" :key="item.dmqxlxid"></el-option>
									</el-select>
								</el-form-item>
								<el-form-item label="" style="font-weight: 700;">
									<el-input v-model="formInline.nd" clearable placeholder="年度" oninput="value=value.replace(/[^\d.]/g,'')" @blur="nd=$event.target.value"
									class="widths">
									</el-input>
								</el-form-item>
								<el-form-item>
									<el-button type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
								</el-form-item>
								<el-form-item>
									<el-button type="warning" icon="el-icon-circle-close" @click="cz">重置</el-button>
								</el-form-item>
							</el-form>
							<el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline"
								style="float:right">
								<!-- <el-form-item style="float: right;">
									<el-button type="danger" size="medium" @click="shanchu" icon="el-icon-delete-solid">
										删除
									</el-button>
								</el-form-item> -->
								<el-form-item style="float: right;">
									<el-button type="primary" size="medium" icon="el-icon-download"
										@click="exportList()">导出
									</el-button>
								</el-form-item>
								<!-- <el-form-item style="float: right;">
									<input type="file" ref="upload"
										style="display: none;position: absolute;top:10px; right:0;opacity:0; cursor: pointer;height: 32px;width: 56px;z-index:1;"
										accept=".xls,.xlsx">
									<el-button type="primary" icon="el-icon-upload2" size="medium"
										@click="dr_dialog = true">
										导入
									</el-button>
								</el-form-item>
								<el-form-item style="float: right;">
									<el-button type="success" size="medium" @click="dialogVisible = true"
										icon="el-icon-plus">
										新增
									</el-button>
								</el-form-item> -->
							</el-form>
						</div>


						<!-- -----------------审查组人员列表--------------------------- -->
						<div class="table_content_padding" style="height: 100%;">
							<div class="table_content" style="height: 100%;">
								<el-table :data="dmsqList" border @selection-change="selectRow"
									:header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
									style="width: 100%;border:1px solid #EBEEF5;" height="calc(100% - 34px - 43px)"
									stripe>
									<el-table-column type="selection" width="55" align="center"> </el-table-column>
									<el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
									<el-table-column prop="nd" label="年度"></el-table-column>
									<el-table-column prop="bsqjg" label="被授权机关、单位名称"></el-table-column>
									<el-table-column prop="sqjg" label="授权机关/单位名称"></el-table-column>
									<el-table-column prop="dmqx" label="权限"></el-table-column>
									<el-table-column prop="lksj" label="时间"></el-table-column>
									<el-table-column prop="qxnd" label="期限（年）"></el-table-column>
									<el-table-column prop="sx" label="事项"></el-table-column>
									<el-table-column prop="tzsj" label="台账时间"></el-table-column>
									<!-- <el-table-column prop="bz" label="备注"></el-table-column> -->
									<el-table-column label="操作" width="120">
										<template slot-scope="scoped">
											<el-button size="medium" type="text" @click="xqyl(scoped.row)">详情
											</el-button>
											<!-- <el-button size="medium" type="text" @click="updateItem(scoped.row)">修改
											</el-button> -->
										</template>
									</el-table-column>

								</el-table>

								<!-- -------------------------分页区域---------------------------- -->
								<div style="border: 1px solid #ebeef5;">
									<el-pagination background @current-change="handleCurrentChange"
										@size-change="handleSizeChange" :pager-count="5" :current-page="page"
										:page-sizes="[5, 10, 20, 30]" :page-size="pageSize"
										layout="total, prev, pager, sizes,next, jumper" :total="total">
									</el-pagination>
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- 模板下载 -->
				<el-dialog title="开始导入" class="scbg-dialog" width="600px" @close="mbxzgb" :visible.sync="dr_dialog"
					show-close>
					<div style="padding: 20px;">
						<div class="daochu">
							<div>一、请点击“导出模板”，并参照模板填写信息。</div>
							<el-button type="primary" size="mini" @click="mbdc">
								模板导出
							</el-button>
						</div>
						<div class="daochu">
							<div>二、数据导入方式：</div>
							<el-radio-group v-model="sjdrfs" @change="Radio($event)">
								<el-radio label="1">追加（导入时已有的记录信息不变，只添加新的记录）</el-radio>
								<el-radio label="2">覆盖（导入时更新已有的记录信息，并添加新的记录）</el-radio>
							</el-radio-group>
						</div>
						<div class="daochu">
							<div>三、将按模板填写的文件，导入到系统中。</div>
							<el-button type="primary" size="mini" @click="chooseFile">
								上传导入
							</el-button>
						</div>
					</div>
				</el-dialog>
				<!-- -----------------导入-弹窗--------------------------- -->
				<el-dialog width="1000px" height="800px" title="导入定密授权信息" class="scbg-dialog"
					:visible.sync="dialogVisible_dr" show-close>
					<div style="height: 600px;">
						<el-table :data="dr_cyz_list" ref="multipleTable" @selection-change="handleSelectionChange"
							style="width: 100%;border:1px solid #EBEEF5;" height="100%" stripe>
							<el-table-column type="selection" width="55"> </el-table-column>
							<el-table-column prop="被授权机关、单位名称" label="被授权机关、单位名称"></el-table-column>
							<el-table-column prop="授权机关/单位名称" label="授权机关/单位名称"></el-table-column>
							<el-table-column prop="权限" label="权限"></el-table-column>
							<el-table-column prop="定密事项（范围）" label="定密事项（范围）"></el-table-column>
							<el-table-column prop="时间" label="时间"></el-table-column>
							<el-table-column prop="期限（年）" label="期限（年）"></el-table-column>
							<el-table-column prop="事项" label="事项"></el-table-column>
							<el-table-column prop="备注" label="备注"></el-table-column>
						</el-table>
					</div>

					<div style="height :30px;display: flex;align-items: center;justify-content: center;margin: 10px 0;">
						<el-button type="primary" @click="drcy" size="mini">导 入</el-button>
						<el-button type="warning" @click="dialogVisible_dr = false" size="mini">关 闭</el-button>
					</div>
				</el-dialog>
				<!-- -----------------涉密人员离岗离职信息-弹窗--------------------------- -->

				<el-dialog title="新增定密授权信息" :close-on-click-modal="false" :visible.sync="dialogVisible" width="50%"
					class="xg" :before-close="handleClose" @close="close('formName')">
					<el-form ref="formName" :model="tjlist" :rules="rules" label-width="180px" size="mini">
						<el-form-item label="年度" prop="nd">
							<el-input placeholder="年度" disabled v-model="tjlist.nd" clearable></el-input>
						</el-form-item>
						<el-form-item label="被授权机关、单位名称" prop="bsqjg">
							<el-input placeholder="被授权机关、单位名称" v-model="tjlist.bsqjg" clearable></el-input>
						</el-form-item>
						<el-form-item label="权限" prop="dmqx">
							<el-select v-model="tjlist.dmqx" clearable placeholder="请选择类型" class="widthx">
								<el-option v-for="item in dmqxlxxz" :label="item.dmqxlxmc" :value="item.dmqxlxmc"
									:key="item.dmqxlxid"></el-option>
							</el-select>
						</el-form-item>
						<el-form-item label="时间" prop="lksj">
							<!-- <el-input type="textarea"
								v-model="tjlist.lksj"></el-input> -->
							<el-date-picker v-model="tjlist.lksj" class="cd" clearable type="date" placeholder="选择时间"
								format="yyyy年MM月dd日" value-format="yyyy年MM月dd日">
							</el-date-picker>
						</el-form-item>
						<el-form-item label="期限（年）" prop="qxnd">
							<!-- <el-input placeholder="期限（年）" @blur="qxnd=$event.target.value" oninput="value=value.replace(/[^\d.]/g,'')" v-model="tjlist.qxnd" clearable></el-input> -->
							<el-input placeholder="期限（年）" v-model="tjlist.qxnd" clearable></el-input>
						</el-form-item>
						<el-form-item label="事项" prop="sx">
							<el-input type="textarea"
								placeholder="‘事项”应填写授予事项的类别或者具体事项名称，如XX工作国家秘密事项，XX工作国家秘密目录第X项，XX涉密任务或者涉密科研生产项目;没有明确具体授权事项的，填无。"
								v-model="tjlist.sx"></el-input>
						</el-form-item>
						<el-form-item label="备注" prop="bz">
							<el-input type="textarea" v-model="tjlist.bz"></el-input>
						</el-form-item>
					</el-form>
					<span slot="footer" class="dialog-footer">
						<el-button type="primary" @click="submitTj('formName')">保 存</el-button>
						<el-button type="warning" @click="dialogVisible = false">关 闭</el-button>
					</span>
				</el-dialog>


				<el-dialog title="修改定密授权信息" :close-on-click-modal="false" :visible.sync="xgdialogVisible" width="50%"
					class="xg" @close="close1('form')">
					<el-form ref="form" :model="xglist" :rules="rules" label-width="180px" size="mini">
						<el-form-item label="年度" prop="nd">
							<el-input placeholder="年度" disabled v-model="xglist.nd" clearable></el-input>
						</el-form-item>
						<el-form-item label="被授权机关、单位名称" prop="bsqjg">
							<el-input placeholder="被授权机关、单位名称" v-model="xglist.bsqjg" clearable></el-input>
						</el-form-item>
						<el-form-item label="权限" prop="dmqx">
							<el-select v-model="xglist.dmqx" clearable placeholder="请选择类型" class="widthx">
								<el-option v-for="item in dmqxlxxz" :label="item.dmqxlxmc" :value="item.dmqxlxmc"
									:key="item.dmqxlxid"></el-option>
							</el-select>
						</el-form-item>
						<el-form-item label="时间" prop="lksj">
							<!-- <el-input type="textarea"
								v-model="xglist.lksj"></el-input> -->
							<el-date-picker v-model="xglist.lksj" class="cd" clearable type="date" placeholder="选择时间"
								format="yyyy年MM月dd日" value-format="yyyy年MM月dd日">
							</el-date-picker>
						</el-form-item>
						<el-form-item label="期限（年）" prop="bsqjg">
							<el-input placeholder="期限（年）" @blur="qxnd = $event.target.value"
								oninput="value=value.replace(/[^\d.]/g,'')" v-model="xglist.qxnd" clearable></el-input>
						</el-form-item>
						<el-form-item label="事项" prop="sx">
							<el-input type="textarea"
								placeholder="‘事项”应填写授予事项的类别或者具体事项名称，如XX工作国家秘密事项，XX工作国家秘密目录第X项，XX涉密任务或者涉密科研生产项目;没有明确具体授权事项的，填无。"
								v-model="xglist.sx"></el-input>
						</el-form-item>
						<el-form-item label="备注" prop="bz">
							<el-input type="textarea" v-model="xglist.bz"></el-input>
						</el-form-item>
					</el-form>
					<span slot="footer" class="dialog-footer">
						<el-button type="primary" @click="updataDialog('form')">修 改</el-button>
						<el-button type="warning" @click="xgdialogVisible = false">关 闭</el-button>
					</span>
				</el-dialog>

				<!-- 详情 -->
				<el-dialog title="定密授权信息详情" :close-on-click-modal="false" :visible.sync="xqdialogVisible" width="50%"
					class="xg" @close="close">
					<el-form ref="form" :model="xglist" label-width="180px" size="mini" disabled>
						<el-form-item label="年度" prop="nd" class="one-line">
							<el-input placeholder="年度" disabled v-model="xglist.nd" clearable></el-input>
						</el-form-item>
						<el-form-item label="被授权机关、单位名称" prop="bsqjg" class="one-line">
							<el-input placeholder="被授权机关、单位名称" v-model="xglist.bsqjg" clearable></el-input>
						</el-form-item>
						<el-form-item label="权限" prop="dmqx" class="one-line">
							<el-select v-model="xglist.dmqx" style="width: 100%;" clearable placeholder="请选择类型" class="widthx">
								<el-option v-for="item in dmqxlxxz" :label="item.dmqxlxmc" :value="item.dmqxlxmc"
									:key="item.dmqxlxid"></el-option>
							</el-select>
						</el-form-item>
						<el-form-item label="时间" prop="lksj" class="one-line">
							<!-- <el-input type="textarea"
								v-model="xglist.lksj"></el-input> -->
							<el-date-picker v-model="xglist.lksj" style="width: 100%;" clearable type="date" placeholder="选择时间"
								format="yyyy年MM月dd日" value-format="yyyy年MM月dd日">
							</el-date-picker>
						</el-form-item>
						<el-form-item label="期限（年）" prop="qxnd" class="one-line">
							<el-date-picker v-model="xglist.qxnd" style="width: 100%;" clearable type="year" placeholder="选择期限"
								format="yyyy年" value-format="yyyy年">
							</el-date-picker>
						</el-form-item>
						<el-form-item label="事项" prop="sx" class="one-line-textarea">
							<el-input type="textarea"
								placeholder="‘事项”应填写授予事项的类别或者具体事项名称，如XX工作国家秘密事项，XX工作国家秘密目录第X项，XX涉密任务或者涉密科研生产项目;没有明确具体授权事项的，填无。"
								v-model="xglist.sx"></el-input>
						</el-form-item>
						<el-form-item label="备注" prop="bz" class="one-line-textarea">
							<el-input type="textarea" v-model="xglist.bz"></el-input>
						</el-form-item>
					</el-form>
					<span slot="footer" class="dialog-footer">

						<el-button type="warning" @click="xqdialogVisible = false">关 闭</el-button>
					</span>
				</el-dialog>


			</div>
		</div>
	</div>

</template>
<script>
import {
	getdmqxlx,
	getdmlb
} from "../../../db/xzdb"
import {
	getlogin
} from "../../../db/loginyhdb";
import {
	getdmsq
} from "../../../db/lstzdb";
import {
	// getdmsq,
	adddmsq,
	deletedmsq,
	revisedmsq,
} from "../../../db/dmsqdb.js";

import {
	exportExcel
} from "../../../utils/exportExcel"; //excel导出工具
import {
	dateFormatNYRChinese
} from "../../../utils/moment"
import {
	getUuid
} from "../../../utils/getUuid"; //获取uuid
import XLSX from "xlsx";

export default {
	components: {},
	props: {},
	data() {

		var checkKsValidator = (rule, value, callback, form) => {
			// console.log('ks value', value, value.length, form)
			// 校验是否存在非数字字符串
			let notNum = value.match(/[^\d]/)
			// console.log('notNum', notNum)
			if (notNum) {
				form.qxnd = value.replace(/[^\d.]/g, '')
				// callback(new Error('课时只能输入数字'))
				return
			}
			if (value.length <= 0) {
				callback(new Error('请输入期限（年），期限（年）只能为数字'))
			}
			callback()
		}

		return {
			dmsqList: [],
			tableDataCopy: [],
			xglist: {},
			updateItemOld: {},
			xgdialogVisible: false,
			xqdialogVisible: false,
			formInline: {

			},
			tjlist: {
				nd: new Date().getFullYear().toString(),
				bsqjg: '',
				sfzhm: '',
				sqjg: '',
				dmqx: '',
				lksj: '',
				qxnd: '',
				sx: '',
				bz: '',
			},
			page: 1,
			pageSize: 10,
			total: 0,
			yearSelect: [],
			selectlistRow: [], //列表的值
			dialogVisible: false, //添加弹窗状态
			//表单验证
			rules: {
				nd: [{
					required: true,
					message: '请输入年度',
					trigger: 'blur'
				},],
				bsqjg: [{
					required: true,
					message: '请输入被授权机关、单位名称',
					trigger: 'blur'
				},],
				sqjg: [{
					required: true,
					message: '请输入授权机关/单位名称',
					trigger: 'blur'
				},],
				dmqx: [{
					required: true,
					message: '请选择权限',
					trigger: 'blur'
				},],
				lksj: [{
					required: true,
					message: '请输入时间',
					trigger: 'blur'
				},],
				// qxnd: [{
				// 	required: true,
				// 	message: '请选择期限（年）',
				// 	trigger: 'blur'
				// },],
				qxnd: [
					{
						validator: (rule, value, callback) => {
							checkKsValidator(rule, value, callback, this.tjlist)
						},
						trigger: ['blur', 'change']
					}
				],
				sx: [{
					required: true,
					message: '请选择事项',
					trigger: 'blur'
				},],
				// bz: [{
				// 	required: true,
				// 	message: '请输入备注',
				// 	trigger: 'blur'
				// },],
			},
			//导入
			dialogVisible_dr: false, //导入成员组弹窗状态
			dr_cyz_list: [], //待选择导入成员组列表
			multipleTable: [], //已选择导入成员组列表
			dmqxlxxz: [],
			dwmc: '',
			dwdm: '',
			dwlxr: '',
			dwlxdh: '',
			year: '',
			yue: '',
			ri: '',
			Date: '',
			xh: [],
			dclist: [],
			dr_dialog: false,
			//数据导入方式
			sjdrfs: ''
		}
	},
	computed: {},
	mounted() {
		this.dwmc = getlogin()[0].dwmc
		this.dwdm = getlogin()[0].xydm
		this.dwlxr = getlogin()[0].dwlxr
		this.dwlxdh = getlogin()[0].dwlxdh
		let date = new Date()
		this.year = date.getFullYear() + '年'; //获取完整的年份(4位)
		this.yue = (date.getMonth() + 1) + '月'; //获取当前月份(0-11,0代表1月)
		this.ri = date.getDate() + '日'; //获取当前日(1-31)
		this.Date = this.year + this.yue + this.ri
		//获取最近十年的年份
		let yearArr = []
		for (let i = new Date().getFullYear(); i > new Date().getFullYear() - 10; i--) {
			yearArr.push(
				{
					label: i.toString(),
					value: i.toString()
				})
		}
		yearArr.unshift({
			label: "全部",
			value: ""
		})
		this.yearSelect = yearArr
		//列表初始化
		this.dmqxlxxz = getdmqxlx()
		this.dmsq()
		// this.$refs.upload.addEventListener('change', e => { //绑定监听表格导入事件
		// 	this.readExcel(e);
		// })
	},
	methods: {
		Radio(val) {
			this.sjdrfs = val
			console.log("当前选中的值", val)
		},
		mbxzgb() {
			this.sjdrfs = ''
		},
		mbdc() {
			console.log("----导出涉密人员----")
			// console.log(this.selectlistRow);
			// if (this.selectlistRow.length > 0) {
			let filename = "定密授权信息模板" + getUuid() + ".xlsx"

			const {
				dialog
			} = require('electron').remote;
			//弹窗title
			let options = {
				title: "保存文件",
				defaultPath: filename,
			};
			console.log(dialog)
			//导出文件夹选择弹窗
			dialog.showSaveDialog(options, result => {
				console.log('result', result)
				if (result == null || result == "") {
					console.log("取消导出")
					return
				}
				let list = []

				//对导出的数据进行格式，js导出excel要求传入的参数格式为[["列1名称","列2名称","列3名称"],["列11","值21","值31"],["列12","值22","值32"]]
				list.push(["序号", "被授权机关、单位名称",
					"授权机关/单位名称", "权限", "时间", "期限（年）", "事项", "备注"]) //确定列名

				exportExcel(result, list) //list 要求为二维数组
				this.dr_dialog = false
				this.$message('导出成功:' + result)
			})
		},
		//导入
		chooseFile() {
			if (this.sjdrfs != '') {
				if (this.sjdrfs == 1) {
					this.$refs.upload.click()
				}
				else if (this.sjdrfs == 2) {
					let valArr = this.dclist
					valArr.forEach(function (item) {
						deletedmsq(item)
					})
					this.$refs.upload.click()
				}
			} else {
				this.$message.warning('请选择导入方式')
			}
		},
		//----成员组选择
		handleSelectionChange(val) {
			this.multipleTable = val
			console.log("选中：", this.multipleTable);
		},
		//---确定导入成员组
		drcy() {
			//遍历已选择导入的成员，进行格式化，然后添加到数据库
			for (var i in this.multipleTable) {
				var cy = {
					bsqjg: this.multipleTable[i]["被授权机关、单位名称"],
					sqjg: this.multipleTable[i]["授权机关/单位名称"],
					dmqx: this.multipleTable[i]["权限"],
					// dmsxfw: this.multipleTable[i]["定密事项（范围）"],
					lksj: dateFormatNYRChinese(this.multipleTable[i]["时间"]),
					qxnd: this.multipleTable[i]["期限（年）"],
					sx: this.multipleTable[i]["事项"],
					bz: this.multipleTable[i]["备注"],
					dmsqid: getUuid()
				}
				adddmsq(cy)
			}
			this.dialogVisible_dr = false
			this.dmsq()
		},
		//----表格导入方法
		readExcel(e) {
			var that = this;
			const files = e.target.files;
			console.log("files", files);
			var vali = /\.(xls|xlsx)$/
			if (files.length <= 0) { //如果没有文件名
				return false;
			} else if (!vali.test(files[0].name.toLowerCase())) {
				this.$Message.error('上传格式不正确，请上传xls或者xlsx格式');
				return false;
			}
			const fileReader = new FileReader();
			fileReader.onload = (e) => {
				try {
					const data = e.target.result;
					const workdata = XLSX.read(data, {
						type: 'binary',
						cellDates: true,//设为true，将天数的时间戳转为时间格式
					});
					console.log("文件的内容：", workdata) // 文件的内容
					//查看打印能看到 workdata的sheetNames里是文件中每个表的有值的集合；
					const wsname = workdata.SheetNames[0]; //取第一张表
					console.log('wsname', wsname)
					const ws = XLSX.utils.sheet_to_json(workdata.Sheets[wsname]); //生成json表格内容
					console.log(ws); //自第二行开始的内容
					this.dialogVisible_dr = true
					this.dr_cyz_list = ws
					console.log("列表的值:", this.dr_cyz_list)
					// 加工excel读取业务类型为数组
					// this.dr_cyz_list.forEach(function(item) {
					// 	console.log(item[0]['业务类型'].splite(','))
					// })
					this.$refs.upload.value = ''; // 处理完成 清空表单值
					this.dr_dialog = false
				} catch (e) {
					return false;
				}
			};
			fileReader.readAsBinaryString(files[0]);
		},
		//修改
		updataDialog(form) {
			this.$refs[form].validate((valid) => {
				if (valid) {
					//删除旧的
					// deletedmsq(this.updateItemOld)
					// 插入新的
					revisedmsq(this.xglist)
					// 刷新页面表格数据
					this.dmsq()
					// 关闭dialog
					this.$message.success('修改成功')
					this.xgdialogVisible = false
				} else {
					console.log('error submit!!');
					return false;
				}
			});

		},
		xqyl(row) {
			this.updateItemOld = JSON.parse(JSON.stringify(row))

			this.xglist = JSON.parse(JSON.stringify(row))
			// this.form1.ywlx = row.ywlx
			console.log('old', row)
			console.log("this.xglist.ywlx", this.xglist);
			this.xqdialogVisible = true
		},

		updateItem(row) {
			this.updateItemOld = JSON.parse(JSON.stringify(row))

			this.xglist = JSON.parse(JSON.stringify(row))
			// this.form1.ywlx = row.ywlx
			console.log('old', row)
			console.log("this.xglist.ywlx", this.xglist);
			this.xgdialogVisible = true
		},
		cz() {
			this.formInline = {}
		},
		//查询
		onSubmit() {
			//  form是查询条件
			console.log(this.formInline);
			// 备份了一下数据
			let arr = this.tableDataCopy
			// 通过遍历key值来循环处理
			Object.keys(this.formInline).forEach(e => {
				console.log(this.formInline[e], e, arr)
				// 调用自己定义好的筛选方法
				console.log(this.formInline[e]);
				arr = this.filterFunc(this.formInline[e], e, arr)
			})
			// 为表格赋值
			this.dmsqList = arr
			// this.dmsq()
		},
		
		filterFunc(val, target, filterArr) {
			// 参数不存在或为空时，就相当于查询全部
			console.log(1);
			if (val == undefined || val == '') {
				console.log(2);
				return filterArr
			}
			return filterArr.filter(p => {
				console.log(p);
				let bool
				let resP
				if (Object.prototype.toString.call(val) == '[object Array]') {
					console.log('是数组')
					if (val.length > 1) {
						let timeArr1 = val[1].replace(/[\u4e00-\u9fa5]/g, '/')
						let date = new Date(timeArr1)
						if ('Invalid Date' != date) {
							// 时间
							if (new Date(p[target].replace(/[\u4e00-\u9fa5]/g, '/')).getTime() <= date.getTime()) {
								console.log('找到小于范围内是记录')
								resP = p
								let timeArr0 = val[0].replace(/[\u4e00-\u9fa5]/g, '/')
								if (new Date(p[target].replace(/[\u4e00-\u9fa5]/g, '/')).getTime() >= new Date(timeArr0)
									.getTime()) {
									console.log('找到大于范围内是记录')
									resP = p
								} else {
									resP = undefined
								}
							}
						} else {
							console.log('非法时间')
						}
						if (resP) {
							console.log('不是时间，通过时间校验')
							bool = true
						}
					} else {
						if (new Date(p[target]).getTime() <= new Date(timeArr0).getTime()) {
							resP = p
						} else {
							resP = undefined
						}
						if (resP) {
							bool = true
						}
					}
					return bool
				}
				return p[target].indexOf(val) > -1
				// return bool
			}) // 可以自己加一个.toLowerCase()来兼容一下大小
		},

		returnSy() {
			this.$router.push("/tzglsy");
		},
		dmsq() {
			let params = {
				page: this.page,
				pageSize: this.pageSize
			}
			Object.assign(params, this.formInline)
			let resList = getdmsq(params)
			console.log("params", params);
			this.tableDataCopy = resList.list
			this.dmsqList = resList.list
			this.dclist = resList.list_total
			this.dclist.forEach((item, label) => {
				this.xh.push(label + 1)
			})
			this.total = resList.total
		},
		//删除
		shanchu(id) {
			if (this.selectlistRow != '') {
				this.$confirm('是否继续删除?', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(() => {
					let valArr = this.selectlistRow
					// console.log("....", val);
					valArr.forEach(function (item) {
						deletedmsq(item)
						console.log("删除：", item);
						console.log("删除：", item);
					})
					let params = valArr
					this.$message({
						message: '删除成功',
						type: 'success'
					});
					this.dmsq()
				}).catch(() => {
					this.$message('已取消删除')
				})
			} else {
				this.$message({
					message: '未选择删除记录，请选择下列列表',
					type: 'warning'
				});
			}
		},
		//添加
		showDialog() {
			this.resetForm()
			this.dialogVisible = true
		},

		//导出
		exportList() {
			let filename = "定密授权历史台账" + getUuid() + ".xlsx"
			const {
				dialog
			} = require('electron').remote;
			//弹窗title
			let options = {
				title: "保存文件",
				defaultPath: filename,
			};
			console.log(dialog)
			//导出文件夹选择弹窗
			dialog.showSaveDialog(options, result => {
				console.log('result', result)
				if (result == null || result == "") {
					console.log("取消导出")
					return
				}
				let list = []
				list.push(["定密授权名录"])

				list.push(["上报单位:", this.dwmc,
					"单位代码:", this.dwdm, "", "", "",
					"年度:", this.year])
				//对导出的数据进行格式，js导出excel要求传入的参数格式为[["列1名称","列2名称","列3名称"],["列11","值21","值31"],["列12","值22","值32"]]
				list.push(["序号", "台账时间", "被授权机关、单位名称",
					"授权机关/单位名称", "权限", "时间", "期限（年）", "事项", "备注"]) //确定列名

				for (var i in this.dclist) { //每一行的值
					let item = this.dclist[i] //{"name":"XXX","dw":"XXX","sqjg":"XXX"}

					console.log("导出值:", this.dclist);
					let column = [(parseInt(i) + 1), item["tzsj"], item["bsqjg"], item["sqjg"],
					item["dmqx"], item["lksj"], item["qxnd"], item["sx"],
					item["bz"]
					]
					list.push(column)
				}

				list.push(["填报人:", this.dwlxr,
					"联系方式:", this.dwlxdh, "", "", "",
					"填报时间:", this.Date])

				let merges = [{
					s: { //s为开始
						c: 0, //开始列
						r: 0 //开始取值范围
					},
					e: { //e结束
						c: 8, //结束列
						r: 0 //结束范围
					}
				}]
				let styles = {
					// 列样式
					cols: {
						// 作用sheet页索引（0开始）（-1全sheet页生效）
						scoped: -1,
						style: [
							{ wpx: 100 },
							{ wpx: 100 },
							{ wpx: 120 },
							{ wpx: 120 },
							{ wpx: 100 },
							{ wpx: 100 },
							{ wpx: 100 },
							{ wpx: 120 },
							{ wpx: 120 },
						]
					},
					// 全局样式
					all: {
						alignment: {
							horizontal: 'center', // 水平居中
							vertical: 'center' // 垂直居中
						},
						font: {
							sz: 11, // 字号
							name: '宋体' // 字体
						},
						border: {  // 边框
							top: {
								style: 'thin'
							},
							bottom: {
								style: 'thin'
							},
							left: {
								style: 'thin'
							},
							right: {
								style: 'thin'
							}
						}
					},
					// 单元格样式
					cell: [
						{
							// 生效sheet页索引（值为 -1 时所有sheet页都生效）
							scoped: -1,
							// 索引
							index: 'A1',
							style: {
								font: {
									name: '宋体',
									sz: 16, // 字号
									bold: true,
								},
								alignment: {
									horizontal: 'center', // 水平居中
									vertical: 'center' // 垂直居中
								}
							}
						}
					]
				}
				exportExcel(result, list, merges, styles) //list 要求为二维数组
				this.$message('导出成功:' + result)
			})
		},
		//确定添加成员组
		submitTj(formName) {
			this.$refs[formName].validate((valid) => {
				if (valid) {
					let params = {
						nd: this.tjlist.nd,
						bsqjg: this.tjlist.bsqjg,
						sfzhm: this.tjlist.sfzhm,
						sqjg: this.dwmc,
						dmqx: this.tjlist.dmqx,
						lksj: this.tjlist.lksj,
						qxnd: this.tjlist.qxnd,
						sx: this.tjlist.sx,
						bz: this.tjlist.bz,
						dmsqid: getUuid()
					}
					adddmsq(params)
					this.dialogVisible = false
					this.$message({
						message: '添加成功',
						type: 'success'
					});
					this.resetForm()
					this.dmsq()
				} else {
					console.log('error submit!!');
					return false;
				}
			});

		},

		deleteTkglBtn() {

		},

		selectRow(val) {
			console.log(val);
			this.selectlistRow = val;
		},
		//列表分页--跳转页数
		handleCurrentChange(val) {
			this.page = val
			this.dmsq()
		},
		//列表分页--更改每页显示个数
		handleSizeChange(val) {
			this.page = 1
			this.pageSize = val
			this.dmsq()
		},
		//添加重置
		resetForm() {
			this.tjlist.nd = ''
			this.tjlist.bsqjg = ''
			this.tjlist.sqjg = ''
			this.tjlist.dmqx = ''
			this.tjlist.lksj = ''
			this.tjlist.qxnd = ''
			this.tjlist.sx = ''
			this.tjlist.bz = ''
			this.tjlist.sfzhm = ''
		},
		handleClose(done) {
			this.resetForm()
			this.dialogVisible = false
		},
		// 弹框关闭触发
		close(formName) {
			// 清空表单校验，避免再次进来会出现上次校验的记录
			this.$refs[formName].resetFields();
		},
		close1(form) {
			// 清空表单校验，避免再次进来会出现上次校验的记录
			this.$refs[form].resetFields();
		},
	},
	watch: {

	}
}
</script>

<style scoped>
.bg_con {
	width: 100%;
}

.dabg {
	/* margin-top: 10px; */
	box-shadow: 0px 2px 12px 0px rgba(9, 70, 255, 0.13);
	border-radius: 8px;
	width: 100%;
}



.xmlb-title {
	line-height: 60px;
	width: 100%;
	padding-left: 10px;
	height: 60px;
	background: url(../../assets/background/bg-02.png) no-repeat left;
	background-size: 100% 100%;
	text-indent: 10px;
	/* margin: 0 20px; */
	color: #0646BF;
	font-weight: 700;
}

.fhsy {
	display: inline-block;
	width: 120px;
	margin-top: 10px;
	height: 40px;
	display: flex;
	justify-content: center;
	align-items: center;
	padding-left: 30px;
	padding-top: 4px;
	float: right;
	background: url(../../assets/icons/zzjg_icon1.png) no-repeat center;
	background-size: 100% 100%;
}

.item_button {
	height: 100%;
	float: left;
	padding-left: 10px;
	line-height: 50px;
}

.daochu {
	display: flex;
	align-items: center;
	margin-bottom: 10px;
}

.select_wrap {
	/* //padding: 5px; */

	.select_wrap_content {
		float: left;
		width: 100%;
		line-height: 50px;
		/* // padding-left: 20px; */
		/* // padding-right: 20px; */
		height: 100%;
		background: rgba(255, 255, 255, 0.7);

		.item_label {
			padding-left: 10px;
			height: 100%;
			float: left;
			line-height: 50px;
			font-size: 1em
		}
	}
}

.mhcx1 {
	margin-top: 0px;
}

.widths {
	width: 6vw;
}

.widthx {
	width: 7vw;
}

:deep(.el-form-item__label) {
	text-align: left;
}

.mhcx :deep(.el-form-item) {
	/* margin-top: 5px; */
	margin-bottom: 5px;
}
</style>
