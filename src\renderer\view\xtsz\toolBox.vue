<template>
  <div>
    <hsoft_top_title>
      <template #left>工具箱</template>
    </hsoft_top_title>
    <!-- 非完全测试按钮,请勿删除 -->
    <div>
      <el-button type="primary" @click="toDataMigration">数据迁移</el-button>
      <el-button type="primary" @click="testLogs">轨迹日志测试</el-button>
      <el-button type="primary" @click="testParseLogs">解析轨迹日志测试</el-button>
    </div>
    <!---->
    <!---->
    <!---->
  </div>
</template>

<script>
import hsoft_top_title from '../../components/hsoft-top-title/hsoft-top-title.vue'

import { getWindowLocation } from '../../../utils/windowLocation'

import { dateFormatChinese } from '../../../utils/moment'

//
import { writeTrajectoryLog, parseTrajectoryLogs } from '../../../utils/logUtils'

export default {
  data () {
    return {}
  },
  components: {
    hsoft_top_title
  },
  methods: {
    // to数据迁移
    toDataMigration() {
      this.$router.push('/dataMigration')
    },
    // 测试轨迹日志
    testLogs() {
      let params = {
        xyybs: 'mk_smjsj',
        id: '2A76A3E8-8622-411D-89EA-64F8F2C36741',
        ymngnmc: '新增'
      }
      writeTrajectoryLog(params)
    },
    // 解析轨迹日志
    testParseLogs() {
      let params = {
        id: '2A76A3E8-8622-411D-89EA-64F8F2C36741',
        xyybs: 'mk_smjsj',
      }
      parseTrajectoryLogs(params, resArr => {
        console.log(resArr)
      })
    }
  },
  mounted () {
  }
}
</script>

<style scoped>
.out-card {
  /* margin-bottom: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04); */
}
/**单位信息区域**/
.out-card .out-card-div {
  font-size: 13px;
  padding: 5px 20px;
}
.out-card .out-card-div div {
  padding: 10px 5px;
  display: flex;
}
.out-card .dwxx div:hover {
  background: #f4f4f5;
  border-radius: 20px;
}
.out-card .dwxx div label {
  /* background-color: red; */
  width: 125px;
  display: inline-block;
  text-align: right;
  font-weight: 600;
  color: #909399;
}
.out-card .dwxx div span {
  /* background-color: rgb(33, 92, 79); */
  flex: 1;
  display: inline-block;
  padding-left: 20px;
}
/**操作区域**/
.out-card .user-options {
  /* background: red; */
  height: 500px;
  text-align: center;
}
.out-card .card-option {
  width: 150px;
  height: 150px;
  background: var(--background);
  font-weight: 700;
  opacity: 0.85;
  display: inline-block;
}
.out-card .card-option:hover {
  cursor: pointer;
  opacity: 1;
}
</style>