<template>
  <div class="bg_con" style="height: calc(100% - 38px);">
    <div style="width: 100%; position: relative; overflow: hidden;height: 100%;">

      <div class="dabg" style="height: 100%;">
        <div class="content" style="height: 100%;">
          <div class="table" style="height: 100%;">
            <!-- -----------------操作区域--------------------------- -->
            <div class="mhcx">
              <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline" style="float:left">
                <el-form-item label="培训形式" style="font-weight: 700;">
                  <el-select v-model="formInline.pxxs" clearable placeholder="请选择培训形式" class="widthw">
                    <el-option v-for="item in pxxsxz" :label="item.pxlxmc" :value="item.pxlxmc" :key="item.pxlxid">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="培训日期" style="font-weight: 700;">
                  <el-date-picker v-model="formInline.pxrq" type="daterange" range-separator="至" style="width:293px;"
                    start-placeholder="培训起始时间" end-placeholder="培训结束日期" value-format="timestamp">
                  </el-date-picker>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
                </el-form-item>

                <el-form-item>
                  <el-button type="warning" icon="el-icon-circle-close" @click="cz">重置</el-button>
                </el-form-item>
              </el-form>
              <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline" style="float:right">
                <el-form-item style="float: right;">
                  <el-button type="danger" size="medium" @click="shanchu" icon="el-icon-delete-solid">
                    删除
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <el-button type="primary" size="medium" icon="el-icon-download" @click="exportList()">导出
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <input type="file" ref="upload"
                    style="display: none;position: absolute;top:10px; right:0;opacity:0; cursor: pointer;height: 32px;width: 56px;z-index:1;"
                    accept=".xls,.xlsx">
                  <el-button type="primary" icon="el-icon-upload2" size="medium" @click="dr_dialog = true">
                    导入
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <el-button type="success" size="medium" @click="xz" icon="el-icon-plus">
                    新增
                  </el-button>
                </el-form-item>
              </el-form>
            </div>

            <!-- -----------------审查组人员列表--------------------------- -->
            <div class="table_content_padding" style="height: 100%;">
              <div class="table_content" style="height: 100%;">
                <el-table :data="pxqdList" border @selection-change="selectRow"
                  :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
                  style="width: 100%;border:1px solid #EBEEF5;" height="calc(100% - 34px - 44px - 10px)" stripe>
                  <el-table-column type="selection" width="55" align="center"> </el-table-column>
                  <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                  <el-table-column prop="pxrq" label="培训日期">
                    <template slot-scope="scoped">
                      <div>
                        {{ sjgsh(scoped.row.pxrq) }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="pxzt" label="培训主题"></el-table-column>
                  <el-table-column prop="pxdd" label="培训地点"></el-table-column>
                  <el-table-column prop="pxdw" label="培训单位"></el-table-column>
                  <el-table-column prop="pxnr" label="培训内容"></el-table-column>
                  <el-table-column prop="ks" label="课时"></el-table-column>
                  <el-table-column prop="pxxs" label="培训形式"></el-table-column>
                  <el-table-column prop="pxlx" label="培训类型"></el-table-column>
                  <el-table-column prop="" label="操作" width="120">
                    <template slot-scope="scoped">
                      <el-button size="medium" type="text" @click="xqyl(scoped.row)">详情
                      </el-button>
                      <el-button size="medium" type="text" @click="updateItem(scoped.row)">修改
                      </el-button>
                    </template>
                  </el-table-column>

                </el-table>

                <!-- -------------------------分页区域---------------------------- -->
                <div style="border: 1px solid #ebeef5;">
                  <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange"
                    :pager-count="5" :current-page="page" :page-sizes="[5, 10, 20, 30]" :page-size="pageSize"
                    layout="total, prev, pager, sizes,next, jumper" :total="total">
                  </el-pagination>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 模板下载 -->
        <el-dialog title="开始导入" class="scbg-dialog" width="600px" @close="mbxzgb" :visible.sync="dr_dialog" show-close>
          <div style="padding: 20px;">
            <div class="daochu">
              <div>一、请点击“导出模板”，并参照模板填写信息。</div>
              <el-button type="primary" size="mini" @click="mbdc">
                模板导出
              </el-button>
            </div>
            <div class="daochu">
              <div>二、数据导入方式：</div>
              <el-radio-group v-model="sjdrfs" @change="Radio($event)">
                <el-radio label="1">追加（导入时已有的记录信息不变，只添加新的记录）</el-radio>
                <el-radio label="2">覆盖（导入时更新已有的记录信息，并添加新的记录）</el-radio>
              </el-radio-group>
            </div>
            <div class="daochu">
              <div>三、将按模板填写的文件，导入到系统中。</div>
              <el-button type="primary" size="mini" @click="chooseFile">
                上传导入
              </el-button>
            </div>
          </div>
        </el-dialog>
        <!-- -----------------导入-弹窗--------------------------- -->
        <el-dialog width="1000px" height="800px" title="导入培训清单汇总情况" class="scbg-dialog" :visible.sync="dialogVisible_dr"
          show-close>
          <div style="height: 600px;">
            <el-table :data="dr_cyz_list" ref="multipleTable" @selection-change="handleSelectionChange"
              style="width: 100%;border:1px solid #EBEEF5;" height="100%" stripe>
              <el-table-column type="selection" width="55"> </el-table-column>
              <el-table-column prop="培训日期" label="培训日期"></el-table-column>
              <el-table-column prop="部门" label="部门"></el-table-column>
              <el-table-column prop="培训单位" label="培训单位"></el-table-column>
              <el-table-column prop="培训内容" label="培训内容"></el-table-column>
              <el-table-column prop="课时" label="课时"></el-table-column>
              <el-table-column prop="培训形式" label="培训形式"></el-table-column>
            </el-table>
          </div>

          <div style="height :30px;display: flex;align-items: center;justify-content: center;margin: 10px 0;">
            <el-button type="primary" @click="drcy" size="mini">导 入</el-button>
            <el-button @click="dialogVisible_dr = false" size="mini">返 回</el-button>
          </div>
        </el-dialog>

        <!-- -----------------培训清单信息-弹窗--------------------------- -->
        <el-dialog title="新增培训清单信息" :close-on-click-modal="false" :visible.sync="dialogVisible" width="47.5%" class="xg"
          :before-close="handleClose" @close="close('formName')">
          <el-form ref="formName" :model="tjlist" :rules="rules" label-width="120px" size="mini">
            <div style="display:flex">
              <el-form-item label="培训主题" prop="pxzt">
                <el-autocomplete class="inline-input" value-key="pxzt" v-model.trim="tjlist.pxzt"
                  :fetch-suggestions="querySearchpxzt" placeholder="培训主题" @blur="onInputBlur(1)" style="width: 100%;">
                </el-autocomplete>
              </el-form-item>
              <el-form-item label="培训日期" prop="pxrq">
                <el-date-picker v-model="tjlist.pxrq" style="width:100%;" clearable type="date" placeholder="选择日期"
                  value-format="timestamp" @blur="onInputBlur(1)">
                </el-date-picker>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="培训地点" prop="pxdd">
                <el-autocomplete class="inline-input" value-key="pxdd" v-model.trim="tjlist.pxdd"
                  :fetch-suggestions="querySearchpxdd" placeholder="培训地点" style="width: 100%;">
                </el-autocomplete>
              </el-form-item>
              <el-form-item label="培训单位" prop="pxdw">
                <el-autocomplete class="inline-input" value-key="pxdw" v-model.trim="tjlist.pxdw"
                  :fetch-suggestions="querySearchpxdw" placeholder="培训单位" style="width: 100%;">
                </el-autocomplete>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="培训讲师" prop="pxjs">
                <el-autocomplete class="inline-input" value-key="pxjs" v-model.trim="tjlist.pxjs"
                  :fetch-suggestions="querySearchpxjs" placeholder="培训讲师" style="width: 100%;">
                </el-autocomplete>
              </el-form-item>
              <el-form-item label="培训形式" prop="pxxs">
                <el-select v-model="tjlist.pxxs" placeholder="请选择培训形式" style="width: 100%;">
                  <el-option v-for="item in pxxsxz" :label="item.pxlxmc" :value="item.pxlxmc" :key="item.pxlxid">
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="培训类型" prop="pxlx" class="one-line">
                <el-select v-model="tjlist.pxlx" placeholder="请选择培训类型" style="width: 100%;">
                  <el-option v-for="item in pxlxxz" :label="item.pxlxmc" :value="item.pxlxmc" :key="item.pxlxid">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="课时" prop="ks" class="one-line">
                <el-input placeholder="课时" v-model="tjlist.ks" clearable>
                </el-input>
              </el-form-item>
            </div>

            <el-form-item label="培训主要内容" prop="pxnr" class="one-line-textarea">
              <el-input type="textarea" v-model="tjlist.pxnr" style="width: 100%;"></el-input>
            </el-form-item>
            <div class="online-titlebtn">
              <div class="title">
                <span>培训人员清单</span>
              </div>
              <div style="text-align: right;">
                <!-- <el-button type="primary" size="small" @click='rydialogVisible = true'>新 增</el-button>
								<el-button size="small" type="danger" @click="deletery1">删 除</el-button> -->
                <el-button type="primary" size="small" @click='rydialogVisible = true'>修改人员清单</el-button>
              </div>
            </div>
          </el-form>
          <div style="margin-top:10px">
            <el-table :data="tianjiaryList" border @selection-change="selectRow1"
              :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
              style="width: 100%;border:1px solid #EBEEF5;" stripe height="250">
              <!-- <el-table-column type="selection" width="55"> </el-table-column> -->
              <el-table-column type="index" width="60" label="序号"></el-table-column>
              <el-table-column prop="bm" label="部门" width="180">
              </el-table-column>
              <el-table-column prop="xm" label="姓名" width="180">
              </el-table-column>
              <el-table-column prop="smdj" label="涉密等级">
              </el-table-column>
            </el-table>
          </div>
          <span slot="footer" class="dialog-footer" style="margin-top:10px">
            <el-button type="primary" @click="submitTj('formName')" style="margin-top:10px">保 存</el-button>
            <el-button type="warning" @click="dialogVisible = false" style="margin-top:10px;">关 闭
            </el-button>
          </span>
        </el-dialog>

        <el-dialog title="修改培训清单信息" :close-on-click-modal="false" :visible.sync="xgdialogVisible" width="47.5%"
          class="xg" @close="close1('form')">
          <el-form ref="form" :model="xglist" :rules="rules" label-width="120px" size="mini">
            <div style="display:flex">
              <el-form-item label="培训主题" prop="pxzt">
                <el-autocomplete class="inline-input" value-key="pxzt" v-model.trim="xglist.pxzt"
                  :fetch-suggestions="querySearchpxzt" placeholder="培训主题" @blur="onInputBlur(2)">
                </el-autocomplete>
              </el-form-item>
              <el-form-item label="培训日期" prop="pxrq">
                <el-date-picker v-model="xglist.pxrq" style="width:100%;" clearable type="date" placeholder="选择日期"
                  value-format="timestamp" @blur="onInputBlur(2)">
                </el-date-picker>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="培训地点" prop="pxdd">
                <el-autocomplete class="inline-input" value-key="pxdd" v-model.trim="xglist.pxdd"
                  :fetch-suggestions="querySearchpxdd" placeholder="培训地点">
                </el-autocomplete>
              </el-form-item>
              <el-form-item label="培训单位" prop="pxdw">
                <el-autocomplete class="inline-input" value-key="pxdw" v-model.trim="xglist.pxdw"
                  :fetch-suggestions="querySearchpxdw" placeholder="培训单位">
                </el-autocomplete>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="培训讲师" prop="pxjs">
                <el-autocomplete class="inline-input" value-key="pxjs" v-model.trim="xglist.pxjs"
                  :fetch-suggestions="querySearchpxjs" placeholder="培训讲师">
                </el-autocomplete>
              </el-form-item>
              <el-form-item label="培训形式" prop="pxxs">
                <el-select v-model="xglist.pxxs" placeholder="请选择培训形式">
                  <el-option v-for="item in pxxsxz" :label="item.pxlxmc" :value="item.pxlxmc" :key="item.pxlxid">
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="培训类型" prop="pxlx" class="one-line">
                <el-select v-model="xglist.pxlx" placeholder="请选择培训类型" style="width: 100%;">
                  <el-option v-for="item in pxlxxz" :label="item.pxlxmc" :value="item.pxlxmc" :key="item.pxlxid">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="课时" prop="ks" class="one-line">
                <el-input placeholder="课时" v-model="xglist.ks" clearable oninput="value=value.replace(/[^\d.]/g,'')"
                  @blur="ks = $event.target.value">
                </el-input>
              </el-form-item>
            </div>

            <el-form-item label="培训主要内容" prop="pxnr" class="one-line-textarea">
              <el-input type="textarea" v-model="xglist.pxnr"></el-input>
            </el-form-item>
            <div class="online-titlebtn">
              <div class="title">
                <span>培训人员清单</span>
              </div>
              <div style="text-align: right;">
                <!-- <el-button type="primary" size="small" @click='ryxg'>保 存</el-button>
								<el-button size="small" type="danger" @click="deletery2">删 除</el-button> -->
                <el-button type="primary" size="small" @click='ryxg'>修改人员清单</el-button>
              </div>
            </div>
          </el-form>
          <div style="margin-top:10px">
            <el-table :data="xglist.ry" border @selection-change="selectRow2"
              :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" height="250"
              style="width: 100%;border:1px solid #EBEEF5;" stripe>
              <!-- <el-table-column type="selection" width="55"> </el-table-column> -->
              <el-table-column type="index" width="60" label="序号"></el-table-column>
              <el-table-column prop="bm" label="部门" width="180">
              </el-table-column>
              <el-table-column prop="xm" label="姓名" width="180">
              </el-table-column>
              <el-table-column prop="smdj" label="涉密等级">
              </el-table-column>
            </el-table>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="updataDialog('form')" style="margin-top:10px">保 存</el-button>
            <el-button type="warning" @click="xgdialogVisible = false" style="margin-top:10px">关 闭
            </el-button>
          </span>
        </el-dialog>

        <el-dialog title="培训清单信息详情" :close-on-click-modal="false" :visible.sync="xqdialogVisible" width="47.5%"
          class="xg">
          <el-form ref="form" :model="xglist" :rules="rules" label-width="120px" size="mini" disabled>
            <div style="display:flex">
              <el-form-item label="培训主题">
                <el-input placeholder="培训主题" v-model="xglist.pxzt" clearable style="width:100%;"></el-input>
              </el-form-item>
              <el-form-item label="培训日期">
                <el-date-picker v-model="xglist.pxrq" clearable type="date" placeholder="选择日期" value-format="timestamp"
                  style="width:100%;">
                </el-date-picker>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="培训地点">
                <el-input placeholder="培训地点" v-model="xglist.pxdd" clearable style="width:100%;"></el-input>
              </el-form-item>
              <el-form-item label="培训单位">
                <el-input placeholder="培训单位" v-model="xglist.pxdw" clearable style="width:100%;"></el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="培训讲师">
                <el-input placeholder="培训讲师" v-model="xglist.pxjs" clearable style="width:100%;"></el-input>
              </el-form-item>
              <el-form-item label="培训形式">
                <el-select v-model="xglist.pxxs" placeholder="请选择培训形式" style="width:100%;">
                  <el-option v-for="item in pxxsxz" :label="item.pxlxmc" :value="item.pxlxmc" :key="item.pxlxid">
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="培训类型"  class="one-line">
                <el-select v-model="xglist.pxlx" placeholder="请选择培训类型" style="width: 100%;">
                  <el-option v-for="item in pxlxxz" :label="item.pxlxmc" :value="item.pxlxmc" :key="item.pxlxid">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="课时" prop="ks" class="one-line">
                <el-input placeholder="课时" v-model="xglist.ks" clearable></el-input>
              </el-form-item>
            </div>
            <!-- <el-form-item label="培训主要内容">
							<el-input type="textarea" v-model="xglist.pxnr"></el-input>
						</el-form-item> -->
            <div class="online-titlebtn">
              <div class="title">
                <span>培训人员清单</span>
              </div>
            </div>
          </el-form>
          <div style="margin-top:10px">
            <el-table :data="xglist.ry" border @selection-change="selectRow"
              :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" height="250"
              style="width: 100%;border:1px solid #EBEEF5;" stripe>
              <!-- <el-table-column type="selection" width="55"> </el-table-column> -->
              <el-table-column type="index" width="60" label="序号"></el-table-column>
              <el-table-column prop="bm" label="部门" width="180">
              </el-table-column>
              <el-table-column prop="xm" label="姓名" width="180">
              </el-table-column>
              <el-table-column prop="smdj" label="涉密等级">
              </el-table-column>
            </el-table>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button type="warning" @click="xqdialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>
        <el-dialog title="培训人员清单" :close-on-click-modal="false" :visible.sync="rydialogVisible" width="54%" class="xg"
          style="margin-top:4vh">
          <el-row type="flex">
            <el-col :span="12" style="height:500px">
              <div style="height:96%;border: 1px solid #dee5e7;">
                <div style="padding-top: 10px;padding-left: 10px;width: 97%;height: 68px;background: #fafafa;">
                  <el-row>待选人员列表</el-row>
                  <el-form :inline="true" :model="formInlinery" size="medium" class="demo-form-inline"
                    style="display:flex;margin-bottom: -3%;">
                    <div class="dialog-select-div">
                      <span class="title">部门</span>
                      <el-cascader v-model="formInlinery.bm" :options="regionOption" clearable class="widths"
                        style="width:14vw" :props="regionParams" filterable ref="cascaderArr">
                      </el-cascader>
                      <el-button type="primary" icon="el-icon-search" @click="onSubmitry">查询
                      </el-button>
                    </div>
                    <!-- <el-form-item label="部门" class="dialog-select-div">
											<el-cascader v-model="formInlinery.bm" :options="regionOption" clearable class="widths"
												style="width:14vw" :props="regionParams" filterable ref="cascaderArr">
											</el-cascader>
										</el-form-item>
										<el-form-item>
											<el-button type="primary" icon="el-icon-search" @click="onSubmitry">查询
											</el-button>
										</el-form-item> -->
                  </el-form>
                </div>
                <el-table :data="table1Data" style="width: 100%;margin-top:1%;" height="400" ref="table1"
                  @selection-change="onTable1Select" @row-click="handleRowClick">
                  <el-table-column type="selection" width="55">
                  </el-table-column>
                  <el-table-column prop="xm" label="姓名">
                  </el-table-column>
                </el-table>
              </div>
              <!-- 分页区 -->
              <!-- <div style="border: 1px solid #ebeef5;">
								<el-pagination background @current-change="handleCurrentChange1" @size-change="handleSizeChange1"
									:pager-count="5" :current-page="page1" :page-sizes="[5, 10, 20, 30]" :page-size="pageSize1"
									layout="total, prev, pager, sizes,next, jumper" :total="total1">
								</el-pagination>
							</div> -->
            </el-col>
            <!-- <el-col :span="4" style="margin-left: 18px;
    margin-top: 70px;display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;">
							<el-button type="primary" @click="onAdd">添 加</el-button>
							<el-button type="danger" @click="onDelete" style="margin-top: 50px;
    margin-left: 0;">删 除</el-button>
						</el-col> -->
            <el-col :span="12" style="margin-left:10px;height:500px">
              <div style="height:96%;
    										border: 1px solid #dee5e7;
    										">
                <div style="padding-top: 10px;
    										padding-left: 10px;
    										width: 97%;
    										height: 68px;
    										background: #fafafa;">
                  <el-row>已选人员列表</el-row>
                  <div style="float:right;">
                    <el-button type="primary" @click="addpxry">保 存</el-button>
                    <el-button type="warning" @click="rydialogVisible = false">关 闭</el-button>
                  </div>

                </div>
                <el-table :data="table2Data" style="width: 100%;" height="404" ref="table2">
                  <el-table-column prop="xm" label="姓名">
                    <template slot-scope="scope">
                      <div style="display:flex;justify-content: space-between;
    														align-items: center;">
                        <div>
                          {{ scope.row.xm }}
                        </div>
                        <i class="el-icon-circle-close btn" @click="onTable2Select(scope.row)"></i>
                      </div>
                    </template>
                  </el-table-column>
                  <!-- <el-table-column type="selection" width="55">
										</el-table-column> -->

                </el-table>
              </div>

            </el-col>
          </el-row>
        </el-dialog>
      </div>
    </div>
  </div>

</template>
<script>
import {
  getpxlx,
  getpxlxnw
} from "../../../db/xzdb"
import {
  getbmmc,
  getsmry
} from "../../../db/smgwgldb";
import {
  //内容管理初始化成员列表
  getPxqd,
  //添加内容管理
  addPxqd,
  //删除内容管理
  deletePxqd,
  updatePxqd,
  jxpxzt,
  getPxzt
} from "../../../db/pxqddb";
import {
  getlogin
} from "../../../db/loginyhdb";
import {
  getpxqdry
} from "../../../db/pxqdry"
import {
  exportExcel,
  exportExcelNumerousSheet
} from "../../../utils/exportExcel"; //excel导出工具
import {
  dateFormatNYRChinese
} from "../../../utils/moment"
import {
  getUuid
} from "../../../utils/getUuid"; //获取uuid
import XLSX from "xlsx";

import {
  selectSettingListByCsbs
} from '../../../db/zczpSystem/zczpSysyemDb'

export default {
  components: {},
  props: {},
  data() {

    var checkKsValidator = (rule, value, callback, form) => {
      // console.log('ks value', value, value.length, form)
      // 校验是否存在非数字字符串
      let notNum = value.match(/[^\d]/)
      // console.log('notNum', notNum)
      if (notNum) {
        form.ks = value.replace(/[^\d.]/g, '')
        // callback(new Error('课时只能输入数字'))
        return
      }
      if (value.length <= 0) {
        callback(new Error('请输入课时，课时只能为数字'))
      }
      callback()
    }

    return {
      pxrq: '',
      pxzt: '',
      pdpxzt: 0,
      pxxsxz: [],
      pxlxxz: [],
      table1Data: [],
      table2Data: [],
      selectedTable1Data: [], // table1已选数据
      selectedTable2Data: [], // table2已选数据
      selectedTableData: [], //备份数据
      // data: generateData(),
      value: [],
      filterMethod(query, item) {
        return item.pinyin.indexOf(query) > -1;
      },
      tianjiaryList: [],
      xgtianjiaryList: [],
      pxqdList: [],
      xglist: {},
      updateItemOld: {},
      xgdialogVisible: false,
      xqdialogVisible: false,
      rydialogVisible: false,
      formInline: {},
      formInlinery: {
        bm: ''
      },
      tjlist: {
        pxzt: '',
        pxrq: '',
        pxdd: '',
        pxdw: '',
        pxjs: '',
        pxlx: '',
        pxxs: '',
        ks: '',
        pxnr: '',
      },
      page: 1,
      pageSize: 10,
      total: 0,
      page1: 1,
      pageSize1: 10,
      total1: 0,
      selectlistRow: [], //列表的值
      selectlistRow1: [], //列表的值
      selectlistRow2: [], //列表的值
      dialogVisible: false, //添加弹窗状态
      //表单验证
      rules: {
        pxzt: [{
          required: true,
          message: '请输入培训主题',
          trigger: ['blur', 'change'],
        },],
        pxrq: [{
          required: true,
          message: '请选择培训日期',
          trigger: 'blur'
        },],
        pxdd: [{
          required: true,
          message: '请输入培训地点',
          trigger: ['blur', 'change'],
        },],
        pxdw: [{
          required: true,
          message: '请输入培训单位',
          trigger: ['blur', 'change'],
        },],
        pxjs: [{
          required: true,
          message: '请输入培训讲师',
          trigger: ['blur', 'change'],
        },],
        pxxs: [{
          required: true,
          message: '请选择培训形式',
          trigger: 'blur'
        },],
        pxlx: [{
          required: true,
          message: '请选择培训类型',
          trigger: 'blur'
        },],
        // ks: [{
        // 	required: true,
        // 	message: '请输入课时',
        // 	trigger: 'blur'
        // },],
        ks: [{
          validator: (rule, value, callback) => {
            checkKsValidator(rule, value, callback, this.tjlist)
          },
          trigger: ['blur', 'change']
        }],
        pxnr: [{
          required: true,
          message: '请输入培训主要内容',
          trigger: 'blur'
        },],
      },
      //导入
      dialogVisible_dr: false, //导入成员组弹窗状态
      dr_cyz_list: [], //待选择导入成员组列表
      multipleTable: [], //已选择导入成员组列表
      regionOption: [], //地域信息
      regionParams: {
        label: 'label', //这里可以配置你们后端返回的属性
        value: 'label',
        children: 'childrenRegionVo',
        expandTrigger: 'click',
        checkStrictly: true,
      }, //地域信息配置参数
      dwmc: '',
      dwdm: '',
      dwlxr: '',
      dwlxdh: '',
      year: '',
      yue: '',
      ri: '',
      Date: '',
      xh: [],
      dclist: [],
      pxtjlist: [],
      sxry: '',
      dr_dialog: false,
      //数据导入方式
      sjdrfs: '',
      pxzxs: ''
    }
  },
  computed: {},
  mounted() {
    this.pxztlist()
    this.dwmc = getlogin()[0].dwmc
    let date = new Date()
    this.year = date.getFullYear() + '年'; //获取完整的年份(4位)
    this.yue = date.getMonth() + '月'; //获取当前月份(0-11,0代表1月)
    this.ri = date.getDate() + '日'; //获取当前日(1-31)
    this.Date = this.year + this.yue + this.ri
    this.pxxsxz = getpxlx()
    this.pxlxxz = getpxlxnw()
    console.log(this.pxxsxz);
    //列表初始化
    this.pxqd()
    this.$refs.upload.addEventListener('change', e => { //绑定监听表格导入事件resList 
      this.readExcel(e);
    })
    this.ry()
    let bmmc = getbmmc()
    let shu = []
    // console.log(bmmc);
    bmmc.forEach(item => {
      let childrenRegionVo = []
      bmmc.forEach(item1 => {
        if (item.bmm == item1.fbmm) {
          // console.log(item, item1);
          childrenRegionVo.push(item1)
          // console.log(childrenRegionVo);
          item.childrenRegionVo = childrenRegionVo
        }
      });
      // console.log(item);
      shu.push(item)
    })
    console.log(shu[0]);
    if (shu[0].childrenRegionVo) {
      shu[0].childrenRegionVo.forEach(item => {
      this.regionOption.push(item)
    })
    }
  },
  methods: {
    //
    handKsInput(value) {
      console.log('ks', value)
      value = value.replace(/[^\d.]/g, '')
      console.log('ks2', value)
      this.tjlist.ks = value
    },
    //
    Radio(val) {
      this.sjdrfs = val
      console.log("当前选中的值", val)
    },
    mbxzgb() {
      this.sjdrfs = ''
    },
    cz() {
      this.formInline = {}
    },
    mbdc() {
      console.log("----导出涉密人员----")
      // console.log(this.selectlistRow);
      // if (this.selectlistRow.length > 0) {
      let filename = "培训清单汇总情况表" + getUuid() + ".xlsx"

      const {
        dialog
      } = require('electron').remote;
      //弹窗title
      let options = {
        title: "保存文件",
        defaultPath: filename,
      };
      console.log(dialog)
      //导出文件夹选择弹窗
      dialog.showSaveDialog(options, result => {

        console.log('result', result)
        if (result == null || result == "") {
          console.log("取消导出")
          return
        }
        // 多sheet页数据的总集合
        let exportList = []
        //
        let list = []

        //对导出的数据进行格式，js导出excel要求传入的参数格式为[["列1名称","列2名称","列3名称"],["列11","值21","值31"],["列12","值22","值32"]]
        list.push(["序号", "培训主题", "培训日期", "培训地点", "培训单位",
          "培训讲师", "培训形式", "培训类型", "受训人员", "课时", "培训主要内容"
        ]) //确定列名

        let list1 = []

        //对导出的数据进行格式，js导出excel要求传入的参数格式为[["列1名称","列2名称","列3名称"],["列11","值21","值31"],["列12","值22","值32"]]
        list1.push(["序号", "姓名", "部门", "涉密岗位", "涉密等级",
          "培训总学时", "培训达标学时"
        ]) //确定列名

        // 将数据添加到sheet页
        // 放入总集合中
        exportList.push({
          data: list,
          sheetName: '保密教育培训清单',
        })
        exportList.push({
          data: list1,
          sheetName: '保密教育培训统计',
        })
        let styles = {
          // 列样式
          cols: {
            // 作用sheet页索引（0开始）（-1全sheet页生效）
            scoped: -1,
            style: [{
              wpx: 100
            },
            {
              wpx: 300
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            }, {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 300
            },
            ]
          },
          // 全局样式
          all: {
            alignment: {
              horizontal: 'center', // 水平居中
              vertical: 'center', // 垂直居中
              wrapText: true, // 文字换行
            },
            font: {
              sz: 11, // 字号
              name: '宋体' // 字体
            },
            border: { // 边框
              top: {
                style: 'thin'
              },
              bottom: {
                style: 'thin'
              },
              left: {
                style: 'thin'
              },
              right: {
                style: 'thin'
              }
            }
          },
          // 单元格样式
          cell: []
        }
        // excel导出方法自定义配置
        let config = {
          // 忽略行索引(从1开始)
          rowIndexArrIgnore: [1]
        }
        // return
        exportExcelNumerousSheet(result, exportList, styles, config) //多sheet页excel导出方法
        this.dr_dialog = false
        this.$message('导出成功:' + result)
      })
    },

    sjgsh(sj) {
      return dateFormatNYRChinese(new Date(sj))
    },
    xz() {
      this.tianjiaryList = []
      this.table2Data = []
      this.formInlinery.bm = ''
      this.ry()
      this.dialogVisible = true
    },
    async ry() {
      let params = {
        page: this.page1,
        pageSize: this.pageSize1
      }
      console.log(this.formInlinery.bm);
      Object.assign(params, this.formInlinery)
      let list = getpxqdry(params)
      this.table1Data = list.list_total
      this.pxtjlist = list.list_total
      console.log("==========================", this.pxtjlist);
      this.sxry = this.pxtjlist.length
      console.log("this.pxtjlist", this.pxtjlist, this.sxry);
      console.log("==========================", this.pxtjlist);
      this.pxtjlist.forEach((item, label) => {
        this.xh.push(label + 1)
      })
      this.total1 = list.total
      console.log(this.table1Data);
    },
    onSubmitry() {
      this.ry()
    },
    /**
     * table1选择事件处理函数
     * @param {array} rows 已勾选的数据
     */
    onTable1Select(rows) {
      console.log(rows);
      this.table2Data = rows
      this.selectlistRow = rows
      // this.selectedTable1Data = [...rows];
      // this.filterAdd(this.selectedTable1Data, this.table2Data, 'sfzhm');
      // this.selectedTable1Data = [];
      // this.$refs.table1.clearSelection();
    },

    /**
     * table2选择事件处理函数
     * @param {array} rows 已勾选的数据
     */
    onTable2Select(rows) {
      console.log(rows);
      console.log(this.table2Data);
      console.log(this.$refs.table1.selection);
      this.$refs.table1.selection.forEach((item, label) => {
        if (item == rows) {
          this.$refs.table1.selection.splice(label, 1)
        }
      })
      this.table2Data.forEach((item, label) => {
        if (item == rows) {
          console.log(label);
          this.table2Data.splice(label, 1)
        }
      })
      // this.selectedTable2Data = [...rows];
      // this.table2Data = this.filterDelete(this.selectedTable2Data, this.table2Data, 'sfzhm');
      // this.selectedTable2Data = [];
    },

    /**
     * 添加按钮事件处理函数
     */
    onAdd() {

      // this.tianjiaryList = []
    },

    /**
     * 删除按钮事件处理函数
     */
    onDelete() {

    },

    /**
     * 根据选中项去重添加到array中
     * @param {array} records   待添加数据
     * @param {array} targetRecords   目标数据
     * @param {string} compareProperty  对比的重复属性
     * @param {boolean} isEnd   往尾部添加？默认往头部添加
     */
    filterAdd(records = [], targetRecords = [], compareProperty, isEnd = false) {
      const o = new Set();
      targetRecords.forEach(record => {
        o.add(record[compareProperty]);
      })
      records.forEach(record => {
        if (!o.has(record[compareProperty])) {
          if (isEnd) {
            targetRecords.push(record);
          } else {
            targetRecords.unshift(record);
          }
        }
      })
    },

    /**
     * 删除数组中数据
     * @param {array} records   待删除数据
     * @param {array} targetRecords   目标数据
     * @param {string} compareProperty  对比的重复属性
     * @return {array} 删除待删除数据后的目标数据
     */
    filterDelete(records = [], targetRecords = [], compareProperty) {
      const o = new Set();
      records.forEach(record => {
        o.add(record[compareProperty]);
      })

      return targetRecords.filter((item) => !o.has(item[compareProperty]))
    },
    addpxry() {
      this.tianjiaryList = this.table2Data
      this.xglist.ry = this.table2Data
      this.rydialogVisible = false
    },
    //导入
    chooseFile() {
      if (this.sjdrfs != '') {
        if (this.sjdrfs == 1) {
          this.$refs.upload.click()
        } else if (this.sjdrfs == 2) {
          let valArr = this.dclist
          valArr.forEach(function (item) {
            deletePxqd(item)
          })
          this.$refs.upload.click()
        }
      } else {
        this.$message.warning('请选择导入方式')
      }
    },
    //----成员组选择
    handleSelectionChange(val) {
      this.multipleTable = val
      console.log("选中：", this.multipleTable);
    },
    //---确定导入成员组
    drcy() {
      //遍历已选择导入的成员，进行格式化，然后添加到数据库
      for (var i in this.multipleTable) {
        var cy = {
          pxrq: this.multipleTable[i]["培训日期"],
          pxdd: this.multipleTable[i]["培训地点"],
          pxdw: this.multipleTable[i]["培训单位"],
          pxnr: this.multipleTable[i]["培训内容"],
          ks: this.multipleTable[i]["课时"],
          pxxs: this.multipleTable[i]["培训形式"],
          pxlx: this.multipleTable[i]["培训类型"],
          bz: this.multipleTable[i]["备注"],
        }
        addPxqd(cy)
      }
      this.dialogVisible_dr = false
      this.pxqd()
    },
    //----表格导入方法
    readExcel(e) {
      var that = this;
      const files = e.target.files;
      console.log("files", files);
      var vali = /\.(xls|xlsx)$/
      if (files.length <= 0) { //如果没有文件名
        return false;
      } else if (!vali.test(files[0].name.toLowerCase())) {
        this.$Message.error('上传格式不正确，请上传xls或者xlsx格式');
        return false;
      }
      const fileReader = new FileReader();
      fileReader.onload = (e) => {
        try {
          const data = e.target.result;
          const workdata = XLSX.read(data, {
            type: 'binary'
          });
          console.log("文件的内容：", workdata) // 文件的内容
          //查看打印能看到 workdata的sheetNames里是文件中每个表的有值的集合；
          const wsname = workdata.SheetNames[0]; //取第一张表
          console.log('wsname', wsname)
          const ws = XLSX.utils.sheet_to_json(workdata.Sheets[wsname]); //生成json表格内容
          console.log(ws); //自第二行开始的内容
          this.dialogVisible_dr = true
          this.dr_cyz_list = ws
          console.log("列表的值:", this.dr_cyz_list)
          // 加工excel读取业务类型为数组
          // this.dr_cyz_list.forEach(function(item) {
          // 	console.log(item[0]['业务类型'].splite(','))
          // })
          this.$refs.upload.value = ''; // 处理完成 清空表单值
          this.dr_dialog = false
        } catch (e) {
          return false;
        }
      };
      fileReader.readAsBinaryString(files[0]);
    },
    //导出
    exportList() {
      console.log("----导出涉密人员----")
      // console.log(this.selectlistRow);
      // if (this.selectlistRow.length > 0) {
      let filename = "培训清单汇总情况表" + getUuid() + ".xlsx"

      // 获取教育培训达标学时（参数设置表）
      let jypxdbxsSetting = selectSettingListByCsbs('jypxdbxs')
      let jypxdbxs = 0
      if (jypxdbxsSetting) {
        jypxdbxs = jypxdbxsSetting.csz
      }

      const {
        dialog
      } = require('electron').remote;
      //弹窗title
      let options = {
        title: "保存文件",
        defaultPath: filename,
      };
      console.log(dialog)
      //导出文件夹选择弹窗
      dialog.showSaveDialog(options, result => {
        // let sheet_name
        // let sheet
        // let sheet1_name
        // let sheet1
        // let work_book = XLSX.utils.book_new()
        console.log('result', result)
        if (result == null || result == "") {
          console.log("取消导出")
          return
        }
        // 多sheet页数据的总集合
        let exportList = []
        //
        let list = []
        // 保密教育培训明细数据
        let list3 = []
        // 培训清单里的人员集合，为了操作方便，这里把人员都抽出来
        let pxqdRyList = []

        list.push(["保密教育培训清单"])
        list.push(["上报单位:", this.dwmc, "", "", "", "", "", "", "", "", ""])
        list.push(["统计年度:", this.year, "", "", "", "", "", "", "", "填报时间:", this.Date])
        //对导出的数据进行格式，js导出excel要求传入的参数格式为[["列1名称","列2名称","列3名称"],["列11","值21","值31"],["列12","值22","值32"]]
        list.push(["序号", "培训主题", "培训日期", "培训地点", "培训单位", "培训讲师", "培训形式", "培训类型", "受训人员", "课时", "培训主要内容"]) //确定列名

        for (var i in this.dclist) { //每一行的值
          let item = this.dclist[i] //{"name":"XXX","dw":"XXX","zw":"XXX"}

          // 放入培训清单人员集合中
          if (item.ry) {
            item.ry.forEach(tempRyItem => {
              // 把培训清单的学时加入到人员中，方便计算
              tempRyItem.ks = item.ks
              pxqdRyList.push(tempRyItem)
              list3.push([(parseInt(i) + 1), item["pxzt"], dateFormatNYRChinese(item["pxrq"]), item.ks, tempRyItem.xm, tempRyItem.bm, tempRyItem.gwmc, tempRyItem.smdj])
            })
          }

          console.log("导出值:", this.dclist);
          let column = [(parseInt(i) + 1), item["pxzt"], dateFormatNYRChinese(item["pxrq"]),
          item["pxdd"],
          item["pxdw"], item["pxjs"], item["pxxs"], item["pxlx"], this.dclist[i].ry.length,
          item["ks"], item["pxnr"],
          ]
          list.push(column)
          console.log("1111111111111", column);
        }

        let merges = [{
          s: { //s为开始
            c: 0, //开始列
            r: 0 //开始取值范围
          },
          e: { //e结束
            c: 10, //结束列
            r: 0 //结束范围
          }
        }]
        // 保密教育培训统计
        let list1 = []
        list1.push(["保密教育培训统计"])
        list1.push(["上报单位:", this.dwm, "", "", "", "", ""])
        list1.push(["统计年度:", this.year, "", "", "", "填报时间:", this.Date])
        //对导出的数据进行格式，js导出excel要求传入的参数格式为[["列1名称","列2名称","列3名称"],["列11","值21","值31"],["列12","值22","值32"]]
        list1.push(["序号", "姓名", "部门", "涉密岗位", "涉密等级", "培训总学时", "培训达标学时"]) //确定列名
        // 获取涉密人员信息（通过涉密人员信息计算每个人的总学时）
        let smryList = this.pxtjlist
        console.log('smryList', smryList, pxqdRyList)
        smryList.forEach((ryItem, ryIndex) => {
          // 总学时
          let zxs = 0
          pxqdRyList.forEach(pxqdRyItem => {
            if (pxqdRyItem.sfzhm == ryItem.sfzhm) {
              zxs += parseInt(pxqdRyItem.ks)
            }
          })
          // 计算完成，加入到excel中
          list1.push([ryIndex + 1, ryItem.xm, ryItem.bm, ryItem.gwmc, ryItem.smdj, zxs, jypxdbxs])
        })
        // return
        // for (var i in this.dclist) { //每一行的值
        // 	let item1 = this.dclist[i] //{"name":"XXX","dw":"XXX","zw":"XXX"}
        // 	for (var i in this.pxtjlist) { //每一行的值
        // 		let item = this.pxtjlist[i] //{"name":"XXX","dw":"XXX","zw":"XXX"}

        // 		console.log("导出值:", this.pxtjlist);
        // 		let column = [(parseInt(i) + 1), item["xm"], item["bm"], item["gwmc"], item["smdj"]]
        // 		// 计算该人员的培训总学时
        // 		console.log('保密教育培训统计', item)
        // 		let isHaveFlag = false
        // 		let ks = 0
        // 		this.dclist.forEach(pxqdItem => {
        // 			pxqdItem.ry.some(ryItem => {
        // 				if (item.sfzhm == ryItem.sfzhm) {
        // 					isHaveFlag = true
        // 					console.log('找到了', ryItem)
        // 					return true
        // 				}
        // 			})
        // 			if (isHaveFlag) {
        // 				ks += parseInt(pxqdItem.ks)
        // 				isHaveFlag = false
        // 			}
        // 		})
        // 		column.push(ks)
        // 		column.push(jypxdbxs)
        // 		list1.push(column)
        // 		console.log("222222222222", column);
        // 	}
        // }
        let merges1 = [{
          s: { //s为开始
            c: 0, //开始列
            r: 0 //开始取值范围
          },
          e: { //e结束
            c: 6, //结束列
            r: 0 //结束范围
          }
        }]
        // 保密教育培训明细(在上方已加入数据，这里再加入头)
        list3.push(['统计年度', '', '', '', '', '', '填报时间', ''])
        list3.push(['序号', '培训主题', '培训时间', '培训学时', '姓名', '部门', '涉密岗位', '涉密等级'])
        list3.push(['上报单位', '', '', '', '', '', '', ''])
        list3.push(['保密教育培训明细'])
        // 翻转数组
        list3.reverse()
        // console.log('list3', list3)
        let merges3 = [{
          s: { //s为开始
            c: 0, //开始列
            r: 0 //开始取值范围
          },
          e: { //e结束
            c: 7, //结束列
            r: 0 //结束范围
          }
        }]
        // 将数据添加到sheet页
        // 放入总集合中
        exportList.push({
          data: list,
          sheetName: '保密教育培训清单',
          merges: merges
        })
        exportList.push({
          data: list1,
          sheetName: '保密教育培训统计',
          merges: merges1
        })
        exportList.push({
          data: list3,
          sheetName: '保密教育培训明细',
          merges: merges3
        })
        let styles = {
          // 列样式
          cols: {
            // 作用sheet页索引（0开始）（-1全sheet页生效）
            scoped: -1,
            style: [{
              wpx: 100
            },
            {
              wpx: 300
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            }, {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 300
            },
            ]
          },
          // 全局样式
          all: {
            alignment: {
              horizontal: 'center', // 水平居中
              vertical: 'center', // 垂直居中
              wrapText: true, // 文字换行
            },
            font: {
              sz: 11, // 字号
              name: '宋体' // 字体
            },
            border: { // 边框
              top: {
                style: 'thin'
              },
              bottom: {
                style: 'thin'
              },
              left: {
                style: 'thin'
              },
              right: {
                style: 'thin'
              }
            }
          },
          // 单元格样式
          cell: [{
            // 生效sheet页索引（值为 -1 时所有sheet页都生效）
            scoped: -1,
            // 索引
            index: 'A1',
            style: {
              font: {
                name: '宋体',
                sz: 16, // 字号
                bold: true,
              },
              alignment: {
                horizontal: 'center', // 水平居中
                vertical: 'center' // 垂直居中
              }
            }
          }]
        }
        // excel导出方法自定义配置
        let config = {
          // 忽略行索引(从1开始)
          rowIndexArrIgnore: [1]
        }
        // return
        exportExcelNumerousSheet(result, exportList, styles, config) //多sheet页excel导出方法
        this.$message('导出成功:' + result)
      })
    },
    //修改
    updataDialog(form) {
      this.$refs[form].validate((valid) => {
        if (valid) {
          // 删除旧的
          // deletePxqd(this.updateItemOld)
          // 插入新的
          if (this.pdpxzt == 0) {
            updatePxqd(this.xglist)
            // 刷新页面表格数据
            this.pxqd()
            // 关闭dialog
            this.$message.success('修改成功')
            this.xgdialogVisible = false
          } else {
            this.$message.error('该日期已有培训内容')
          }
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    xqyl(row) {
      this.updateItemOld = JSON.parse(JSON.stringify(row))

      this.xglist = JSON.parse(JSON.stringify(row))
      this.pxrq = this.xglist.pxrq
      this.pxzt = this.xglist.pxzt
      // this.form1.ywlx = row.ywlx
      console.log('old', row)
      console.log("this.xglist.ywlx", this.xglist);
      this.xqdialogVisible = true
    },

    updateItem(row) {
      this.ry()
      this.updateItemOld = JSON.parse(JSON.stringify(row))
      this.xglist = JSON.parse(JSON.stringify(row))
      this.table2Data = this.xglist.ry
      // this.form1.ywlx = row.ywlx
      console.log('old', row)
      console.log("this.xglist.ywlx", this.xglist);
      this.xgtianjiaryList = this.xglist.ry
      this.xgdialogVisible = true
    },
    ryxg() {
      this.rydialogVisible = true
      this.xglist.ry.forEach((item) => {
        this.table1Data.forEach((item1, index) => {
          if (item.sfzhm == item1.sfzhm) {
            this.$nextTick(() => {
              this.$refs.table1.toggleRowSelection(this.table1Data[index], true);
            })

          }
        })
      })
    },
    //查询
    onSubmit() {
      this.pxqd()
    },

    returnSy() {
      this.$router.push("/tzglsy");
    },
    pxqd() {
      let params = {
        page: this.page,
        pageSize: this.pageSize
      }
      Object.assign(params, this.formInline)
      let resList = getPxqd(params)
      console.log("params", params);

      this.pxqdList = resList.list
      this.dclist = resList.list_total
      this.dclist.forEach((item, label) => {
        this.xh.push(label + 1)
      })
      this.total = resList.total
    },
    //删除
    shanchu(id) {
      if (this.selectlistRow != '') {
        this.$confirm('是否继续删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let valArr = this.selectlistRow
          // console.log("....", val);
          valArr.forEach(function (item) {
            deletePxqd(item)
            console.log("删除：", item);
            console.log("删除：", item);
          })
          let params = valArr
          this.$message({
            message: '删除成功',
            type: 'success'
          });
          this.pxqd()
        }).catch(() => {
          this.$message('已取消删除')
        })
      } else {
        this.$message({
          message: '未选择删除记录，请选择下列列表',
          type: 'warning'
        });
      }
    },
    //添加
    showDialog() {
      this.resetForm()
      this.dialogVisible = true
    },
    //确定添加成员组
    submitTj(formName) {
      console.log(this.tjlist.pxrq);
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let params = {
            pxzt: this.tjlist.pxzt,
            pxjs: this.tjlist.pxjs,
            pxrq: this.tjlist.pxrq,
            pxdd: this.tjlist.pxdd,
            pxdw: this.tjlist.pxdw,
            pxnr: this.tjlist.pxnr,
            ks: this.tjlist.ks,
            pxxs: this.tjlist.pxxs,
            pxlx: this.tjlist.pxlx,
            ry: this.tianjiaryList,
            pxqdid: getUuid()
          }
          if (this.pdpxzt == 0) {
            addPxqd(params)
            this.dialogVisible = false
            this.resetForm()
            this.pxqd()
            this.pxztlist()
            this.$message({
              message: '添加成功',
              type: 'success'
            });
          } else {
            this.$message.error('该日期已有培训内容');
          }


        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    deleteTkglBtn() {

    },
    tianjiary() { },
    selectRow(val) {
      console.log(val);
      this.selectlistRow = val;
    },
    selectRow1(val) {
      console.log(val);
      this.selectlistRow1 = val;
    },
    selectRow2(val) {
      console.log(val);
      this.selectlistRow2 = val;
    },
    deletery1() {
      this.selectlistRow1.forEach(item => {
        console.log(item);
        this.tianjiaryList.forEach((item1, index) => {
          if (item.sfzhm == item1.sfzhm) {
            console.log(index);
            this.tianjiaryList.splice(index, 1)
          }
        })
      })
    },
    deletery2() {
      this.selectlistRow2.forEach(item => {
        console.log(item);
        this.xglist.ry.forEach((item1, index) => {
          if (item.sfzhm == item1.sfzhm) {
            console.log(index);
            this.xglist.ry.splice(index, 1)
          }
        })
      })
    },
    //列表分页--跳转页数
    handleCurrentChange(val) {
      this.page = val
      this.pxqd()
    },
    //列表分页--更改每页显示个数
    handleSizeChange(val) {
      this.page = 1
      this.pageSize = val
      this.pxqd()
    },
    handleCurrentChange1(val) {
      this.page1 = val
      this.ry()
    },
    //列表分页--更改每页显示个数
    handleSizeChange1(val) {
      this.page1 = 1
      this.pageSize1 = val
      this.ry()
    },
    //添加重置
    resetForm() {
      this.tjlist.pxzt = ''
      this.tjlist.pxjs = ''
      this.tjlist.pxrq = ''
      this.tjlist.pxdd = ''
      this.tjlist.pxdw = ''
      this.tjlist.pxnr = ''
      this.tjlist.ks = ''
      this.tjlist.pxxs = ''
      this.tjlist.pxlx = ''
      this.formInlinery.bm = ''
    },
    handleClose(done) {
      this.pxztlist()
      this.resetForm()
      this.dialogVisible = false
    },
    // 弹框关闭触发
    close(formName) {
      // 清空表单校验，避免再次进来会出现上次校验的记录
      this.$refs[formName].resetFields();
    },
    close1(form) {
      // 清空表单校验，避免再次进来会出现上次校验的记录
      this.$refs[form].resetFields();
      this.formInlinery.bm = ''
    },
    onInputBlur(index) {
      if (index == 1) {
        this.pdpxzt = jxpxzt(this.tjlist)
        if (this.pdpxzt == 1) {
          this.$message.error('该时间已有该培训主题')
        }
      } else if (index == 2) {
        this.pdpxzt = 0
        if (this.pxrq != this.xglist.pxrq) {
          this.pdpxzt = jxpxzt(this.xglist)
          if (this.pdpxzt == 1) {
            this.$message.error('该时间已有该培训主题')
          }
        } else if (this.pxzt != this.xglist.pxzt) {
          this.pdpxzt = jxpxzt(this.xglist)
          if (this.pdpxzt == 1) {
            this.$message.error('该时间已有该培训主题')
          }
        }

      }

    },
    //模糊查询培训主题
    querySearchpxzt(queryString, cb) {
      var restaurants = this.restaurantspxzt;
      console.log("restaurants", restaurants);
      var results = queryString ? restaurants.filter(this.createFilterpxzt(queryString)) : restaurants;
      console.log("results", results);
      // 调用 callback 返回建议列表的数据
      for (var i = 0; i < results.length; i++) {
        for (var j = i + 1; j < results.length; j++) {
          if (results[i].pxzt === results[j].pxzt) {
            results.splice(j, 1);
            j--;
          }
        }
      }
      cb(results);
      console.log("cb(results.zw)", results);
    },
    createFilterpxzt(queryString) {
      return (restaurant) => {
        return (restaurant.pxzt.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
      };
    },
    //模糊查询培训地点
    querySearchpxdd(queryString, cb) {
      var restaurants = this.restaurantspxzt;
      console.log("restaurants", restaurants);
      var results = queryString ? restaurants.filter(this.createFilterpxdd(queryString)) : restaurants;
      console.log("results", results);
      // 调用 callback 返回建议列表的数据
      for (var i = 0; i < results.length; i++) {
        for (var j = i + 1; j < results.length; j++) {
          if (results[i].pxdd === results[j].pxdd) {
            results.splice(j, 1);
            j--;
          }
        }
      }
      cb(results);
      console.log("cb(results.zw)", results);
    },
    createFilterpxdd(queryString) {
      return (restaurant) => {
        return (restaurant.pxdd.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
      };
    },
    //模糊查询培训单位
    querySearchpxdw(queryString, cb) {
      var restaurants = this.restaurantspxzt;
      console.log("restaurants", restaurants);
      var results = queryString ? restaurants.filter(this.createFilterpxdw(queryString)) : restaurants;
      console.log("results", results);
      // 调用 callback 返回建议列表的数据
      for (var i = 0; i < results.length; i++) {
        for (var j = i + 1; j < results.length; j++) {
          if (results[i].pxdw === results[j].pxdw) {
            results.splice(j, 1);
            j--;
          }
        }
      }
      cb(results);
      console.log("cb(results.zw)", results);
    },
    createFilterpxdw(queryString) {
      return (restaurant) => {
        return (restaurant.pxdw.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
      };
    },
    //模糊查询培训讲师
    querySearchpxjs(queryString, cb) {
      var restaurants = this.restaurantspxzt;
      console.log("restaurants", restaurants);
      var results = queryString ? restaurants.filter(this.createFilterpxjs(queryString)) : restaurants;
      console.log("results", results);
      // 调用 callback 返回建议列表的数据
      for (var i = 0; i < results.length; i++) {
        for (var j = i + 1; j < results.length; j++) {
          if (results[i].pxjs === results[j].pxjs) {
            results.splice(j, 1);
            j--;
          }
        }
      }
      cb(results);
      console.log("cb(results.zw)", results);
    },
    createFilterpxjs(queryString) {
      return (restaurant) => {
        return (restaurant.pxjs.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
      };
    },
    pxztlist() {
      let resList = getPxzt()
      this.restaurantspxzt = resList;
      console.log("this.restaurants", this.restaurantsbm);
      console.log(resList)
    },
    handleRowClick(row, column, event) {
      this.$refs.table1.toggleRowSelection(row);
    },
  },
  watch: {

  }
}
</script>

<style scoped>
.bg_con {
  width: 100%;
}

.daochu {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

/* /deep/.el-radio {
	display: block;
	margin-top: 10px;
	margin-bottom: 10px;
} */

.dabg {
  /* margin-top: 10px; */
  box-shadow: 0px 2px 12px 0px rgba(9, 70, 255, 0.13);
  border-radius: 8px;
  /* padding: 20px 20px; */
  width: 100%;
}

/deep/.mhcx .el-form-item {
  margin-top: 5px;
  margin-bottom: 5px;
}

.xmlb-title {
  line-height: 60px;
  width: 100%;
  padding-left: 10px;
  height: 60px;
  background: url(../../assets/background/bg-02.png) no-repeat left;
  background-size: 100% 100%;
  text-indent: 10px;
  /* margin: 0 20px; */
  color: #0646bf;
  font-weight: 700;
}

.fhsy {
  display: inline-block;
  width: 120px;
  margin-top: 10px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-left: 30px;
  padding-top: 4px;
  float: right;
  background: url(../../assets/icons/zzjg_icon1.png) no-repeat center;
  background-size: 100% 100%;
}

.item_button {
  height: 100%;
  float: left;
  padding-left: 10px;
  line-height: 50px;
}

.select_wrap {
  /* //padding: 5px; */

  .select_wrap_content {
    float: left;
    width: 100%;
    line-height: 50px;
    /* // padding-left: 20px; */
    /* // padding-right: 20px; */
    height: 100%;
    background: rgba(255, 255, 255, 0.7);

    .item_label {
      padding-left: 10px;
      height: 100%;
      float: left;
      line-height: 50px;
      font-size: 1em;
    }
  }
}

.mhcx1 {
  margin-top: 0px;
}

.widthw {
  width: 8vw;
}

.clearfix:after {
  /*伪元素是行内元素 正常浏览器清除浮动方法*/
  content: "";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

/* .cd {
		width: 184px;
	} */

.pxryqd::before {
  content: "";
  position: absolute;
  left: 8px;
  top: 314px;
  width: 5px;
  height: 20px;
  border-radius: 2px;
  background: #409eef;
}

.btn:hover {
  cursor: pointer;
}

/deep/.el-transfer-panel .el-transfer-panel__footer {
  position: relative;
}

/deep/.el-dialog {
  margin-top: 6vh !important;
}
</style>