<template>
  <div id="syMain">
    <div v-show="cxxs" class="syBody">
      <!-- 头部检测结果文字显示以及搜索框start -->
      <div class="syHeader">
          <div class="syHeaderLeft">
            <!-- 检查结果 -->
            <div class="syHeaderfont"  v-if="normalShow">
              <img src="./img/duihao.png" alt="">
              <p>本次检测，未发现问题</p>
            </div>
            <div class="syHeaderfont"  v-if="nonormalShow">
              <img src="./img/tanhao.png" alt="">
              <p>本次检测，发现<span style="color:#E6A23C">{{ problemCount }}</span>项问题</p>
            </div>
          </div>
          <!-- 搜索框 -->
          <div class="syHeaderRight">
              <div class="center-right" @click="onSubmit">
                <img src="./img/search.png" alt="">
              </div>
              <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline" style="float:right; margin-top: 80px;">
                <el-form-item style="font-weight: 700;">
                  <el-select v-model="formInline.name" :popper-append-to-body="false" filterable class="sySearchIput" placeholder="请选择">
                    <el-option-group
                      v-for="group in searchOptions"
                      :key="group.label"
                      :label="group.label">
                      <el-option
                        v-for="item in group.options"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                      </el-option>
                    </el-option-group>
                  </el-select>
                  <!-- <el-autocomplete
                    class="inline-input sySearchIput"
                    v-model="formInline.name"
                    :fetch-suggestions="querySearch"
                    placeholder="请输入内容"
                  ></el-autocomplete> -->
                </el-form-item>
              </el-form>
          </div>
      </div>
      <div style="clear:both"></div>
      <!-- 评分水流图、评分结果、八大模块start -->
      <div class="syWhole">
        <!-- 水流图 -->
        <div :class="[score<60?'syWholeLeftSmall':'syWholeLeft']">
          <div class="header-left" id="echart"></div>
        </div>
        <!-- 检查结果 -->
        <div class="syWholeCenter" >
          <ul v-if="wtgsObj != null">
            <li>
              <div class="dian"></div>
              <p class="title">保密制度</p>
              <span v-if="wtgsObj.proJurisdictionriskCount == 0 && wtgsObj.proJurisdictionOptimizeCount == 0" class="fonts wwt">未发现问题</span>
              <span v-if="wtgsObj.proJurisdictionriskCount != 0" class="fonts fxwt">有 {{wtgsObj.proJurisdictionriskCount}} 个风险</span>
              <span v-if="wtgsObj.proJurisdictionOptimizeCount != 0" class="fonts yhwt">有 {{wtgsObj.proJurisdictionOptimizeCount}} 个可优化项</span>
            </li>
            <li>
              <div class="dian"></div>
              <p class="title">保密组织</p>
              <span v-if="wtgsObj.organizationriskCount == 0 && wtgsObj.organizationOptimizeCount == 0" class="fonts wwt">未发现问题</span>
              <span v-if="wtgsObj.organizationriskCount != 0" class="fonts fxwt">有 {{wtgsObj.organizationriskCount}} 个风险</span>
              <span v-if="wtgsObj.organizationOptimizeCount != 0" class="fonts yhwt">有 {{wtgsObj.organizationOptimizeCount}} 个可优化项</span>
            </li>
            <li>
              <div class="dian"></div>
              <p class="title">涉密人员</p>
              <span v-if="wtgsObj.secretPersonnelriskCount == 0 && wtgsObj.secretPersonnelOptimizeCount == 0" class="fonts wwt">未发现问题</span>
              <span v-if="wtgsObj.secretPersonnelriskCount != 0" class="fonts fxwt">有 {{wtgsObj.secretPersonnelriskCount}} 个风险</span>
              <span v-if="wtgsObj.secretPersonnelOptimizeCount != 0" class="fonts yhwt">有 {{wtgsObj.secretPersonnelOptimizeCount}} 个可优化项</span>
            </li>
            <li>
              <div class="dian"></div>
              <p class="title">教育培训</p>
              <span v-if="wtgsObj.eduTrainriskCount == 0 && wtgsObj.eduTrainOptimizeCount == 0" class="fonts wwt">未发现问题</span>
              <span v-if="wtgsObj.eduTrainriskCount != 0" class="fonts fxwt">有 {{wtgsObj.eduTrainriskCount}} 个风险</span>
              <span v-if="wtgsObj.eduTrainOptimizeCount != 0" class="fonts yhwt">有 {{wtgsObj.eduTrainOptimizeCount}} 个可优化项</span>
            </li>
            <li>
              <div class="dian"></div>
              <p class="title">涉密场所</p>
              <span v-if="wtgsObj.secretSitesriskCount == 0 && wtgsObj.secretSitesOptimizeCount == 0" class="fonts wwt">未发现问题</span>
              <span v-if="wtgsObj.secretSitesriskCount != 0" class="fonts fxwt">有 {{wtgsObj.secretSitesriskCount}} 个风险</span>
              <span v-if="wtgsObj.secretSitesOptimizeCount != 0" class="fonts yhwt">有 {{wtgsObj.secretSitesOptimizeCount}} 个可优化项</span>
            </li>
            <li>
              <div class="dian"></div>
              <p class="title">设备信息</p>
              <span v-if="wtgsObj.deviceriskCount == 0 && wtgsObj.deviceOptimizeCount == 0" class="fonts wwt">未发现问题</span>
              <span v-if="wtgsObj.deviceriskCount != 0" class="fonts fxwt">有 {{wtgsObj.deviceriskCount}} 个风险</span>
              <span v-if="wtgsObj.deviceOptimizeCount != 0" class="fonts yhwt">有 {{wtgsObj.deviceOptimizeCount}} 个可优化项</span>
            </li>
            <li>
              <div class="dian"></div>
              <p class="title">涉密载体</p>
              <span v-if="wtgsObj.carrierriskCount == 0 && wtgsObj.carrierOptimizeCount == 0" class="fonts wwt">未发现问题</span>
              <span v-if="wtgsObj.carrierriskCount != 0" class="fonts fxwt">有 {{wtgsObj.carrierriskCount}} 个风险</span>
              <span v-if="wtgsObj.carrierOptimizeCount != 0" class="fonts yhwt">有 {{wtgsObj.carrierOptimizeCount}} 个可优化项</span>
            </li>
            <li>
              <div class="dian"></div>
              <p class="title">自查自评</p>
              <span v-if="wtgsObj.selfEvaluationriskCount == 0" class="fonts wwt">未发现问题</span>
              <span v-if="wtgsObj.selfEvaluationriskCount != 0" class="fonts fxwt">有 {{wtgsObj.selfEvaluationriskCount}} 个风险</span>
            </li>
            <el-button class="lookResult" type="primary" round  @click="$router.push('/yjgzsy?activeName=second')">查看结果</el-button>
          </ul>
          <div class="syFirstCome" v-else>
            <div class="syHeaderfont"  v-if="firstShow">
              <p>暂无分数，请进行自检评分！</p>
            </div>
            <el-button class="yjjcBtn" type="primary" round @click="goCheck">前往检测</el-button>
          </div>
        </div>
        <!-- 导航项跳转 -->
        <div class="syWholeRight">
          <div class="itemStyle"  @click="toIndex('1')">
            <div class="itemFont">
              <p class="p1">保密制度</p>
              <p class="p2"> <span>{{ bmzd }} </span> 项</p>
            </div>
            <div class="itemImgDiv">
              <img src="./img/baomizhidu.png" alt="">
            </div>
          </div>
          <div class="itemStyle"  @click="toIndex('2s')">
            <div class="itemFont">
              <p class="p1">组织机构</p>
              <p class="p2"> <span>{{ zzjg }} </span> 个</p>
            </div>
            <div class="itemImgDiv">
              <img src="./img/zhjg.png" alt="">
            </div>
          </div>
          <div class="itemStyle"  @click="toIndex('3')">
            <div class="itemFont">
              <p class="p1">涉密岗位</p>
              <p class="p2"> <span>{{ smgw }} </span> 个</p>
            </div>
            <div class="itemImgDiv">
              <img src="./img/smgw.png" alt="">
            </div>
          </div>
          <div class="itemStyle"  @click="toIndex('4')">
            <div class="itemFont">
              <p class="p1">涉密人员</p>
              <p class="p2"> <span>{{ smry }} </span> 人</p>
            </div>
            <div class="itemImgDiv">
              <img src="./img/smry.png" alt="">
            </div>
          </div>
          <div class="itemStyle"  @click="toIndex('5')">
            <div class="itemFont">
              <p class="p1">涉密场所</p>
              <p class="p2"> <span>{{ smcs }} </span> 个</p>
            </div>
            <div class="itemImgDiv">
              <img src="./img/smcs.png" alt="">
            </div>
          </div>
          <div class="itemStyle"  @click="toIndex('6')">
            <div class="itemFont">
              <p class="p1">涉密设备</p>
              <p class="p2"> <span>{{ smsb }} </span>台</p>
            </div>
            <div class="itemImgDiv">
              <img src="./img/smsb.png" alt="">
            </div>
          </div>
          <div class="itemStyle"  @click="toIndex('7')">
            <div class="itemFont">
              <p class="p1">教育培训</p>
              <p class="p2"> <span>{{ jypx }} </span>次</p>
            </div>
            <div class="itemImgDiv">
              <img src="./img/smsb.png" alt="">
            </div>
          </div>
          <div class="itemStyle"  @click="toIndex('8')">
            <div class="itemFont">
              <p class="p1">涉密载体</p>
              <p class="p2"> <span>{{ smzt }} </span>个</p>
            </div>
            <div class="itemImgDiv">
              <img src="./img/smzt.png" alt="">
            </div>
          </div>
        </div>
      </div>
      <!-- 评分水流图、评分结果、八大模块end -->
      <!-- 头部检测结果文字显示以及搜索框end -->
    </div>
    <!-- 搜索结果 -->
    <div v-show="!cxxs">
      <div class="xmlb-title">
        <span style="font-size: 24px; float: left;">搜索结果</span>
        <img @click="gbxs" class="fhsy" src="./img/back.png" alt="">
        <!-- <span style="" @click="gbxs" class="fhsy">返回</span> -->
      </div>
      <div v-for="(item, index) in cxjg" :key="index" class="searchItem">
        <div>{{ item.name }}</div>
        <el-button type="primary" size="small" @click="searchLook(item.path)">查 看</el-button>
      </div>
    </div>
  </div>

</template>
<script>
import {
  getSysj
} from '../../../db/sysjdb'
import { detectZoom } from '../../../utils/detectZoom.js';

export default {
  data() {
    return {
      // 获取个数信息
      bmzd: '',
      zzjg: '',
      smgw: '',
      smry: '',
      smcs: '',
      smsb: '',
      jypx: '',
      smzt: '',
      input: '',
      cxjd: [],
      cxjg: [],
      restaurants: [],
      cxxs: true,
      wtgsObj: {},
      formInline: {
        input: '',
        name: ""
      },
      score: 0,
      problemCount: 0,
      normalShow: false,
      nonormalShow: false,
      firstShow: true,
      searchOptions: [
        {
          label: '热门关键字',
          options: []
        }, 
        {
          label: '搜索关键字',
          options: []
        }
      ],
    }
  },
  computed: {},
  methods: {
    sj() {
      let sj = getSysj()
      this.bmzd = sj.bmzd
      this.zzjg = sj.zzjg
      this.smgw = sj.smgw
      this.smry = sj.smry
      this.smcs = sj.smcs
      this.smsb = sj.smsb
      this.jypx = sj.jypx
      this.smzt = sj.smzt
    },
    gbxs() {
      this.cxxs = true
    },
    // 搜索建议
    querySearch(queryString, cb) {
      var restaurants = this.restaurants;
      var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    createFilter(queryString) {
      return (restaurant) => {
        return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
      };
    },
    loadAll() {
      return [
        { "value": "在岗涉密人员"},
        { "value": "培训清单"},
        { "value": "涉密计算机"},
        { "value": "载体管理"},
        { "value": "新建自查自评"},
      ];
    },
    getLoadEcharts() {
      // var myChart = this.$echarts.init(
      //   document.getElementById("echart")
      // );
      const domMap = document.getElementById("echart");
      // 清除Echarts默认添加的属性
      domMap.removeAttribute("_echarts_instance_");
      let colorRange
      let labelColor
      if(this.score < 60){
        colorRange = ['rgba(243,125,68,1)', 'rgba(243,125,68,0.4)']
        labelColor = 'rgba(239,115,55,1)'
      }else {
        colorRange = ['rgba(53,190,228,1)', 'rgba(26,132,220,0.68)']
        labelColor = '#ffffff'
      }
      let myChart = this.$echarts.init(domMap);
      var option = {
        series: [{
          type: 'liquidFill',
          radius: '90%',
          data: [this.score / 100, this.score / 100 - 0.1],
          color: colorRange,
          label: {
            normal: {
              color: '#ffffff',
              insideColor: 'transparent',
							// position: [120, 70],
              formatter: this.score + "分", //显示文本
              textStyle: {
                fontSize: 50,
                fontWeight: 'bold',
                fontFamily: 'Microsoft YaHei'
              }
            }
          },
          outline: {
            show: true,
            borderDistance: 5,
            itemStyle: {
              borderColor: 'rgba(0,0,0,0)',
              borderWidth: 2
            }
          }
        }]
      };
      myChart.setOption(option);
    },
    onSubmit() {
      let metaArr = []
      this.cxjd.forEach((item)=>{
        if(item.meta.menuList && item.meta.menuList.length > 0){
          metaArr.push(item)
        }
      })
      let res = metaArr.find((val,index,arr)=>{
        return val.meta.menuList.indexOf(this.formInline.name) > -1
      })
      let routeJump
      if(res != undefined){
        routeJump = res.value + "?activeName=" + this.formInline.name
      }else{
        routeJump = this.formInline.name
      }
      if (routeJump=='/bggl') {
        routeJump='/bgglsy'
      }
      this.$router.push(routeJump)
      // this.$router.push(this.formInline.name)
      //  form是查询条件
      // 备份了一下数据
      // let arr = this.cxjd
      // console.log(this.cxjd)
      // // 通过遍历key值来循环处理
      // Object.keys(this.formInline).forEach(e => {
      //   // 调用自己定义好的筛选方法
      //   arr = this.filterFunc(this.formInline[e], e, arr)
      // })
      // this.cxjg = arr
      // if (arr == '') {
      //   this.$message({
      //     message: '未找到相关内容',
      //     type: 'warning'
      //   });
      // } else if (this.formInline.name == '') {
      //   this.$message({
      //     message: '请输入查询内容',
      //     type: 'warning'
      //   });
      // } else {
      //   this.cxxs = false
      // }
    },
    filterFunc(val, target, filterArr) {
      // 参数不存在或为空时，就相当于查询全部
      if (val == undefined || val == '') {
        return filterArr
      }
      return filterArr.filter(p => {
        return p[target].indexOf(val) > -1
        // return bool
      }) // 可以自己加一个.toLowerCase()来兼容一下大小
    },
    // 搜索列表点击查看跳页方法
    // searchLook(path){
    //   this.$router.push('/tzglsy?activeName='+path)
    // },
    // 去一键自检界面
    goCheck(){
      this.$router.push('/yjgzsy?activeName=second')
    },
    // 跳页
    toIndex(i) {
      switch (i) {
          case '1':
              this.$router.push('/tzglsy?activeName=/bmzd')
              break
          case '2s':
              this.$router.push('/tzglsy?activeName=/zzjg')
              break
          case '3':
              this.$router.push('/tzglsy?activeName=/smgwgl')
              break
          case '4':
              this.$router.push('/tzglsy?activeName=/smry')
              break
          case '5':
              this.$router.push('/tzglsy?activeName=/csgl')
              break
          case '6':
              this.$router.push('/tzglsy?activeName=/smjsj')
              break
          case '7':
              this.$router.push('/tzglsy?activeName=/pxqd')
              break
          case '8':
              this.$router.push('/tzglsy?activeName=/smzttz')
              break
          default:
              this.$router.push('/tzglsy')
              break
      }
    }
  },
  watch: {
  },
  created(){
    this.$nextTick(()=>{
      const { screen } = require('electron')
      const primaryDisplay = screen.getPrimaryDisplay()
      const { width } = primaryDisplay.workAreaSize
      const m = detectZoom();
      if(width == 1920){
        document.getElementById("syMain").style.zoom = 1; 
      }
      if(m != 100){
        document.getElementById("syMain").style.zoom = 100 / Number(m) - 0.08; 
      }
      if(width == 1600){
        document.getElementById("syMain").style.zoom = 0.8; 
      }else if(width == 1680){
        document.getElementById("syMain").style.zoom = 0.9; 
      }else if(width == 1440){
        document.getElementById("syMain").style.zoom = 0.75; 
      }else if(width == 1400){
        document.getElementById("syMain").style.zoom = 0.73; 
      }else if(width == 1366){
        document.getElementById("syMain").style.zoom = 0.65; 
      }else if(width == 1360){
        document.getElementById("syMain").style.zoom = 0.64; 
      }else if(width == 1024){
        document.getElementById("syMain").style.zoom = 0.52; 
      }
    })
  },
  mounted() {
    this.restaurants = this.loadAll();
    // 获取最新分数 
    this.score = localStorage.getItem('allCounts') != null ? localStorage.getItem('allCounts') : 0
    this.problemCount = localStorage.getItem('proNumber') != null ? localStorage.getItem('proNumber') : 0
    this.wtgsObj = JSON.parse(localStorage.getItem('checkCount'))
    if (this.score == 100 || this.problemCount == 0) {
      this.normalShow = true
      this.nonormalShow = false
      this.firstShow = false
    }else if(this.score == 0){
      this.firstShow = true
      this.normalShow = false
      this.nonormalShow = false
    }else{
      this.nonormalShow = true
      this.normalShow = false
      this.firstShow = false
    }
    this.sj()
    
    this.$router.options.routes.forEach(item => {
      if (item.path != '/' && item.path != '*') {
        this.cxjd.push({
          path: item.path,
          name: item.meta.name,
          value: item.path,
          label: item.meta.name,
          meta: item.meta
        })
      }
    });
    // 搜索框赋值热门词汇
    this.searchOptions[0].options = [
      {label:'在岗涉密人员', value:'/smry'},
      {label:'培训清单', value:'/pxqd'},
      {label:'涉密计算机', value:'/smjsj'},
      {label:'载体管理', value:'/smzttz'},
      {label:'新建自查自评', value:'/xjzczp'},
    ]
    // 普通词汇
    this.searchOptions[1].options = this.cxjd
    if (this.score < 0) {
      this.score = 0
    }
    this.getLoadEcharts()
  }
};
</script>
<style scoped>
/* 新版本界面 */
.wwt {
  color: green;
}
.fxwt {
  color: orange;
}
.yhwt {
  color: orange;
}
#syMain {
  height: calc(100% + 48px);
}
ul,li{
  list-style:none
}
.syBody {
  width: 1700px;
  margin: 0 auto;
  /* height: 464px; */
  /* margin-top: 20px; */
  display: flex;
  flex-wrap: wrap;
}
.syHeader {
  width: 100%;
  height: 180px;
  /* overflow: hidden; */
}
.syHeader .syHeaderLeft{
  padding-top: 89px;
  width: calc(50% - 100px);
  padding-left: 100px;
  float: left;
}
.syHeaderfont {
  overflow: hidden;
}
.syHeaderfont img{
  float: left;
  width: 40px;
  height: 40px;
} 
.syHeaderfont  p{
  float: left;
  /* width: 280px; */
  height: 41px;
  font-family: 'SourceHanSansSCziti';
  font-size: 28px;
  color: #333333;
  letter-spacing: 0;
  font-weight: 400;
  margin-left: 20px;
  margin-top: 0px;
}
.syHeader .syHeaderRight{
  width: 50%;
  float: right;
  height: 100%;
}
>>>.el-input__inner {
  border: 0px;
  background: none;
  height: 50px!important;
  font-size: 16px;
  font-family: 'SourceHanSansSCziti';
}
>>>.el-form--inline .el-form-item{
    margin-right: 0px;
}
.sySearchIput {
  width: 409px;
  height: 48px;
  background: #FFFFFF;
  border: 1px solid rgba(220,220,220,1);
  border-radius: 100px 0px 0px 100px;
}
.center-right {
  width: 79px;
  height: 50px;
  background-image: linear-gradient(67deg, #1668C5 0%, #1964DD 100%);
  float: right;
  /* margin-right: 59px; */
  margin-top: 80px;
  border-radius: 0px 100px 100px 0px;
  cursor: pointer;
}
.center-right img {
  position: relative;
    top: 14px;
    left: 25px;
}
/* 整体 */
.syWhole {
  width: 100%;
  overflow: hidden;
  height: 620px;
}
.syWhole .syWholeLeft{
  width: 402px;
  height: 100%;
  float: left;
  background-image: url(./img/yuan.png);
  background-repeat: no-repeat;
  background-position-x: 35px;
  background-position-y: 85px;
}
.syWholeLeftSmall {
  width: 402px;
  height: 100%;
  float: left;
  background-image: url(./img/60xia.png);
  background-repeat: no-repeat;
  background-position-x: 35px;
  background-position-y: 85px;
}
.header-left {
  width: 274px;
  height: 274px;
  padding-left: 70px;
  padding-top: 122px;
}
.syWholeCenter {
  width: 574px;
  height: 100%;
  float: left;
  padding-left: 90px;
  padding-top: 17px;
}
.syWholeCenter ul {
  width: 100%;
  height: 100%;
} 
.syWholeCenter ul .lookResult{
  width: 160px;
  height: 48px;
  background: #1766D1;
  border-radius: 24px;
  margin-top: 20px;
  font-size: 20px;
  color: #FFFFFF;
  letter-spacing: 0;
  font-weight: 400;
  font-family: 'SourceHanSansSCziti';
}

.syWholeCenter ul li {
  overflow: hidden ;
  /* margin-top: 20px; */
  margin-top: 12px;
}
.syWholeCenter ul li .dian {
  width: 5px;
  height: 5px;
  background: #3790E3;
  float: left;
  margin-top: 13px;
}
.syWholeCenter ul li .title {
  /* width: 96px; */
  height: 36px;
  font-family: 'SourceHanSansSCziti';
  font-size: 24px;
  color: #333333;
  letter-spacing: 0;
  /* font-weight: 600; */
  float: left;
  margin-left: 17px;
}
.syWholeCenter ul li .fonts {
  /* width: 242px; */
  height: 33px;
  font-family: 'SourceHanSansSCziti';
  font-size: 22px;
  /* color: #999999; */
  letter-spacing: 0;
  font-weight: 400;
  float: left;
  margin-left: 54px;
  line-height: 32px;
  opacity: 0.8;
}
.syWholeRight {
  width: calc(100% - 1066px);
  height: 100%;
  float: left;
  /* display: flex;
  flex-wrap: wrap; */
}
.itemStyle {
  width: 264px;
  height: 84px;
  background-image: linear-gradient(90deg, #F0F5FF 4%, #FFFFFF 100%);
  box-shadow: 0px 2px 20px 0px rgba(19,88,207,0.13);
  border-radius: 4px;
  margin-left: 30px;
  padding: 18px;
  overflow: hidden;
  cursor: pointer;
  float: left;
  margin-top: 20px;
}
.itemStyle:nth-child(2n+1) {
  margin-left: 0px;
}
.itemStyle:nth-child(1),.itemStyle:nth-child(2) {
  margin-top: 0px;
}
.itemFont {
  width: 120px;
  height: 100%;
  float: left;
}
.itemFont .p1 {
  height: 29px;
  font-family: 'SourceHanSansSCziti';
  font-size: 20px;
  color: #080808;
  letter-spacing: 0;
  font-weight: 400;
}

.itemFont .p2 {
  /* height: 29px; */
  font-family: 'SourceHanSansSCziti';
  font-size: 20px;
  color: #666666;
  letter-spacing: 0;
  /* text-align: center; */
  font-weight: 400;
}
.itemFont .p2 span{
  height: 62px;
  font-family: 'SourceHanSansSCziti';
  font-size: 42px;
  color: #080808;
  letter-spacing: 0;
  /* text-align: center; */
  font-weight: 500;
}
.itemImgDiv {
  float: right;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: rgba(6,126,254,0.13);
  margin-bottom: 10px;
  margin-top: 14px;
}
.searchItem {
  width: 500px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  margin-top:10px;
  align-items: center;
}
.fhsy {
  width: 50px;
  float: right;
  cursor: pointer;
}
.yjjcBtn {
    /* float: right;
    margin-right: 20px; */
    width: 300px;
    height: 75px;
    background-image: linear-gradient(90deg, rgb(40, 220, 134) 0%, rgb(33, 165, 102) 56%);
    font-size: 30px;
    color: #FFFFFF;
    letter-spacing: 0;
    text-align: center;
    /* line-height: 18px; */
    font-weight: 500;
    border: none;
    border-radius: 50px;
}
.syFirstCome {
  margin-top: 125px;
  margin-left: -80px;
}
.syFirstCome .syHeaderfont p {
  line-height: 41px;
    /* margin-left: 20px; */
    margin-bottom: 30px;
    font-size: 40px;
}
.xmlb-title {
  overflow: hidden;
}
</style>