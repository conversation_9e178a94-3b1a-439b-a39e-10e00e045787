<template>
  <div style="width: 100%;height: 100%;">
    <!---->
    <div class="mhcx">
      <el-form :inline="true" size="medium" class="demo-form-inline" style="float:left">
        <!-- <el-form-item style="float: left;">
          <div>当前审查任务：{{dialogObj.rwmc}}</div>
        </el-form-item> -->
        <el-form-item style="float: left;">
          <div>姓名：{{dialogObj.xm}}</div>
        </el-form-item>
        <el-form-item style="float: left;">
          <div>部门：{{dialogObj.bm}}</div>
        </el-form-item>
        <el-form-item style="float: left;">
          <div>职务：{{dialogObj.zw}}</div>
        </el-form-item>
      </el-form>
      <!---->
      <el-form :inline="true" size="medium" class="demo-form-inline" style="float:right">
        <el-form-item style="float: right;">
          <el-button type="primary" size="medium" icon="el-icon-document-add" @click="submit">
            提交
          </el-button>
        </el-form-item>
        <el-form-item style="float: right;">
          <el-button type="warning" size="medium" icon="el-icon-document" @click="save">
            临时保存
          </el-button>
        </el-form-item>
      </el-form>
      <div style="clear: both;"></div>
    </div>
    <!---->
    <el-table :data="showDxList" :span-method="objectSpanMethod" border height="calc(100% - 58px - 5px)">
      <el-table-column label="自查类" width="150">
        <template slot-scope="scope">
          <div>
            <span :id="showDxList[scope.$index].dxMdIndex"></span>{{showDxList[scope.$index].dxmc}}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="自查内容">
        <template slot-scope="scope">
          <div>
            <span :id="showDxList[scope.$index].mdIndex"></span>{{showDxList[scope.$index].nr}}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="是否符合要求" width="150">
        <template slot-scope="scope">
          <el-radio-group v-model="showDxList[scope.$index].sffhyq">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </template>
      </el-table-column>
      <el-table-column label="备注说明">
        <template slot-scope="scope">
          <el-input type="textarea" v-model.trim="showDxList[scope.$index].bzsm" :rows="3"></el-input>
        </template>
      </el-table-column>
    </el-table>
    <!--锚点索引-->
    <div class="md-menu" @mouseover="mouseoverMdMenu" @mouseout="mouseoutMenu">
      <div class="md-left"></div>
      <transition name="el-fade-in-linear">
        <div v-show="showMdmenu" class="md-right">
          <div class="md-article">
            <el-timeline :reverse="reverse">
              <el-timeline-item v-for="(item, index) in activities" :key="index">
                <div>
                  <h4>{{ item.dxmc }}</h4>
                  <div v-for="(xxItem, xxIndex) in item.children" :key="xxIndex" class="md-article-article">
                    <span v-if="xxItem.xxmc" style="color:#409EFF;">【{{ xxItem.xxmc }}】</span>
                    <a :href="xxItem.href">
                      <span>{{ xxItem.nr }}</span>
                    </a>
                    <span v-if="xxItem.ykf" style="color:#F56C6C;">扣{{ xxItem.ykf }}分<span class="el-icon-caret-right"></span></span>
                  </div>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </div>
      </transition>
      <div class="md-right-margin-div"></div>
    </div>
    <!---->
  </div>
</template>

<script>

import {
  // 通过抽查的人员流水ID获取抽查的人员信息
  selectCcdryxxByCcdryid,
  // 获取字典
  getRyzcxxjlZD,
  //
  insertUpdateRyjlByCcdnryid,
  //
  selectRypfjlListByCcdryid,
  //
  updateCcdryById
} from '../../../../db/zczpdb'

import { getZczpIdsObj } from '../../../../utils/windowLocation'

import { writeOptionsLog } from '../../../../utils/logUtils'

export default {
  data () {
    return {
      dialogObj: {
        // rwid: 'D66215EC-1B9A-408A-BEE8-7A9396165EA7',
        // ccdryid: 'F9E1E031-3649-42C4-879E-5297B14B36D6'
      },
      //页面实际操作的评分数据[dx:{scnr:[]}]
      showDxList: [],
      //单元格合并规则
      spanArr: [],
      // 时间线排序方向
      reverse: true,
      // 锚点菜单集合
      activities: [],
      // 锚点菜单显隐
      showMdmenu: false
    }
  },
  computed: {},
  components: {
  },
  methods: {
    //人员自查入库
    ryzcRK (dxXxList, zt) {
      //插入或更新人员评分记录表
      let bool = insertUpdateRyjlByCcdnryid(dxXxList, this.dialogObj.ccdryid)
      /*=====部门详细自查记录入库完成=====*/
      // 更新 抽查的内设机构表(ccdnsjg_list) 表状态
      let params = {
        ccdryid: this.dialogObj.ccdryid,
        zt: zt
      }
      bool = updateCcdryById(params)
      if (bool) {
        let logYmngnmc = '人员' + this.dialogObj.xm + '登记信息'
        if (zt == 1) {
          logYmngnmc += '临时保存'
        }
        if (zt == 2) {
          logYmngnmc += '保存完成'
        }
        // 写入操作日志
        writeOptionsLog('yybs-ccdryDj', logYmngnmc, this.dialogObj)
        // 更新数据
        this.getRyzcxxjl()
        // 更新vuex状态机关闭tag值，让tags组件能够监测到需要关闭的tag
        this.$router.app.$options.store.commit('changeCloseTag', { path: this.$route.path })
        //
        this.$message.success('人员自查自评结果登记成功')
      }
    },
    /**
     * 获取人员详细自查记录
     */
    getRyzcxxjl () {
      //
      const ryzcxxjlList = selectRypfjlListByCcdryid(this.dialogObj.ccdryid)
      ryzcxxjlList.forEach(element => {
        if (element.sffhyq === undefined) {
          element.sffhyq = true
        }
      })
      this.spanArr = this.getSpanArr(ryzcxxjlList)
      //
      this.showDxList = ryzcxxjlList
    },
    submit () {
      this.ryzcRK(this.showDxList, 2)
    },
    save () {
      this.ryzcRK(this.showDxList, 1)
    },
    returnSy () {
      this.$router.go(-1)
    },
    getCcdryxxByCcdryid () {
      let ccdryxx = selectCcdryxxByCcdryid(this.dialogObj)
      console.log('ccdryxx', ccdryxx)
      Object.assign(this.dialogObj, ccdryxx)
      this.dialogObj = JSON.parse(JSON.stringify(this.dialogObj))
    },
    //----------------------------用来返回this.spanArr数组的，定义每一行的 rowspan-----------
    getSpanArr (list) {
      let spanArr = []
      for (var i = 0; i < list.length; i++) {
        if (i === 0) {
          spanArr.push(1)
          this.pos = 0
        } else {
          // 判断当前元素与上一个元素是否相同
          if (list[i].dxid == list[i - 1].dxid) {
            spanArr[this.pos] += 1
            spanArr.push(0)
          } else {
            spanArr.push(1)
            this.pos = i
          }
        }
      }
      return spanArr
    },
    objectSpanMethod ({
      row,
      column,
      rowIndex,
      columnIndex
    }) {
      if (columnIndex === 0) {
        //
        const _row = this.spanArr[row.nrid - 1]
        return {
          rowspan: _row,
          colspan: 1
        }
      }
    },
    /**
     * 获取部门详细自查记录字典
     */
    getZD () {
      console.log("getZD")
      //
      const zdList = getRyzcxxjlZD()
      console.log(zdList)
      zdList.forEach((nr) => {
        if (nr.sffhyq === undefined) {
          nr.sffhyq = true
        }
      });
      this.spanArr = this.getSpanArr(zdList)
      //
      return zdList
    },
    // 锚点菜单鼠标移入事件
    mouseoverMdMenu () {
      this.showMdmenu = true
    },
    // 锚点菜单鼠标移出事件
    mouseoutMenu () {
      this.showMdmenu = false
    }
  },
  watch: {
    '$route': {
      handler (newVal, oldVal) {
        console.log('route changed', newVal)
      },
      deep: true
    },
    showDxList: {
      handler (newVal, oldVal) {
        ///////
        // 清空锚点，防重复
        this.activities = []
        // 大项ID数组，用以对数据进行分组
        let dxMdIdArr = []
        ///////////
        //
        newVal.forEach((nr, nrIndex) => {
          // nr.dxMdIndex = 'dxMd' + nr.dxid
          //
          nr.mdIndex = 'md' + nrIndex
          if (!nr.sffhyq) {
            nr.mdIndex = 'md' + nrIndex
            //
            if (dxMdIdArr.indexOf(nr.dxid) == -1) {
              dxMdIdArr.push(nr.dxid)
            }
            if (dxMdIdArr.indexOf(nr.dxid) != -1) {
              if (!this.activities[dxMdIdArr.indexOf(nr.dxid)]) {
                this.activities.push({
                  dxmc: nr.dxmc,
                  children: []
                })
              }
              this.activities[dxMdIdArr.indexOf(nr.dxid)].children.push({
                href: '#' + nr.mdIndex,
                nr: nr.nr,
                ykf: nr.ykf
              })
            }
          }
        })
      },
      deep: true
    },
  },
  mounted () {
    // let params = this.$route.query
    let params = getZczpIdsObj()
    if (params && Object.keys(params).length > 0) {
      console.log('抽查的人员登记', params)
      this.dialogObj.rwid = params.rwid
      this.dialogObj.ccdryid = params.ccdryid
      // 获取抽查的人员信息
      this.getCcdryxxByCcdryid()
      //
      this.getRyzcxxjl()
      return
    }
    this.$message.warning('未能正确获取抽查的人员ID，请关闭页面重新进入')
    // // 获取字典
    // this.showDxList = this.getZD()
  }
}
</script>

<style scoped></style>
