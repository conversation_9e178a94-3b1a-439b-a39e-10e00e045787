{"options": [{"type": 1, "option": {"fieldList": [{"fieldName": "a1", "fieldDefaultValue": "1111"}, {"fieldName": "a2", "fieldDefaultValue": "222"}], "tableName": "aaa"}}, {"type": 2, "option": ["dwzcdx_list"]}, {"type": 3, "option": [{"tableName": "dwzcxx_list", "tableNameNew": "eeee"}, {"tableName": "dwzcnr_list"}, {"tableName": "bmzcdx_list"}, {"tableName": "bmzcnr_list"}, {"tableName": "ryzcdx_list"}, {"tableName": "ryzcnr_list"}, {"tableName": "scrw_list"}, {"tableName": "dwpfjl_list"}, {"tableName": "ccdnsjg_list"}, {"tableName": "nsjgpfjl_list"}, {"tableName": "ccdry_list"}, {"tableName": "rypfjl_list"}, {"tableName": "zw_list"}, {"tableName": "yhzw_list"}, {"tableName": "dwxx_List"}, {"tableName": "dmb_dwlx"}, {"tableName": "dmb_ssly"}, {"tableName": "dmb_sscc"}, {"tableName": "yh"}, {"tableName": "Bmzd_list"}, {"tableName": "dwgl_list"}, {"tableName": "Smgwgl_list"}, {"tableName": "Smry_list"}, {"tableName": "Csbg_list"}, {"tableName": "Csgl_list"}, {"tableName": "Lglz_list"}, {"tableName": "Gwbg_list"}, {"tableName": "Ryxz_list"}, {"tableName": "dwxxzcjl_list"}, {"tableName": "ccbm_list"}, {"tableName": "bmxxzcjl_list"}, {"tableName": "zcry_list"}, {"tableName": "ryxxzcjl_list"}, {"tableName": "scls_list"}, {"tableName": "Nrgl_list"}, {"tableName": "Rygl_list"}, {"tableName": "Pxqd_list"}, {"tableName": "Smjsj_list"}, {"tableName": "Fsmjsj_list"}, {"tableName": "Smwlsb_list"}, {"tableName": "Fmwlsb_list"}, {"tableName": "Smydccjz_list"}, {"tableName": "Smbgzdhsb_list"}, {"tableName": "Fsmbgzdhsb_list"}, {"tableName": "Aqcp_list"}, {"tableName": "Smzttz_list"}, {"tableName": "dmzrr_list"}, {"tableName": "jgxx_list"}, {"tableName": "jgyh_list"}, {"tableName": "zzjg_list"}, {"tableName": "smdj_xz"}, {"tableName": "gwqdyj_xz"}, {"tableName": "jbzc_xz"}, {"tableName": "zgxl_xz"}, {"tableName": "sflx_xz"}, {"tableName": "yrxx_xz"}, {"tableName": "pxlx_xz"}, {"tableName": "lzlglx_xz"}, {"tableName": "sbmj_xz"}, {"tableName": "sblx_xz"}, {"tableName": "sbsyqk_xz"}, {"tableName": "sbbgzdhlx_xz"}, {"tableName": "sbsmwllx_xz"}, {"tableName": "aqcplx_xz"}, {"tableName": "ztlx_xz"}, {"tableName": "ztscyy_xz"}, {"tableName": "ztzt_xz"}, {"tableName": "dmqxlx_xz"}, {"tableName": "dmlb_xz"}, {"tableName": "dmsq_list"}, {"tableName": "dmpx_list"}, {"tableName": "dmmj_xz"}, {"tableName": "dmqsxqdqk_list"}, {"tableName": "fsbbgzdhlx_xz"}, {"tableName": "dbgz_status"}, {"tableName": "Gjmmsx_list"}, {"tableName": "GjmmsxLeft_list"}, {"tableName": "aaa"}]}, {"type": 4, "option": {"fieldList": [{"fieldName": "nr111111", "fieldDefaultValue": "<PERSON><PERSON><PERSON>"}], "tableName": "dwzcnr_list"}}, {"type": 5, "option": {"fieldList": [{"fieldName": "xxid"}, {"fieldName": "nr"}, {"fieldName": "fz"}, {"fieldName": "kffs"}, {"fieldName": "zgkffz"}, {"fieldName": "zdkffz"}, {"fieldName": "kfzf"}, {"fieldName": "kfbz"}, {"fieldName": "nr111111"}], "deleteFieldList": ["nrid"], "tableName": "dwzcnr_list"}}, {"type": 6, "option": {"fieldList": [{"fieldName": "xxid", "fieldNameNew": "wwww"}, {"fieldName": "nr"}, {"fieldName": "fz"}, {"fieldName": "kffs"}, {"fieldName": "zgkffz"}, {"fieldName": "zdkffz"}, {"fieldName": "kfzf"}, {"fieldName": "kfbz"}, {"fieldName": "nr111111"}], "tableName": "dwzcnr_list"}}]}