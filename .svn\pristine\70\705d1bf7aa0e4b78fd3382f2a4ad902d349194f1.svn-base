import db from './adapter/zczpHistoryAdaptor'
// 获取所有的保密制度
export const getDbgzStatus = () => {
	let resList = db.get('dbgz_status').filter(function(item) {
			return item
	}).cloneDeep().value()
	return resList
}
// 修改待办工作当年状态
export const reviseDbgzStatus = (params) => {
	let id = params.id
	if (!id || id == '') {
		return
	}
	params.gxsj = new Date().getTime()
	params.dqnf = new Date().getFullYear().toString()
	// 全字段更新方法(传进来的字段都会更新)
	db.read().get('dbgz_status').find({ id }).assign(params).write()
}
// 历史台账-涉密岗位
export const addSmgwglHistory = (params) => {
	params.forEach(element => {
		let nowTime = parseInt(new Date().getTime()  / 1000)
		element.tzsj = new Date().getFullYear().toString()
		element.scsj = nowTime
		db.read().get('lsSmgw_list').push(element).write()
	});
}
// 历史台账-涉密人员汇总
export const addSmryHistory = (params) => {
	params.forEach(element => {
		let nowTime = parseInt(new Date().getTime()  / 1000)
		element.tzsj = new Date().getFullYear().toString()
		element.scsj = nowTime
		db.read().get('lsSmry_list').push(element).write()
	});
}
// 历史台账-人员新增汇总
export const addRyxzhzHistory = (params) => {
	params.forEach(element => {
		let nowTime = parseInt(new Date().getTime()  / 1000)
		element.tzsj = new Date().getFullYear().toString()
		element.scsj = nowTime
		db.read().get('lsryxzhz_list').push(element).write()
	});
}
// 历史台账-岗位变更汇总
export const addRymjbghzHistory = (params) => {
	params.forEach(element => {
		let nowTime = parseInt(new Date().getTime()  / 1000)
		element.tzsj = new Date().getFullYear().toString()
		element.scsj = nowTime
		db.read().get('lsrymjbg_list').push(element).write()
	});
}
// 历史台账-离职离岗汇总
export const addLglzHistory = (params) => {
	params.forEach(element => {
		let nowTime = parseInt(new Date().getTime()  / 1000)
		element.tzsj = new Date().getFullYear().toString()
		element.scsj = nowTime
		db.read().get('lslzlg_list').push(element).write()
	});
}
// 历史台账-涉密场所
export const addSmcsglHistory = (params) => {
	params.forEach(element => {
		let nowTime = parseInt(new Date().getTime()  / 1000)
		element.tzsj = new Date().getFullYear().toString()
		element.scsj = nowTime
		console.log(element.tzsj)
		console.log(typeof(element.tzsj))
		db.read().get('lssmcs_list').push(element).write()
	});
}
// 历史台账-场所变更
export const addCsbgHistory = (params) => {
	params.forEach(element => {
		let nowTime = parseInt(new Date().getTime()  / 1000)
		element.tzsj = new Date().getFullYear().toString()
		element.scsj = nowTime
		db.read().get('lscsbg_list').push(element).write()
	});
}
// 历史台账-涉密计算机
export const addSmjsjHistory = (params) => {
	params.forEach(element => {
		let nowTime = parseInt(new Date().getTime()  / 1000)
		element.tzsj = new Date().getFullYear().toString()
		element.scsj = nowTime
		db.read().get('lssmjsj_list').push(element).write()
	});
}
// 历史台账-非涉密计算机
export const addFsmjsjHistory = (params) => {
	params.forEach(element => {
		let nowTime = parseInt(new Date().getTime()  / 1000)
		element.tzsj = new Date().getFullYear().toString()
		element.scsj = nowTime
		db.read().get('lsfsmjsj_list').push(element).write()
	});
}
// 历史台账-涉密移动存储介质
export const addSmydccjzHistory = (params) => {
	params.forEach(element => {
		let nowTime = parseInt(new Date().getTime()  / 1000)
		element.tzsj = new Date().getFullYear().toString()
		element.scsj = nowTime
		db.read().get('lsSmydccjz_list').push(element).write()
	});
}
// 历史台账-涉密办公自动化设备
export const addBgzdhsbHistory = (params) => {
	params.forEach(element => {
		let nowTime = parseInt(new Date().getTime()  / 1000)
		element.tzsj = new Date().getFullYear().toString()
		element.scsj = nowTime
		db.read().get('lsBgzdhsb_list').push(element).write()
	});
}
// 历史台账-非涉密办公自动化设备
export const addfBgzdhsbHistory = (params) => {
	params.forEach(element => {
		let nowTime = parseInt(new Date().getTime()  / 1000)
		element.tzsj = new Date().getFullYear().toString()
		element.scsj = nowTime
		db.read().get('lsfBgzdhsb_list').push(element).write()
	});
}
// 历史台账-涉密网络设备台账
export const addSmwlsbHistory = (params) => {
	params.forEach(element => {
		let nowTime = parseInt(new Date().getTime()  / 1000)
		element.tzsj = new Date().getFullYear().toString()
		element.scsj = nowTime
		db.read().get('lssmwlsb_list').push(element).write()
	});
}
// 历史台账-非涉密网络设备台账
export const addFsmwlsbHistory = (params) => {
	params.forEach(element => {
		let nowTime = parseInt(new Date().getTime()  / 1000)
		element.tzsj = new Date().getFullYear().toString()
		element.scsj = nowTime
		db.read().get('lsfsmwlsb_list').push(element).write()
	});
}
// 历史台账-安全产品台账
export const addAqcpHistory = (params) => {
	params.forEach(element => {
		let nowTime = parseInt(new Date().getTime()  / 1000)
		element.tzsj = new Date().getFullYear().toString()
		element.scsj = nowTime
		db.read().get('lsaqcp_list').push(element).write()
	});
}
// 历史台账-涉密载体
export const addSmztHistory = (params) => {
	params.forEach(element => {
		let nowTime = parseInt(new Date().getTime()  / 1000)
		element.tzsj = new Date().getFullYear().toString()
		element.scsj = nowTime
		db.read().get('lssmzt_list').push(element).write()
	});
}
// 历史台账-定密责任人
export const addDmzrrHistory = (params) => {
	params.forEach(element => {
		let nowTime = parseInt(new Date().getTime()  / 1000)
		element.tzsj = new Date().getFullYear().toString()
		element.scsj = nowTime
		db.read().get('lsdmzrrt_list').push(element).write()
	});
}
// 历史台账-定密授权
export const addDmsqHistory = (params) => {
	params.forEach(element => {
		let nowTime = parseInt(new Date().getTime()  / 1000)
		element.tzsj = new Date().getFullYear().toString()
		element.scsj = nowTime
		db.read().get('lsdmsq_list').push(element).write()
	});
}
// 历史台账-国家秘密事项
export const addGjmmsxHistory = (params) => {
	params.forEach(element => {
		let nowTime = parseInt(new Date().getTime()  / 1000)
		element.tzsj = new Date().getFullYear().toString()
		element.scsj = nowTime
		db.read().get('lsgjmmsx_list').push(element).write()
	});
}
// 历史台账-定密培训
export const addDmpxHistory = (params) => {
	params.forEach(element => {
		let nowTime = parseInt(new Date().getTime()  / 1000)
		element.tzsj = new Date().getFullYear().toString()
		element.scsj = nowTime
		db.read().get('lsdmpx_list').push(element).write()
	});
}
// 历史台账-定密情况年度统计
export const addDmqkndtjHistory = (params) => {
	params.forEach(element => {
		let nowTime = parseInt(new Date().getTime()  / 1000)
		element.tzsj = new Date().getFullYear().toString()
		element.scsj = nowTime
		db.read().get('lsdmqkndtj_list').push(element).write()
	});
}
// 历史台账-不明确事项确定情况
export const addBmqsxqdqkListHistory = (params) => {
	params.forEach(element => {
		let nowTime = parseInt(new Date().getTime()  / 1000)
		element.tzsj = new Date().getFullYear().toString()
		element.scsj = nowTime
		db.read().get('lsbmqsxqdqk_list').push(element).write()
	});
}
// 历史台账-政府采购项目情况
export const addZfcgxmqkListHistory = (params) => {
	params.forEach(element => {
		let nowTime = parseInt(new Date().getTime()  / 1000)
		element.tzsj = new Date().getFullYear().toString()
		element.scsj = nowTime
		db.read().get('lszfcgxmqk_list').push(element).write()
	});
}

// 历史台账查询-获取当前年份已生成涉密岗位历史台账
export const getSmgwHistory = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('lsSmgw_list').filter(function (item) {
			return item.tzsj == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('lsSmgw_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 重新生成-删除当前年生成的所有历史数据
export const deleteLsSmgw = (params) => {
	db.read().get("lsSmgw_list").remove(params).write();
};
// 历史台账查询-获取当前年份已生成涉密人员历史台账
export const getSmryHistory = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('lsSmry_list').filter(function (item) {
			return item.tzsj == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('lsSmry_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 重新生成-删除当前年生成的所有历史数据
export const deleteLsSmry = (params) => {
	db.read().get("lsSmry_list").remove(params).write();
};
// 历史台账查询-获取当前年份已生成人员新增汇总历史台账
export const getRyxzhzHistory = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('lsryxzhz_list').filter(function (item) {
			return item.tzsj == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('lsryxzhz_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 重新生成-删除当前年生成的所有历史数据
export const deleteLsRyxzhz = (params) => {
	db.read().get("lsryxzhz_list").remove(params).write();
};
// 历史台账查询-获取当前年份已生成人员密级变更汇总历史台账
export const getRymjbghzHistory = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('lsrymjbg_list').filter(function (item) {
			return item.tzsj == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('lsrymjbg_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 重新生成-删除当前年生成的所有历史数据
export const deleteLsRymjbghz = (params) => {
	db.read().get("lsrymjbg_list").remove(params).write();
};
// 历史台账查询-获取当前年份已生成离岗离职历史台账
export const getLglzHistory = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('lslzlg_list').filter(function (item) {
			return item.tzsj == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('lslzlg_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 重新生成-删除当前年生成的所有历史数据
export const deleteLsLglz = (params) => {
	db.read().get("lslzlg_list").remove(params).write();
};
// 历史台账查询-获取当前年份已生成涉密场所历史台账
export const getSmcsglHistory = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('lssmcs_list').filter(function (item) {
			return item.tzsj == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('lssmcs_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 重新生成-删除当前年生成的所有历史数据
export const deleteLsCsgl = (params) => {
	db.read().get("lssmcs_list").remove(params).write();
};
// 历史台账查询-获取当前年份已生成场所变更历史台账
export const getSmCsbgHistory = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('lscsbg_list').filter(function (item) {
			return item.tzsj == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('lscsbg_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 重新生成-删除当前年生成的所有历史数据
export const deleteLsCsbg = (params) => {
	db.read().get("lscsbg_list").remove(params).write();
};
// 历史台账查询-获取当前年份已生成涉密计算机历史台账
export const getSmjsjHistory = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('lssmjsj_list').filter(function (item) {
			return item.tzsj == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('lssmjsj_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 重新生成-删除当前年生成的所有历史数据
export const deleteLsSmjsj = (params) => {
	db.read().get("lssmjsj_list").remove(params).write();
};
// 历史台账查询-获取当前年份已生成非涉密计算机历史台账
export const getFsmjsjHistory = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('lsfsmjsj_list').filter(function (item) {
			return item.tzsj == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('lsfsmjsj_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 重新生成-删除当前年生成的所有历史数据
export const deleteLsFsmjsj = (params) => {
	db.read().get("lsfsmjsj_list").remove(params).write();
};
// 历史台账查询-获取当前年份已生成涉密移动存储介质历史台账
export const getSmydccjzHistory = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('lsSmydccjz_list').filter(function (item) {
			return item.tzsj == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('lsSmydccjz_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 重新生成-删除当前年生成的所有历史数据
export const deleteLsSmydccjz = (params) => {
	db.read().get("lsSmydccjz_list").remove(params).write();
};
// 历史台账查询-获取当前年份已生成涉密办公自动化设备历史台账
export const getSmBgzdhsbHistory = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('lsBgzdhsb_list').filter(function (item) {
			return item.tzsj == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('lsBgzdhsb_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 重新生成-删除当前年生成的所有历史数据
export const deleteLsSmBgzdhsb = (params) => {
	db.read().get("lsBgzdhsb_list").remove(params).write();
};
// 历史台账查询-获取当前年份已生成非涉密办公自动化设备历史台账
export const getFsmBgzdhsbHistory = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('lsfBgzdhsb_list').filter(function (item) {
			return item.tzsj == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('lsfBgzdhsb_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 重新生成-删除当前年生成的所有历史数据
export const deleteLsFsmBgzdhsb = (params) => {
	db.read().get("lsfBgzdhsb_list").remove(params).write();
};
// 历史台账查询-获取当前年份已生成涉密网络设备历史台账
export const getSmwlsbHistory = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('lssmwlsb_list').filter(function (item) {
			return item.tzsj == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('lssmwlsb_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 重新生成-删除当前年生成的所有历史数据
export const deleteLsSmwlsb = (params) => {
	db.read().get("lssmwlsb_list").remove(params).write();
};
// 历史台账查询-获取当前年份已生成非涉密网络设备历史台账
export const getFsmwlsbHistory = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('lsfsmwlsb_list').filter(function (item) {
			return item.tzsj == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('lsfsmwlsb_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 重新生成-删除当前年生成的所有历史数据
export const deleteLsFsmwlsb = (params) => {
	db.read().get("lsfsmwlsb_list").remove(params).write();
};
// 历史台账查询-获取当前年份已生成安全产品历史台账
export const getAqcpHistory = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('lsaqcp_list').filter(function (item) {
			return item.tzsj == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('lsaqcp_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 重新生成-删除当前年生成的所有历史数据
export const deleteLsAqcp = (params) => {
	db.read().get("lsaqcp_list").remove(params).write();
};
// 历史台账查询-获取当前年份已生成涉密载体历史台账
export const getSmzttzHistory = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('lssmzt_list').filter(function (item) {
			return item.tzsj == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('lssmzt_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 重新生成-删除当前年生成的所有历史数据
export const deleteLsSmzttz = (params) => {
	db.read().get("lssmzt_list").remove(params).write();
};
// 历史台账查询-获取当前年份已生成定密责任人体历史台账
export const getDmzrrHistory = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('lsdmzrrt_list').filter(function (item) {
			return item.tzsj == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('lsdmzrrt_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 重新生成-删除当前年生成的所有历史数据
export const deleteLsDmzrr = (params) => {
	db.read().get("lsdmzrrt_list").remove(params).write();
};
// 历史台账查询-获取当前年份已生成定密授权历史台账
export const getDmsqHistory = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('lsdmsq_list').filter(function (item) {
			return item.tzsj == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('lsdmsq_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 重新生成-删除当前年生成的所有历史数据
export const deleteLsDmsq = (params) => {
	db.read().get("lsdmsq_list").remove(params).write();
};
// 历史台账查询-获取当前年份已生成国家秘密事项历史台账
export const getGjmmsxHistory = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('lsgjmmsx_list').filter(function (item) {
			return item.tzsj == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('lsgjmmsx_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 重新生成-删除当前年生成的所有历史数据
export const deleteLsGjmmsx = (params) => {
	db.read().get("lsgjmmsx_list").remove(params).write();
};
// 历史台账查询-获取当前年份已生成定密培训历史台账
export const getDmpxHistory = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('lsdmpx_list').filter(function (item) {
			return item.tzsj == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('lsdmpx_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 重新生成-删除当前年生成的所有历史数据
export const deleteLsDmpx = (params) => {
	db.read().get("lsdmpx_list").remove(params).write();
};
// 历史台账查询-获取当前年份已生成定密情况年度统计历史台账
export const getDmqkndtjHistory = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('lsdmqkndtj_list').filter(function (item) {
			return item.tzsj == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('lsdmqkndtj_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 重新生成-删除当前年生成的所有历史数据
export const deleteLsDmqkndtj = (params) => {
	db.read().get("lsdmqkndtj_list").remove(params).write();
};
// 历史台账查询-获取当前年份已生成不明确事项确定情况历史台账
export const getBmqsxqdqkHistory = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('lsbmqsxqdqk_list').filter(function (item) {
			return item.tzsj == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('lsbmqsxqdqk_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 重新生成-删除当前年生成的所有历史数据
export const deleteLsBmqsxqdqk = (params) => {
	db.read().get("lsbmqsxqdqk_list").remove(params).write();
};
// 历史台账查询-获取当前年份已生成政府采购项目情况历史台账
export const getZfcgxmqkHistory = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('lszfcgxmqk_list').filter(function (item) {
			return item.tzsj == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('lszfcgxmqk_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 重新生成-删除当前年生成的所有历史数据
export const deleteLsZfcgxmqk = (params) => {
	db.read().get("lszfcgxmqk_list").remove(params).write();
};


