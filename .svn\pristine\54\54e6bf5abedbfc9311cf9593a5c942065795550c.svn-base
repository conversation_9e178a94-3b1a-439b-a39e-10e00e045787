import {networkInterFaces} from 'os'
export const getMacAddress = ()=>{
  const interfaces = networkInterFaces();
  console.log(interfaces);
  // let macAddress = '';
  // for(const interfaceName of Object.keys(interfaces)){
  //   const interfaceInfos = interfaces[interfaceName];
  //   if(interfaceInfos){
  //     for(const interfaceInfo of interfaceInfos){
  //       if(interfaceInfo.mac && interfaceInfo.mac !== "00:00:00:00:00:00"){
  //         macAddress = interfaceInfo.mac;
  //         break;
  //       }
  //     }
  //   }
  //   if (macAddress.length>0)break;
  // }
  // return macAddress;
}
export const detectZoom = () => {
  let ratio = 0,
    screen = window.screen,
    ua = navigator.userAgent.toLowerCase();
  if (window.devicePixelRatio !== undefined) {
    ratio = window.devicePixelRatio;
  } else if (~ua.indexOf('msie')) {
    if (screen.deviceXDPI && screen.logicalXDPI) {
      ratio = screen.deviceXDPI / screen.logicalXDPI;
    }
  } else if (
    window.outerWidth !== undefined &&
    window.innerWidth !== undefined
  ) {
    ratio = window.outerWidth / window.innerWidth;
  }
  if (ratio) {
    ratio = Math.round(ratio * 100);
  }
  return ratio;
};
