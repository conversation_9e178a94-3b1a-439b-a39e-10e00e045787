// import db from './zpzcdb'
import db from './adapter/zczpAdaptor'
/**
 *
 * 一些共享的方法，不论是临时、总的还是保存至下一步
 * kind 1:数据加工，模拟mybatis没有该字段就不更新的操作（不限定对象类型，发什么对象过来我都能进行解析并判断是否有值未实现）
 * kind 2:返回生成的id字符串
 * kind 3:数据加工，把数据加工成对应数据库对象的格式，方便插入
 */
/*********************kind 1********************************/
export const getScrw = (scrw) => {
  console.log('getScrw...')
  // console.log(scrw);
  const resObj = {}
  if (scrw.rwid !== undefined) {
    resObj.rwid = scrw.rwid
  }
  if (scrw.rwmc !== undefined) {
    resObj.rwmc = scrw.rwmc
  }
  if (scrw.kssj !== undefined) {
    resObj.kssj = scrw.kssj
  }
  if (scrw.jzsj !== undefined) {
    resObj.jzsj = scrw.jzsj
  }
  if (scrw.bz !== undefined) {
    resObj.bz = scrw.bz
  }
  if (scrw.djr !== undefined) {
    resObj.djr = scrw.djr
  }
  if (scrw.lxr !== undefined) {
    resObj.lxr = scrw.lxr
  }
  if (scrw.lxdh !== undefined) {
    resObj.lxdh = scrw.lxdh
  }
  if (scrw.jcdf !== undefined) {
    resObj.jcdf = scrw.jcdf
  }
  if (scrw.jcjg !== undefined) {
    resObj.jcjg = scrw.jcjg
  }
  if (scrw.zt !== undefined) {
    resObj.zt = scrw.zt
  }
  if (scrw.pjjg !== undefined) {
    resObj.pjjg = scrw.pjjg
  }
  if (scrw.pjyj !== undefined) {
    resObj.pjyj = scrw.pjyj
  }
  if (scrw.gxsj !== undefined) {
    resObj.gxsj = scrw.gxsj
  }
  console.log(resObj)
  return resObj
}

export const getDwxxzcjl = (dwxxzcjl) => {
  console.log('getDwxxzcjl')
  const resDwxxzcjl = {}
  if (dwxxzcjl.jlid !== undefined) {
    resDwxxzcjl.jlid = dwxxzcjl.jlid
  }
  if (dwxxzcjl.lsid !== undefined) {
    resDwxxzcjl.lsid = dwxxzcjl.lsid
  }
  if (dwxxzcjl.nrid !== undefined) {
    resDwxxzcjl.nrid = dwxxzcjl.nrid
  }
  if (dwxxzcjl.ykf !== undefined) {
    resDwxxzcjl.ykf = dwxxzcjl.ykf
  }
  if (dwxxzcjl.sfsynr !== undefined) {
    resDwxxzcjl.sfsynr = dwxxzcjl.sfsynr
  }
  if (dwxxzcjl.synr !== undefined) {
    resDwxxzcjl.synr = dwxxzcjl.synr
  }
  if (dwxxzcjl.dfsm !== undefined) {
    resDwxxzcjl.dfsm = dwxxzcjl.dfsm
  }
  return resDwxxzcjl
}

export const getCcbm = (ccbm) => {
  console.log('getCcbm')
  const resCcbm = {}
  if (ccbm.bmid !== undefined) {
    resCcbm.bmid = ccbm.bmid
  }
  if (ccbm.ccbm !== undefined) {
    resCcbm.ccbm = ccbm.ccbm
  }
  if (ccbm.lsid !== undefined) {
    resCcbm.lsid = ccbm.lsid
  }
  return resCcbm
}

export const getBmxxzcjl = (bmxxzcjlList) => {
  console.log('getBmxxzcjl')
  if (bmxxzcjlList === undefined || bmxxzcjlList.length < 1) {
    console.log('bmxxzcjlList为undefind或空')
    return
  }
  const resBmxxzcjlList = []
  bmxxzcjlList.forEach((bmxxzcjl) => {
    const resBmxxzcjl = {}
    if (bmxxzcjl.jlid !== undefined) {
      resBmxxzcjl.jlid = bmxxzcjl.jlid
    }
    if (bmxxzcjl.lsid !== undefined) {
      resBmxxzcjl.lsid = bmxxzcjl.lsid
    }
    if (bmxxzcjl.nrid !== undefined) {
      resBmxxzcjl.nrid = bmxxzcjl.nrid
    }
    if (bmxxzcjl.ykf !== undefined) {
      resBmxxzcjl.ykf = bmxxzcjl.ykf
    }
    if (bmxxzcjl.sfsynr !== undefined) {
      resBmxxzcjl.sfsynr = bmxxzcjl.sfsynr
    }
    if (bmxxzcjl.synr !== undefined) {
      resBmxxzcjl.synr = bmxxzcjl.synr
    }
    if (bmxxzcjl.dfsm !== undefined) {
      resBmxxzcjl.dfsm = bmxxzcjl.dfsm
    }
    resBmxxzcjlList.push(resBmxxzcjl)
  })

  return resBmxxzcjlList
}

export const getZcry = (zcry) => {
  console.log('getZcry')
  if (getZcry === undefined) {
    console.log('getZcry为undefind')
    return
  }
  const resZcry = {}
  if (zcry.ryid !== undefined) {
    resZcry.ryid = zcry.ryid
  }
  if (zcry.lsid !== undefined) {
    resZcry.lsid = zcry.lsid
  }
  if (zcry.name !== undefined) {
    resZcry.name = zcry.name
  }
  if (zcry.idcard !== undefined) {
    resZcry.idcard = zcry.idcard
  }
  if (zcry.sfsc !== undefined) {
    resZcry.sfsc = zcry.sfsc
  }
  if (zcry.bm !== undefined) {
    resZcry.bm = zcry.bm
  }
  return resZcry
}

export const getRyxxzcjlList = (ryxxzcjlList) => {
  console.log('getRyxxzcjlList')
  if (ryxxzcjlList === undefined || ryxxzcjlList.length < 1) {
    console.log('ryxxzcjlList为undefind或空')
    return
  }
  const resRyxxzcjlList = []
  ryxxzcjlList.forEach((ryxxzcjl) => {
    const resRyxxzcjl = {}
    if (ryxxzcjl.jlid !== undefined) {
      resRyxxzcjl.jlid = ryxxzcjl.jlid
    }
    if (ryxxzcjl.lsid !== undefined) {
      resRyxxzcjl.lsid = ryxxzcjl.lsid
    }
    if (ryxxzcjl.nrid !== undefined) {
      resRyxxzcjl.nrid = ryxxzcjl.nrid
    }
    if (ryxxzcjl.sffhyq !== undefined) {
      resRyxxzcjl.sffhyq = ryxxzcjl.sffhyq
    }
    if (ryxxzcjl.bzsm !== undefined) {
      resRyxxzcjl.bzsm = ryxxzcjl.bzsm
    }
    resRyxxzcjlList.push(resRyxxzcjl)
  })

  return resRyxxzcjlList
}

/*********************kind 2********************************/
//生成scls唯一id
export const getSclsId = () => {
  const shortId = require('shortid')
  let id
  let count = 0
  while (count < 10) {
    id = shortId.generate()
    const idExist = db
      .get('scls_list')
      .filter({
        lsid: id,
      })
      .size()
      .value()
    if (idExist === 0) {
      break
    }
    count++
  }
  //声明返回的id为const
  const resId = id
  return resId
}

//生成dwpfjl唯一id
export const getDwpfjlId = () => {
  const shortId = require('shortid')
  let id
  let count = 0
  while (count < 10) {
    id = shortId.generate()
    const idExist = db
      .get('dwpfjl_list')
      .filter({
        dwpfjlid: id,
      })
      .size()
      .value()
    if (idExist === 0) {
      break
    }
    count++
  }
  //声明返回的id为const
  const resId = id
  return resId
}

//生成ccbm唯一id
export const getCcbmId = () => {
  const shortId = require('shortid')
  let id
  let count = 0
  while (count < 10) {
    id = shortId.generate()
    const idExist = db
      .get('ccbm_list')
      .filter({
        lsid: id,
      })
      .size()
      .value()
    if (idExist === 0) {
      break
    }
    count++
  }
  //声明返回的id为const
  const resId = id
  return resId
}

//生成bmxxzcjl唯一id
export const getBmxxzcjlId = () => {
  const shortId = require('shortid')
  let id
  let count = 0
  while (count < 10) {
    id = shortId.generate()
    const idExist = db
      .get('bmxxzcjl_list')
      .filter({
        lsid: id,
      })
      .size()
      .value()
    if (idExist === 0) {
      break
    }
    count++
  }
  //声明返回的id为const
  const resId = id
  return resId
}

//生成zcry唯一id
export const getZcryId = () => {
  const shortId = require('shortid')
  let id
  let count = 0
  while (count < 10) {
    id = shortId.generate()
    const idExist = db
      .get('zcry_list')
      .filter({
        lsid: id,
      })
      .size()
      .value()
    if (idExist === 0) {
      break
    }
    count++
  }
  //声明返回的id为const
  const resId = id
  return resId
}

//生成ryxxzcjl唯一id
export const getRyxxzcjlId = () => {
  const shortId = require('shortid')
  let id
  let count = 0
  while (count < 10) {
    id = shortId.generate()
    const idExist = db
      .get('ryxxzcjl_list')
      .filter({
        lsid: id,
      })
      .size()
      .value()
    if (idExist === 0) {
      break
    }
    count++
  }
  //声明返回的id为const
  const resId = id
  return resId
}

/*********************kind 3********************************/
//把前台返回的[dx:{xx:[]}]结构数据加工成单位评分记录表数据
export const getDwpfjlListByDxXx = (dxXx, rwid) => {
  console.log('加工前入参', dxXx)
  const resDwpfjlList = []
  dxXx.forEach((dx) => {
    dx.xx.forEach((xx) => {
      // console.log(xx);
      const resDwpfjl = {
        dwpfjlid: xx.dwpfjlid,
        rwid: rwid,
        nrid: xx.nrid,
        ykf: xx.ykf,
        sfsynr: xx.sfsynr,
        synr: xx.synr,
        kfsm: xx.kfsm
      }
      resDwpfjlList.push(resDwpfjl)
    })
  })
  // console.log(resDwpfjlList);
  return resDwpfjlList
}
