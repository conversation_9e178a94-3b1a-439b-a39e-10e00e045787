<template>
  <div style="width: 100%;height: 100%;">
    <!---->
    <div class="mhcx">
      <el-form :inline="true" size="medium" class="demo-form-inline" style="float:left">
        <!-- <el-form-item style="float: left;">
          <div>当前审查任务：{{dialogObj.rwmc}}</div>
        </el-form-item> -->
        <el-form-item style="float: left;">
          <div>单位：{{dialogObj.dwmc}}</div>
        </el-form-item>
        <el-form-item style="float: left;">
          <div>抽查内设机构：{{dialogObj.zzjgmc}}</div>
        </el-form-item>
      </el-form>
      <!---->
      <div style="clear: both;"></div>
    </div>
    <!---->
    <div style="overflow-y: scroll;height: calc(100% - 58px - 64px - 108px - 16px - 20px);margin-bottom: 5px;">
      <el-card style="margin-bottom: 1em">
        <el-table :data="showDxList" :span-method="objectSpanMethod" :header-cell-style="{ 'text-align': 'center' }" border>
          <el-table-column label="自查类" width="80">
            <template slot-scope="scope">
              <div>
                <span :id="showDxList[scope.$index].dxMdIndex"></span>{{ showDxList[scope.$index].dxmc }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="检查内容">
            <template slot-scope="scope">
              <div>
                <span :id="showDxList[scope.$index].mdIndex"></span>{{ showDxList[scope.$index].nr }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="扣分" width="150" align="center">
            <template slot-scope="scope">
              <div>
                <!--计数器扣分-->
                <div v-if="showDxList[scope.$index].kffs == 6">
                  <el-checkbox-group v-model="showDxList[scope.$index].check" disabled>
                    <el-checkbox label="" name="checkbox" style="margin-right: 0.5em">
                    </el-checkbox>
                    <el-input-number v-model="showDxList[scope.$index].jsqkf" disabled :min="showDxList[scope.$index].zdkffz" :max="showDxList[scope.$index].zgkffz" :step="showDxList[scope.$index].kfzf" size="mini" style="width: 100px" @change="handleKfjsq(showDxList[scope.$index])"></el-input-number>
                  </el-checkbox-group>
                </div>
                <!--固定扣分-->
                <div v-if="showDxList[scope.$index].kffs == 7">
                  <el-checkbox-group v-model="showDxList[scope.$index].check" disabled>
                    <el-checkbox label="" name="checkbox">{{
                      showDxList[scope.$index].gdkffz
                    }}</el-checkbox>
                  </el-checkbox-group>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="fz" label="分值" width="50" align="center"></el-table-column>
          <el-table-column label="实有项目" width="80" align="center">
            <template slot-scope="scope">
              <el-checkbox-group v-model="showDxList[scope.$index].sfsynr" disabled>
                <el-checkbox label="是" name="checkboxSynr"></el-checkbox>
              </el-checkbox-group>
            </template>
          </el-table-column>
          <el-table-column prop="" label="实有内容">
            <template slot-scope="scope">
              <el-input type="textarea" v-model.trim="showDxList[scope.$index].synr" disabled :rows="3" @input="handleSynrTextarea(showDxList[scope.$index])"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="得分" width="50" align="center">
            <template slot-scope="scope">
              <!-- <div>{{(showDxList[scope.$index].fz-showDxList[scope.$index].ykf)>0?(showDxList[scope.$index].fz-showDxList[scope.$index].ykf):0}}</div> -->
              <div>{{ showDxList[scope.$index].df }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="kfbz" label="扣分标准"></el-table-column>
          <el-table-column label="评分说明">
            <template slot-scope="scope">
              <el-input type="textarea" v-model.trim="showDxList[scope.$index].kfsm" disabled :rows="3" @input="handleDfsmTextarea(showDxList[scope.$index])"></el-input>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
    <!---->
    <el-card id="sslist" style="margin-bottom: 5px;">
      <span class="sslist_class">实有项目总分：<span style="color: red">{{ syxmzf }}</span></span>
      <span class="sslist_class">实有项目得分：<span style="color: red">{{ syxmdf }}</span></span>
      <span class="sslist_class">实有项目得分占实有项目总分百分比：<span style="color: red">{{ syxmbfb }}%</span></span>
    </el-card>
    <el-card id="sslist">
      <span style="color: red; display: block; width: 100%; font-weight: bold">备注：</span>
      <ol style="margin-left: 2em; list-style: disc">
        <li style="list-style: disc">
          1.单项检查内容存在多起不符合要求行为的，每个单项的总扣分最高不超过该项的总分值。
        </li>
        <li style="list-style: disc">
          2.实行100分评分制,得分为实有项目得分与实有项目总分比值的百分制得分。实有项目总分为实有检查内容各项分值之和，实有项目得分为实有项目总分扣除自查发现问题分值之后的得分。
        </li>
      </ol>
    </el-card>
    <!---->
    <!---->
    <!---->
    <!---->
  </div>
</template>

<script>

import {
  // 通过任务ID获取任务信息、单位信息和抽查机构信息
  selectRwxxDwxxCcnsjgxxByRwidCcdnsjgid,
  // 获取部门详细自查记录字典
  getBmxxzcjlZD,
  // 插入或更新内设机构评分记录表
  insertUpdateNsjgpfjlByCcdnsjgid,
  // 更新 抽查的内设机构表(ccdnsjg_list)
  updateCcdnsjgById,
  // 通过[抽查内设机构流水ID]获取内设机构评分记录历史信息
  selectNsjgpfjlListByCcdnsjgid
} from '../../../../db/zczpdb'

import { getZczpIdsObj, removeZczpIdsObjField } from '../../../../utils/windowLocation'

export default {
  data () {
    return {
      dialogObj: {
        rwid: '93BC8D4D-2FDE-4BB9-85E5-F6214393B82E',
        ccdnsjgid: '3AE1A8CD-83E7-4228-951C-1D71604C4631'

      },
      //各大项得分总和array（实时变化）
      dxdfArr: [],
      //实有项目分值array
      syxmDfArr: [],
      //table实际操作的检查评分数据
      showDxList: [],
      //单元格合并规则
      spanArr: []
    }
  },
  computed: {
    syxmzf () {
      let zf = 0;
      this.showDxList.forEach((nr) => {
        if (nr.sfsynr) {
          zf += nr.fz
        }
      })
      return zf
    },
    syxmdf () {
      let resDf = 0
      this.syxmDfArr.forEach((df) => {
        resDf += df
      })
      return resDf
    },
    syxmbfb () {
      if (this.syxmzf == 0) {
        return 0
      }
      // return (this.syxmdf / this.syxmzf).toFixed(3) * 100;
      let num = this.syxmdf / this.syxmzf
      return Math.round(num * 1000) / 10
    }
  },
  components: {
  },
  methods: {
    // 临时保存
    save () {
      console.log('this.showDxList', this.showDxList)
      this.ccbmRK(this.showDxList, 1)
    },
    // 提交
    submit () {
      this.ccbmRK(this.showDxList, 2)
    },
    //部门抽查入库，状态为该记录对应的 抽查的内设机构表(ccdnsjg_list) 的状态（0待登记 1继续登记 2完成登记）
    ccbmRK (dxXxList, zt) {
      //插入或更新内设机构评分记录表
      let bool = insertUpdateNsjgpfjlByCcdnsjgid(dxXxList, this.dialogObj.ccdnsjgid)
      /*=====部门详细自查记录入库完成=====*/
      // 更新 抽查的内设机构表(ccdnsjg_list) 表状态
      let params = {
        ccdnsjgid: this.dialogObj.ccdnsjgid,
        zt: zt
      }
      bool = updateCcdnsjgById(params)
      if (bool) {
        // 更新数据
        this.updateTable()
        //
        this.$message.success('内设机构自查自评结果登记成功')
      }
    },
    // 通过抽查的内设机构流水ID更新数据
    updateTable () {
      let bmxxzcjlList = selectNsjgpfjlListByCcdnsjgid(this.dialogObj.ccdnsjgid)
      let resBmxxzcjlList = []
      bmxxzcjlList.forEach((bmxxzcjl) => {
        console.log('bmxxzcjl', bmxxzcjl)
        bmxxzcjl.gdkffz = bmxxzcjl.fz
        bmxxzcjl.jsqkf = bmxxzcjl.ykf
        // console.log(bmxxzcjl.ykf);
        bmxxzcjl.gdkffz = bmxxzcjl.fz
        if (bmxxzcjl.ykf !== undefined && bmxxzcjl.ykf != 0) {
          bmxxzcjl.check = true
        } else {
          bmxxzcjl.check = false
        }
        //   console.log(bmxxzcjl.sfsynr);
        if (bmxxzcjl.sfsynr === undefined) {
          bmxxzcjl.sfsynr = false
        }
      })
      resBmxxzcjlList = bmxxzcjlList
      //计算单元格合并规则
      this.spanArr = this.getSpanArr(resBmxxzcjlList)
      this.showDxList = resBmxxzcjlList
    },
    returnSy () {
      this.$router.go(-1)
    },
    // 获取抽查单位信息
    getRwxxDwxxCcnsjgxxByRwidCcdnsjgid () {
      let rwxxDwxxCcnsjgxx = selectRwxxDwxxCcnsjgxxByRwidCcdnsjgid(this.dialogObj)
      console.log('rwxxDwxxCcnsjgxx', rwxxDwxxCcnsjgxx)
      Object.assign(this.dialogObj, rwxxDwxxCcnsjgxx)
      this.dialogObj = JSON.parse(JSON.stringify(this.dialogObj))
    },
    //----------------------------用来返回this.spanArr数组的，定义每一行的 rowspan-----------
    getSpanArr (list) {
      // console.log(list)
      let spanArr = []
      for (var i = 0; i < list.length; i++) {
        if (i === 0) {
          spanArr.push(1)
          this.pos = 0
        } else {
          // 判断当前元素与上一个元素是否相同
          if (list[i].dxid == list[i - 1].dxid) {
            spanArr[this.pos] += 1
            spanArr.push(0)
          } else {
            spanArr.push(1)
            this.pos = i
          }
        }
      }
      return spanArr
    },
    objectSpanMethod ({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        //
        const _row = this.spanArr[row.nrid - 1]
        return {
          rowspan: _row,
          colspan: 1
        }
      }
    },
    // 扣分计数器改变
    handleKfjsq (nr) {
      nr.check = true
    },
    /**
     * 实有内容textarea值改变
     */
    handleSynrTextarea (nr) {
      if (nr.synr !== undefined && nr.synr != "") {
        nr.sfsynr = true
        // xx.check=true
      } else {
        nr.sfsynr = false
        // xx.check=true
      }
    },
    /**
     * 评分说明改变
     */
    handleDfsmTextarea (nr) {
      if (nr.kfsm !== undefined && nr.kfsm != "") {
        nr.check = true
      } else {
        nr.check = false
      }
    },
    /**
     * 获取部门详细自查记录字典
     */
    getZD () {
      //
      const zdList = getBmxxzcjlZD()
      console.log(zdList)
      zdList.forEach((nr) => {
        nr.ykf = 0
        nr.check = false
        nr.jsqkf = 0
        nr.gdkffz = nr.fz
        //是否实有内容在字典里默认选中
        nr.sfsynr = true
      })
      this.spanArr = this.getSpanArr(zdList)
      //
      return zdList
    },
  },
  watch: {
    showDxList: {
      handler (newVal, oldVal) {
        console.log("bmcc showDxList changed...")
        const _this = this
        //
        _this.syxmDfArr = []
        //
        let dxIdArr = []
        //
        newVal.forEach((nr, nrIndex) => {
          // console.log(dxIdArr)
          // console.log(nr.dxid)
          if (dxIdArr.indexOf(nr.dxid) == -1) {
            dxIdArr.push(nr.dxid)
          }
          //
          if (nr.check) {
            if (nr.kffs == 6) {
              nr.ykf = nr.jsqkf
            }
            if (nr.kffs == 7) {
              nr.ykf = nr.gdkffz
            }
            console.log(nr.nrid, nr.fz, nr.ykf, nr.fz - nr.ykf)
            nr.df = nr.fz - nr.ykf
          } else {
            nr.ykf = 0
            nr.jsqkf = 0
            nr.df = nr.fz
          }
          //
          if (nr.sfsynr) {
            _this.syxmDfArr.push(nr.df)
          }
        })
      },
      deep: true
    }
  },
  mounted () {
    // let params = this.$route.query
    let params = getZczpIdsObj()
    if (params && Object.keys(params).length > 0) {
      console.log('抽查的内设机构登记', params)
      this.dialogObj.rwid = params.rwid
      this.dialogObj.ccdnsjgid = params.ccdnsjgid
    } else {
      this.$message.warning('未检测到抽查的内设机构ID，请关闭页面重新进入')
      return
    }
    // 获取抽查单位信息
    // 获取任务信息、单位信息和抽查机构信息
    this.getRwxxDwxxCcnsjgxxByRwidCcdnsjgid()
    // // 获取字典信息
    // this.showDxList = this.getZD()
    // 调用更新数据方法，如果没有历史信息，则返回的是字典信息
    this.updateTable()
  }
}
</script>

<style scoped></style>
