import db from './adapter/zczpAdaptor'

// 日志工具类
import { writeTrajectoryLog } from '../utils/logUtils'

//保密制度-----------------------------------保密制度初始化列表********
export const getSmjsj = (params) => {
  let page = params.page
  let pagesize = params.pageSize
  //
  let bmbh = params.bmbh
  let zrr = params.zrr
  let sybm = params.sybm
  let lx = params.lx
  let mj = params.mj
  let qyrq = params.qyrq
  //
  let list_total = db
    .get('Smjsj_list')
    .sortBy('cjsj')
    .filter(function (item) {
      // 1、bm型号，类型，密级，日期都没有
      // if ((bmbh === undefined || bmbh == "") && (lx === undefined || lx == "") && (mj === undefined || mj == '') && (qyrq === undefined || qyrq == '' ||qyrq == null)) {
      return item
    })
    .cloneDeep()
    .value()

  // 模糊查询过滤
  if (bmbh) {
    list_total = list_total.filter((item) => {
      if (item.bmbh.indexOf(bmbh) != -1) {
        return item
      }
    })
  }
  if (zrr) {
    list_total = list_total.filter((item) => {
      if (item.zrr.indexOf(zrr) != -1) {
        return item
      }
    })
  }
  if (sybm) {
    list_total = list_total.filter((item) => {
      if (item.sybm.indexOf(sybm.join('/')) != -1) {
        return item
      }
    })
  }
  if (lx) {
    list_total = list_total.filter((item) => {
      if (item.lx == lx) {
        return item
      }
    })
  }
  if (mj) {
    list_total = list_total.filter((item) => {
      if (item.mj == mj) {
        return item
      }
    })
  }
  if (qyrq) {
    list_total = list_total.filter((item) => {
      console.log(item.qyrq >= qyrq[0], item.qyrq <= qyrq[1])
      if (item.qyrq >= qyrq[0] && item.qyrq <= qyrq[1]) {
        return item
      }
    })
  }

  // 手动分页
  console.log(pagesize * (page - 1), pagesize * (page - 1) + pagesize)
  let pageList = list_total.slice(
    pagesize * (page - 1),
    pagesize * (page - 1) + pagesize
  )

  let resList = {
    list: pageList,
    list_total: list_total,
    total: list_total.length,
  }
  console.log('保密制度', resList)
  return resList
}
//保密制度-----------------------------------保密制度添加成员********
export const addSmjsj = (params) => {
  let zcbh = params.zcbh
  let bmbh = params.bmbh
  let checkObj = db
    .get('Smjsj_list')
    .find({ zcbh: zcbh, bmbh: bmbh })
    .cloneDeep()
    .value()
  if (checkObj) {
    throw new Error('已有该保密编号、固定资产编号记录')
  }
  let sjc = new Date().getTime()
  params.cjsj = sjc
  params.gxsj = sjc
  db.read().get('Smjsj_list').push(params).write()
  // 添加日志
  let paramsLog = {
    xyybs: 'mk_smjsj',
    id: params.smjsjid,
    ymngnmc: '新增',
    extraParams: {
      zrr: params.zrr,
    },
  }
  writeTrajectoryLog(paramsLog)
}
export const jysmjsj = (params) => {
  let bmbh = params.bmbh
  let zcbh = params.zcbh
  let zjxlh = params.zjxlh
  let message = 0
  let pdbmbh = db
    .read()
    .get('Smjsj_list')
    .find({ bmbh: bmbh })
    .cloneDeep()
    .value()
  let pdzcbh = db
    .read()
    .get('Smjsj_list')
    .find({ zcbh: zcbh })
    .cloneDeep()
    .value()
  let pdzjxlh = db
    .read()
    .get('Smjsj_list')
    .find({ zjxlh: zjxlh })
    .cloneDeep()
    .value()
  if (pdbmbh) {
    message = 1
  } else if (pdzcbh) {
    message = 2
  } else if (pdzjxlh) {
    message = 3
  }
  console.log(message)
  return message
}
export const jysmjsjbmbh = (params) => {
  let bmbh = params.bmbh
  let message = 0
  let pdbmbh = db
    .read()
    .get('Smjsj_list')
    .find({ bmbh: bmbh })
    .cloneDeep()
    .value()
  if (pdbmbh) {
    message = 1
  }
  console.log(message)
  return message
}
export const jysmjsjzcbh = (params) => {
  let zcbh = params.zcbh
  let message = 0
  let pdzcbh = db
    .read()
    .get('Smjsj_list')
    .find({ zcbh: zcbh })
    .cloneDeep()
    .value()
  if (pdzcbh) {
    message = 2
  }
  console.log(message)
  return message
}
export const jysmjsjzjxlh = (params) => {
  let zjxlh = params.zjxlh
  let message = 0
  let pdzjxlh = db
    .read()
    .get('Smjsj_list')
    .find({ zjxlh: zjxlh })
    .cloneDeep()
    .value()
  if (zjxlh) {
    message = 3
  }
  console.log(message)
  return message
}
//保密制度-----------------------------------保密制度删除成员********
//修改
export const reviseSmjsj = (params) => {
  let sybm = params.sybm.join('/')
  params.sybm = sybm
  let glbm = params.glbm.join('/')
  params.glbm = glbm
  let smjsjid = params.smjsjid
  console.log('smjsjid', smjsjid)
  if (!smjsjid || smjsjid == '') {
    return
  }
  params.gxsj = new Date().getTime()
  // 全字段更新方法(传进来的字段都会更新)
  db.read().get('Smjsj_list').find({ smjsjid: smjsjid }).assign(params).write()
}
//保密制度-----------------------------------保密制度删除成员********
export const deleteSmjsj = (params) => {
  console.log(params)
  db.read().get('Smjsj_list').remove(params).write()
}
// 使用保密编号和固定资产编号删除数据
export const deleteSmjsjConditions = (params) => {
  let bmbh = params.bmbh
  let zcbh = params.zcbh
  let removeObj = db
    .get('Smjsj_list')
    .find({ bmbh: bmbh, zcbh: zcbh })
    .cloneDeep()
    .value()
  console.log('removeObj', removeObj)
  if (removeObj) {
    db.get('Smjsj_list').remove(removeObj).write()
  }
}
//非是秘密计算机修改
export const xgsmjsjsyzt_zy = (params) => {
  // console.log(params)
  params.forEach((item) => {
    // 使用情况旧值（使用情况改变才插入轨迹日志）
    let itemSyqkOld = item.syqk
    db.get('Smjsj_list')
      .find({ zcbh: item.zcbh })
      .assign({
        syqk: '在用',
      })
      .write()
    // 添加日志
    if (itemSyqkOld != '在用') {
      let paramsLog = {
        xyybs: 'mk_smjsj',
        id: item.smjsjid,
        ymngnmc: '在用',
        extraParams: {
          zrr: item.zrr,
        },
      }
      writeTrajectoryLog(paramsLog)
    }
  })
}
//非是秘密计算机修改
export const xgsmjsjsyzt_ty = (params) => {
  // console.log(params)
  params.forEach((item) => {
    // 使用情况旧值（使用情况改变才插入轨迹日志）
    let itemSyqkOld = item.syqk
    db.get('Smjsj_list')
      .find({ zcbh: item.zcbh })
      .assign({
        syqk: '停用',
      })
      .write()
    // 添加日志
    if (itemSyqkOld != '停用') {
      let paramsLog = {
        xyybs: 'mk_smjsj',
        id: item.smjsjid,
        ymngnmc: '停用',
        extraParams: {
          zrr: item.zrr,
        },
      }
      writeTrajectoryLog(paramsLog)
    }
  })
}
//非是秘密计算机修改
export const xgsmjsjsyzt_bf = (params) => {
  // console.log(params)
  params.forEach((item) => {
    // 使用情况旧值（使用情况改变才插入轨迹日志）
    let itemSyqkOld = item.syqk
    db.get('Smjsj_list')
      .find({ zcbh: item.zcbh })
      .assign({
        syqk: '报废',
      })
      .write()
    // 添加日志
    if (itemSyqkOld != '报废') {
      let paramsLog = {
        xyybs: 'mk_smjsj',
        id: item.smjsjid,
        ymngnmc: '报废',
        extraParams: {
          zrr: item.zrr,
        },
      }
      writeTrajectoryLog(paramsLog)
    }
  })
}
//秘密计算机修改
export const xgsmjsjsyzt_jc = (params) => {
  console.log(params)
  params.forEach((item) => {
    // 使用情况旧值（使用情况改变才插入轨迹日志）
    let itemSyqkOld = item.syqk
    db.get('Smjsj_list')
      .find({ zcbh: item.zcbh })
      .assign({
        syqk: '借出',
      })
      .write()
    // 添加日志
    if (itemSyqkOld != '借出') {
      let paramsLog = {
        xyybs: 'mk_smjsj',
        id: item.smjsjid,
        ymngnmc: '借出',
        extraParams: {
          zrr: item.zrr,
        },
      }
      writeTrajectoryLog(paramsLog)
    }
  })
}
//秘密计算机修改
export const xgsmjsjsyzt_xh = (params) => {
  console.log(params)
  params.forEach((item) => {
    // 使用情况旧值（使用情况改变才插入轨迹日志）
    let itemSyqkOld = item.syqk
    db.get('Smjsj_list')
      .find({ zcbh: item.zcbh })
      .assign({
        syqk: '销毁',
      })
      .write()
    // 添加日志
    if (itemSyqkOld != '销毁') {
      let paramsLog = {
        xyybs: 'mk_smjsj',
        id: item.smjsjid,
        ymngnmc: '销毁',
        extraParams: {
          zrr: item.zrr,
        },
      }
      writeTrajectoryLog(paramsLog)
    }
  })
}
export const getPpxh = () => {
  let ppxh = db.get('Smjsj_list').cloneDeep().value()
  console.log()
  return ppxh
}
