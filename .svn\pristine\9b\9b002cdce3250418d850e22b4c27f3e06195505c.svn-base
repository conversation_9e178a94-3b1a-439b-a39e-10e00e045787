import {
    getJgxx
} from "../../../db/zzjgdb";
import { getDirectory } from '../../../utils/pathUtil'
import { exportWord } from '../../../utils/exportWord'
// 获取workbook2ArrayBuffer方法
import { workbook2ArrayBuffer } from '../../../utils/exportExcel2'
import XLSX from "xlsx"
import {
    //内容管理初始化成员列表
    getAllBmzd,
    getSmgwglList,
    getRyxzhzList,
    getGwbgList,
    getLglzList,
    getCsglList,
    getCsbgList,
    getSmjsjList,
    getSmydccjzList,
    getSmbgzdhsbList,
    getSmwlsbList,
    getAqcpList,
    getSmzttzList,
    getZczpRiskDatas,
    getZgsmrylList,
    getDwxxDatas
} from "../../../db/yjjcdb";
import { dateFormatNYRChinese } from '../../../utils/moment'
import { getDocZczpMbPath } from '../../../utils/pathUtil'
import {
    selectScrwZczplsPage,
    // 通过任务ID获取单位评分记录
    selectDwpfjlListByRwid,
    // 通过任务ID获取抽查的内设机构集合
    selectCcdnsjgListByRwid,
    // 通过[抽查内设机构流水ID]获取内设机构评分记录历史信息
    selectNsjgpfjlListByCcdnsjgid,
    // 获取抽查的人员
    selectCcdryListPage,
    // 通过抽查的人员流水ID获取抽查的人员评分信息
    selectRypfjlListByCcdryid,
    // 通过任务ID获取任务信息
    selectScrwByRwid,
    // 通过任务ID获取单位评分记录(非组合)
    selectDwpfjlListByRwidFzh,
    // 通过任务ID获取抽查的内设机构抽查结果
    selectCcdsjgListCcjg,
    // 通过任务ID获取抽查的人员抽查结果
    selectCcdryListCcjg
} from '../../../db/zczpdb'
import {
    // 通过任务ID获取单位信息
    selectDwxxByRwid
} from '../../../db/dwxxDb'
import html2canvas from 'html2canvas';
import JsPDF from 'jspdf';
export default {
    data() {
        return {
            // 根据表中数据判断对应模块已完成或者未完成
            bmzhIsPerfectShow: false,
            zzyhIsPerfectShow: false,
            smgwIsPerfectShow: false,
            ryhzIsPerfectShow: false,
            xzryxxIsPerfectShow: false,
            gwbgIsPerfectShow: false,
            lglzIsPerfectShow: false,
            csxxIsPerfectShow: false,
            bgxxIsPerfectShow: false,
            jsjtzIsPerfectShow: false,
            ydccjzIsPerfectShow: false,
            bgzdhsbIsPerfectShow: false,
            wlsbIsPerfectShow: false,
            aqcpIsPerfectShow: false,
            zttzIsPerfectShow: false,
            zczp1IsPerfectShow: false,
            zczp2IsPerfectShow: false,
            zczp3IsPerfectShow: false,
            zczp4IsPerfectShow: false,
        }
    },
    methods: {
        // 日期转换
        dateFormatNYRChinese(time) {
            let date = new Date(time)
            if ('Invalid Date' == date) {
                return date
            }
            return dateFormatNYRChinese(date)
        },
        // 保密制度检查项
        bmzhIsPerfect() {
            let resListArr = getAllBmzd(new Date().getFullYear())
            this.bmzhIsPerfectShow = resListArr.length == 0 ? true : false
        },
        // 组织用户检查项
        zzyhIsPerfect() {
            let resListArr = getJgxx(new Date().getFullYear())
            if (resListArr.length == 0 || resListArr.length == 1) {
                this.zzyhIsPerfectShow = true
            } else {
                this.zzyhIsPerfectShow = false
            }
        },
        // 人员信息检查项-涉密岗位登记表
        smgwIsPerfect() {
            let resListArr = getSmgwglList(new Date().getFullYear())
            this.smgwIsPerfectShow = resListArr.length == 0 ? true : false
        },
        // 人员信息检查项-涉密人员汇总表
        ryhzIsPerfect() {
            let resListArr = getZgsmrylList(new Date().getFullYear())
            this.ryhzIsPerfectShow = resListArr.length == 0 ? true : false
        },
        // 人员信息检查项-人员新增汇总表
        xzryxxIsPerfect() {
            let resListArr = getRyxzhzList(new Date().getFullYear())
            this.xzryxxIsPerfectShow = resListArr.length == 0 ? true : false
        },
        // 人员信息检查项-人员密级变更汇总表
        gwbgIsPerfect() {
            let resListArr = getGwbgList(new Date().getFullYear())
            this.gwbgIsPerfectShow = resListArr.length == 0 ? true : false
        },
        // 人员信息检查项-离岗离职汇总表
        lglzIsPerfect() {
            let resListArr = getLglzList(new Date().getFullYear())
            this.lglzIsPerfectShow = resListArr.length == 0 ? true : false
        },
        // 场所检查项-涉密场所登记表
        csxxIsPerfect() {
            let resListArr = getCsglList(new Date().getFullYear())
            this.csxxIsPerfectShow = resListArr.length == 0 ? true : false
        },
        // 场所检查项-涉密场所变更登记表
        bgxxIsPerfect() {
            let resListArr = getCsbgList(new Date().getFullYear())
            this.bgxxIsPerfectShow = resListArr.length == 0 ? true : false
        },
        // 设备检查项-涉密计算机台账
        jsjtzIsPerfect() {
            let resListArr = getSmjsjList(new Date().getFullYear())
            this.jsjtzIsPerfectShow = resListArr.length == 0 ? true : false
        },
        // 设备检查项-移动存储介质台账
        ydccjzIsPerfect() {
            let resListArr = getSmydccjzList(new Date().getFullYear())
            this.ydccjzIsPerfectShow = resListArr.length == 0 ? true : false
        },
        // 设备检查项-办公自动化设备台账
        bgzdhsbIsPerfect() {
            let resListArr = getSmbgzdhsbList(new Date().getFullYear())
            this.bgzdhsbIsPerfectShow = resListArr.length == 0 ? true : false
        },
        // 设备检查项-网络设备台账
        wlsbIsPerfect() {
            let resListArr = getSmwlsbList(new Date().getFullYear())
            this.wlsbIsPerfectShow = resListArr.length == 0 ? true : false
        },
        // 设备检查项-安全产品台账
        aqcpPerfect() {
            let resListArr = getAqcpList(new Date().getFullYear())
            this.aqcpIsPerfectShow = resListArr.length == 0 ? true : false
        },
        // 载体检查项-涉密载体台账
        zttzPerfect() {
            let resListArr = getSmzttzList(new Date().getFullYear())
            this.zttzIsPerfectShow = resListArr.length == 0 ? true : false
        },
        // 自查自评-第一季度
        zczp1Perfect() {
            let resRiskDatas = getZczpRiskDatas(1)
            let bhgItem = resRiskDatas.some((item) => {
                let { zt } = item
                if (zt == 7) {
                    return item
                }
            });
            if (resRiskDatas.length > 0 && bhgItem) {
                this.zczp1IsPerfectShow = true
            }
        },
        // 自查自评-第二季度
        zczp2Perfect() {
            let resRiskDatas = getZczpRiskDatas(2)
            let bhgItem = resRiskDatas.some((item) => {
                let { zt } = item
                if (zt == 7) {
                    return item
                }
            });
            if (resRiskDatas.length > 0 && bhgItem) {
                // alert(1)
                this.zczp2IsPerfectShow = true
            }
        },
        // 自查自评-第三季度
        zczp3Perfect() {
            let resRiskDatas = getZczpRiskDatas(3)
            let bhgItem = resRiskDatas.some((item) => {
                let { zt } = item
                if (zt == 7) {
                    return item
                }
            });
            if (resRiskDatas.length > 0 && bhgItem) {
                this.zczp3IsPerfectShow = true
            }
        },
        // 自查自评-第四季度
        zczp4Perfect() {
            let resRiskDatas = getZczpRiskDatas(4)
            let bhgItem = resRiskDatas.some((item) => {
                let { zt } = item
                if (zt == 7) {
                    return item
                }
            });
            if (resRiskDatas.length > 0 && bhgItem) {
                this.zczp4IsPerfectShow = true
            }
        },
        // 一键生成
        yjscBtn() {
            // 保密制度登记
            const fs = require('fs')
            // zip 文件名称
            let zipFileName = '迎检材料相关台账' + '-' + (new Date().getTime()) + '.zip'
            // 弹出dialog选择保存文件的位置
            const { dialog } = require("electron").remote
            let options = {
                title: "迎检材料相关台账", //下载文件名称
                defaultPath: zipFileName //下载文件title
            }
            dialog.showSaveDialog(options, (result) => {
                if (!result) {
                    this.$message.warning('迎检材料相关台账下载任务已取消')
                    return
                }
                // 判断路径是否存在
                let path = getDirectory(result)
                // 判断路径是否存在
                if (!fs.existsSync(path)) {
                    this.$message.warning('所选路径不存在，请重新点击下载按钮进行下载')
                    return
                }
                // this.exportZip(result, '', '')
                if (!this.zzyhIsPerfectShow) {
                    this.downloadPDF(this.$refs.zzjgpicXz, result);
                } else {
                    // 准备下载zip文件
                    this.exportZip(result, '', '')
                }
            })
        },
        // 
        downloadPDF(ele, result) {
            let eleW = ele.offsetWidth;// 获得该容器的宽
            let eleH = ele.offsetHeight;// 获得该容器的高
            var pdf = new JsPDF('', 'pt', 'a4');
            let eleOffsetTop = ele.offsetTop;  // 获得该容器到文档顶部的距离
            let eleOffsetLeft = ele.offsetLeft; // 获得该容器到文档最左的距离
            var canvas = document.createElement("canvas");
            var abs = 0;
            let win_in = document.documentElement.clientWidth || document.body.clientWidth; // 获得当前可视窗口的宽度（不包含滚动条）
            let win_out = window.innerWidth; // 获得当前窗口的宽度（包含滚动条）
            if (win_out > win_in) {
                // abs = (win_o - win_i)/2;    // 获得滚动条长度的一半
                abs = (win_out - win_in) / 2;    // 获得滚动条宽度的一半
            }
            canvas.width = eleW * 2;    // 将画布宽&&高放大两倍
            canvas.height = eleH * 2;
            var context = canvas.getContext("2d");
            context.scale(2, 2);
            context.translate(-eleOffsetLeft - abs, -eleOffsetTop);
            // 这里默认横向没有滚动条的情况，因为offset.left(),有无滚动条的时候存在差值，因此
            // translate的时候，要把这个差值去掉
            // html2canvas(element).then( (canvas)=>{ //报错
            // html2canvas(element[0]).then( (canvas)=>{
            html2canvas(ele, {
                dpi: 300,
                // allowTaint: true,  //允许 canvas 污染， allowTaint参数要去掉，否则是无法通过toDataURL导出canvas数据的
                useCORS: true  //允许canvas画布内 可以跨域请求外部链接图片, 允许跨域请求。
            }).then((canvas) => {
                var contentWidth = canvas.width;
                var contentHeight = canvas.height;
                //一页pdf显示html页面生成的canvas高度;
                var pageHeight = contentWidth / 592.28 * 841.89;
                //未生成pdf的html页面高度
                var leftHeight = contentHeight;
                //页面偏移
                var position = 0;
                //a4纸的尺寸[595.28,841.89]，html页面生成的canvas在pdf中图片的宽高
                var imgWidth = 595.28;
                var imgHeight = 595.28 / contentWidth * contentHeight;
                var pageData = canvas.toDataURL('image/jpeg', 1.0);
                //有两个高度需要区分，一个是html页面的实际高度，和生成pdf的页面高度(841.89)
                //当内容未超过pdf一页显示的范围，无需分页
                if (leftHeight < pageHeight) {
                    //在pdf.addImage(pageData, 'JPEG', 左，上，宽度，高度)设置在pdf中显示；
                    pdf.addImage(pageData, 'JPEG', 0, 0, imgWidth, imgHeight);
                    // pdf.addImage(pageData, 'JPEG', 20, 40, imgWidth, imgHeight);
                } else {    // 分页
                    while (leftHeight > 0) {
                        pdf.addImage(pageData, 'JPEG', 0, position, imgWidth, imgHeight);
                        leftHeight -= pageHeight;
                        position -= 841.89;
                        //避免添加空白页
                        if (leftHeight > 0) {
                            pdf.addPage();
                        }
                    }
                }
                this.exportZip(result, pdf, '')
            })
        },
        // 导出zip
        async exportZip(savePath, pdf, row, rowjd) {
            // 准备生成zip
            var AdmZip = require("adm-zip")
            var zip = new AdmZip()
            let params = {
                zt: 7
            }
            let admArr = []
            let scrwListPage = selectScrwZczplsPage(params)
            // 第四季度
            if (scrwListPage.list_total.length > 0) {
                scrwListPage.list_total.forEach((row, index) => {
                    admArr.push(new AdmZip())
                    if (!row) {
                        this.$message.warning('检测到记录数据为空，请重试或联系管理员处理')
                        return
                    }
                    if (!row.rwid) {
                        this.$message.warning('检测到检测任务ID为空，请重试或联系管理员处理')
                        return
                    }
                    // 生成单位检查评分记录excel
                    let dwJcjlExcelBuffer = this.generateDwJcjlExcel(row)
                    if (dwJcjlExcelBuffer) {
                        admArr[index].addFile(row.rwmc + '-' + row.jcjdmc + ".xlsx", dwJcjlExcelBuffer, "")
                    }
                    //生成内设机构检查评分记录excel
                    let nsjgJcjlExcelBuffer = this.generateNsjgJcjlExcel(row)
                    if (nsjgJcjlExcelBuffer) {
                        admArr[index].addFile(row.rwmc + '-' + row.jcjdmc + '-' + '抽查的内设机构检查记录' + ".xlsx", nsjgJcjlExcelBuffer, "")
                    }
                    //生成人员检查评分记录excel
                    try {
                        let ryJcjlExcelBuffer = this.generateRyJcjlExcel(row)
                        if (ryJcjlExcelBuffer) {
                            admArr[index].addFile(row.rwmc + '-' + row.jcjdmc + '-' + '抽查的涉密人员检查记录' + ".xlsx", ryJcjlExcelBuffer, "")
                        }
                    } catch (error) {
                        this.$message.error('[下载异常][生成人员检查记录表异常]' + error.message)
                    }
                    //生成检查报告word  
                    let jcbgWord = this.generateJcbgWord(row)
                    if (jcbgWord) {
                        admArr[index].addFile(row.rwmc + '-' + row.jcjdmc + '-' + '检查报告' + ".docx", jcbgWord, "")
                    }
                    zip.addFile(row.rwmc + '-' + row.jcjdmc + '自查自检报告' + ".zip", admArr[index].toBuffer(), "")
                })
            }
            zip.writeZip(savePath)
            //#region 
            // 组织用户
            if (!this.zzyhIsPerfectShow && pdf != '') {
                let rawdata = pdf.output();
                let len = rawdata.length,
                    u8 = new Uint8Array(len);
                while (len--) u8[len] = rawdata.charCodeAt(len);
                let buffer = new Buffer(u8)
                zip.addFile('组织机构图' + ".pdf", buffer, "")
            }
            // 生成涉密岗位统计情况
            if (!this.smgwIsPerfectShow) {
                let smcsExcelBuffer = this.generateSmcslExcel('scsmgwhz')
                if (smcsExcelBuffer) {
                    zip.addFile('涉密岗位统计情况' + ".xlsx", smcsExcelBuffer, "")
                }
            }
            // 生成涉密人员汇总表
            if (!this.ryhzIsPerfectShow) {
                let smcsExcelBuffer = this.generateSmcslExcel('scsmryhz')
                if (smcsExcelBuffer) {
                    zip.addFile('涉密人员统计情况' + ".xlsx", smcsExcelBuffer, "")
                }
            }
            // 生成涉密人员岗位变更汇总表
            if (!this.gwbgIsPerfectShow) {
                let smcsExcelBuffer = this.generateSmcslExcel('scsmrygwbg')
                if (smcsExcelBuffer) {
                    zip.addFile('涉密人员岗位变更汇总表' + ".xlsx", smcsExcelBuffer, "")
                }
            }
            // 生成涉密人员脱密期管理台账
            if (!this.lglzIsPerfectShow) {
                let smcsExcelBuffer = this.generateSmcslExcel('scsmrytmqgl')
                if (smcsExcelBuffer) {
                    zip.addFile('涉密人员脱密期管理台账' + ".xlsx", smcsExcelBuffer, "")
                }
            }
            // 生成涉密场所登记表
            if (!this.csxxIsPerfectShow) {
                let smcsExcelBuffer = this.generateSmcslExcel('scsmcsb')
                if (smcsExcelBuffer) {
                    zip.addFile('涉密场所登记表' + ".xlsx", smcsExcelBuffer, "")
                }
            }
            // 生成涉密场所变更登记表
            if (!this.bgxxIsPerfectShow) {
                let smcsExcelBuffer = this.generateSmcslExcel('sccsbg')
                if (smcsExcelBuffer) {
                    zip.addFile('涉密场所变更登记表' + ".xlsx", smcsExcelBuffer, "")
                }
            }
            // 生成涉密计算机台账
            if (!this.jsjtzIsPerfectShow) {
                let smcsExcelBuffer = this.generateSmcslExcel('scsmjsj')
                if (smcsExcelBuffer) {
                    zip.addFile('涉密计算机台账' + ".xlsx", smcsExcelBuffer, "")
                }
            }
            // 生成涉密移动存储介质台账
            if (!this.ydccjzIsPerfectShow) {
                let smcsExcelBuffer = this.generateSmcslExcel('scydccjz')
                if (smcsExcelBuffer) {
                    zip.addFile('涉密移动存储介质台账' + ".xlsx", smcsExcelBuffer, "")
                }
            }
            // 生成涉密办公自动化设备台账
            if (!this.bgzdhsbIsPerfectShow) {
                let smcsExcelBuffer = this.generateSmcslExcel('scsmbgzdhsb')
                if (smcsExcelBuffer) {
                    zip.addFile('涉密办公自动化设备台账' + ".xlsx", smcsExcelBuffer, "")
                }
            }
            // 生成涉密网络设备台账
            if (!this.wlsbIsPerfectShow) {
                let smcsExcelBuffer = this.generateSmcslExcel('scsmwlsb')
                if (smcsExcelBuffer) {
                    zip.addFile('涉密网络设备台账' + ".xlsx", smcsExcelBuffer, "")
                }
            }
            // 生成安全产品台账
            if (!this.aqcpIsPerfectShow) {
                let smcsExcelBuffer = this.generateSmcslExcel('scaqcp')
                if (smcsExcelBuffer) {
                    zip.addFile('安全产品台账' + ".xlsx", smcsExcelBuffer, "")
                }
            }
            // 生成涉密载体台账
            if (!this.zttzIsPerfectShow) {
                let smcsExcelBuffer = this.generateSmcslExcel('scsmzt')
                if (smcsExcelBuffer) {
                    zip.addFile('涉密载体台账' + ".xlsx", smcsExcelBuffer, "")
                }
            }
            // let folderPath = process.cwd()+`\\`+__dirname+`\\jdfolder`
            // 生成涉密人员新增汇总表
            if (!this.xzryxxIsPerfectShow) {
                let smcsExcelBuffer = this.generateSmcslExcel('scsmryxz')
                if (smcsExcelBuffer) {
                    zip.addFile('涉密人员新增台账' + ".xlsx", smcsExcelBuffer, "")
                }
            }
            // 生成保密制度登记
            if (!this.bmzhIsPerfectShow) {
                let smcsExcelBuffer = this.generateSmcslExcel('scbmzddj')
                if (smcsExcelBuffer) {
                    // zip.addLocalFolder(folderPath, '第一季度自检自查表');
                    zip.addFile('保密制度登记' + ".xlsx", smcsExcelBuffer, "")
                }
            }
            // #endregion
            zip.writeZip(savePath, (error) => {
                if (error) {
                    this.$message.error('[下载异常]' + error.message)
                    return
                }
                this.$message.success('迎检材料相关台账下载已完成')
            })
        },
        // 获取文件流
        generateSmcslExcel(name) {
            //sheet页数据
            let list = []
            let year = new Date().getFullYear() // 获取当前年份
            let date = new Date()
            let Y = date.getFullYear() + '-';
            let M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
            let D = date.getDate() + ' ';
            let dqsj = Y + M + D // 获取填报日期
            let sbdw = getDwxxDatas()[0].dwmc // 获取上报单位
            switch (name) {
                case 'scsmgwhz':
                    var smgwglList = getSmgwglList(new Date().getFullYear())
                    list.push(["涉密岗位统计情况"]) //确定列名
                    list.push(["上报单位：", sbdw]) //确定列名
                    list.push(["统计年度：", year + '年', "", "", "", "", "填报时间：", dqsj]) //确定列名
                    list.push(["序号", "部门", "岗位名称", "涉密等级", "岗位确定依据", "级别职称", "该岗位涉密人员人数", "备注"]) //确定列名
                    let lock1 = 0
                    for (let i in smgwglList) { //每一行的值
                        lock1 = lock1 + 1
                        let item = smgwglList[i] //{"name":"XXX","dw":"XXX","zw":"XXX"}
                        let column = [lock1, item["bm"], item["gwmc"], item["smdj"], item["gwqdyj"], item["gwmc"], item["gwmc"], item["bz"],]
                        list.push(column)
                    }
                    //Excel sheet页的名称
                    var sheet_name = "涉密岗位统计情况"
                    var sum = 9 // 列的数量，根据自身项目进行数据的更改
                    break
                case 'scsmryhz':
                    var smgwglList = getZgsmrylList(new Date().getFullYear())
                    list.push(["涉密人员统计情况"]) //确定列名
                    list.push(["上报单位：", sbdw]) //确定列名
                    list.push(["统计年度：", year + '年', "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "填报时间：", dqsj]) //确定列名
                    list.push(["序号", "姓名", "性别", "年龄", "身份证号", "部门", "岗位名称", "涉密等级", "最高学历", "级别职称", "职务", "职级", "身份类型", "用人形式", "是否审查", "是否出入境登记备案", "是否统一保管出入境证件", "上岗时间（现涉密岗位）", "备注"]) //确定列名
                    let lock2 = 0
                    for (let i in smgwglList) { //每一行的值
                        lock2 = lock2 + 1
                        let item = smgwglList[i] //{"name":"XXX","dw":"XXX","zw":"XXX"}
                        let column = [lock2, item["xm"], item["xb"], item["nl"], item["sfzhm"], item["bm"], item["gwmc"], item["smdj"], item["zgxl"], item["zc"], item["zw"], item["zj"], item["sflx"], item["yrxs"], item["sfsc"], item["crjdjba"], item["tybgcrjzj"], item["sgsj"], item["bz"],]
                        list.push(column)
                    }
                    //Excel sheet页的名称
                    var sheet_name = "涉密人员统计情况"
                    var sum = 20 // 列的数量，根据自身项目进行数据的更改
                    break
                case 'scsmrygwbg':
                    var smgwglList = getGwbgList(new Date().getFullYear())
                    list.push(["涉密人员岗位变更汇总表"]) //确定列名
                    list.push(["上报单位：", sbdw]) //确定列名
                    list.push(["统计年度：", year + '年', "", "", "", "", "", "", "", "", "填报时间：", dqsj]) //确定列名
                    list.push(["序号", "姓名", "性别", "身份证号", "所在部门", "原涉密岗位", "原涉密等级", "变更后涉密岗位", "变更后涉密等级", "入职涉密岗位日期", "变更日期", "备注",]) //确定列名
                    let lock3 = 0
                    for (let i in smgwglList) { //每一行的值
                        lock3 = lock3 + 1
                        let item = smgwglList[i] //{"name":"XXX","dw":"XXX","zw":"XXX"}
                        let column = [lock3, item["xm"], item["xb"], item["sfzhm"], item["bm"], item["gwmc"], item["smdj"], item["bghgwmc"], item["bghsmdj"], item["sgsj"], item["bgsj"], item["bz"],]
                        list.push(column)
                    }
                    //Excel sheet页的名称
                    var sheet_name = "涉密人员岗位变更汇总表"
                    var sum = 13 // 列的数量，根据自身项目进行数据的更改
                    break
                case 'scsmrytmqgl':
                    var smgwglList = getLglzList(new Date().getFullYear())
                    list.push(["涉密人员脱密期管理台账"]) //确定列名
                    list.push(["上报单位：", sbdw]) //确定列名
                    list.push(["统计年度：", year + '年', "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "填报时间：", dqsj]) //确定列名
                    list.push(["序号", "姓名", "性别", "身份证号", "年龄", "原部门", "原涉密岗位", "职务", "职级", "级别职称", "原身份类型", "是否到公安机关备案", "是否委托管理", "脱密期开始时间", "脱密期结束时间", "手机号码", "离职离岗类型", "去向单位名称", "备注",]) //确定列名
                    let lock4 = 0
                    for (let i in smgwglList) { //每一行的值
                        lock4 = lock4 + 1
                        let item = smgwglList[i] //{"name":"XXX","dw":"XXX","zw":"XXX"}
                        let column = [lock4, item["xm"], item["xb"], item["sfzhm"], item["nl"], item["ybm"], item["ygwmc"], item["zw"], item["zj"], item["zc"], item["ysflx"], item["gajgcrjba"], item["sfwtgl"], item["tmqkssj"], item["tmqjssj"], item["sjhm"], item["lzlglx"], item["qxdwmc"], item["bz"],]
                        list.push(column)
                    }
                    //Excel sheet页的名称
                    var sheet_name = "涉密人员脱密期管理台账"
                    var sum = 20 // 列的数量，根据自身项目进行数据的更改
                    break
                case 'scsmcsb':
                    var smgwglList = getCsglList(new Date().getFullYear())
                    list.push(["涉密场所登记表"]) //确定列名
                    list.push(["上报单位：", sbdw]) //确定列名
                    list.push(["统计年度：", year + '年', "", "", "", "", "", "", "填报时间：", dqsj]) //确定列名
                    list.push(["序号", "场所名称", "责任部门", "涉密程度", "责任人", "责任人电话", "确认日期", "所在地点", "用途", "备注",]) //确定列名
                    let lock5 = 0
                    for (let i in smgwglList) { //每一行的值
                        lock5 = lock5 + 1
                        let item = smgwglList[i] //{"name":"XXX","dw":"XXX","zw":"XXX"}
                        let column = [lock5, item["csmc"], item["zrbm"], item["smcd"], item["zrr"], item["zrrdh"], item["sqrq"], item["szwz"], item["yt"], item["bz"],]
                        list.push(column)
                    }
                    //Excel sheet页的名称
                    var sheet_name = "涉密场所登记表"
                    var sum = 11 // 列的数量，根据自身项目进行数据的更改
                    break
                case 'sccsbg':
                    var smgwglList = getCsbgList(new Date().getFullYear())
                    list.push(["涉密场所变更登记表"]) //确定列名
                    list.push(["上报单位：", sbdw]) //确定列名
                    list.push(["统计年度：", year + '年', "", "", "", "", "", "填报时间：", dqsj]) //确定列名
                    list.push(["序号", "场所名称", "责任部门", "涉密程度", "责任人", "变更日期", "所在地点", "用途", "变更事项",]) //确定列名
                    let lock6 = 0
                    for (let i in smgwglList) { //每一行的值
                        lock6 = lock6 + 1
                        let item = smgwglList[i] //{"name":"XXX","dw":"XXX","zw":"XXX"}
                        let date = new Date(item["gxsj"]);
                        let Y = date.getFullYear() + '年';
                        let M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '月';
                        let D = date.getDate() + '日';
                        item["rq"] = Y + M + D
                        let column = [lock6, item["csmc"], item["zrbm"], item["smcd"], item["zrr"], item["rq"], item["szwz"], item["yt"]?item["yt"]:'', item["bgls"]?item["bgls"].join(","):'',]
                        list.push(column)
                    }
                    //Excel sheet页的名称
                    var sheet_name = "涉密场所变更登记表"
                    var sum = 10 // 列的数量，根据自身项目进行数据的更改
                    break
                case 'scsmjsj':
                    var smgwglList = getSmjsjList(new Date().getFullYear())
                    list.push(["涉密计算机台账"]) //确定列名
                    list.push(["上报单位：", sbdw]) //确定列名
                    list.push(["统计年度：", year + '年', "", "", "", "", "", "", "", "", "", "", "", "", "", "", "填报时间：", dqsj]) //确定列名
                    list.push(["序号", "类型", "品牌型号", "主机序列号", "硬盘序列号", "固定资产编号", "保密编号", "密级", "启用日期", "操作系统", "版本号", "IP地址", "MAC地址", "操作系统安装日期", "责任人", "管理部门", "使用部门", "使用状态",]) //确定列名
                    let lock7 = 0
                    for (let i in smgwglList) { //每一行的值
                        lock6 = lock7 + 1
                        let item = smgwglList[i] //{"name":"XXX","dw":"XXX","zw":"XXX"}
                        let column = [lock7, item["lx"], item["ppxh"], item["zjxlh"], item["ypxlh"], item["zcbh"], item["bmbh"], item["mj"], item["qyrq"], item["czxt"], item["bbh"], item["ipdz"], item["macdz"], item["czxtazrq"], item["zrr"], item["glbm"], item["sybm"], item["syqk"],]
                        list.push(column)
                    }
                    //Excel sheet页的名称
                    var sheet_name = "涉密计算机台账"
                    var sum = 19 // 列的数量，根据自身项目进行数据的更改
                    break
                case 'scydccjz':
                    var smgwglList = getSmydccjzList(new Date().getFullYear())
                    list.push(["涉密移动存储介质台账"]) //确定列名
                    list.push(["上报单位：", sbdw]) //确定列名
                    list.push(["统计年度：", year + '年', "", "", "", "", "", "", "", "", "", "填报时间：", dqsj]) //确定列名
                    list.push(["序号", "移动存储介质名称", "品牌型号", "序列号", "存储容量", "固定资产编号", "保密编号", "密级", "启用日期", "责任人", "管理部门", "存放地点", "使用状态",]) //确定列名
                    let lock8 = 0
                    for (let i in smgwglList) { //每一行的值
                        lock8 = lock8 + 1
                        let item = smgwglList[i] //{"name":"XXX","dw":"XXX","zw":"XXX"}
                        let column = [lock8, item["mc"], item["ppxh"], item["zjxlh"], item["ccrl"], item["zcbh"], item["bmbh"], item["mj"], item["qyrq"], item["zrr"], item["glbm"], item["cfdd"], item["syqk"],]
                        list.push(column)
                    }
                    //Excel sheet页的名称
                    var sheet_name = "涉密移动存储介质台账"
                    var sum = 14 // 列的数量，根据自身项目进行数据的更改
                    break
                case 'scsmbgzdhsb':
                    var smgwglList = getSmbgzdhsbList(new Date().getFullYear())
                    list.push(["涉密办公自动化设备台账"]) //确定列名
                    list.push(["上报单位：", sbdw]) //确定列名
                    list.push(["统计年度：", year + '年', "", "", "", "", "", "", "", "", "", "", "填报时间：", dqsj]) //确定列名
                    list.push(["序号", "涉密办公自动化设备名称", "类型", "品牌型号", "序列号", "固定资产编号", "保密编号", "密级", "启用日期", "IP地址", "MAC地址", "责任人", "管理部门", "使用状态",]) //确定列名
                    let lock9 = 0
                    for (let i in smgwglList) { //每一行的值
                        lock9 = lock9 + 1
                        let item = smgwglList[i] //{"name":"XXX","dw":"XXX","zw":"XXX"}
                        let column = [lock9, item["mc"], item["lx"], item["ppxh"], item["zjxlh"], item["zcbh"], item["bmbh"], item["mj"], item["qyrq"], item["ipdz"], item["macdz"], item["zrr"], item["glbm"], item["syqk"],]
                        list.push(column)
                    }
                    //Excel sheet页的名称
                    var sheet_name = "涉密办公自动化设备台账"
                    var sum = 15 // 列的数量，根据自身项目进行数据的更改
                    break
                case 'scsmwlsb':
                    var smgwglList = getSmwlsbList(new Date().getFullYear())
                    list.push(["涉密网络设备台账"]) //确定列名
                    list.push(["上报单位：", sbdw]) //确定列名
                    list.push(["统计年度：", year + '年', "", "", "", "", "", "", "", "", "", "", "填报时间：", dqsj]) //确定列名
                    list.push(["序号", "涉密网络设备名称", "类型", "品牌型号", "序列号", "固定资产编号", "保密编号", "密级", "启用日期", "IP地址", "MAC地址", "责任人", "管理部门", "使用状态",]) //确定列名
                    let lock10 = 0
                    for (let i in smgwglList) { //每一行的值
                        lock10 = lock10 + 1
                        let item = smgwglList[i] //{"name":"XXX","dw":"XXX","zw":"XXX"}
                        let column = [lock10, item["mc"], item["lx"], item["ppxh"], item["zjxlh"], item["zcbh"], item["bmbh"], item["mj"], item["qyrq"], item["ipdz"], item["macdz"], item["zrr"], item["glbm"], item["syqk"],]
                        list.push(column)
                    }
                    //Excel sheet页的名称
                    var sheet_name = "涉密网络设备台账"
                    var sum = 15 // 列的数量，根据自身项目进行数据的更改
                    break
                case 'scaqcp':
                    var smgwglList = getAqcpList(new Date().getFullYear())
                    list.push(["安全产品台账"]) //确定列名
                    list.push(["上报单位：", sbdw]) //确定列名
                    list.push(["统计年度：", year + '年', "", "", "", "", "填报时间：", dqsj]) //确定列名
                    list.push(["序号", "类型", "品牌型号", "资产编号", "启用日期", "用途", "安装位置", "数量",]) //确定列名
                    let lock11 = 0
                    for (let i in smgwglList) { //每一行的值
                        lock11 = lock11 + 1
                        let item = smgwglList[i] //{"name":"XXX","dw":"XXX","zw":"XXX"}
                        let column = [lock11, item["lx"], item["ppxh"], item["zcbh"], item["qyrq"], item["yt"], item["azwz"], item["sl"],]
                        list.push(column)
                    }
                    //Excel sheet页的名称
                    var sheet_name = "安全产品台账"
                    var sum = 9 // 列的数量，根据自身项目进行数据的更改
                    break
                case 'scsmzt':
                    var smgwglList = getSmzttzList(new Date().getFullYear())
                    list.push(["涉密载体台账"]) //确定列名
                    list.push(["上报单位：", sbdw]) //确定列名
                    list.push(["统计年度：", year + '年', "", "", "", "", "", "", "", "", "", "", "", "填报时间：", dqsj]) //确定列名
                    list.push(["序号", "载体名称", "载体编号", "载体类型", "生成原因", "密级", "保密期限", "份数", "页数", "知悉范围", "生成日期", "生成部门", "责任人", "保管位置", "使用状态",]) //确定列名
                    let lock12 = 0
                    for (let i in smgwglList) { //每一行的值
                        lock11 = lock12 + 1
                        let item = smgwglList[i] //{"name":"XXX","dw":"XXX","zw":"XXX"}
                        let column = [lock2, item["mc"], item["ztbh"], item["lx"], item["scyy"], item["mj"], item["bmqx"], item["fs"], item["ys"], item["zxfw"], item["scrq"], item["scbm"], item["zrr"], item["bmwz"], item["zt"],]
                        list.push(column)
                    }
                    //Excel sheet页的名称
                    var sheet_name = "涉密载体台账"
                    var sum = 16 // 列的数量，根据自身项目进行数据的更改
                    break
                case 'scsmryxz':
                    var smgwglList = getRyxzhzList(new Date().getFullYear())
                    list.push(["涉密人员新增台账"]) //确定列名
                    list.push(["上报单位：", sbdw]) //确定列名
                    list.push(["统计年度：", year + '年', "", "", "", "", "", "", "", "填报时间：", dqsj]) //确定列名
                    list.push(["序号", "姓名", "部门", "岗位名称", "涉密等级", "职务", "职级", "职称", "是否审查", "上岗时间", "备注",]) //确定列名
                    let lock13 = 0
                    for (let i in smgwglList) { //每一行的值
                        lock13 = lock13 + 1
                        let item = smgwglList[i] //{"name":"XXX","dw":"XXX","zw":"XXX"}
                        let column = [lock13, item["xm"], item["bm"], item["gwmc"], item["smdj"], item["zw"], item["zj"], item["zc"], item["sfsc"], item["sgsj"], item["bz"],]
                        list.push(column)
                    }
                    //Excel sheet页的名称
                    var sheet_name = "涉密人员新增台账"
                    var sum = 12 // 列的数量，根据自身项目进行数据的更改
                    break
                case 'scbmzddj':
                    var smgwglList = getAllBmzd(new Date().getFullYear())
                    list.push(["保密制度登记"]) //确定列名
                    list.push(["上报单位：", sbdw]) //确定列名
                    list.push(["统计年度：", year + '年', "", "", "填报时间：", dqsj]) //确定列名
                    list.push(["序号", "保密制度名称", "文号", "颁发日期", "实施日期", "备注",]) //确定列名
                    let lock14 = 0
                    for (let i in smgwglList) { //每一行的值
                        lock14 = lock14 + 1
                        let item = smgwglList[i] //{"name":"XXX","dw":"XXX","zw":"XXX"}
                        let column = [lock14, item["wjm"], item["wh"], item["bfrq"], item["ssrq"], item["bz"],]
                        list.push(column)
                    }
                    //Excel sheet页的名称
                    var sheet_name = "保密制度登记"
                    var sum = 7 // 列的数量，根据自身项目进行数据的更改
                    break
                default:
                    console.log(0)
            }

            var sheet = XLSX.utils.aoa_to_sheet(list)
            // 设置列宽（这里用到列的数量是用来设置不同列的不同宽度的）
            let counts = smgwglList.length + 5
            sheet['!cols'] = []
            sheet['!rows'] = []
            for (let i = 1; i < sum; i++) {
                sheet['!cols'].push({ wpx: 150 }) // 设置列宽，只有最后一列的宽度是不同的
            }
            for (let j = 1; j < counts; j++) {
                sheet['!rows'].push({ hpx: 14 }) // 设置列宽，只有最后一列的宽度是不同的
            }
            sheet['!rows'][0].hpx = 35
            sheet['!rows'][1].hpx = 20
            sheet['!rows'][2].hpx = 20
            sheet['!rows'][3].hpx = 20
            // 所有设置边框字体水平居中等样式
            for (let key in sheet) {
                if (sheet[key] instanceof Object) {
                    sheet[key].s = {
                        alignment: {
                            horizontal: 'center', // 水平居中
                            vertical: 'center' // 垂直居中
                        },
                        font: {
                            sz: 11, // 字号
                            name: '宋体' // 字体
                        },
                        border: {  // 边框
                            top: {
                                style: 'thin'
                            },
                            bottom: {
                                style: 'thin'
                            },
                            left: {
                                style: 'thin'
                            },
                            right: {
                                style: 'thin'
                            }
                        }
                    }
                }
            }
            // 标题样式修改
            sheet.A1.s = {
                font: {
                    name: '宋体',
                    sz: 16, // 字号
                    bold: true,
                },
                alignment: {
                    horizontal: 'center', // 水平居中
                    vertical: 'center' // 垂直居中
                }
            }
            //switch case
            switch (name) {
                case 'scsmgwhz':
                    // 标题以及输入性标题去掉border
                    sheet.A2.s.border = sheet.A3.s.border = sheet.B2.s.border = sheet.B3.s.border = sheet.F3.s.border = sheet.B3.s.border = sheet.C3.s.border = sheet.D3.s.border = sheet.E3.s.border = sheet.G3.s.border = sheet.H3.s.border = {}
                    // 标题以及输入性标题以及excel首行文字加粗
                    sheet.A2.s.font.bold = sheet.A3.s.font.bold = sheet.F3.s.font.bold = sheet.G3.s.font.bold = sheet.A4.s.font.bold = sheet.B4.s.font.bold = sheet.C4.s.font.bold = sheet.D4.s.font.bold = sheet.E4.s.font.bold = sheet.F4.s.font.bold = sheet.G4.s.font.bold = sheet.H4.s.font.bold = true
                    sheet['!merges'] = [
                        { s: { r: 0, c: 0 }, e: { r: 0, c: 7 } }
                    ];
                    break
                case 'scsmryhz':
                    sheet.A2.s.border = sheet.A3.s.border = sheet.B2.s.border = sheet.B3.s.border = sheet.F3.s.border = sheet.B3.s.border = sheet.C3.s.border = sheet.D3.s.border = sheet.E3.s.border = sheet.F3.s.border = sheet.G3.s.border = sheet.H3.s.border = sheet.I3.s.border = sheet.J3.s.border = sheet.K3.s.border = sheet.L3.s.border = sheet.M3.s.border = sheet.N3.s.border = sheet.O3.s.border = sheet.P3.s.border = sheet.Q3.s.border = sheet.R3.s.border = sheet.S3.s.border = {}
                    sheet.A2.s.font.bold = sheet.R3.s.font.bold = sheet.A3.s.font.bold = sheet.F3.s.font.bold = sheet.G3.s.font.bold = sheet.A4.s.font.bold = sheet.B4.s.font.bold = sheet.C4.s.font.bold = sheet.D4.s.font.bold = sheet.E4.s.font.bold = sheet.F4.s.font.bold = sheet.G4.s.font.bold = sheet.H4.s.font.bold = sheet.I4.s.font.bold = sheet.J4.s.font.bold = sheet.K4.s.font.bold = sheet.L4.s.font.bold = sheet.M4.s.font.bold = sheet.N4.s.font.bold = sheet.O4.s.font.bold = sheet.P4.s.font.bold = sheet.Q4.s.font.bold = sheet.R4.s.font.bold = sheet.S4.s.font.bold = true
                    sheet['!merges'] = [
                        { s: { r: 0, c: 0 }, e: { r: 0, c: 18 } }
                    ];
                    break
                case 'scsmrygwbg':
                    sheet.A2.s.border = sheet.A3.s.border = sheet.B2.s.border = sheet.B3.s.border = sheet.F3.s.border = sheet.B3.s.border = sheet.C3.s.border = sheet.D3.s.border = sheet.E3.s.border = sheet.F3.s.border = sheet.G3.s.border = sheet.H3.s.border = sheet.I3.s.border = sheet.J3.s.border = sheet.K3.s.border = sheet.L3.s.border = {}
                    sheet.A2.s.font.bold = sheet.K3.s.font.bold = sheet.A3.s.font.bold = sheet.F3.s.font.bold = sheet.G3.s.font.bold = sheet.A4.s.font.bold = sheet.B4.s.font.bold = sheet.C4.s.font.bold = sheet.D4.s.font.bold = sheet.E4.s.font.bold = sheet.F4.s.font.bold = sheet.G4.s.font.bold = sheet.H4.s.font.bold = sheet.I4.s.font.bold = sheet.J4.s.font.bold = sheet.K4.s.font.bold = sheet.L4.s.font.bold = true
                    sheet['!merges'] = [
                        { s: { r: 0, c: 0 }, e: { r: 0, c: 11 } }
                    ];
                    break
                case 'scsmrytmqgl':
                    sheet.A2.s.border = sheet.A3.s.border = sheet.B2.s.border = sheet.B3.s.border = sheet.F3.s.border = sheet.B3.s.border = sheet.C3.s.border = sheet.D3.s.border = sheet.E3.s.border = sheet.F3.s.border = sheet.G3.s.border = sheet.H3.s.border = sheet.I3.s.border = sheet.J3.s.border = sheet.K3.s.border = sheet.L3.s.border = sheet.M3.s.border = sheet.N3.s.border = sheet.O3.s.border = sheet.P3.s.border = sheet.Q3.s.border = sheet.R3.s.border = sheet.S3.s.border = {}
                    sheet.A2.s.font.bold = sheet.R3.s.font.bold = sheet.A3.s.font.bold = sheet.F3.s.font.bold = sheet.G3.s.font.bold = sheet.A4.s.font.bold = sheet.B4.s.font.bold = sheet.C4.s.font.bold = sheet.D4.s.font.bold = sheet.E4.s.font.bold = sheet.F4.s.font.bold = sheet.G4.s.font.bold = sheet.H4.s.font.bold = sheet.I4.s.font.bold = sheet.J4.s.font.bold = sheet.K4.s.font.bold = sheet.L4.s.font.bold = sheet.M4.s.font.bold = sheet.N4.s.font.bold = sheet.O4.s.font.bold = sheet.P4.s.font.bold = sheet.Q4.s.font.bold = sheet.R4.s.font.bold = sheet.S4.s.font.bold = true
                    sheet['!merges'] = [
                        { s: { r: 0, c: 0 }, e: { r: 0, c: 18 } }
                    ];
                    break
                case 'scsmcsb':
                    sheet.A2.s.border = sheet.A3.s.border = sheet.B2.s.border = sheet.B3.s.border = sheet.F3.s.border = sheet.B3.s.border = sheet.C3.s.border = sheet.D3.s.border = sheet.E3.s.border = sheet.F3.s.border = sheet.G3.s.border = sheet.H3.s.border = sheet.I3.s.border = sheet.J3.s.border = {}
                    sheet.A2.s.font.bold = sheet.I3.s.font.bold = sheet.A3.s.font.bold = sheet.F3.s.font.bold = sheet.G3.s.font.bold = sheet.A4.s.font.bold = sheet.B4.s.font.bold = sheet.C4.s.font.bold = sheet.D4.s.font.bold = sheet.E4.s.font.bold = sheet.F4.s.font.bold = sheet.G4.s.font.bold = sheet.H4.s.font.bold = sheet.I4.s.font.bold = sheet.J4.s.font.bold = true
                    sheet['!merges'] = [
                        { s: { r: 0, c: 0 }, e: { r: 0, c: 9 } }
                    ];
                case 'sccsbg':
                    sheet.A2.s.border = sheet.A3.s.border = sheet.B2.s.border = sheet.B3.s.border = sheet.F3.s.border = sheet.B3.s.border = sheet.C3.s.border = sheet.D3.s.border = sheet.E3.s.border = sheet.F3.s.border = sheet.G3.s.border = sheet.H3.s.border = sheet.I3.s.border = {}
                    sheet.A2.s.font.bold = sheet.H3.s.font.bold = sheet.A3.s.font.bold = sheet.F3.s.font.bold = sheet.G3.s.font.bold = sheet.A4.s.font.bold = sheet.B4.s.font.bold = sheet.C4.s.font.bold = sheet.D4.s.font.bold = sheet.E4.s.font.bold = sheet.F4.s.font.bold = sheet.G4.s.font.bold = sheet.H4.s.font.bold = sheet.I4.s.font.bold = true
                    sheet['!merges'] = [
                        { s: { r: 0, c: 0 }, e: { r: 0, c: 8 } }
                    ];
                    break
                case 'scsmjsj':
                    sheet.A2.s.border = sheet.A3.s.border = sheet.B2.s.border = sheet.B3.s.border = sheet.F3.s.border = sheet.B3.s.border = sheet.C3.s.border = sheet.D3.s.border = sheet.E3.s.border = sheet.F3.s.border = sheet.G3.s.border = sheet.H3.s.border = sheet.I3.s.border = sheet.J3.s.border = sheet.K3.s.border = sheet.L3.s.border = sheet.M3.s.border = sheet.N3.s.border = sheet.O3.s.border = sheet.P3.s.border = sheet.Q3.s.border = sheet.R3.s.border = {}
                    sheet.A2.s.font.bold = sheet.Q3.s.font.bold = sheet.A3.s.font.bold = sheet.F3.s.font.bold = sheet.G3.s.font.bold = sheet.A4.s.font.bold = sheet.B4.s.font.bold = sheet.C4.s.font.bold = sheet.D4.s.font.bold = sheet.E4.s.font.bold = sheet.F4.s.font.bold = sheet.G4.s.font.bold = sheet.H4.s.font.bold = sheet.I4.s.font.bold = sheet.J4.s.font.bold = sheet.K4.s.font.bold = sheet.L4.s.font.bold = sheet.M4.s.font.bold = sheet.N4.s.font.bold = sheet.O4.s.font.bold = sheet.P4.s.font.bold = sheet.Q4.s.font.bold = sheet.R4.s.font.bold = true
                    sheet['!merges'] = [
                        { s: { r: 0, c: 0 }, e: { r: 0, c: 17 } }
                    ];
                    break
                case 'scydccjz':
                    sheet.A2.s.border = sheet.A3.s.border = sheet.B2.s.border = sheet.B3.s.border = sheet.F3.s.border = sheet.B3.s.border = sheet.C3.s.border = sheet.D3.s.border = sheet.E3.s.border = sheet.F3.s.border = sheet.G3.s.border = sheet.H3.s.border = sheet.I3.s.border = sheet.J3.s.border = sheet.K3.s.border = sheet.L3.s.border = sheet.M3.s.border = {}
                    sheet.A2.s.font.bold = sheet.L3.s.font.bold = sheet.A3.s.font.bold = sheet.F3.s.font.bold = sheet.G3.s.font.bold = sheet.A4.s.font.bold = sheet.B4.s.font.bold = sheet.C4.s.font.bold = sheet.D4.s.font.bold = sheet.E4.s.font.bold = sheet.F4.s.font.bold = sheet.G4.s.font.bold = sheet.H4.s.font.bold = sheet.I4.s.font.bold = sheet.J4.s.font.bold = sheet.K4.s.font.bold = sheet.L4.s.font.bold = sheet.M4.s.font.bold = true
                    sheet['!merges'] = [
                        { s: { r: 0, c: 0 }, e: { r: 0, c: 12 } }
                    ];
                    break
                case 'scsmbgzdhsb':
                    sheet.A2.s.border = sheet.A3.s.border = sheet.B2.s.border = sheet.B3.s.border = sheet.F3.s.border = sheet.B3.s.border = sheet.C3.s.border = sheet.D3.s.border = sheet.E3.s.border = sheet.F3.s.border = sheet.G3.s.border = sheet.H3.s.border = sheet.I3.s.border = sheet.J3.s.border = sheet.K3.s.border = sheet.L3.s.border = sheet.M3.s.border = sheet.N3.s.border = {}
                    sheet.A2.s.font.bold = sheet.M3.s.font.bold = sheet.A3.s.font.bold = sheet.F3.s.font.bold = sheet.G3.s.font.bold = sheet.A4.s.font.bold = sheet.B4.s.font.bold = sheet.C4.s.font.bold = sheet.D4.s.font.bold = sheet.E4.s.font.bold = sheet.F4.s.font.bold = sheet.G4.s.font.bold = sheet.H4.s.font.bold = sheet.I4.s.font.bold = sheet.J4.s.font.bold = sheet.K4.s.font.bold = sheet.L4.s.font.bold = sheet.M4.s.font.bold = sheet.N4.s.font.bold = true
                    sheet['!merges'] = [
                        { s: { r: 0, c: 0 }, e: { r: 0, c: 13 } }
                    ];
                    break
                case 'scsmwlsb':
                    sheet.A2.s.border = sheet.A3.s.border = sheet.B2.s.border = sheet.B3.s.border = sheet.F3.s.border = sheet.B3.s.border = sheet.C3.s.border = sheet.D3.s.border = sheet.E3.s.border = sheet.F3.s.border = sheet.G3.s.border = sheet.H3.s.border = sheet.I3.s.border = sheet.J3.s.border = sheet.K3.s.border = sheet.L3.s.border = sheet.M3.s.border = sheet.N3.s.border = {}
                    sheet.A2.s.font.bold = sheet.M3.s.font.bold = sheet.A3.s.font.bold = sheet.F3.s.font.bold = sheet.G3.s.font.bold = sheet.A4.s.font.bold = sheet.B4.s.font.bold = sheet.C4.s.font.bold = sheet.D4.s.font.bold = sheet.E4.s.font.bold = sheet.F4.s.font.bold = sheet.G4.s.font.bold = sheet.H4.s.font.bold = sheet.I4.s.font.bold = sheet.J4.s.font.bold = sheet.K4.s.font.bold = sheet.L4.s.font.bold = sheet.M4.s.font.bold = sheet.N4.s.font.bold = true
                    sheet['!merges'] = [
                        { s: { r: 0, c: 0 }, e: { r: 0, c: 13 } }
                    ];
                    break
                case 'scaqcp':
                    sheet.A2.s.border = sheet.A3.s.border = sheet.B2.s.border = sheet.B3.s.border = sheet.F3.s.border = sheet.B3.s.border = sheet.C3.s.border = sheet.D3.s.border = sheet.E3.s.border = sheet.F3.s.border = sheet.G3.s.border = sheet.H3.s.border = {}
                    sheet.A2.s.font.bold = sheet.G3.s.font.bold = sheet.A3.s.font.bold = sheet.F3.s.font.bold = sheet.G3.s.font.bold = sheet.A4.s.font.bold = sheet.B4.s.font.bold = sheet.C4.s.font.bold = sheet.D4.s.font.bold = sheet.E4.s.font.bold = sheet.F4.s.font.bold = sheet.G4.s.font.bold = sheet.H4.s.font.bold = true
                    sheet['!merges'] = [
                        { s: { r: 0, c: 0 }, e: { r: 0, c: 7 } }
                    ];
                    break
                case 'scsmzt':
                    sheet.A2.s.border = sheet.A3.s.border = sheet.B2.s.border = sheet.B3.s.border = sheet.F3.s.border = sheet.B3.s.border = sheet.C3.s.border = sheet.D3.s.border = sheet.E3.s.border = sheet.F3.s.border = sheet.G3.s.border = sheet.H3.s.border = sheet.I3.s.border = sheet.J3.s.border = sheet.K3.s.border = sheet.L3.s.border = sheet.M3.s.border = sheet.N3.s.border = sheet.O3.s.border = {}
                    sheet.A2.s.font.bold = sheet.N3.s.font.bold = sheet.A3.s.font.bold = sheet.F3.s.font.bold = sheet.G3.s.font.bold = sheet.A4.s.font.bold = sheet.B4.s.font.bold = sheet.C4.s.font.bold = sheet.D4.s.font.bold = sheet.E4.s.font.bold = sheet.F4.s.font.bold = sheet.G4.s.font.bold = sheet.H4.s.font.bold = sheet.I4.s.font.bold = sheet.J4.s.font.bold = sheet.K4.s.font.bold = sheet.L4.s.font.bold = sheet.M4.s.font.bold = sheet.N4.s.font.bold = sheet.O4.s.font.bold = true
                    sheet['!merges'] = [
                        { s: { r: 0, c: 0 }, e: { r: 0, c: 14 } }
                    ];
                    break
                case 'scsmryxz':
                    sheet.A2.s.border = sheet.A3.s.border = sheet.B2.s.border = sheet.B3.s.border = sheet.F3.s.border = sheet.B3.s.border = sheet.C3.s.border = sheet.D3.s.border = sheet.E3.s.border = sheet.F3.s.border = sheet.G3.s.border = sheet.H3.s.border = sheet.I3.s.border = sheet.J3.s.border = sheet.K3.s.border = {}
                    sheet.A2.s.font.bold = sheet.J3.s.font.bold = sheet.A3.s.font.bold = sheet.F3.s.font.bold = sheet.G3.s.font.bold = sheet.A4.s.font.bold = sheet.B4.s.font.bold = sheet.C4.s.font.bold = sheet.D4.s.font.bold = sheet.E4.s.font.bold = sheet.F4.s.font.bold = sheet.G4.s.font.bold = sheet.H4.s.font.bold = sheet.I4.s.font.bold = sheet.J4.s.font.bold = sheet.K4.s.font.bold = true
                    sheet['!merges'] = [
                        { s: { r: 0, c: 0 }, e: { r: 0, c: 10 } }
                    ];
                    break
                case 'scbmzddj':
                    sheet.A2.s.border = sheet.A3.s.border = sheet.B2.s.border = sheet.B3.s.border = sheet.F3.s.border = sheet.B3.s.border = sheet.C3.s.border = sheet.D3.s.border = sheet.E3.s.border = sheet.F3.s.border = {}
                    sheet.A2.s.font.bold = sheet.E3.s.font.bold = sheet.A3.s.font.bold = sheet.F3.s.font.bold = sheet.A4.s.font.bold = sheet.B4.s.font.bold = sheet.C4.s.font.bold = sheet.D4.s.font.bold = sheet.E4.s.font.bold = sheet.F4.s.font.bold = true
                    sheet['!merges'] = [
                        { s: { r: 0, c: 0 }, e: { r: 0, c: 5 } }
                    ];
                    break
                default:
                    console.log(0)
            }
            //新建book
            var work_book = XLSX.utils.book_new()
            //将数据添加到工作薄
            XLSX.utils.book_append_sheet(work_book, sheet, sheet_name)
            const workbookArrayBuffer = workbook2ArrayBuffer(work_book)
            //arrayBuffer转Buffer
            var buf = new Buffer(workbookArrayBuffer.byteLength)
            var view = new Uint8Array(workbookArrayBuffer)
            for (var i = 0; i < buf.length; ++i) {
                buf[i] = view[i]
            }
            return buf
        },
        // 生成单位检查评分记录excel
        generateDwJcjlExcel(zczpls) {
            if (!zczpls || !zczpls.rwid) {
                this.$message.warning('[单位检查]检查任务信息异常，无法下载检查信息')
                return
            }
            //sheet页数据
            let list = []
            list.push(["检查类", "检查项", "检查内容", "分值", "实有内容", "得分", "扣分标准", "评分说明"])
            // 通过任务ID获取单位评分记录历史信息
            const dwxxzcjlList = selectDwpfjlListByRwid(zczpls.rwid)
            if (!dwxxzcjlList) {
                this.$message.warning('检查任务评分信息异常，无法下载检查信息')
                return
            }
            dwxxzcjlList.forEach(dx => {
                dx.xx.forEach(nr => {
                    list.push([
                        dx.dxmc, nr.xxmc, nr.nr, nr.fz, nr.synr, nr.fz - nr.ykf, nr.kfbz, nr.kfsm
                    ])
                })
            })
            //Excel sheet页的名称
            var sheet_name = "单位详细检查记录"
            var sheet = XLSX.utils.aoa_to_sheet(list)
            //新建book
            var work_book = XLSX.utils.book_new()
            //将数据添加到工作薄
            XLSX.utils.book_append_sheet(work_book, sheet, sheet_name)
            const workbookArrayBuffer = workbook2ArrayBuffer(work_book)
            //arrayBuffer转Buffer
            var buf = new Buffer(workbookArrayBuffer.byteLength)
            var view = new Uint8Array(workbookArrayBuffer)
            for (var i = 0; i < buf.length; ++i) {
                buf[i] = view[i]
            }
            return buf
        },
        // 生成内设机构检查评分记录excel
        generateNsjgJcjlExcel(zczpls) {
            if (!zczpls || !zczpls.rwid) {
                this.$message.warning('[内设机构]检查任务信息异常，无法下载检查信息')
                return
            }
            // 获取所有抽查的内设机构
            let params = {
                rwid: zczpls.rwid
            }
            let ccdnsjgListPage = selectCcdnsjgListByRwid(params)
            let ccdnsjgList
            if (ccdnsjgListPage) {
                ccdnsjgList = ccdnsjgListPage.list_total
            }
            if (!ccdnsjgList) {
                this.$message.warning('[内设机构]抽查的内设机构信息异常，无法下载检查信息')
                return
            }
            let list
            let sheet_name
            let sheet
            let work_book = XLSX.utils.book_new()
            // 判断是否全部数据都正确获取
            let allCheck = true
            let errorNsjgmc
            let zzjgmc
            ccdnsjgList.some(item => {
                list = []
                list.push(["检查类", "检查内容", "分值", "实有内容", "得分", "扣分标准", "评分说明"])
                zzjgmc = item.zzjgmc
                if (zzjgmc) {
                    sheet_name = zzjgmc.replace(/[\\/?*]/g, '-')
                    sheet_name = sheet_name.substring(sheet_name.lastIndexOf('-') + 1)
                } else {
                    sheet_name = ''
                }
                // 获取内设机构评分记录信息
                let bmxxzcjlList = selectNsjgpfjlListByCcdnsjgid(item.ccdnsjgid)
                if (!bmxxzcjlList) {
                    allCheck = false
                    errorNsjgmc = item.nsjgmc
                    return true
                }
                bmxxzcjlList.forEach(nsjgpfjlItem => {
                    list.push([nsjgpfjlItem.dxmc, nsjgpfjlItem.nr, nsjgpfjlItem.fz, nsjgpfjlItem.synr, nsjgpfjlItem.fz - nsjgpfjlItem.ykf, nsjgpfjlItem.kfbz, nsjgpfjlItem.kfsm])
                })
                // 将数据添加到sheet页
                sheet = XLSX.utils.aoa_to_sheet(list)
                //将数据添加到工作薄
                XLSX.utils.book_append_sheet(work_book, sheet, sheet_name)
            })
            if (!allCheck) {
                this.$message.error('[内设机构]' + errorNsjgmc + '数据异常，无法下载检查信息')
                return
            }
            ////////////
            const workbookArrayBuffer = workbook2ArrayBuffer(work_book)
            //arrayBuffer转Buffer
            var buf = new Buffer(workbookArrayBuffer.byteLength)
            var view = new Uint8Array(workbookArrayBuffer)
            for (var i = 0; i < buf.length; ++i) {
                buf[i] = view[i]
            }
            return buf
        },
        // 生成人员检查评分记录excel
        generateRyJcjlExcel(zczpls) {
            if (!zczpls || !zczpls.rwid) {
                this.$message.warning('[涉密人员]检查任务信息异常，无法下载检查信息')
                return
            }
            // 获取所有抽查的涉密人员
            let params = {
                rwid: zczpls.rwid
            }
            let ccdnryListPage = selectCcdryListPage(params)
            let ccdryList
            if (ccdnryListPage) {
                ccdryList = ccdnryListPage.list_total
            }
            if (!ccdryList) {
                this.$message.warning('[涉密人员]抽查的涉密人员信息异常，无法下载检查信息')
                return
            }
            let list
            let sheet_name
            let sheet
            let work_book = XLSX.utils.book_new()
            // 判断是否全部数据都正确获取
            let allCheck = true
            let errorRyxm
            //
            ccdryList.some(item => {
                list = []
                list.push(["检查类", "检查内容", "是否符合要求", "备注"])
                // sheet_name = item.xm + '-' + item.bm + '-' + item.zw
                sheet_name = item.xm + '-' + item.zw
                sheet_name = sheet_name.replace(/[\\/?*]/g, '-')
                // 获取人员评分记录信息
                params = {
                    ccdryid: item.ccdryid
                }
                let ryzcjlList = selectRypfjlListByCcdryid(item.ccdryid)
                if (!ryzcjlList) {
                    allCheck = false
                    errorRyxm = item.xm
                    return true
                }
                ryzcjlList.forEach(rypfjlItem => {
                    list.push([rypfjlItem.dxmc, rypfjlItem.nr, rypfjlItem.sffhyq ? '是' : '否', rypfjlItem.bzsm])
                })
                // 将数据添加到sheet页
                sheet = XLSX.utils.aoa_to_sheet(list)
                //将数据添加到工作薄
                XLSX.utils.book_append_sheet(work_book, sheet, sheet_name)
            })
            if (!allCheck) {
                this.$message.error('[涉密人员]' + errorRyxm + '数据异常，无法下载检查信息')
                return
            }
            ////////////
            const workbookArrayBuffer = workbook2ArrayBuffer(work_book)
            //arrayBuffer转Buffer
            var buf = new Buffer(workbookArrayBuffer.byteLength)
            var view = new Uint8Array(workbookArrayBuffer)
            for (var i = 0; i < buf.length; ++i) {
                buf[i] = view[i]
            }
            return buf
        },
        // 生成检查报告word
        generateJcbgWord(zczpls) {
            if (!zczpls || !zczpls.rwid) {
                this.$message.warning('[检查报告]检查任务信息异常，无法下载检查信息')
                return
            }
            // 检查报告数据
            let wordData = {
                dwmc: '',
                lxr: '',
                zw: '',
                lxdh: '',
                //
                jcjd: '',
                ksrq: '',
                jsrq: '',
                //
                df: 100,
                synrList: [],
                kfsmList: [],
                //
                nsjgList: [],
                ryList: [],
                jcpj: '',
                pjyj: ''
            }
            // 通过任务ID获取单位信息
            let dwxx = selectDwxxByRwid(zczpls.rwid)
            if (dwxx) {
                wordData.dwmc = dwxx.dwmc
                wordData.lxr = dwxx.dwlxr
                wordData.zw = dwxx.zw
                wordData.lxdh = dwxx.dwlxdh
            }
            // 通过任务ID获取任务信息
            let rwxx = selectScrwByRwid(zczpls.rwid)
            if (rwxx) {
                wordData.jcjd = rwxx.jcjdmc
                wordData.ksrq = dateFormatNYRChinese(rwxx.kssj)
                wordData.jsrq = dateFormatNYRChinese(rwxx.jzsj)
                let pjjg = rwxx.pjjg
                switch (pjjg) {
                    case 1:
                        pjjg = '优秀'
                        break
                    case 2:
                        pjjg = '合格'
                        break
                    case 3:
                        pjjg = '基本合格'
                        break
                    case 4:
                        pjjg = '不合格'
                        break
                    default:
                        pjjg = '未知'
                        break
                }
                wordData.pjjg = pjjg
                wordData.pjyj = rwxx.pjyj
            }
            // 通过任务ID获取单位评分记录信息(非组合)
            const dwpfjlList = selectDwpfjlListByRwidFzh(zczpls.rwid)
            dwpfjlList.forEach(item => {
                if (item.ykf) {
                    wordData.df -= item.ykf
                }
                if (item.sfsynr && item.synr) {
                    wordData.synrList.push({
                        item: (wordData.synrList.length + 1) + '、' + item.synr
                    })
                }
                if (item.kfsm) {
                    wordData.kfsmList.push({
                        item: (wordData.kfsmList.length + 1) + '、' + item.kfsm
                    })
                }
            })
            // 获取内设机构抽查结果
            wordData.nsjgList = selectCcdsjgListCcjg(zczpls.rwid)
            // console.log('检查报告nsjgList', wordData.nsjgList)
            // 获取人员抽查结果
            wordData.ryList = selectCcdryListCcjg(zczpls.rwid)
            // console.log('检查报告ryList', wordData.ryList)
            // console.log('检查报告wordData', wordData)
            //////////////
            return exportWord(getDocZczpMbPath(), wordData)
        },
    },
    mounted() {
        // 获取各列表数据看是否完成 状态管理
        this.bmzhIsPerfect()
        this.zzyhIsPerfect()
        this.smgwIsPerfect()
        this.ryhzIsPerfect()
        this.xzryxxIsPerfect()
        this.gwbgIsPerfect()
        this.lglzIsPerfect()
        this.csxxIsPerfect()
        this.bgxxIsPerfect()

        this.jsjtzIsPerfect()
        this.ydccjzIsPerfect()
        this.bgzdhsbIsPerfect()
        this.wlsbIsPerfect()
        this.aqcpPerfect()

        this.zttzPerfect()

        this.zczp1Perfect()
        this.zczp2Perfect()
        this.zczp3Perfect()
        this.zczp4Perfect()
    }
}