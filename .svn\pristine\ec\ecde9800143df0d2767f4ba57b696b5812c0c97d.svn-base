import DB from './adapter/zczpAdaptor'

import { getUuid } from '../utils/getUuid'

import MD5 from 'md5'

/*****
 * 用户表
 * ******/
// 插入用户表
export const insertYh = (params) => {
  // 校验
  if (!params) {
    console.log('注册用户失败，参数为空', params)
    return false
  }
  let yhm = params.yhm
  let findObj = DB.get('yh').find({ yhm: yhm }).cloneDeep().value()
  if (findObj) {
    return false
  }
  params.yhid = getUuid()
  DB.get('yh').push(params).write()
  return true
}

// 通过用户名查询用户表
export const selectYhByYhm = (yhm) => {
  //
  if (!yhm) {
    console.log('通过用户名查询用户失败，参数为空', yhm)
    return true
  }
  return DB.get('yh').find({ yhm: yhm }).cloneDeep().value()
}

// 通过用户名和密码查询用户表
export const selectYhByYhmPassword = (params) => {
  //
  let yhm
  let password
  if (!params) {
    console.log('查询用户失败，参数为空', params)
    return true
  }
  yhm = params.yhm
  password = MD5(params.password)
  return DB.get('yh')
    .filter((item) => {
      if (item.yhm == yhm && item.password == password) {
        return item
      }
    })
    .cloneDeep()
    .value()
}

// 通过用户ID修改用户信息
export const updateYhByYhid = (params) => {
  //
  let yhid
  let password
  let xm
  if (!params) {
    console.log('修改用户信息失败，参数为空', params)
    return false
  }
  yhid = params.yhid
  if (!yhid || yhid == '') {
    console.log('修改用户信息失败，用户ID为空', params)
    return false
  }
  // 密码判断，明文密码最大长度不会超过16，MD5密码为32
  password = params.password
  if (password) {
    if (password.length == 32) {
      // MD5，直接修改即可
    } else {
      params.password = MD5(password)
    }
  }
  DB.get('yh').find({ yhid: yhid }).assign(params).write()
  return true
}

// 查询用户信息
export const selectYh = (params) => {
  if (!params) {
    return DB.get('yh').cloneDeep().value()
  }
  return DB.get('yh').filter(params).cloneDeep().value()
}

// 获取所有非admin用户集合(20230203超级管理员用户admin更改为root)
export const selectYhNotAdmin = () => {
  return DB.get('yh').filter((item) => {
    if(item.yhm != 'root') {
      return item
    }
  }).cloneDeep().value()
}

