const LOW = require('lowdb')
const FileSyncZczpSystem = require('lowdb/adapters/FileSync')

// 打windows包时解开
require('../../utils/databaseSystemUtils')

// 获取不到工具类方法中定义的变量（问题未知）
// const adapterZczpSystem = new FileSyncZczpSystem(getDatabaseSystemFileSavePath())
const adapterZczpSystem = new FileSyncZczpSystem('C:\\hsoft\\data\\database\\system.json')
// const adapterZczpSystem = new FileSyncZczpSystem('system.json')
// const adapterZczpSystem = new FileSyncZczpSystem('/opt/secretkeeper/data/database/system.json')

const dbZczpSystem = LOW(adapterZczpSystem)

dbZczpSystem
  .defaults({
    "wjxg_list": [
      {
        "csbs": "lswjcclj",
        "cssm": "临时文件存储路径",
        "csz": "C:\\hsoft\\temp\\",
        "fzh": "1",
        "filesettingid": "7DDB5932-8ADB-48B2-82BF-F2E11CFCB19F",
        "gxsj": 1669202852584
      },
      {
        "csbs": "rzwjccjl",
        "cssm": "日志文件存储路径",
        "csz": "C:\\hsoft\\archive\\",
        "fzh": "1",
        "filesettingid": "860C14E5-9BB7-438F-8848-58F79BC6A751",
        "gxsj": 1669202845347
      },
      {
        "csbs": "fjcclj",
        "cssm": "附件存储路径",
        "fzh": "1",
        "filesettingid": "DF32E27C-2DA5-4521-B937-4BBEBDC9FC52",
        "gxsj": 1669626799470,
        "csz": "C:\\hsoft\\fj\\"
      },
      {
        "csbs": "back",
        "cssm": "备份文件存储路径",
        "fzh": "1",
        "filesettingid": "93802D2A-D597-41DD-9D80-FA322CB91D22",
        "gxsj": 1669953725323,
        "csz": "C:\\hsoft\\back\\"
      }
    ],
    "setting_list": [
      {
        "cssm": "年度台账生成时间",
        "csbs": "ndtzscsj",
        "csz": "6",
        "cszlx": 1,
        "cszdw": "年",
        "csbz": "年度台账生成的时间周期，单位为年，如参数值为1则代表每一年生成一次台账。备注：周期并非从您设置该参数时开始计算",
        "fzh": "1",
        "settingid": "672BF76D-148A-463A-9C50-E61405DDCB09"
      },
      {
        "cssm": "保密制度更新阈值",
        "csbs": "bmzdgxyz",
        "csz": "10",
        "cszlx": 1,
        "cszdw": "天",
        "csbz": "保密制度更新阈值",
        "fzh": "2",
        "settingid": "1FCF5744-A33C-4A4A-89AA-0E5CAB6223FA"
      },
      {
        "cssm": "组织机构更新阈值",
        "csz": "22",
        "cszlx": 1,
        "cszdw": "天",
        "csbz": "组织机构更新阈值",
        "fzh": "2",
        "csbs": "zzjggxyz",
        "settingid": "1DF9CDB3-5D6B-466C-A972-9774C0AF7ECE"
      },
      {
        "cssm": "自查自评任务",
        "csbs": "zczprw",
        "csz": "3",
        "cszlx": 1,
        "cszdw": "季度",
        "csbz": "自查自评任务",
        "fzh": "1",
        "settingid": "C9354730-3869-48B4-990C-718F378B6347"
      },
      {
        "cssm": "涉密人员上报任务",
        "csbs": "smrysbrw",
        "csz": "1",
        "cszlx": 1,
        "cszdw": "年",
        "csbz": "涉密人员上报任务",
        "fzh": "1",
        "settingid": "647E78A7-2436-4572-84E4-DCCCF6125E3A"
      },
      {
        "cssm": "定密事项上报任务",
        "csbs": "dmsxsbrw",
        "csz": "1",
        "cszlx": 1,
        "cszdw": "年",
        "csbz": "定密事项上报任务",
        "fzh": "1",
        "settingid": "635C63D8-8BF3-4970-A29A-35542929C83A"
      },
      {
        "cssm": "月度数据未更新提示任务",
        "csbs": "ydsjwgxtsrw",
        "csz": "3",
        "cszlx": 1,
        "cszdw": "月",
        "csbz": "月度数据未更新提示任务",
        "fzh": "1",
        "settingid": "2B207B0C-253E-4AC0-B882-2317C17088B9"
      },
      {
        "cssm": "年度数据未更新提示任务",
        "csbs": "ndsjwgxtsrw",
        "csz": "1",
        "cszlx": 1,
        "cszdw": "年",
        "csbz": "年度数据未更新提示任务",
        "fzh": "1",
        "settingid": "CD1D47DA-2B3E-41B5-9A15-B88B0BF899AC"
      },
      {
        "cssm": "涉密岗位更新阈值",
        "csbs": "smgwgxyz",
        "csz": "10",
        "cszlx": 1,
        "cszdw": "天",
        "csbz": "涉密岗位更新阈值",
        "fzh": "2",
        "settingid": "19E276D0-73C7-4A83-81A0-84BBF077D8BB"
      },
      {
        "cssm": "在岗涉密人员更新阈值",
        "csbs": "zgsmzrgxyz",
        "csz": "10",
        "cszlx": 1,
        "cszdw": "天",
        "csbz": "在岗涉密人员更新阈值",
        "fzh": "2",
        "settingid": "D7A5D2B6-207E-4D69-8564-62BCB5C058C9"
      },
      {
        "cssm": "人员新增汇总更新阈值",
        "csbs": "ryxzhzgxyz",
        "csz": "10",
        "cszlx": 1,
        "cszdw": "天",
        "csbz": "人员新增汇总更新阈值",
        "fzh": "2",
        "settingid": "052C48FF-24C0-4446-8736-2A949032E94B"
      },
      {
        "cssm": "岗位变更更新阈值",
        "csbs": "gwbggxyz",
        "csz": "10",
        "cszlx": 1,
        "cszdw": "天",
        "csbz": "岗位变更更新阈值",
        "fzh": "2",
        "settingid": "2A4DF22A-88BC-4860-87C7-EDEE6F1F8298"
      },
      {
        "cssm": "离岗离职更新阈值",
        "csbs": "lglzgxyz",
        "csz": "10",
        "cszlx": 1,
        "cszdw": "天",
        "csbz": "离岗离职更新阈值",
        "fzh": "2",
        "settingid": "9CCFC49B-64F4-440E-BD3D-9789F13D3434"
      },
      {
        "cssm": "教育培训达标学时",
        "csbs": "jypxdbxs",
        "csz": "10",
        "cszlx": 1,
        "cszdw": "学时",
        "csbz": "教育培训达标学时",
        "fzh": "3",
        "settingid": "782D886C-94CC-456C-B2AA-1F0E41CE6F21"
      },
      {
        "cssm": "培训清单更新阈值",
        "csbs": "pxqdgxyz",
        "csz": "10",
        "cszlx": 1,
        "cszdw": "天",
        "csbz": "培训清单更新阈值",
        "fzh": "2",
        "settingid": "608F06EE-193C-43A8-811A-3169B2D5FD39"
      },
      {
        "cssm": "涉密场所更新阈值",
        "csbs": "smcsgxyz",
        "csz": "10",
        "cszlx": 1,
        "cszdw": "天",
        "csbz": "涉密场所更新阈值",
        "fzh": "2",
        "settingid": "515FD372-0E81-4C2D-BEC3-F7B0486CC2DD"
      },
      {
        "cssm": "涉密场所变更更新阈值",
        "csbs": "smcsgbgxyz",
        "csz": "10",
        "cszlx": 1,
        "cszdw": "天",
        "csbz": "涉密场所变更更新阈值",
        "fzh": "2",
        "settingid": "0C0F0179-0863-4C97-91C6-014F763B2AB4"
      },
      {
        "cssm": "涉密计算机更新阈值",
        "csbs": "smjsjgxyz",
        "csz": "10",
        "cszlx": 1,
        "cszdw": "天",
        "csbz": "涉密计算机更新阈值",
        "fzh": "2",
        "settingid": "77C280E4-A7B6-4688-9A37-7F26E74F9905"
      },
      {
        "cssm": "非涉密计算机更新阈值",
        "csbs": "fsmjsjgxyz",
        "csz": "10",
        "cszlx": 1,
        "cszdw": "天",
        "csbz": "非涉密计算机更新阈值",
        "fzh": "2",
        "settingid": "7C0DBE1E-7EC2-4507-B3DC-789F91419D79"
      },
      {
        "cssm": "涉密移动存储介质更新阈值",
        "csbs": "smydccjzgxyz",
        "csz": "10",
        "cszlx": 1,
        "cszdw": "天",
        "csbz": "涉密移动存储介质更新阈值",
        "fzh": "2",
        "settingid": "5EC4B909-80EA-4344-8B58-5782297F8857"
      },
      {
        "cssm": "涉密办公自动化设备更新阈值",
        "csbs": "smbgzdhsbgxyz",
        "csz": "10",
        "cszlx": 1,
        "cszdw": "天",
        "csbz": "涉密办公自动化设备更新阈值",
        "fzh": "2",
        "settingid": "4A8FBF95-D57D-4893-8D74-FC9B79933F57"
      },
      {
        "cssm": "非密办公自动化设备更新阈值",
        "csbs": "fmbgzdhsbgxyz",
        "csz": "10",
        "cszlx": 1,
        "cszdw": "天",
        "csbz": "非密办公自动化设备更新阈值",
        "fzh": "2",
        "settingid": "A7ACA205-1E8B-4335-855E-4DEEBB8C444D"
      },
      {
        "cssm": "涉密网络设备更新阈值",
        "csbs": "smwlsbgxyz",
        "csz": "10",
        "cszlx": 1,
        "cszdw": "天",
        "csbz": "涉密网络设备更新阈值",
        "fzh": "2",
        "settingid": "3CEE8453-B20E-477C-A1BF-B02D608F1728"
      },
      {
        "cssm": "非密网络设备更新阈值",
        "csbs": "fmwlsbgxyz",
        "csz": "10",
        "cszlx": 1,
        "cszdw": "天",
        "csbz": "非密网络设备更新阈值",
        "fzh": "2",
        "settingid": "3606BC94-2D7A-411F-82C7-332561EF6483"
      },
      {
        "cssm": "安全产品更新阈值",
        "csbs": "aqcpgxyz",
        "csz": "10",
        "cszlx": 1,
        "cszdw": "天",
        "csbz": "安全产品更新阈值",
        "fzh": "2",
        "settingid": "4CD3F8B0-0484-4E8C-B4F0-2CDB41A82E9C"
      },
      {
        "cssm": "载体信息更新阈值",
        "csbs": "ztxxgxyz",
        "csz": "10",
        "cszlx": 1,
        "cszdw": "天",
        "csbz": "载体信息更新阈值",
        "fzh": "2",
        "settingid": "751872B6-8370-4084-8A91-E9FC23178045"
      },
      {
        "cszlx": 3,
        "csbs": "csbs_dbgzscrq",
        "cssm": "待办工作生成日期区间",
        "csz": [
          1670688000000,
          1674748800000
        ],
        "csbz": "从开始日期0点开始，到结束日期0点结束",
        "fzh": "1",
        "cszdw": "固定值",
        "settingid": "99CC72BF-D58B-4FF2-8332-50A0A0411FA5"
      },
      {
        "cszlx": 3,
        "cssm": "年度涉密人员上报日期区间",
        "csbs": "csbs_ndsmrysbrq",
        "csbz": "从开始日期0点开始，到结束日期0点结束",
        "fzh": "1",
        "csz": [
          {
            "month": "12",
            "day": "01"
          },
          {
            "month": "12",
            "day": "31"
          }
        ],
        "cszdw": "固定值",
        "settingid": "777DEB32-B17E-49E0-B6ED-8F7AE4A8E941"
      },
      {
        "cszlx": 3,
        "csbs": "csbs_nddmsxsbrq",
        "cssm": "年度定密事项上报日期区间",
        "csz": [
          {
            "month": "12",
            "day": "01"
          },
          {
            "month": "12",
            "day": "31"
          }
        ],
        "csbz": "从开始日期0点开始，到结束日期0点结束",
        "fzh": "1",
        "cszdw": "固定值",
        "settingid": "4213704C-BBDA-42A4-892A-392107E4CB07"
      }
    ]
  })
  .write()

// //
// import { writeLog } from '../../utils/logUtils'

// let handler = {
//   get(target, key, receiver) {
//     // 如果是对象，就递归添加 proxy 拦截
//     if (typeof target[key] === 'object' && target[key] !== null) {
//       return new Proxy(target[key], handler)
//     }
//     writeLog(target, key, 0)
//     return Reflect.get(target, key, receiver)
//   },
//   set(target, key, value, receiver) {
//     writeLog(target, key, 1, value)
//     return Reflect.set(target, key, value, receiver)
//   },
//   construct: function (target, args, newTarget) {
//     /*
//      输出： function A(name) {
//               this.name = name;
//            }
//     */
//     // console.log('construct target', target)
//     // 输出： ['kongzhi', {age: 30}]
//     // console.log('construct args', args)
//     return args
//   },
// }
// let dbZczpSystemProxy = new Proxy(dbZczpSystem, handler)

// export default dbZczpSystemProxy
export default dbZczpSystem
