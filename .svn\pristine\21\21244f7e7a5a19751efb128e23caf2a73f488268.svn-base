<template>
  <div class="bg_con" style="height: calc(100% - 38px);">
    <div style="width: 100%; position: relative; overflow: hidden; height: 100%;">
      <!-- <div class="xmlb-title" style=" cursor: pointer;">
				<span style="font-size: 24px; cursor: pointer;">涉密网络设备</span>
				<span style="" @click="returnSy" class="fhsy">返回</span>
			</div> -->

      <div class="dabg" style="height: 100%;">
        <div class="content" style="height: 100%;">
          <div class="table" style="height: 100%;">
            <!-- -----------------操作区域--------------------------- -->
            <div class="mhcx">
              <div class="mhcxxxx">
                <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline" style="float:left">

                  <el-form-item style="font-weight: 700;">
                    <el-input v-model="formInline.bmbh" clearable placeholder="保密编号" class="widths">
                    </el-input>
                  </el-form-item>
                  <el-form-item style="font-weight: 700;">
                    <el-input v-model="formInline.zrr" clearable placeholder="责任人" class="widths">
                    </el-input>
                  </el-form-item>
                  <el-form-item style="font-weight: 700;">
                    <el-cascader v-model="formInline.sybm" :options="regionOption" clearable class="widths" :props="regionParams" filterable ref="cascaderArr" placeholder="部门">
                    </el-cascader>
                  </el-form-item>
                  <el-form-item style="font-weight: 700;">
                    <el-select v-model="formInline.lx" clearable placeholder="类型" class="widthx">
                      <el-option v-for="item in sblxxz" :label="item.sblxmc" :value="item.sblxmc" :key="item.sblxid">
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item style="font-weight: 700;">
                    <el-select v-model="formInline.mj" clearable placeholder="密级" class="widthx">
                      <el-option v-for="item in sbmjxz" :label="item.sbmjmc" :value="item.sbmjmc" :key="item.sbmjid">
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item style="font-weight: 700;">
                    <el-date-picker v-model="formInline.qyrq" type="daterange" range-separator="至" start-placeholder="启用起始日期" end-placeholder="启用结束日期" format="yyyy年MM月dd日" value-format="yyyy年MM月dd日">
                    </el-date-picker>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="warning" icon="el-icon-circle-close" @click="cz">重置</el-button>
                  </el-form-item>
                </el-form>
              </div>
              <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline" style="float:right">
                <el-form-item style="float: right;">
                  <el-button type="danger" size="medium" @click="shanchu" icon="el-icon-delete-solid">
                    删除
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <el-button type="primary" size="medium" icon="el-icon-download" @click="exportList()">导出
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <input type="file" ref="upload" style="display: none;position: absolute;top:10px; right:0;opacity:0; cursor: pointer;height: 32px;width: 56px;z-index:1;" accept=".xls,.xlsx">
                  <el-button type="primary" icon="el-icon-upload2" size="medium" @click="dr_dialog = true">
                    导入
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <el-button type="danger" size="medium" icon="el-icon-delete" @click="xhsb">销毁
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <el-button type="primary" size="medium" icon="el-icon-position" @click="jcsb">外借
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <el-button type="danger" size="medium" icon="el-icon-circle-close" @click="bfsb">报废
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <el-button type="warning" size="medium" icon="el-icon-remove-outline" @click="tysb">
                    停用
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <el-button type="success" size="medium" icon="el-icon-circle-check" @click="zysb">启用
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <el-button type="success" size="medium" @click="xzsmsb()" icon="el-icon-plus">
                    新增
                  </el-button>
                </el-form-item>
              </el-form>
            </div>

            <!-- -----------------审查组人员列表--------------------------- -->
            <div class="table_content_padding" style="height: 100%;">
              <div class="table_content" style="height: 100%;">
                <el-table :data="smwlsbList" border @selection-change="selectRow" :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" style="width: 100%;border:1px solid #EBEEF5;" height="calc(100% - 34px - 44px - 41px - 7px)" stripe>
                  <el-table-column type="selection" width="55" align="center"> </el-table-column>
                  <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                  <el-table-column prop="mc" label="名称"></el-table-column>
                  <el-table-column prop="ppxh" label="品牌型号"></el-table-column>
                  <el-table-column prop="lx" label="类型"></el-table-column>
                  <el-table-column prop="zcbh" label="固定资产编号"></el-table-column>
                  <el-table-column prop="bmbh" label="保密编号"></el-table-column>
                  <el-table-column prop="mj" label="密级"></el-table-column>
                  <el-table-column prop="qyrq" label="启用日期"></el-table-column>
                  <el-table-column prop="zrr" label="责任人"></el-table-column>
                  <el-table-column prop="syqk" label="使用状态"></el-table-column>
                  <el-table-column prop="" label="操作" width="140">
                    <template slot-scope="scoped">
                      <!-- <el-button slot="reference" icon="el-icon-timer" type="text" style="color:#E6A23C;" @click="getTrajectory(scoped.row)"></el-button> -->
                      <el-button size="medium" type="text" @click="getTrajectory(scoped.row)">轨迹
                      </el-button>
                      <el-button size="medium" type="text" @click="xqyl(scoped.row)">详情
                      </el-button>
                      <el-button size="medium" type="text" @click="updateItem(scoped.row)">修改
                      </el-button>
                    </template>
                  </el-table-column>

                </el-table>

                <!-- -------------------------分页区域---------------------------- -->
                <div style="border: 1px solid #ebeef5;">
                  <el-pagination background @current-change="handleCurrentChange" class="fmwlsbfy" @size-change="handleSizeChange" :pager-count="5" :current-page="page" :page-sizes="[5, 10, 20, 30]" :page-size="pageSize" layout="total, prev, pager, sizes,next, jumper" :total="total">
                  </el-pagination>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 模板下载 -->
        <el-dialog title="开始导入" class="scbg-dialog" width="600px" @close="mbxzgb" :visible.sync="dr_dialog" show-close>
          <div style="padding: 20px;">
            <div class="daochu">
              <div>一、请点击“导出模板”，并参照模板填写信息。</div>
              <el-button type="primary" size="mini" @click="mbdc">
                模板导出
              </el-button>
            </div>
            <div class="daochu">
              <div>二、数据导入方式：</div>
              <el-radio-group v-model="sjdrfs" @change="Radio($event)">
                <el-radio label="1">追加（导入时已有的记录信息不变，只添加新的记录）</el-radio>
                <el-radio label="2">覆盖（导入时更新已有的记录信息，并添加新的记录）</el-radio>
              </el-radio-group>
            </div>
            <div class="daochu">
              <div>三、将按模板填写的文件，导入到系统中。</div>
              <el-button type="primary" size="mini" @click="chooseFile">
                上传导入
              </el-button>
            </div>
          </div>
        </el-dialog>
        <!-- -----------------导入-弹窗--------------------------- -->
        <el-dialog width="1000px" height="800px" title="导入涉密网络设备" class="scbg-dialog" :visible.sync="dialogVisible_dr" show-close>
          <div style="height: 600px;">
            <el-table :data="dr_cyz_list" ref="multipleTable" @selection-change="handleSelectionChange" style="width: 100%;border:1px solid #EBEEF5;" height="100%" stripe>
              <el-table-column type="selection" width="55"> </el-table-column>
              <el-table-column prop="涉密网络设备名称" label="涉密网络设备名称"></el-table-column>
              <el-table-column prop="类型" label="类型"></el-table-column>
              <el-table-column prop="品牌型号" label="品牌型号"></el-table-column>
              <el-table-column prop="序列号" label="序列号"></el-table-column>
              <el-table-column prop="固定资产编号" label="固定资产编号"></el-table-column>
              <el-table-column prop="保密编号" label="保密编号"></el-table-column>
              <el-table-column prop="密级" label="密级"></el-table-column>
              <el-table-column prop="IP地址" label="IP地址"></el-table-column>
              <el-table-column prop="MAC地址" label="MAC地址"></el-table-column>
              <el-table-column prop="责任人" label="责任人"></el-table-column>
              <el-table-column prop="管理部门" label="管理部门"></el-table-column>
              <el-table-column prop="使用状态" label="使用状态"></el-table-column>
            </el-table>
          </div>

          <div style="height :30px;display: flex;align-items: center;justify-content: center;margin: 10px 0;">
            <el-button type="primary" @click="drcy" size="mini">导 入</el-button>
            <el-button type="warning" @click="dialogVisible_dr = false" size="mini">关 闭</el-button>
          </div>
        </el-dialog>
        <!-- -----------------涉密人员离岗离职信息-弹窗--------------------------- -->

        <el-dialog title="涉密网络设备详细信息" :close-on-click-modal="false" :visible.sync="dialogVisible" width="50%" class="xg" :before-close="handleClose" @close="close('formName')">
          <el-form ref="formName" :model="tjlist" :rules="rules" label-width="120px" size="mini">
            <el-form-item label="涉密网络设备名称" prop="mc" class="one-line wlsb">
              <el-input placeholder="涉密网络设备名称" v-model="tjlist.mc" clearable></el-input>
            </el-form-item>
            <div style="display:flex">
              <el-form-item label="保密编号" prop="bmbh">
                <el-input placeholder="保密编号" v-model="tjlist.bmbh" clearable @blur="onInputBlur(1)">
                </el-input>
              </el-form-item>
              <el-form-item label="资产编号" prop="zcbh">
                <el-input placeholder="资产编号" v-model="tjlist.zcbh" clearable @blur="onInputBlur(1)">
                </el-input>
              </el-form-item>
            </div>
            <el-form-item label="密级" prop="mj" class="one-line">
              <el-radio-group v-model="tjlist.mj">
                <el-radio v-for="item in sbmjxz" :label="item.sbmjmc" :value="item.sbmjmc" :key="item.sbmjid">
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <div style="display:flex">
              <el-form-item label="启用日期" prop="qyrq">
                <!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
                <el-date-picker v-model="tjlist.qyrq" class="cd" clearable type="date" style="width: 100%;" placeholder="选择日期" format="yyyy年MM月dd日" value-format="yyyy年MM月dd日">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="类型" prop="lx">
                <el-select v-model="tjlist.lx" placeholder="请选择类型" style="width: 100%;">
                  <el-option v-for="item in sblxxz" :label="item.sblxmc" :value="item.sblxmc" :key="item.sblxid">
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="品牌型号" prop="ppxh">
                <el-autocomplete class="inline-input" value-key="ppxh" v-model.trim="tjlist.ppxh" style="width: 100%;" :fetch-suggestions="querySearchppxh" placeholder="品牌型号">
                </el-autocomplete>
              </el-form-item>
              <el-form-item label="序列号" prop="zjxlh">
                <el-input placeholder="序列号" v-model="tjlist.zjxlh" clearable @blur="onInputBlur(1)">
                </el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="IP地址">
                <el-input placeholder="ip地址" v-model="tjlist.ipdz" clearable></el-input>
              </el-form-item>
              <el-form-item label="MAC地址">
                <el-input placeholder="MAC地址" v-model="tjlist.macdz" clearable></el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="使用部门">
                <el-cascader v-model="tjlist.sybm" :options="regionOption" :props="regionParams" style="width: 100%;" filterable ref="cascaderArr"></el-cascader>
              </el-form-item>
              <el-form-item label="管理部门" prop="glbm">
                <el-cascader v-model="tjlist.glbm" :options="regionOption" :props="regionParams" style="width: 100%;" filterable ref="cascaderArr" @change="handleChange(1)"></el-cascader>
              </el-form-item>
            </div>
            <el-form-item label="责任人" prop="zrr" class="one-line wlsb">
              <el-autocomplete class="inline-input" value-key="xm" v-model.trim="tjlist.zrr" style="width: 100%;" :fetch-suggestions="querySearch" placeholder="请输入责任人">
              </el-autocomplete>
            </el-form-item>
            <el-form-item label="使用情况" prop="syqk" class="one-line">
              <el-radio-group v-model="tjlist.syqk">
                <el-radio v-for="item in sbsyqkxz" :label="item.sbsyqkmc" :value="item.sbsyqkmc" :key="item.sbsyqkid">
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitTj('formName')">保 存</el-button>
            <el-button type="warning" @click="handleClose()">关 闭</el-button>
          </span>
        </el-dialog>

        <el-dialog title="修改涉密网络设备详细信息" :close-on-click-modal="false" :visible.sync="xgdialogVisible" width="50%" class="xg" @close="close1('form')">
          <el-form ref="form" :model="xglist" :rules="rules" label-width="120px" size="mini">
            <el-form-item label="涉密网络设备名称" prop="mc" class="one-line wlsb">
              <el-input placeholder="涉密网络设备名称" v-model="xglist.mc" clearable></el-input>
            </el-form-item>
            <div style="display:flex">
              <el-form-item label="保密编号" prop="bmbh">
                <el-input placeholder="保密编号" v-model="xglist.bmbh" clearable @blur="onInputBlur(2)" disabled>
                </el-input>
              </el-form-item>
              <el-form-item label="资产编号" prop="zcbh">
                <el-input placeholder="资产编号" v-model="xglist.zcbh" clearable @blur="onInputBlur(3)" disabled>
                </el-input>
              </el-form-item>
            </div>
            <el-form-item label="密级" prop="mj" class="one-line">
              <el-radio-group v-model="xglist.mj">
                <el-radio v-for="item in sbmjxz" :label="item.sbmjmc" :value="item.sbmjmc" :key="item.sbmjid">
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <div style="display:flex">
              <el-form-item label="启用日期" prop="qyrq">
                <!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
                <el-date-picker v-model="xglist.qyrq" class="cd" clearable type="date" style="width: 100%;" placeholder="选择日期" format="yyyy年MM月dd日" value-format="yyyy年MM月dd日">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="类型" prop="lx">
                <el-select v-model="xglist.lx" placeholder="请选择类型" style="width: 100%;">
                  <el-option v-for="item in sblxxz" :label="item.sblxmc" :value="item.sblxmc" :key="item.sblxid">
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="品牌型号" prop="ppxh">
                <el-autocomplete class="inline-input" value-key="ppxh" v-model.trim="xglist.ppxh" style="width: 100%;" :fetch-suggestions="querySearchppxh" placeholder="品牌型号">
                </el-autocomplete>
              </el-form-item>
              <el-form-item label="序列号" prop="zjxlh">
                <el-input placeholder="序列号" v-model="xglist.zjxlh" clearable @blur="onInputBlur(4)">
                </el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="IP地址">
                <el-input placeholder="ip地址" v-model="xglist.ipdz" clearable></el-input>
              </el-form-item>
              <el-form-item label="MAC地址">
                <el-input placeholder="MAC地址" v-model="xglist.macdz" clearable></el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="使用部门">
                <el-cascader v-model="xglist.sybm" :options="regionOption" :props="regionParams" style="width: 100%;" filterable ref="cascaderArr"></el-cascader>
              </el-form-item>
              <el-form-item label="管理部门" prop="glbm">
                <el-cascader v-model="xglist.glbm" :options="regionOption" :props="regionParams" style="width: 100%;" filterable ref="cascaderArr" @change="handleChange(2)"></el-cascader>
              </el-form-item>
            </div>
            <el-form-item label="责任人" prop="zrr" class="one-line wlsb">
              <el-autocomplete class="inline-input" value-key="xm" v-model.trim="xglist.zrr" style="width: 100%;" :fetch-suggestions="querySearch" placeholder="请输入责任人">
              </el-autocomplete>
            </el-form-item>

            <el-form-item label="使用情况" prop="syqk" class="one-line">
              <el-radio-group v-model="xglist.syqk">
                <el-radio v-for="item in sbsyqkxz" :label="item.sbsyqkmc" :value="item.sbsyqkmc" :key="item.sbsyqkid">
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="updataDialog('form')">保 存</el-button>
            <el-button type="warning" @click="xgdialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>
        <!-- 详情 -->
        <el-dialog title="涉密网络设备详细信息详情" :close-on-click-modal="false" :visible.sync="xqdialogVisible" width="50%" class="xg">
          <el-form ref="form" :model="xglist" label-width="120px" size="mini" disabled>
            <el-form-item label="涉密网络设备名称" prop="mc" class="one-line">
              <el-input placeholder="涉密网络设备名称" v-model="xglist.mc" clearable></el-input>
            </el-form-item>
            <div style="display:flex">
              <el-form-item label="保密编号" prop="bmbh">
                <el-input placeholder="保密编号" v-model="xglist.bmbh" clearable></el-input>
              </el-form-item>
              <el-form-item label="资产编号" prop="zcbh">
                <el-input placeholder="资产编号" v-model="xglist.zcbh" clearable></el-input>
              </el-form-item>
            </div>
            <el-form-item label="密级" prop="mj" class="one-line">
              <el-radio-group v-model="xglist.mj">
                <el-radio v-for="item in sbmjxz" :label="item.sbmjmc" :value="item.sbmjmc" :key="item.sbmjid">
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <div style="display:flex">
              <el-form-item label="启用日期" prop="qyrq">
                <!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
                <el-date-picker v-model="xglist.qyrq" class="cd" clearable type="date" style="width: 100%;" placeholder="选择日期" format="yyyy年MM月dd日" value-format="yyyy年MM月dd日">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="类型" prop="lx">
                <el-select v-model="xglist.lx" placeholder="请选择类型" style="width: 100%;">
                  <el-option v-for="item in sblxxz" :label="item.sblxmc" :value="item.sblxmc" :key="item.sblxid">
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="品牌型号" prop="ppxh">
                <el-input placeholder="品牌型号" v-model="xglist.ppxh" clearable></el-input>
              </el-form-item>
              <el-form-item label="序列号" prop="zjxlh">
                <el-input placeholder="序列号" v-model="xglist.zjxlh" clearable></el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="IP地址" prop="ipdz">
                <el-input placeholder="ip地址" v-model="xglist.ipdz" clearable></el-input>
              </el-form-item>
              <el-form-item label="MAC地址" prop="macdz">
                <el-input placeholder="MAC地址" v-model="xglist.macdz" clearable></el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="使用部门" prop="sybm">
                <el-cascader v-model="xglist.sybm" :options="regionOption" :props="regionParams" style="width: 100%;" filterable ref="cascaderArr"></el-cascader>
              </el-form-item>
              <el-form-item label="管理部门" prop="glbm">
                <el-cascader v-model="xglist.glbm" :options="regionOption" :props="regionParams" style="width: 100%;" filterable ref="cascaderArr" @change="handleChange(2)"></el-cascader>
              </el-form-item>
            </div>
            <el-form-item label="责任人" prop="zrr" class="one-line">
              <el-autocomplete class="inline-input" value-key="xm" v-model.trim="xglist.zrr" style="width: 100%;" :fetch-suggestions="querySearch" placeholder="请输入责任人">
              </el-autocomplete>
            </el-form-item>

            <el-form-item label="使用情况" prop="syqk" class="one-line">
              <el-radio-group v-model="xglist.syqk">
                <el-radio v-for="item in sbsyqkxz" :label="item.sbsyqkmc" :value="item.sbsyqkmc" :key="item.sbsyqkid">
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">

            <el-button type="warning" @click="xqdialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>
        <!-- 历史轨迹 dialog -->
        <el-dialog title="历史轨迹" :close-on-click-modal="false" :visible.sync="lsgjDialogVisible" width="46%" class="xg">
          <div style="padding-left: 10px;background: #EBEEF5;height: 40px;line-height: 40px;font-size: 16px;border-radius: 5px;margin-bottom: 5px;">
            <span>保密编号：<span style="font-size: 14px;">{{lsgjDialogData.bmbh}}</span></span>
            <span>资产编号：<span style="font-size: 14px;">{{lsgjDialogData.zcbh}}</span></span>
          </div>
          <div style="max-height: 400px;overflow-y: scroll;padding: 10px;">
            <el-timeline>
              <el-timeline-item v-for="(activity, index) in lsgjDialogData.timelineList" :key="index" :icon="activity.icon" :color="activity.color" :size="'large'" :timestamp="activity.time">
                <div>
                  <p>{{activity.ymngnmc}}</p>
                  <p>操作人：{{activity.xm}}</p>
                  <p v-if="activity.extraParams && activity.extraParams.zrr">责任人：{{ activity.extraParams.zrr }}</p>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button type="warning" @click="lsgjDialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>

      </div>
    </div>
  </div>

</template>
<script>
import {
  getsbmj,
  getsbsyqk,
  getsbsmwllx
} from "../../../db/xzdb"
import {
  dateFormatNYRChinese
} from "../../../utils/moment"
import {
  getlogin
} from "../../../db/loginyhdb";
import {
  getbmmc,
  getsmry
} from "../../../db/smgwgldb";
import {
  getsmry1
} from "../../../db/smrydb"
import {
  //内容管理初始化成员列表
  getSmwlsb,
  //添加内容管理
  addSmwlsb,
  //删除内容管理
  deleteSmwlsb,
  xgSmwlsb,
  reviseSmwlsb,
  jyaddSmwlsb,
  jyreviseSmwlsbbmdh,
  jyreviseSmwlsbzcdh,
  jyreviseSmwlsbzjxlh,
  getPpxh,
} from "../../../db/smwlsbdb";

import {
  exportExcel
} from "../../../utils/exportExcel"; //excel导出工具

import {
  writeTrajectoryLog,
  parseTrajectoryLogs,
  setTrajectoryIcons
} from '../../../utils/logUtils'

// 数据比对工具类
import {
  dataComparison
} from '../../../utils/dataComparison'

import {
  checkArr
} from '../../../utils/utils'

import {
  getUuid
} from "../../../utils/getUuid"; //获取uuid
import XLSX from "xlsx";

export default {
  components: {},
  props: {},
  data () {
    return {
      // 历史轨迹dialog显隐
      lsgjDialogVisible: false,
      // 历史轨迹dialog数据
      lsgjDialogData: {
        bmbh: '',
        zcbh: '',
        // 历史轨迹时间线数据
        timelineList: [],
      },
      pdwlsb: 0,
      sbmjxz: [],
      sblxxz: [],
      sbsyqkxz: [],
      smwlsbList: [],
      tableDataCopy: [],
      // 修改dialog旧值对象，用来做修改情况比对的
      xglistOld: {},
      xglist: {},
      updateItemOld: {},
      xgdialogVisible: false,
      xqdialogVisible: false,
      formInline: {

      },
      tjlist: {
        mc: '',
        bmbh: '',
        zcbh: '',
        mj: '',
        qyrq: '',
        lx: '',
        ppxh: '',
        zjxlh: '',
        ipdz: '',
        macdz: '',
        sybm: '',
        glbm: '',
        zrr: '',
        syqk: '',
      },
      page: 1,
      pageSize: 10,
      total: 0,
      selectlistRow: [], //列表的值
      dialogVisible: false, //添加弹窗状态
      //表单验证
      rules: {
        mc: [{
          required: true,
          message: '请输入涉密网络设备名称',
          trigger: 'blur'
        },],
        bmbh: [{
          required: true,
          message: '请输入保密编号',
          trigger: 'blur'
        },],
        zcbh: [{
          required: true,
          message: '请输入资产编号',
          trigger: 'blur'
        },],
        mj: [{
          required: true,
          message: '请选择密级',
          trigger: 'blur'
        },],
        qyrq: [{
          required: true,
          message: '请选择启用日期',
          trigger: 'blur'
        },],
        lx: [{
          required: true,
          message: '请选择类型',
          trigger: 'blur'
        },],
        ppxh: [{
          required: true,
          message: '请输入品牌型号',
          trigger: ['blur', 'change'],
        },],
        zjxlh: [{
          required: true,
          message: '请输入序列号',
          trigger: 'blur'
        },],
        ipdz: [{
          required: true,
          message: '请输入IP地址',
          trigger: 'blur'
        },],
        macdz: [{
          required: true,
          message: '请输入MAC地址',
          trigger: 'blur'
        },],
        sybm: [{
          required: true,
          message: '请输入使用部门',
          trigger: ['blur', 'change'],
        },],
        glbm: [{
          required: true,
          message: '请输入管理部门',
          trigger: ['blur', 'change'],
        },],
        zrr: [{
          required: true,
          message: '请输入责任人',
          trigger: ['blur', 'change'],
        },],
        syqk: [{
          required: true,
          message: '请选择使用情况',
          trigger: 'blur'
        },],
      },
      //导入
      dialogVisible_dr: false, //导入成员组弹窗状态
      dr_cyz_list: [], //待选择导入成员组列表
      multipleTable: [], //已选择导入成员组列表
      regionOption: [], //地域信息
      regionParams: {
        label: 'label', //这里可以配置你们后端返回的属性
        value: 'label',
        children: 'childrenRegionVo',
        expandTrigger: 'click',
        checkStrictly: true,
      }, //地域信息配置参数
      dwmc: '',
      year: '',
      yue: '',
      ri: '',
      Date: '',
      xh: [],
      bmbh: '',
      zcbh: '',
      zjxlh: '',
      dclist: [],
      dr_dialog: false,
      //数据导入方式
      sjdrfs: ''

    }
  },
  computed: {},
  mounted () {
    //列表初始化
    this.smwlsb()
    this.ppxhlist()
    this.dwmc = getlogin()[0].dwmc
    let date = new Date()
    this.year = date.getFullYear() + '年'; //获取完整的年份(4位)
    this.yue = (date.getMonth() + 1) + '月'; //获取当前月份(0-11,0代表1月)
    this.ri = date.getDate() + '日'; //获取当前日(1-31)
    this.Date = this.year + this.yue + this.ri
    this.tjlist.mj = '秘密'
    this.tjlist.lx = '路由器'
    this.tjlist.syqk = '在用'
    this.smry()
    this.sbmjxz = getsbmj()
    this.sblxxz = getsbsmwllx()
    this.sbsyqkxz = getsbsyqk()

    this.$refs.upload.addEventListener('change', e => { //绑定监听表格导入事件
      this.readExcel(e);
    })
    let bmmc = getbmmc()
    let shu = []
    // console.log(bmmc);
    bmmc.forEach(item => {
      let childrenRegionVo = []
      bmmc.forEach(item1 => {
        if (item.bmm == item1.fbmm) {
          // console.log(item, item1);
          childrenRegionVo.push(item1)
          // console.log(childrenRegionVo);
          item.childrenRegionVo = childrenRegionVo
        }
      });
      // console.log(item);
      shu.push(item)
    })
    console.log(shu[0]);
    if (shu[0].childrenRegionVo) {
      shu[0].childrenRegionVo.forEach(item => {
      this.regionOption.push(item)
    })
    }
  },
  methods: {
    // 获取轨迹日志
    getTrajectory (row) {
      console.log(row)
      let params = {
        id: row.smwlsbid,
        xyybs: 'mk_smwlsb',
      }
      parseTrajectoryLogs(params, resArr => {
        console.log(resArr)
        if (resArr.length <= 0) {
          this.$message.warning('暂无轨迹')
          return
        }
        // icon图标处理
        setTrajectoryIcons(resArr)
        //
        this.lsgjDialogData.bmbh = row.bmbh
        this.lsgjDialogData.zcbh = row.zcbh
        this.lsgjDialogData.timelineList = resArr
        //
        this.lsgjDialogVisible = true
      })
    },
    xzsmsb () {
      this.dialogVisible = true
      this.smwlsb()
      this.tjlist.qyrq = this.Date
      // this.tjlist.glbm = this.tjlist.glbm.split('/')
      // this.tjlist.sybm = this.tjlist.sybm.split('/')
    },
    Radio (val) {
      this.sjdrfs = val
      console.log("当前选中的值", val)
    },
    mbxzgb () {
      this.sjdrfs = ''
    },
    mbdc () {
      console.log("----导出涉密人员----")
      // console.log(this.selectlistRow);
      // if (this.selectlistRow.length > 0) {
      let filename = "涉密网络设备模板" + getUuid() + ".xlsx"

      const {
        dialog
      } = require('electron').remote;
      //弹窗title
      let options = {
        title: "保存文件",
        defaultPath: filename,
      };
      console.log(dialog)
      //导出文件夹选择弹窗
      dialog.showSaveDialog(options, result => {
        console.log('result', result)
        if (result == null || result == "") {
          console.log("取消导出")
          return
        }
        let list = []

        //对导出的数据进行格式，js导出excel要求传入的参数格式为[["列1名称","列2名称","列3名称"],["列11","值21","值31"],["列12","值22","值32"]]
        list.push(["序号", "涉密网络设备名称", "类型", "品牌型号", "序列号",
          "固定资产编号",
          "保密编号", "密级", "启用日期", "IP地址",
          "MAC地址", "责任人", "管理部门", "使用状态"
        ]) //确定列名


        let styles = {
          // 列样式
          cols: {
            // 作用sheet页索引（0开始）（-1全sheet页生效）
            scoped: -1,
            style: [{
              wpx: 100
            },
            {
              wpx: 130
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            }
            ]
          },
          // 全局样式
          all: {
            alignment: {
              horizontal: 'center', // 水平居中
              vertical: 'center' // 垂直居中
            },
            font: {
              sz: 11, // 字号
              name: '宋体' // 字体
            },
            border: { // 边框
              top: {
                style: 'thin'
              },
              bottom: {
                style: 'thin'
              },
              left: {
                style: 'thin'
              },
              right: {
                style: 'thin'
              }
            }
          },
          // 单元格样式
          cell: []
        }

        exportExcel(result, list, undefined, styles) //list 要求为二维数组
        this.dr_dialog = false
        this.$message('导出成功:' + result)
      })
    },
    //导入
    chooseFile () {
      if (this.sjdrfs != '') {
        if (this.sjdrfs == 1) {
          this.$refs.upload.click()
        } else if (this.sjdrfs == 2) {
          let valArr = this.dclist
          valArr.forEach(function (item) {
            deleteSmwlsb(item)
          })
          this.$refs.upload.click()
        }
      } else {
        this.$message.warning('请选择导入方式')
      }
    },
    //----成员组选择
    handleSelectionChange (val) {
      this.multipleTable = val
      console.log("选中：", this.multipleTable);
    },
    //---确定导入成员组
    drcy () {
      //遍历已选择导入的成员，进行格式化，然后添加到数据库
      for (var i in this.multipleTable) {
        var cy = {
          mc: this.multipleTable[i]["涉密网络设备名称"],
          ppxh: this.multipleTable[i]["品牌型号"],
          lx: this.multipleTable[i]["类型"],
          zjxlh: this.multipleTable[i]["序列号"],
          zcbh: this.multipleTable[i]["固定资产编号"],
          bmbh: this.multipleTable[i]["保密编号"],
          mj: this.multipleTable[i]["密级"],
          qyrq: dateFormatNYRChinese(this.multipleTable[i]["启用日期"]),
          ipdz: this.multipleTable[i]["IP地址"],
          macdz: this.multipleTable[i]["MAC地址"],
          zrr: this.multipleTable[i]["责任人"],
          glbm: this.multipleTable[i]["管理部门"],
          syqk: this.multipleTable[i]["使用状态"],
          smwlsbid: getUuid()
        }
        addSmwlsb(cy)
      }
      this.dialogVisible_dr = false
      this.smwlsb()
    },
    //----表格导入方法
    readExcel (e) {
      var that = this;
      const files = e.target.files;
      console.log("files", files);
      var vali = /\.(xls|xlsx)$/
      if (files.length <= 0) { //如果没有文件名
        return false;
      } else if (!vali.test(files[0].name.toLowerCase())) {
        this.$Message.error('上传格式不正确，请上传xls或者xlsx格式');
        return false;
      }
      const fileReader = new FileReader();
      fileReader.onload = (e) => {
        try {
          const data = e.target.result;
          const workdata = XLSX.read(data, {
            type: 'binary',
            cellDates: true,
          });
          console.log("文件的内容：", workdata) // 文件的内容
          //查看打印能看到 workdata的sheetNames里是文件中每个表的有值的集合；
          const wsname = workdata.SheetNames[0]; //取第一张表
          console.log('wsname', wsname)
          const ws = XLSX.utils.sheet_to_json(workdata.Sheets[wsname]); //生成json表格内容
          console.log(ws); //自第二行开始的内容
          this.dialogVisible_dr = true
          this.dr_cyz_list = ws
          console.log("列表的值:", this.dr_cyz_list)
          // 加工excel读取业务类型为数组
          // this.dr_cyz_list.forEach(function(item) {
          // 	console.log(item[0]['业务类型'].splite(','))
          // })
          this.$refs.upload.value = ''; // 处理完成 清空表单值
          this.dr_dialog = false
        } catch (e) {
          return false;
        }
      };
      fileReader.readAsBinaryString(files[0]);
    },
    //修改
    updataDialog (form) {
      this.$refs[form].validate((valid) => {
        if (valid) {
          if (this.pdwlsb == 0) {
            reviseSmwlsb(this.xglist)
            // 判断修改并写入轨迹日志（只有有改动才写入轨迹日志）
            let newObj = JSON.parse(JSON.stringify(this.xglist))
            console.log(newObj.sybm, newObj.glbm)
            if (checkArr(newObj.sybm)) {
              newObj.sybm = newObj.sybm.join("/")
            }
            if (checkArr(newObj.glbm)) {
              newObj.glbm = newObj.glbm.join("/")
            }
            let resArr = dataComparison(this.xglistOld, newObj)
            console.log('resArr', resArr)
            if (resArr[1].syqk) {
              // 添加日志
              let paramsLog = {
                xyybs: 'mk_smwlsb',
                id: newObj.smwlsbid,
                ymngnmc: resArr[1].syqk,
                extraParams: {
                  zrr: newObj.zrr,
                },
              }
              writeTrajectoryLog(paramsLog)
            }
            // 刷新页面表格数据
            this.smwlsb()
            this.ppxhlist()
            // 关闭dialog
            this.$message.success('修改成功')
            this.xgdialogVisible = false
          } else if (this.pdwlsb == 1) {
            this.$message.error('保密编号已存在');
          } else if (this.pdwlsb == 2) {
            this.$message.error('资产编号已存在');
          } else if (this.pdwlsb == 3) {
            this.$message.error('序列号已存在');
          }

        } else {
          console.log('error submit!!');
          return false;
        }
      });

    },
    xqyl (row) {
      this.updateItemOld = JSON.parse(JSON.stringify(row))

      this.xglist = JSON.parse(JSON.stringify(row))
      // this.form1.ywlx = row.ywlx
      console.log('old', row)
      console.log("this.xglist.ywlx", this.xglist);
      this.xglist.sybm = this.xglist.sybm.split('/')
      this.xglist.glbm = this.xglist.glbm.split('/')
      this.xqdialogVisible = true
    },

    updateItem (row) {
      this.updateItemOld = JSON.parse(JSON.stringify(row))

      this.xglist = JSON.parse(JSON.stringify(row))
      this.bmbh = this.xglist.bmbh
      this.zcbh = this.xglist.zcbh
      this.zjxlh = this.xglist.zjxlh
      // this.form1.ywlx = row.ywlx
      console.log('old', row)
      console.log("this.xglist.ywlx", this.xglist);
      this.xglist.sybm = this.xglist.sybm.split('/')
      this.xglist.glbm = this.xglist.glbm.split('/')
      //
      this.xglistOld = JSON.parse(JSON.stringify(row))
      this.xgdialogVisible = true
    },
    //查询
    onSubmit () {
      // //  form是查询条件
      // console.log(this.formInline);
      // // 备份了一下数据
      // let arr = this.tableDataCopy
      // // 通过遍历key值来循环处理
      // Object.keys(this.formInline).forEach((e, label) => {
      // 	// 调用自己定义好的筛选方法
      // 	if (typeof (this.formInline[e]) == 'object') {
      // 		if (this.formInline[e] == null || this.formInline[e].length == 0) {
      // 			arr = this.filterFunc(this.formInline[e], e, arr)
      // 			return
      // 		}
      // 		let timeArr1 = this.formInline[e][0].replace(/[\u4e00-\u9fa5]/g, '/')

      // 		if (!(isNaN(timeArr1) && !isNaN(Date.parse(timeArr1)))) {
      // 			this.formInline[e] = this.formInline[e].join('/')
      // 			arr = this.filterFunc(this.formInline[e], e, arr)
      // 			this.formInline[e] = this.formInline[e].split('/')
      // 		} else {
      // 			arr = this.filterFunc(this.formInline[e], e, arr)
      // 		}
      // 	} else {
      // 		arr = this.filterFunc(this.formInline[e], e, arr)
      // 	}
      // })
      // // 为表格赋值
      // this.smwlsbList = arr
      this.smwlsb()
    },
    filterFunc (val, target, filterArr) {
      // 参数不存在或为空时，就相当于查询全部
      console.log(1);
      if (val == undefined || val == '') {
        console.log(2);
        return filterArr
      }
      return filterArr.filter(p => {
        console.log(p);
        let bool
        let resP
        if (Object.prototype.toString.call(val) == '[object Array]') {
          console.log('是数组')
          if (val.length > 1) {
            let timeArr1 = val[1].replace(/[\u4e00-\u9fa5]/g, '/')
            let date = new Date(timeArr1)
            if ('Invalid Date' != date) {
              // 时间
              if (new Date(p[target].replace(/[\u4e00-\u9fa5]/g, '/')).getTime() <= date
                .getTime()) {
                console.log('找到小于范围内是记录')
                resP = p
                let timeArr0 = val[0].replace(/[\u4e00-\u9fa5]/g, '/')
                if (new Date(p[target].replace(/[\u4e00-\u9fa5]/g, '/')).getTime() >= new Date(
                  timeArr0)
                  .getTime()) {
                  console.log('找到大于范围内是记录')
                  resP = p
                } else {
                  resP = undefined
                }
              }
            } else {
              console.log('非法时间')
            }
            if (resP) {
              console.log('不是时间，通过时间校验')
              bool = true
            }
          } else {
            if (new Date(p[target]).getTime() <= new Date(timeArr0).getTime()) {
              resP = p
            } else {
              resP = undefined
            }
            if (resP) {
              bool = true
            }
          }
          return bool
        }
        return p[target].indexOf(val) > -1
        // return bool
      }) // 可以自己加一个.toLowerCase()来兼容一下大小
    },

    returnSy () {
      this.$router.push("/tzglsy");
    },
    smwlsb () {
      let params = {
        page: this.page,
        pageSize: this.pageSize
      }
      Object.assign(params, this.formInline)
      let resList = getSmwlsb(params)
      console.log("params", params);
      this.tableDataCopy = resList.list
      this.smwlsbList = resList.list
      this.dclist = resList.list_total
      if (resList.list_total.length != 0) {
        this.tjlist = resList.list_total[resList.list_total.length - 1]
        if (this.tjlist.sybm) {
            this.tjlist.sybm = this.tjlist.sybm.split('/')
          }
          this.tjlist.glbm = this.tjlist.glbm.split('/')
      }
      this.dclist.forEach((item, label) => {
        this.xh.push(label + 1)
      })
      this.total = resList.total
    },
    //删除
    shanchu (id) {
      if (this.selectlistRow != '') {
        this.$confirm('是否继续删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let valArr = this.selectlistRow
          // console.log("....", val);
          valArr.forEach(function (item) {
            if (Array.isArray(item.glbm)) {
             item.glbm = item.glbm.join('/')
             item.sybm = item.sybm.join('/')
            }
            deleteSmwlsb(item)
            console.log("删除：", item);
            console.log("删除：", item);
          })
          let params = valArr
          this.$message({
            message: '删除成功',
            type: 'success'
          });
          this.smwlsb()
          this.ppxhlist()
        }).catch(() => {
          this.$message('已取消删除')
        })
      } else {
        this.$message({
          message: '未选择删除记录，请选择下列列表',
          type: 'warning'
        });
      }
    },
    //添加
    showDialog () {
      this.resetForm()
      this.dialogVisible = true
    },

    //导出
    exportList () {
      console.log("----导出涉密人员----")
      // console.log(this.selectlistRow);
      // if (this.selectlistRow.length > 0) {
      let filename = "涉密网络设备表" + getUuid() + ".xlsx"

      const {
        dialog
      } = require('electron').remote;
      //弹窗title
      let options = {
        title: "保存文件",
        defaultPath: filename,
      };
      console.log(dialog)
      //导出文件夹选择弹窗
      dialog.showSaveDialog(options, result => {
        console.log('result', result)
        if (result == null || result == "") {
          console.log("取消导出")
          return
        }
        let list = []
        list.push(["涉密网络设备台账"])
        list.push(["上报单位:", this.dwmc, "", "", "", "", "", ""])
        list.push(["统计年度:", this.year, "", "", "", "", "", "", "", "", "",
          "", "填报时间:", this.Date
        ])
        //对导出的数据进行格式，js导出excel要求传入的参数格式为[["列1名称","列2名称","列3名称"],["列11","值21","值31"],["列12","值22","值32"]]
        list.push(["序号", "涉密网络设备名称", "类型", "品牌型号", "序列号",
          "固定资产编号",
          "保密编号", "密级", "启用日期", "IP地址",
          "MAC地址", "责任人", "管理部门", "使用状态"
        ]) //确定列名

        for (var i in this.dclist) { //每一行的值
          let item = this.dclist[i] //{"name":"XXX","dw":"XXX","zw":"XXX"}

          console.log("导出值:", this.dclist);
          let column = [(parseInt(i) + 1), item["mc"], item["lx"],
          item["ppxh"], item["zjxlh"],
          item["zcbh"], item["bmbh"], item["mj"], item["qyrq"],
          item["ipdz"], item["macdz"], item["zrr"], item["glbm"],
          item["syqk"]
          ]
          list.push(column)
        }
        let merges = [{
          s: { //s为开始
            c: 0, //开始列
            r: 0 //开始取值范围
          },
          e: { //e结束
            c: 13, //结束列
            r: 0 //结束范围
          }
        }]
        let styles = {
          // 列样式
          cols: {
            // 作用sheet页索引（0开始）（-1全sheet页生效）
            scoped: -1,
            style: [{
              wpx: 100
            },
            {
              wpx: 150
            },
            {
              wpx: 100
            },
            {
              wpx: 150
            },
            {
              wpx: 150
            },
            {
              wpx: 150
            },
            {
              wpx: 150
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 150
            },
            {
              wpx: 150
            },
            {
              wpx: 100
            },
            {
              wpx: 200
            },
            {
              wpx: 120
            },
            ]
          },
          // 全局样式
          all: {
            alignment: {
              horizontal: 'center', // 水平居中
              vertical: 'center', // 垂直居中
              wrapText: true, // 文字换行
            },
            font: {
              sz: 11, // 字号
              name: '宋体' // 字体
            },
            border: { // 边框
              top: {
                style: 'thin'
              },
              bottom: {
                style: 'thin'
              },
              left: {
                style: 'thin'
              },
              right: {
                style: 'thin'
              }
            }
          },
          // 单元格样式
          cell: [{
            // 生效sheet页索引（值为 -1 时所有sheet页都生效）
            scoped: -1,
            // 索引
            index: 'A1',
            style: {
              font: {
                name: '宋体',
                sz: 16, // 字号
                bold: true,
              },
              alignment: {
                horizontal: 'center', // 水平居中
                vertical: 'center' // 垂直居中
              }
            }
          }]
        }
        // excel导出方法自定义配置
        let config = {
          // 忽略行索引(从1开始)
          rowIndexArrIgnore: [1]
        }
        exportExcel(result, list, merges, styles, config) //list 要求为二维数组
        this.$message('导出成功:' + result)
      })
    },
    //确定添加成员组
    submitTj (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let uuid = getUuid()
          let params = {
            mc: this.tjlist.mc,
            bmbh: this.tjlist.bmbh,
            zcbh: this.tjlist.zcbh,
            mj: this.tjlist.mj,
            qyrq: this.tjlist.qyrq,
            lx: this.tjlist.lx,
            ppxh: this.tjlist.ppxh,
            zjxlh: this.tjlist.zjxlh,
            ipdz: this.tjlist.ipdz,
            macdz: this.tjlist.macdz,
            // sybm: this.tjlist.sybm.join('/'),
            // glbm: this.tjlist.glbm.join('/'),
            zrr: this.tjlist.zrr,
            syqk: this.tjlist.syqk,
            smwlsbid: uuid
          }
          // 使用部门、管理部门单独处理
          if (this.tjlist.sybm) {
            params.sybm = this.tjlist.sybm.join('/')
          }
          if (this.tjlist.glbm) {
            params.glbm = this.tjlist.glbm.join('/')
          }
          //
          this.onInputBlur(1)
          if (this.pdwlsb == 0) {
            addSmwlsb(params)
            this.dialogVisible = false
            this.$message({
              message: '添加成功',
              type: 'success'
            });
             this.resetForm()
        this.smwlsb()
        this.ppxhlist()
          }

        } else {
          console.log('error submit!!');
          return false;
        }
       
      });

    },

    deleteTkglBtn () {

    },

    selectRow (val) {
      console.log(val);
      this.selectlistRow = val;
    },
    //列表分页--跳转页数
    handleCurrentChange (val) {
      this.page = val
      this.smwlsb()
    },
    //列表分页--更改每页显示个数
    handleSizeChange (val) {
      this.page = 1
      this.pageSize = val
      this.smwlsb()
    },
    //添加重置
    resetForm () {
      this.tjlist.mc = ''
      this.tjlist.mj = '秘密'
      this.tjlist.qyrq = this.Date
      this.tjlist.lx = '路由器'
      this.tjlist.ppxh = ''
      this.tjlist.sybm = ''
      this.tjlist.glbm = ''
      this.tjlist.zrr = ''
      this.tjlist.syqk = '在用'
    },
    handleClose (done) {
      // this.resetForm()
      this.dialogVisible = false
      this.tjlist.sybm = this.tjlist.sybm.join('/')
      this.tjlist.glbm = this.tjlist.glbm.join('/')
      this.smwlsb()
    },
    // 弹框关闭触发
    close (formName) {
      // 清空表单校验，避免再次进来会出现上次校验的记录
      this.$refs[formName].clearValidate();
    },
    close1 (form) {
      // 清空表单校验，避免再次进来会出现上次校验的记录
      this.$refs[form].clearValidate();
    },

    xhsb () {
      if (this.selectlistRow.length == 0) {
        this.$message({
          message: '操作失败',
          type: 'warning'
        });
      } else {
        let syzt = '销毁'
        let select = this.selectlistRow
        let params = {
          syzt: syzt,
          select: select
        }
        console.log(this.selectlistRow);
        xgSmwlsb(params)
        this.smwlsb()
        this.$message({
          message: '操作成功',
          type: 'success'
        });
      }

    },
    jcsb () {
      if (this.selectlistRow.length == 0) {
        this.$message({
          message: '操作失败',
          type: 'warning'
        });
      } else {
        let syzt = '借出'
        let select = this.selectlistRow
        let params = {
          syzt: syzt,
          select: select
        }
        console.log(this.selectlistRow);
        xgSmwlsb(params)
        this.smwlsb()
        this.$message({
          message: '操作成功',
          type: 'success'
        });
      }
    },
    bfsb () {
      if (this.selectlistRow.length == 0) {
        this.$message({
          message: '操作失败',
          type: 'warning'
        });
      } else {
        let syzt = '报废'
        let select = this.selectlistRow
        let params = {
          syzt: syzt,
          select: select
        }
        console.log(this.selectlistRow);
        xgSmwlsb(params)
        this.smwlsb()
        this.$message({
          message: '操作成功',
          type: 'success'
        });
      }
    },
    tysb () {
      if (this.selectlistRow.length == 0) {
        this.$message({
          message: '操作失败',
          type: 'warning'
        });
      } else {
        let syzt = '停用'
        let select = this.selectlistRow
        let params = {
          syzt: syzt,
          select: select
        }
        console.log(this.selectlistRow);
        xgSmwlsb(params)
        this.smwlsb()
        this.$message({
          message: '操作成功',
          type: 'success'
        });
      }
    },
    zysb () {
      if (this.selectlistRow.length == 0) {
        this.$message({
          message: '操作失败',
          type: 'warning'
        });
      } else {
        let syzt = '在用'
        let select = this.selectlistRow
        let params = {
          syzt: syzt,
          select: select
        }
        console.log(this.selectlistRow);
        xgSmwlsb(params)
        this.smwlsb()
        this.$message({
          message: '操作成功',
          type: 'success'
        });
      }
    },
    onInputBlur (index) {
      if (index == 1) {
        this.pdwlsb = jyaddSmwlsb(this.tjlist)
        if (this.pdwlsb == 1) {
          this.$message.error('保密编号已存在');
        } else if (this.pdwlsb == 2) {
          this.$message.error('资产编号已存在');
        } else if (this.pdwlsb == 3) {
          this.$message.error('主机序列号已存在');
        }
      } else if (index == 2) {
        this.pdwlsb = 0
        if (this.bmbh != this.xglist.bmbh) {
          this.pdwlsb = jyreviseSmwlsbbmdh(this.xglist)
          if (this.pdwlsb == 1) {
            this.$message.error('保密编号已存在');
          }
        }

      } else if (index == 3) {
        this.pdwlsb = 0
        if (this.zcbh != this.xglist.zcbh) {
          this.pdwlsb = jyreviseSmwlsbzcdh(this.xglist)
          if (this.pdwlsb == 2) {
            this.$message.error('资产编号已存在');
          }
        }

      } else if (index == 4) {
        this.pdwlsb = 0
        if (this.zjxlh != this.xglist.zjxlh) {
          this.pdwlsb = jyreviseSmwlsbzjxlh(this.xglist)
          if (this.pdwlsb == 3) {
            this.$message.error('主机序列号已存在');
          }
        }

      }
    },
    querySearch (queryString, cb) {
      var restaurants = this.restaurants;
      console.log("restaurants", restaurants);
      var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
      console.log("results", results);
      // 调用 callback 返回建议列表的数据
      cb(results);
      console.log("cb(results.dwmc)", results);
    },
    createFilter (queryString) {
      return (restaurant) => {
        return (restaurant.xm.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
      };
    },
    smry () {
      this.restaurants = getsmry1()
    },
    handleChange (index) {
      let resList
      if (index == 1) {
        resList = getsmry(this.tjlist.glbm.join('/'))
      } else if (index == 2) (
        resList = getsmry(this.xglist.glbm.join('/'))
      )
      this.restaurants = resList;
      this.tjlist.zrr = "";
      this.xglist.zrr = "";
    },
    //模糊查询品牌型号
    querySearchppxh (queryString, cb) {
      var restaurants = this.restaurantsppxh;
      console.log("restaurants", restaurants);
      var results = queryString ? restaurants.filter(this.createFilterppxh(queryString)) : restaurants;
      console.log("results", results);
      // 调用 callback 返回建议列表的数据
      for (var i = 0; i < results.length; i++) {
        for (var j = i + 1; j < results.length; j++) {
          if (results[i].ppxh === results[j].ppxh) {
            results.splice(j, 1);
            j--;
          }
        }
      }
      cb(results);
      console.log("cb(results.zw)", results);
    },
    createFilterppxh (queryString) {
      return (restaurant) => {
        return (restaurant.ppxh.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
      };
    },
    ppxhlist () {
      let resList = getPpxh()
      this.restaurantsppxh = resList;
      console.log("this.restaurants", this.restaurantsppxh);
      console.log(resList)
    },
    cz () {
      this.formInline = {}
    },
  },
  watch: {

  }
}
</script>

<style scoped>
.bg_con {
  width: 100%;
}

.dabg {
  /* margin-top: 10px; */
  box-shadow: 0px 2px 12px 0px rgba(9, 70, 255, 0.13);
  border-radius: 8px;
  width: 100%;
}

.xmlb-title {
  line-height: 60px;
  width: 100%;
  padding-left: 10px;
  height: 60px;
  background: url(../../assets/background/bg-02.png) no-repeat left;
  background-size: 100% 100%;
  text-indent: 10px;
  /* margin: 0 20px; */
  color: #0646bf;
  font-weight: 700;
}

.daochu {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

/* /deep/.el-radio {
	display: block;
	margin-top: 10px;
	margin-bottom: 10px;
} */

.fhsy {
  display: inline-block;
  width: 120px;
  margin-top: 10px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-left: 30px;
  padding-top: 4px;
  float: right;
  background: url(../../assets/icons/zzjg_icon1.png) no-repeat center;
  background-size: 100% 100%;
}

.item_button {
  height: 100%;
  float: left;
  padding-left: 10px;
  line-height: 50px;
}

.select_wrap {
  /* //padding: 5px; */

  .select_wrap_content {
    float: left;
    width: 100%;
    line-height: 50px;
    /* // padding-left: 20px; */
    /* // padding-right: 20px; */
    height: 100%;
    background: rgba(255, 255, 255, 0.7);

    .item_label {
      padding-left: 10px;
      height: 100%;
      float: left;
      line-height: 50px;
      font-size: 1em;
    }
  }
}

.mhcx1 {
  margin-top: 0px;
}

.widths {
  width: 7vw;
}

.widthx {
  width: 8vw;
}

.cd {
  width: 184px;
}

/deep/.mhcx .el-form-item {
  /* margin-top: 5px; */
  margin-bottom: 5px;
}

/deep/.mhcxxxx .el-form--inline .el-form-item {
  margin-right: 0px;
}

.fmwlsbfy {
  width: 100%;
}

.dialog-footer {
  display: block;
  margin-top: 10px;
}
</style>