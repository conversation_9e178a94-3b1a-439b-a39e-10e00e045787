import db from "./adapter/zczpAdaptor";
// 日志工具类
import { writeTrajectoryLog } from '../utils/logUtils'

// 获取所有离岗离职信息(上报数据自选模式专用)
export const getAllCgcjZxms = () => {
  return db.get('Cgcj_list').cloneDeep().value()
}

//保密制度-----------------------------------保密制度初始化列表********
export const getLglz = (params) => {
  let page = params.page;
  let pagesize = params.pageSize;
  let xm = params.xm;
  let cgsj = params.cgsj;
  let list_total = db
    .get("Cgcj_list")
    .sortBy("cjsj")
    .filter(function (item) {
      // 1、试卷名称和创建时间都没有
      if (
        (xm === undefined || xm == "") &&
        (cgsj === undefined || cgsj == null)
      ) {
        return item;
        console.log("全都没有", item);
      }
      // 2、试卷名称有，创建时间没有
      else if (xm && (cgsj === undefined || cgsj == null)) {
        if (item.xm) {
          if (item.xm.indexOf(xm) != -1) {
            console.log("ccc", item);
            return item;
          }
        } else {
          console.log("item.xm", item.xm);
        }
      }
      // 3、试卷名称没有，创建时间有
      else if ((xm === undefined || xm == "") && cgsj) {
        if (item.cgsj) {
          if (item.cgsj >= cgsj[0] && item.cgsj <= cgsj[1]) {
            return item;
          }
        } else {
          console.log("item.tmqjssj", item.cgsj);
        }
      }
      // 4、试卷名称有，创建时间有
      else if (xm && cgsj) {
        if (item.xm && item.cgsj) {
          if (
            item.xm.indexOf(xm) != -1 &&
            item.cgsj >= cgsj[0] &&
            item.cgsj <= cgsj[1]
          ) {
            return item;
          }
        } else {
          console.log("item.xm", item.xm, "item.cgsj", item.tmqjssj);
        }
      }
    })
    .cloneDeep()
    .value();
 
  // 手动分页
  console.log(pagesize * (page - 1), pagesize * (page - 1) + pagesize);
  let pageList = list_total.slice(
    pagesize * (page - 1),
    pagesize * (page - 1) + pagesize
  );

  let resList = {
    list: pageList,
    list_total: list_total,
    total: list_total.length,
  };
  console.log("保密制度", resList);
  return resList;
};
//保密制度-----------------------------------保密制度添加成员********
export const addCgcj = (params) => {
  let sjc = new Date().getTime();
  params.cjsj = sjc;
  params.gxsj = sjc;
  db.read().get("Cgcj_list").push(params).write();
  // 添加日志
  // let paramsLog = {
  //   xyybs: 'mk_smry',
  //   id: params.lglzid,
  //   ymngnmc: '出国（境）',
  //   extraParams: {
  //     xm: params.xm,
  //     sfzhm: params.sfzhm,
  //     bm: params.bm,
  //     gwmc: params.gwmc,
  //     smdj: params.smdj,
  //     cjsj: params.cjsj,
  //     fhsj: params.fhsj,
  //     bz: params.bz
  //   }
  // }
  // writeTrajectoryLog(paramsLog)
};
//保密制度-----------------------------------保密制度删除成员********
export const deleteCgcj = (params) => {
  /**
   * 使用该离岗离职人员的身份证号码修改其在岗涉密人员(Smry_list)在岗状态(zgzt)为可见
   * 在岗状态(zgzt): 0-删除 1-可见
  */
 if(!params) {
  console.log('出国出境撤销参数为空')
  return
 }
 let sfzhm = params.sfzhm
 let cgcjid = params.cgcjid
 // 参数校验
 if(!sfzhm) {
  console.log('出国出境撤销人员身份证号码为空')
  return
 }
 if(!cgcjid) {
  console.log('出国出境撤销失败，离岗离职ID为空')
  return
 }
 // 更新
//  db.get('Smry_list').find({sfzhm: sfzhm}).assign({zgzt:1}).write()
 // 删除该离岗离职记录
 let delTargetObj = db.get('Cgcj_list').find({cgcjid:cgcjid}).cloneDeep().value()
  db.get("Cgcj_list").remove(delTargetObj).write()
};
export const deleteCgcjall = () => {
  db.get("Cgcj_list").remove().write()
};
export const updateLglz = (params) => {
  // let bm = params.bm.join('/')
  // params.bm = bm
  let cgcjid = params.cgcjid;
  console.log("lglzid", cgcjid);
  if (!cgcjid || cgcjid == "") {
    return;
  }
  params.gxsj = new Date().getTime();
  // 全字段更新方法(传进来的字段都会更新)
  db.read().get("Cgcj_list").find({ cgcjid: cgcjid }).assign(params).write();
};
