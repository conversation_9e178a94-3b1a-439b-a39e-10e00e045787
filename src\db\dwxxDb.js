import DB from './adapter/zczpAdaptor'

import { checkObjIsBlank, machineSendParams } from '../utils/utils'

import { getUuid } from '../utils/getUuid'

/**
 * 获取单位信息
 * 如果有id则通过ID查询
 * 如果有分页信息则再次通过分页信息查询
 * 返回结果始终是分页的格式
 */
export const getDwxxDB = (params) => {
  console.log('单位信息入参', params)
  let paramsId = undefined
  let page = undefined
  let pageSize = undefined
  if (params) {
    paramsId = params.dwid
    page = params.page
    pageSize = params.pageSize
  }
  let list_total = undefined
  if (paramsId) {
    list_total = DB.get('dwxx_List')
      .filter((item) => {
        if (paramsId == item.dwlxid) {
          return item
        }
      })
      .cloneDeep()
      .value()
  } else {
    list_total = DB.get('dwxx_List').cloneDeep().value()
  }
  // 手动分页
  let pageList = list_total
  if (page && pageSize && list_total) {
    pageList = list_total.slice(
      pageSize * (page - 1),
      pageSize * (page - 1) + pageSize
    )
  }
  let resList = {
    list: pageList,
    list_total: list_total,
    total: list_total.length,
  }
  console.log('单位信息', resList)
  return resList
}

/**
 * 通过单位ID获取单位信息
 */
export const selectDwxxByDwid = (dwid) => {
  return DB.get('dwxx_List').find({ dwid: dwid }).cloneDeep().value()
}

/**
 * 获取单位信息集合
 */
export const getDwxxListAll = () => {
  return DB.get('dwxx_List').cloneDeep().value()
}

// 插入单位信息表 dwxx_list
export const insertDwxxList = (params) => {
  console.log('单位信息入参(新增)', params)
  try {
    // 加入uuid
    params.dwid = getUuid()
    // 已注册则不然再次注册
    let dwxxList = DB.get('dwxx_List').cloneDeep().value()
    if(dwxxList.length > 0) {
      console.log('单位信息已存在')
      return
    }
    // 插入数据库
    DB.get('dwxx_List').push(params).write()
  } catch (error) {
    console.log('error', error)
    return false
  }
  return true
}

export const addzzjg = (param) => {
  try {
    DB.get('zzjg_list').push(param).write()
  } catch (error) {
    console.log('单位信息注册组织机构异常', error)
    return false
  }
  return true
}

// 通过任务ID获取单位信息
export const selectDwxxByRwid = (rwid) => {
  console.log('rwid', rwid)
  const rwxx = DB.get('scrw_list').find({ rwid: rwid }).cloneDeep().value()
  console.log('rwxx', rwxx)
  return DB.get('dwxx_List').find({ dwid: rwxx.dwid }).cloneDeep().value()
}

export const getDwxx = (params) => {
  console.log('单位信息入参', params)
  let login = DB.get('dwxx_List').cloneDeep().value()
	console.log(login)
	return login
}
//单位注册-----------------------------------单位注册********
export const addDwxx = (params) => {
	DB.read().get('dwxx_List').push(params).write()
}
//单位注册-----------------------------------单位注册********
export const deleteDwxx = (params) => {
	DB.read().get('dwxx_List').remove(params).write()
}

// 单位信息更新
export const updateDwxx = (params) => {
  let dwid = params.dwid
  if(!dwid) {
    console.log('dwid为空')
    return
  }
  DB.get('dwxx_List').find({dwid:dwid}).assign(params).write()
}
