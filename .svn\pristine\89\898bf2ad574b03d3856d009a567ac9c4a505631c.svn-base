export default [
  {
    name: 'bgglsy',
    path: '/bgglsy',
    component: () => import('../bgglsy.vue'),
    meta: {
      name: '报告管理首页',
      icon: 'aaa',
      // hidden: false,
      showHeaderMenu: true,
      // showAsideMenu: true
    }
  },
  {
    name: 'bggl',
    path: '/bggl',
    component: () => import('../bggl.vue'),
    meta: {
      name: '报告管理',
      icon: 'aaa',
      // hidden: false,
      showHeaderMenu: true,
      // showAsideMenu: true
    }
  },
  {
    name: 'bgglgl',
    path: '/bgglgl',
    component: () => import('../bgglgl.vue'),
    meta: {
      name: '文档管理',
      icon: 'aaa',
      // hidden: false,
      showHeaderMenu: true,
      // showAsideMenu: true
    }
  }
]