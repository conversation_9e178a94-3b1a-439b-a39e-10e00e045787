export default [
  {
    name: 'tzglsy',
    path: '/tzglsy',
    component: () => import('../tzgltabs.vue'),
    meta: {
      name: '台账管理',
      icon: 'aaa',
      hidden: false,
      showHeaderMenu: true,
      showAsideMenu: true,
      menuList: ['/bmzd', '/dmzrr','/dmsq','/zzjg','/zzgl', '/pxqd','/smgwgl', '/smry','/slsmry','/bmgbqk','/smrybzz', '/ryxz', '/gwbg', '/lglz','/cgcj','/smwlsb','/fmwlsb', '/csgl', '/csbg','/smjsj','/fsmjsj','/smydccjz','/smbgzdhsb','/fsmbgzdhsb','/aqcp',"/smzttz",'/gjmmsx','/dmpx','/dmqkndtj','/bmqsxqdqk','/zfcgxmqk',]
    }
  },
  {
    name: 'bmzd',
    path: '/bmzd',
    component: () => import('../bmzd.vue'),
    meta: {
      name: '制度管理',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'zzjg',
    path: '/zzjg',
    component: () => import('../zzjg.vue'),
    meta: {
      name: '机构管理',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'zzgl',
    path: '/zzgl',
    component: () => import('../zzgl.vue'),
    meta: {
      name: '组织概览',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'smry',
    path: '/smry',
    component: () => import('../smry.vue'),
    meta: {
      name: '在岗涉密人员',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'slsmry',
    path: '/slsmry',
    component: () => import('../slsmry.vue'),
    meta: {
      name: '三类涉密人员',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'bmgbqk',
    path: '/bmgbqk',
    component: () => import('../bmgbqk.vue'),
    meta: {
      name: '保密干部情况',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'smrybzz',
    path: '/smrybzz',
    component: () => import('../smrybzz.vue'),
    meta: {
      name: '涉密人员被追责情况',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'ryxz',
    path: '/ryxz',
    component: () => import('../ryxz.vue'),
    meta: {
      name: '人员新增汇总',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'gwbg',
    path: '/gwbg',
    component: () => import('../gwbg.vue'),
    meta: {
      name: '涉密岗位变更',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'lglz',
    path: '/lglz',
    component: () => import('../lglz.vue'),
    meta: {
      name: '离岗离职',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'cgcj',
    path: '/cgcj',
    component: () => import('../cgcj.vue'),
    meta: {
      name: '出国出境涉密人员',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'csgl',
    path: '/csgl',
    component: () => import('../csgl.vue'),
    meta: {
      name: '场所管理',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'csbg',
    path: '/csbg',
    component: () => import('../csbg.vue'),
    meta: {
      name: '场所变更',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'smgwgl',
    path: '/smgwgl',
    component: () => import('../smgwgl.vue'),
    meta: {
      name: '涉密岗位',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'smjsj',
    path: '/smjsj',
    component: () => import('../smjsj.vue'),
    meta: {
      name: '涉密计算机',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'fsmjsj',
    path: '/fsmjsj',
    component: () => import('../fsmjsj.vue'),
    meta: {
      name: '非涉密计算机',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'smydccjz',
    path: '/smydccjz',
    component: () => import('../smydccjz.vue'),
    meta: {
      name: '涉密移动存储介质',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'smbgzdhsb',
    path: '/smbgzdhsb',
    component: () => import('../smbgzdhsb.vue'),
    meta: {
      name: '涉密办公自动化设备',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'fsmbgzdhsb',
    path: '/fsmbgzdhsb',
    component: () => import('../fsmbgzdhsb.vue'),
    meta: {
      name: '非涉密办公自动化设备',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'smwlsb',
    path: '/smwlsb',
    component: () => import('../smwlsb.vue'),
    meta: {
      name: '涉密网络设备',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'fmwlsb',
    path: '/fmwlsb',
    component: () => import('../fmwlsb.vue'),
    meta: {
      name: '非密网络设备',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'aqcp',
    path: '/aqcp',
    component: () => import('../aqcp.vue'),
    meta: {
      name: '安全产品',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'smzttz',
    path: '/smzttz',
    component: () => import('../smzttz.vue'),
    meta: {
      name: '载体管理',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'pxqd',
    path: '/pxqd',
    component: () => import('../pxqd.vue'),
    meta: {
      name: '培训清单',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'dmzrr',
    path: '/dmzrr',
    component: () => import('../dmzrr.vue'),
    meta: {
      name: '定密责任人',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'dmsq',
    path: '/dmsq',
    component: () => import('../dmsq.vue'),
    meta: {
      name: '定密授权',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'gjmmsx',
    path: '/gjmmsx',
    component: () => import('../gjmmsx.vue'),
    meta: {
      name: '国家秘密事项',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'dmpx',
    path: '/dmpx',
    component: () => import('../dmpx.vue'),
    meta: {
      name: '定密培训',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'dmqkndtj',
    path: '/dmqkndtj',
    component: () => import('../dmqkndtj.vue'),
    meta: {
      name: '定密情况年度统计',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'bmqsxqdqk',
    path: '/bmqsxqdqk',
    component: () => import('../bmqsxqdqk.vue'),
    meta: {
      name: '不明确事项确定情况',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'zfcgxmqk',
    path: '/zfcgxmqk',
    component: () => import('../zfcgxmqk.vue'),
    meta: {
      name: '政府采购项目情况',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
]