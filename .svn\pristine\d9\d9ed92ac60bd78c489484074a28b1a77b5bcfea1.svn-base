import db from './adapter/zczpSystemAdaptor'

//保密制度-----------------------------------保密制度初始化列表********
// 一键检查查询Csgl_list中数据
export const getZjYz = () => {
	let resList = db.get('smcs_setting_list').filter(function(item) {
			return item
	}).cloneDeep().value()
	return resList
}
// 一键检查查询Csgl_list中数据
// export const getZjBmYz = () => {
// 	let resList = db.get('bmzd_setting_list').filter(function(item) {
// 			return item
// 	}).cloneDeep().value()
// 	return resList
// }
export const getYz = (param) => {
	let resList = db.get('setting_list').filter(function(item) {
			if(item.csbs == param){
				console.log(item)
				return item
			}
	}).cloneDeep().value()
	return resList
}




// 一键检查查询Csgl_list中数据
export const getZzjgYz = () => {
	let resList = db.get('zzjg_setting_list').filter(function(item) {
			return item
	}).cloneDeep().value()
	return resList
}
// 一键检查查询smgw_setting_list中数据  长时间未对涉密岗位进行更新
export const getSmgwgxYz = () => {
	let resList = db.get('smgw_setting_list').filter(function(item) {
			return item
	}).cloneDeep().value()
	return resList
}
// 一键检查查询zgsmry_setting_list中数据  长时间未对在岗涉密人员进行更新
export const getZgSmrygxYz = () => {
	let resList = db.get('zgsmry_setting_list').filter(function(item) {
			return item
	}).cloneDeep().value()
	return resList
}
// 一键检查查询ryxzhz_setting_list中数据  未建立或长时间未对人员新增汇总进行更新
export const getRyxzhzYz = () => {
	let resList = db.get('ryxzhz_setting_list').filter(function(item) {
			return item
	}).cloneDeep().value()
	return resList
}
// 一键检查查询gwbg_setting_list中数据  未建立或长时间未对岗位变更进行更新
export const getGwbgYz = () => {
	let resList = db.get('gwbg_setting_list').filter(function(item) {
			return item
	}).cloneDeep().value()
	return resList
}
// 一键检查查询lglz_setting_list中数据  未建立或长时间未对离岗离职进行更新
export const getLglzYz = () => {
	let resList = db.get('lglz_setting_list').filter(function(item) {
			return item
	}).cloneDeep().value()
	return resList
}
// 一键检查查询iypx setting list中数据  未建立或长时间未对离岗离职进行更新
export const getJypxqdYz = () => {
	let resList = db.get('lglz_setting_list').filter(function(item) {
			return item
	}).cloneDeep().value()
	return resList
}

// 一键检查查询sbxx_setting_list中数据 
export const getSmjsjYz = () => {
	let resList = db.get('sbxx_setting_list').filter(function(item) {
		return item
	}).cloneDeep().value()
	return resList
}
// 一键检查查询sbxx_setting_list中数据 
export const getSmzttzYz = () => {
	let resList = db.get('ztxx_setting_list').filter(function(item) {
		return item
	}).cloneDeep().value()
	return resList
}
export const getJdzczp = () => {
	let resList = db.get('jcjd_list').filter(function(item) {
		return item
	}).cloneDeep().value()
	return resList
}







