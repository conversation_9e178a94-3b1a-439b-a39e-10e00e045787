<template>
  <div style="height: 100%;">
    <hsoft_top_title>
      <template #left>参数设置</template>
    </hsoft_top_title>
    <!---->
    <div style="padding: 10px 0;text-align: right;">
      <el-button type="success" @click="showAddDialog">添加</el-button>
      <!-- <el-button type="primary" @click="getSettingList()">查询</el-button> -->
    </div>
    <el-table :data="settingList" border :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" style="width: 100%;border:1px solid #EBEEF5;" height="calc(100% - 32px - 60px - 32px - 10px - 10px)" stripe>
      <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
      <el-table-column prop="csbs" label="参数标识" width="120"></el-table-column>
      <el-table-column prop="cssm" label="参数说明"></el-table-column>
      <el-table-column prop="" label="参数值" width="160" align="center">
        <template slot-scope="scoped">
          <div>
            <span v-if="scoped.row.cszlx == 1">{{scoped.row.csz}}</span>
            <span v-if="scoped.row.cszlx == 2">{{formatTime(scoped.row.csz)}}</span>
            <span v-if="scoped.row.cszlx == 3">
              {{scoped.row.csz[0].month}}月{{scoped.row.csz[0].day}}日
              -
              {{scoped.row.csz[1].month}}月{{scoped.row.csz[1].day}}日
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="cszdw" label="单位" width="80" align="center"></el-table-column>
      <el-table-column prop="csbz" label="备注" width=""></el-table-column>
      <el-table-column prop="" label="操作" width="100">
        <template slot-scope="scoped">
          <el-button size="small" type="text" @click="modifySetting(scoped.row)">修改</el-button>
          <el-button size="small" type="text" @click="deleteSetting(scoped.row)" style="color:#F56C6C;">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange" :pager-count="5" :current-page="pageInfo.page" :page-sizes="[5, 10, 20, 30]" :page-size="pageInfo.pageSize" layout="total, prev, pager, sizes,next, jumper" :total="pageInfo.total" style="    padding-top: 10px;">
    </el-pagination>
    <!---->
    <!-- 添加系统参数 -->
    <el-dialog title="添加系统参数" :visible.sync="dialogVisibleSetting" width="35%">
      <div>
        <el-form :model="settingForm" :label-position="'right'" label-width="120px" size="mini">
          <div style="display:flex">
            <el-form-item label="参数标识" class="one-line">
              <el-input v-model="settingForm.csbs"></el-input>
            </el-form-item>
          </div>
          <el-form-item label="参数说明" class="one-line-textarea">
            <el-input type="textarea" v-model="settingForm.cssm"></el-input>
          </el-form-item>
          <div style="display:flex">
            <el-form-item label="参数值类型" class="one-line">
              <el-select v-model="settingForm.cszlx" style="width: 100%;">
                <el-option label="数字类型" :value="1"></el-option>
                <el-option label="日期" :value="2"></el-option>
                <el-option label="日期范围（范围）" :value="3"></el-option>
              </el-select>
            </el-form-item>
          </div>
          <div style="display:flex">
            <el-form-item label="参数值" class="one-line">
              <el-input v-if="settingForm.cszlx == 1" v-model="settingForm.csz" type="number" onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"></el-input>
              <el-date-picker v-if="settingForm.cszlx == 2" v-model="settingForm.csz" type="datetime" value-format="timestamp" placeholder="选择日期时间" style="width:calc(100%);">
              </el-date-picker>
              <div v-if="settingForm.cszlx == 3">
                <el-date-picker v-model="settingForm.csz" type="daterange" format="MM月dd日" value-format="MM-dd" :picker-options="pickerOptions" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" style="width:calc(100% - 20px);">
                </el-date-picker>
                <el-popover placement="bottom" width="100" trigger="hover">
                  <div>
                    <div style="display:flex;margin-bottom:10px">
                      <i class="el-icon-info" style="color:#409eef;position: relative;top: 2px;"></i>
                      <div class="tszt">提示</div>
                    </div>
                    <div>
                      从开始日期0点开始，到结束日期0点结束
                    </div>
                  </div>
                  <i class="el-icon-info" style="color:#409eef;cursor: pointer;" slot="reference"></i>
                </el-popover>
              </div>
            </el-form-item>
          </div>
          <div v-show="settingForm.cszlx == 1" style="display:flex">
            <el-form-item label="参数值计量单位" class="one-line">
              <el-input v-model="settingForm.cszdw"></el-input>
            </el-form-item>
          </div>
          <el-form-item label="备注" class="one-line-textarea">
            <el-input type="textarea" v-model="settingForm.csbz"></el-input>
          </el-form-item>
          <div style="display:flex">
            <el-form-item label="分组号" class="one-line">
              <el-input v-model="settingForm.fzh"></el-input>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="addSetting()">保 存</el-button>
        <el-button type="warning" @click="dialogVisibleSetting = false">关 闭</el-button>
      </span>
    </el-dialog>
    <!-- 修改系统参数 -->
    <el-dialog title="修改系统参数" :visible.sync="dialogVisibleSettingModify" width="35%">
      <el-form :label-position="'right'" label-width="120px" size="mini">
        <div style="display:flex">
          <el-form-item label="参数标识" class="one-line">
            <el-input v-model="settingForm.csbs" disabled></el-input>
          </el-form-item>
        </div>
        <el-form-item label="参数说明" class="one-line-textarea">
          <el-input type="textarea" v-model="settingForm.cssm" disabled></el-input>
        </el-form-item>
        <div style="display:flex">
          <el-form-item label="参数值类型" class="one-line">
            <el-select v-model="settingForm.cszlx" style="width: 100%;">
              <el-option label="数字类型" :value="1"></el-option>
              <el-option label="日期" :value="2"></el-option>
              <el-option label="日期范围（范围）" :value="3"></el-option>
            </el-select>
          </el-form-item>
        </div>
        <div style="display:flex">
          <el-form-item label="参数值" class="one-line">
            <el-input v-if="settingForm.cszlx == 1" v-model="settingForm.csz" type="number" onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"></el-input>
            <el-date-picker v-if="settingForm.cszlx == 2" v-model="settingForm.csz" type="datetime" value-format="timestamp" placeholder="选择日期时间" style="width:calc(100% - 20px);">
            </el-date-picker>
            <div v-if="settingForm.cszlx == 3">
              <el-date-picker v-model="settingForm.csz" type="daterange" format="MM月dd日" value-format="MM-dd" :picker-options="pickerOptions" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" style="width:calc(100% - 20px);">
              </el-date-picker>
              <el-popover placement="bottom" width="100" trigger="hover">
                <div>
                  <div style="display:flex;margin-bottom:10px">
                    <i class="el-icon-info" style="color:#409eef;position: relative;top: 2px;"></i>
                    <div class="tszt">提示</div>
                  </div>
                  <div>
                    从开始日期0点开始，到结束日期0点结束
                  </div>
                </div>
                <i class="el-icon-info" style="color:#409eef;cursor: pointer;" slot="reference"></i>
              </el-popover>
            </div>
          </el-form-item>
        </div>
        <div v-show="settingForm.cszlx == 1" style="display:flex">
          <el-form-item label="参数值计量单位" class="one-line">
            <el-input v-model="settingForm.cszdw" disabled></el-input>
          </el-form-item>
        </div>
        <el-form-item label="备注" class="one-line-textarea">
          <el-input type="textarea" v-model="settingForm.csbz" disabled></el-input>
        </el-form-item>
        <div style="display:flex">
          <el-form-item label="分组号" class="one-line">
            <el-input v-model="settingForm.fzh"></el-input>
          </el-form-item>
        </div>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="modifySettingDialog()">确 定</el-button>
        <el-button type="warning" @click="dialogVisibleSettingModify = false">取 消</el-button>
      </span>
    </el-dialog>
    <!---->
    <!---->
    <!---->
  </div>
</template>

<script>
import hsoft_top_title from '../../components/hsoft-top-title/hsoft-top-title.vue'

import { getWindowLocation } from '../../../utils/windowLocation'

import { dateFormatChinese } from '../../../utils/moment'

import { writeSystemOptionsLog } from '../../../utils/logUtils'

import { checkArr, decideChange } from '../../../utils/utils'

// 系统参数设置表
import {
  // 插入系统参数表
  insertSettingList,
  // 查询系统参数表
  selectSettingList,
  // 删除系统参数设置
  deleteSettingList,
  // 修改系统参数设置
  updateSettingList
} from '../../../db/zczpSystem/zczpSysyemDb'

export default {
  data () {
    return {
      // 分页信息
      pageInfo: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      // 更新系统参数dialog
      dialogVisibleSettingModify: false,
      // 添加系统参数dialog
      dialogVisibleSetting: false,
      settingForm: {},
      settingFormOld: {},
      cszlx: 1,
      // 系统参数设置表格数据
      settingList: [],
      pickerOptions: {
        disabledDate: time => {
          if (this.selectDate == null) {
            return false
          } else {
            return (this.selectDate.getFullYear() != time.getFullYear())
          }
        },
        onPick: date => {
          // 如果只选择一个则保存至selectDate 否则selectDate 为空
          if (date.minDate && !date.maxDate) {
            this.selectDate = date.minDate
          } else {
            this.selectDate = null
          }
        }
      }
    }
  },
  components: {
    hsoft_top_title
  },
  methods: {
    showAddDialog () {
      this.settingForm = {
        cszlx: 1
      }
      console.log(this.settingForm)
      this.dialogVisibleSetting = true
    },
    // 格式化时间
    formatTime (time) {
      return dateFormatChinese(new Date(time))
    },
    handleCurrentChange (val) {
      this.pageInfo.page = val
      this.getSettingList()
    },
    handleSizeChange (val) {
      this.pageInfo.pageSize = val
      this.getSettingList()
    },
    // 修改(表格)
    modifySetting (row) {
      let csz = row.csz
      // console.log(csz,checkArr(csz))
      if (checkArr(csz)) {
        row.csz[0] = csz[0].month + '-' + csz[0].day
        row.csz[1] = csz[1].month + '-' + csz[1].day
      }
      this.settingForm = JSON.parse(JSON.stringify(row))
      this.settingFormOld = JSON.parse(JSON.stringify(row))
      this.dialogVisibleSettingModify = true
    },
    // 修改（dialog）
    modifySettingDialog () {
      let params = this.settingForm
      let settingid = params.settingid
      if (!settingid) {
        this.$message.warning('系统参数设置ID为空')
        return
      }
      if (params.cszlx == 3) {
        let csz = params.csz
        // 放入月份和日期的对象
        params.csz = []
        // 开始月日
        let time = csz[0].split('-')
        params.csz.push({
          month: time[0],
          day: time[1]
        })
        // 结束月日
        time = csz[1].split('-')
        params.csz.push({
          month: time[0],
          day: time[1]
        })
      }
      updateSettingList(params)
      this.getSettingList()
      // 写入日志
      // 加入审计日志需要显示的内容
      let paramsExtra = {
        bs: params.csbs,
        modifyArr: []
      }
      // 判定修改
      paramsExtra.modifyArr = decideChange(this.settingFormOld, params, ['settingid', 'gxsj'])
      Object.assign(params, paramsExtra)
      let logParams = {
        xyybs: 'yybs_cssz',
        ymngnmc: '修改',
        extraParams: params
      }
      writeSystemOptionsLog(logParams)
      this.dialogVisibleSettingModify = false
    },
    // 删除参数设置
    deleteSetting (row) {
      let params = {
        settingid: row.settingid
      }
      deleteSettingList(params)
      this.getSettingList()
      // 写入日志
      // 加入审计日志需要显示的内容
      let paramsExtra = {
        bs: row.csbs,
        modifyArr: []
      }
      // 判定修改
      paramsExtra.modifyArr = decideChange(row, {}, ['settingid', 'gxsj'])
      Object.assign(row, paramsExtra)
      let logParams = {
        xyybs: 'yybs_cssz',
        ymngnmc: '删除',
        extraParams: row
      }
      writeSystemOptionsLog(logParams)
    },
    // 获取参数设置集合
    getSettingList () {
      this.settingForm = {}
      let params = {}
      Object.assign(params, this.pageInfo)
      let settingPage = selectSettingList(params)
      this.settingList = settingPage.list
      this.pageInfo.total = settingPage.total
    },
    // 添加参数设置
    addSetting () {
      let params = JSON.parse(JSON.stringify(this.settingForm))
      console.log('表单数据', params)
      if (params.cszlx != 1) {
        params.cszdw = '固定值'
      }
      if (params.cszlx == 3) {
        let csz = params.csz
        // 放入月份和日期的对象
        params.csz = []
        // 开始月日
        let time = csz[0].split('-')
        params.csz.push({
          month: time[0],
          day: time[1]
        })
        // 结束月日
        time = csz[1].split('-')
        params.csz.push({
          month: time[0],
          day: time[1]
        })
      }
      console.log(params)
      insertSettingList(params)
      this.getSettingList()
      // 写入日志
      // 加入审计日志需要显示的内容
      let paramsExtra = {
        bs: params.csbs,
        modifyArr: []
      }
      // 判定修改
      paramsExtra.modifyArr = decideChange({}, params, ['settingid', 'gxsj'])
      Object.assign(params, paramsExtra)
      let logParams = {
        xyybs: 'yybs_cssz',
        ymngnmc: '添加',
        extraParams: params
      }
      writeSystemOptionsLog(logParams)
      this.dialogVisibleSetting = false
    }
  },
  mounted () {
    //
    this.getSettingList()
    //
    console.log(new Date(2022, 11, 1))
  }
}
</script>

<style scoped>
.out-card {
  /* margin-bottom: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04); */
}
/**单位信息区域**/
.out-card .out-card-div {
  font-size: 13px;
  padding: 5px 20px;
}
.out-card .out-card-div div {
  padding: 10px 5px;
  display: flex;
}
.out-card .dwxx div:hover {
  background: #f4f4f5;
  border-radius: 20px;
}
.out-card .dwxx div label {
  /* background-color: red; */
  width: 125px;
  display: inline-block;
  text-align: right;
  font-weight: 600;
  color: #909399;
}
.out-card .dwxx div span {
  /* background-color: rgb(33, 92, 79); */
  flex: 1;
  display: inline-block;
  padding-left: 20px;
}
</style>