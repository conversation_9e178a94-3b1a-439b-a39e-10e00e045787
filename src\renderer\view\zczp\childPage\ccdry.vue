<template>
  <div style="height: 100%;width: 100%;">
    <!---->
    <div class="mhcx">
      <el-form :inline="true" size="medium" class="demo-form-inline" style="float:left">
        <el-form-item style="float: right;">
          <div>当前审查任务：{{dialogObj.rwmc}}</div>
        </el-form-item>
      </el-form>
      <!---->
      <el-form :inline="true" size="medium" class="demo-form-inline" style="float:right">
        <el-form-item style="float: right;">
          <el-button type="danger" size="medium" icon="el-icon-caret-left" @click="fhsyb">
            返回上一步
          </el-button>
        </el-form-item>
        <el-form-item style="float: right;">
          <el-button type="primary" size="medium" icon="el-icon-document-add" @click="saveToNext">
            保存至下一步
          </el-button>
        </el-form-item>
        <el-form-item style="float: right;">
          <el-button type="warning" size="medium" icon="el-icon-document-add" @click="save">
            临时保存
          </el-button>
        </el-form-item>
        <el-form-item style="float: right;">
          <el-button type="warning" size="medium" icon="el-icon-document-add" @click="tjry">
            添加人员
          </el-button>
        </el-form-item>
      </el-form>
      <div style="clear: both;"></div>
    </div>
    <!-- 表格区域 -->
    <div style="height: calc(100% - 58px - 34px - 42px);">
      <el-table :data="scList" border :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" style="width: 100%;border:1px solid #EBEEF5;" height="100%" stripe>
        <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
        <el-table-column prop="xm" label="姓名"></el-table-column>
        <el-table-column prop="bm" label="所在部门"></el-table-column>
        <el-table-column prop="zw" label="职务"></el-table-column>
        <el-table-column label="登记状态" width="250">
          <template slot-scope="scoped">
            <div>
              <span v-if="scoped.row.zt == 0">待登记</span>
              <span v-if="scoped.row.zt == 1">继续登记</span>
              <span v-if="scoped.row.zt == 2">完成登记</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="" label="操作" width="120">
          <template slot-scope="scoped">
            <el-button v-if="scoped.row.zt == 0" size="small" type="text" @click="dj(scoped.row)">登记</el-button>
            <el-button v-if="scoped.row.zt == 1" size="small" type="text" @click="dj(scoped.row)">继续登记</el-button>
            <el-button v-if="scoped.row.zt == 2" size="small" type="text" @click="toXqxx(scoped.row)">详情</el-button>
            <el-button size="small" type="text" @click="yc(scoped.row)">移除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 分页组件区域 -->
    <div style="border: 1px solid #ebeef5">
      <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange" :pager-count="5" :current-page="pageInfo.page" :page-sizes="[5, 10, 20, 30]" :page-size="pageInfo.pageSize" layout="total, prev, pager, sizes,next, jumper" :total="pageInfo.total">
      </el-pagination>
    </div>
    <!---->
    <!--添加人员dialog-->
    <el-dialog title="添加人员" :visible.sync="dialogVisibleTjry" width="50%">
      <div>
        <el-table :data="smryAllList" ref="smryAllTable" border :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" style="width: 100%;border:1px solid #EBEEF5;" height="50vh" stripe>
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column type="index" width="60" label="序号"></el-table-column>
          <el-table-column prop="xm" label="姓名"></el-table-column>
          <el-table-column prop="bm" label="所在部门"></el-table-column>
          <el-table-column prop="zw" label="职务"></el-table-column>
        </el-table>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" size="medium" @click="dialogSmryQdxz">保 存</el-button>
        <el-button type="warning" size="medium" @click="dialogVisibleTjry = false">关 闭</el-button>
      </span>
    </el-dialog>
    <!---->
    <!---->
    <!---->
  </div>
</template>

<script>

// 涉密人员
import { getsmry } from '../../../../db/smrydb'

import { setZczpIdsObj, getZczpIdsObj, removeZczpIdsObjField } from '../../../../utils/windowLocation'

import { writeOptionsLog } from '../../../../utils/logUtils'

import {
  //
  deleteCcdryListByID,
  //
  selectScrwByRwid,
  // 获取抽查的人员
  selectCcdryListPage,
  // 插入抽查的人员列表中(批量)
  insertCcdryList,
  // 更新审查任务
  updateScrwListZt
} from '../../../../db/zczpdb'

export default {
  data () {
    return {
      dialogObj: {
        // rwid: '93BC8D4D-2FDE-4BB9-85E5-F6214393B82E'
      },
      pageInfo: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      scList: [],
      smryAllList: [],
      dialogVisibleTjry: false
    }
  },
  computed: {},
  components: {
  },
  methods: {
    // 调转到详情信息页面（不可编辑的页面）
    toXqxx (row) {
      setZczpIdsObj('ccdryid', row.ccdryid)
      this.$router.push({
        path: '/ccdryDjXqxx',
        // query: {
        //   rwid: this.dialogObj.rwid,
        //   ccdryid: row.ccdryid
        // }
      })
    },
    // 登记
    dj (row) {
      setZczpIdsObj('ccdryid', row.ccdryid)
      this.$router.push({
        path: '/ccdryDj',
        // query: {
        //   rwid: this.dialogObj.rwid,
        //   ccdryid: row.ccdryid
        // }
      })
    },
    // 移除，使用[抽查的人员流水ID]移除该记录的[抽查的人员表]和[人员评分记录表]
    yc (row) {
      console.log(row)
      removeZczpIdsObjField(row.ccdryid)
      let bool = deleteCcdryListByID(row.ccdryid)
      if (bool) {
        this.$message.success('移除成功')
        // 写入操作日志
        writeOptionsLog('yybs-ccdry', '人员'+row.xm+'登记信息移除成功', row)
        // 刷新数据
        this.getCcdnryListByRwid()
      }
    },
    // dialog涉密人员确认选择事件触发
    dialogSmryQdxz () {
      let selection = this.$refs.smryAllTable.selection
      console.log('selection', selection)
      // 加入到抽查的人员表中
      let bool = insertCcdryList(selection, this.dialogObj.rwid)
      if (bool) {
        // 将任务状态码拨到4-抽查的人员临时保存
        // 更新任务状态码
        let updateScrwListParams = {
          rwid: this.dialogObj.rwid,
          zt: 4
        }
        bool = updateScrwListZt(updateScrwListParams)
        if (bool) {
          // 写入操作日志
          writeOptionsLog('yybs-ccdry', '添加抽查的人员', this.dialogObj)
          // 获取抽查的的人员
          this.getCcdnryListByRwid()
          this.dialogVisibleTjry = false
        }
      }
    },
    // 获取任务下抽查的人员信息
    getCcdnryListByRwid () {
      let params = {
        rwid: this.dialogObj.rwid
      }
      Object.assign(params, this.pageInfo)
      let ccdnryListPage = selectCcdryListPage(params)
      this.scList = ccdnryListPage.list
    },
    // 临时保存
    save () {
      // 更新任务状态码
      let updateScrwListParams = {
        rwid: this.dialogObj.rwid,
        zt: 4
      }
      let bool = updateScrwListZt(updateScrwListParams)
      if (bool) {
        this.$message.success('临时保存成功')
        // 写入操作日志
        writeOptionsLog('yybs-ccdry', '抽查的人员临时保存', this.dialogObj)
        return
      }
      this.$message.success('临时保存失败')
    },
    //
    saveToNext () {
      // 更新任务状态码
      let updateScrwListParams = {
        rwid: this.dialogObj.rwid,
        zt: 5
      }
      let bool = updateScrwListZt(updateScrwListParams)
      if (bool) {
        // 写入操作日志
        writeOptionsLog('yybs-ccdry', '抽查的人员保存完成', this.dialogObj)
        this.$message.success('保存成功')
        this.$router.push({
          path: '/jczj',
          // query: {
          //   // 任务ID
          //   rwid: this.dialogObj.rwid
          // }
        })
        return
      }
      this.$message.success('保存失败')
    },
    fhsyb () {
      // 更新任务状态码
      let updateScrwListParams = {
        rwid: this.dialogObj.rwid,
        zt: 3
      }
      let bool = updateScrwListZt(updateScrwListParams)
      if (bool) {
        this.$message.success('抽查的人员记录录入成功')
        this.$router.push({
          path: '/ccdnsjg',
          // query: {
          //   // 任务ID
          //   rwid: this.dialogObj.rwid
          // }
        })
      }
    },
    //
    handleSizeChange () { },
    handleCurrentChange (val) { },
    tjry () {
      this.dialogVisibleTjry = true
    },
    //
    returnSy () {
      this.$router.go(-1)
    }
  },
  watch: {},
  mounted () {
    //
    // this.dialogObj.rwid = this.$route.query.rwid
    this.dialogObj.rwid = getZczpIdsObj().rwid
    //
    let scrw = selectScrwByRwid(this.dialogObj.rwid)
    console.log('scrw', scrw)
    this.dialogObj.rwmc = scrw.rwmc
    // 获取抽查的人员（分页）
    this.getCcdnryListByRwid()
    // 获取所有涉密人员
    this.smryAllList = getsmry({}).list_total
    // console.log(this.smryAllList)
  }
}
</script>

<style scoped></style>
