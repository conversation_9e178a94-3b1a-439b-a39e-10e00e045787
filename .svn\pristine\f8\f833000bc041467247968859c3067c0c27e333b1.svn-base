<template>
    <div class="bgContainer">
                <!-- <div @click="scmw">生成密文</div> -->
        <div class="con positionR">
            <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline fl">
                <el-form-item label="" style="font-weight: 700;">
                    <el-input v-model="formInline.wdmc" clearable placeholder="请输入报告名称" class="widthw">
                    </el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
                </el-form-item>
                <el-form-item class="positionA bgBtnPosition" v-if="wdglisShow">
                    <el-button type="primary" icon="el-icon-s-order" @click="onBgClick">文档管理</el-button>
                </el-form-item>
                <div class="clearBoth"></div>
            </el-form>
            <div class="clearBoth"></div>
        </div>
        <div class="con-center" v-if="wdList.length > 0">
            <div class="center">
                <div class="border-wz" v-for="item in wdList" :key="item.id">
                    <div class="wz-title">
                        <img v-if="'.'+item.wdmc.split('.').pop() == '.docx'" src="./img/word.png" width="30px" alt="" >
                        <img v-if="'.'+item.wdmc.split('.').pop() == '.pdf'" src="./img/pdf.png" width="30px" alt="" >
                        <div class="wenzi">{{item.wdmc}}</div>
                        <img class="vip" src="./img/vip.png" width="30px" alt="" />
                    </div>
                    <div class="wz-con">
                        <img :src="item.wdslt" width="60px" height="80px" alt="" >
                        <div class="maxWidth">
                            <div class="wenzi1">{{item.wdzy}}...</div>
                            <div class="wz-btn">
                                <div>共 {{item.wdys }} 页&nbsp;&nbsp;&nbsp;&nbsp;类型: {{ item.wdlx }}</div>
                            </div>
                        </div>
                        <div class="andw">
                            <div  @click="quickLook(item)" class="quickLook cursorClick">快速预览</div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- -------------------------分页区域---------------------------- -->
            <!-- <div style="border: 1px solid #ebeef5;">
                <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange"
                    :pager-count="5" :current-page="page" :page-sizes="[5, 10, 20, 30]" :page-size="pageSize"
                    layout="total, prev, pager, sizes,next, jumper" :total="total">
                </el-pagination>
            </div> -->
        </div>
        <div class="noDatas" v-else>暂无数据</div>

    </div>
</template>
  
<script>
import { getDeportConfigPathDev } from '../../../utils/pathUtil'
// import { encryptAes,decryptAes } from '../../../utils/aesUtils'
import { decryptAesCBCHy,encryptAesCBCHy } from '../../../utils/aesUtils'
import { removeWindowLocation, getWindowLocation } from '../../../utils/windowLocation'
import {
  getlogin
} from "../../../db/loginyhdb"
import {
  getWdInfoDatas,
  getHykl,
  deleteFileItem,
  reviseHyKl
} from "../../../db/bgwdgldb.js";
import MD5 from 'md5'
// import unzipper from 'unzipper'
export default {
    //import引入的组件需要注入到对象中才能使用
    components: {},
    props: {},
    data() {
        //这里存放数据
        return {
            formInline: {
                wdmc: '',
            },
            wdList: [],
            tableDataCopy: [],
            listDatas: [],
            page: 1,
            pageSize: 10,
            total: 0,
            encryptList:[],
            wdglisShow: false
        }
    },
    //计算属性 类似于data概念
    computed: {},
    //监控data中数据变化
    watch: {},
    //方法集合
    methods: {
        // 根据文件名称生成文件id
        toCode (str) {
            var key = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"
            var len = key.length
            var a = key.split("")
            var s = "",b, b1, b2, b3
            for (var i = 0; i <str.length; i ++) { 
                b = str.charCodeAt(i)
                b1 = b % len
                b = (b - b1) / len
                b2 = b % len
                b = (b - b2) / len
                b3 = b % len
                s += a[b3] + a[b2] + a[b1]
            }
            return s
        },
        // scmw(){
        //     const fs = require('fs')
        //     let file = getDeportConfigPathDev('all')
        //     // 生成密钥
        //     let currentYear = new Date().valueOf()
        //     let that = this
        //     fs.readdir(file, function (err, files) {
        //         if (err) {
        //             return console.log('目录不存在，请用绝对路径')
        //         }
        //         files.forEach(fileName => { 
        //             let filePath = getDeportConfigPathDev(fileName)
        //             let fileType = '.'+fileName.split('.').pop()
        //             let id = that.toCode(fileName)
        //             let keyStr = 'hsoftBanner' + id
        //             // that.listDatas.push({id,fileName,fileType})
        //             // 数据加密
        //             fs.readFile(filePath, async function (err, data) {
        //                 if (err) {
        //                     console.log(err);
        //                 } else {
        //                     console.log(data)
        //                     var buf = Buffer.from(data);
        //                     let encryptStr
        //                     encryptStr =await encryptAes(JSON.stringify(buf), keyStr)
        //                     fs.writeFileSync(filePath, encryptStr, { encoding: 'utf8' })
        //                 }
        //             });
        //         }); 
        //     })
        // },
        // 初始化
        initFiles() {
            const fs = require('fs')
            let file = getDeportConfigPathDev('all')
            let params = {
                page: this.page,
                pageSize: this.pageSize
            }
            Object.assign(params, this.formInline)
            let resList = getWdInfoDatas(params)
            this.wdList = resList.list_total

            let chDatas = []
            let folderDatas = []
            this.wdList.forEach((item)=>{
                chDatas.push(item.wdmc)
            })
            let _this = this
            fs.readdir(file, function (err, files) {
                if (err) {
                    return console.log('目录不存在，请用绝对路径')
                }
                files.find((i)=>{
                    folderDatas.push(i)
                })
                for (var i=0;i<chDatas.length; i++) {
                    if (folderDatas.indexOf(chDatas[i]) == -1) {
                        _this.wdList.forEach((e)=>{
                            if(e.wdmc == chDatas[i]){
                                deleteFileItem(e)
                                let resList = getWdInfoDatas(params)
                                _this.wdList = resList.list_total

                            }
                        })
                    }
                }
                _this.wdList.forEach((item)=>{
                    let file1 = getDeportConfigPathDev(item.wdmc + item.wdsltType)
                    // console.log()
                    item.wdslt = file1
                })
            })
            this.tableDataCopy = resList.list_total
        },
        //查询方法
        onSubmit() {
            //  form是查询条件
            console.log(this.formInline);
            // 备份了一下数据
            let arr = this.tableDataCopy
            // 通过遍历key值来循环处理
            Object.keys(this.formInline).forEach(e => {
                // 调用自己定义好的筛选方法
                console.log(this.formInline[e]);
                arr = this.filterFunc(this.formInline[e], e, arr)
            })
            // 为表格赋值
            this.wdList = arr
        },
        filterFunc(val, target, filterArr) {
            // 参数不存在或为空时，就相当于查询全部
            if (val == undefined || val == '') {
                return filterArr
            }
            return filterArr.filter(p => {
                return p[target].indexOf(val) > -1
            })
        },
        // 报告管理
        onBgClick(){
            this.$router.push("/bgglgl")
            // this.$router.push({path: '/bgglgly',query:{ name:'zhang' }})
        },
        // 快速预览
        quickLook (item){
            item.fileType = '.'+item.wdmc.split('.').pop()
            // 数据库中是否有数据
            let hyklDatas = getHykl()
            console.log('hyklDatas', hyklDatas)
            // 单位注册号
            let dwzch = getlogin()[0].dwzch
            let keyStr = MD5(dwzch).substring(8,24)
            // 数据库中无会员口令提示输入密钥串
            if (!hyklDatas || !hyklDatas[0] || hyklDatas[0].kl == ""){
                console.log('数据库里没有key，非会员----带锁')
                // 如果没有密钥或者密钥不匹配，查看首页并提示提示相关信息
                this.$router.push({path: '/bggl',query:{ item,allFileDatas:this.wdList,klIsRight:false }})
            } else if(hyklDatas.length > 0) { // 数据库中存在密钥串 验证库中密钥串是否有效
                console.log('数据库里有key------待验证')
                // 如果有，则验证密钥是否有效或是否在有限期内
                let backStr = decryptAesCBCHy(hyklDatas[0].kl,keyStr)
                console.log(backStr)
                // 获取口令里的单位注册号
                let mDwzch = backStr.dwzch
                // 验证本地单位注册号与解密单位注册号是否相同
                if(mDwzch == dwzch){
                    console.log('验证此key中单位注册号与本地单位注册号一致')
                    console.log(backStr.isZcStatus)
                    if(!backStr.isZcStatus){
                        // 未注册
                        console.log('未注册')
                        if(Date.now() > backStr.qfrq && Date.now() < backStr.qfrq + backStr.yxqqz * 86399915){
                            // 当前系统时间是否在签发日期+注册阈值内
                            backStr.isZcStatus = 1
                            backStr.zcDate = Date.now()
                            encryptAesCBCHy(backStr,keyStr)
                            let params = {
                                "id":"1",
                                "kl":encryptAesCBCHy(JSON.stringify(backStr),keyStr)
                            }
                            reviseHyKl(params)
                            console.log('注册成功可访问全部！')
                            // 如果正确并且在有效期内  可查看全部文章并可以下载全文
                            this.$router.push({path: '/bggl',query:{ item,allFileDatas:this.wdList,klIsRight:true }})
                        }else{
                            console.log('密钥串已经失效')
                            this.$message.error('密钥串已经失效')
                        }
                    }else{
                        console.log('已经注册')
                        // 已经注册
                        // let bsItem = backStr.yxrq.find((item)=>{
                        //     return item.bs == "测试标识"
                        // })
                        let bsItem = backStr.yxrq[0]
                        console.log(backStr.qfrq)
                        console.log(backStr.zcDate)
                        console.log(bsItem.yxq)
                        if(backStr.zcDate > backStr.qfrq && backStr.zcDate < bsItem.yxq){
                            console.log('已经注册 在有效期内  可查看全部文章并可以下载全文')
                            // 如果正确并且在有效期内  可查看全部文章并可以下载全文
                            this.$router.push({path: '/bggl',query:{ item,allFileDatas:this.wdList,klIsRight:true }})
                        }else{
                            console.log('已经注册 密钥不匹配，查看首页并提示提示相关信息')
                            // 如果没有密钥或者密钥不匹配，查看首页并提示提示相关信息
                            this.$router.push({path: '/bggl',query:{ item,allFileDatas:this.wdList,klIsRight:false }})
                        }
                    }
                    
                }else{ // 提示非法密钥串
                    console.log('非法密钥串')
                    this.$message.error('非法密钥串')
                }
            }
        },
        //列表分页--跳转页数
        handleCurrentChange(val) {
            this.page = val
            // this.smgwgl()
        },
        //列表分页--更改每页显示个数
        handleSizeChange(val) {
            this.page = 1
            this.pageSize = val
            // this.smgwgl()
        }
    },
    //生命周期 - 创建完成（可以访问当前this实例）
    created() {
        let localObj = getWindowLocation()
        this.wdglisShow =  localObj.yhm == 'root' ? true : false
    },
    //生命周期 - 挂载完成（可以访问DOM元素）
    mounted() {
        this.initFiles()
    },
    //生命周期-创建之前
    beforeCreated() { },
    //生命周期-挂载之前
    beforeMount() { },
    //生命周期-更新之前
    beforUpdate() { },
    //生命周期-更新之后
    updated() { },
    //生命周期-销毁之前
    beforeDestory() { },
    //生命周期-销毁完成
    destoryed() { },
    //如果页面有keep-alive缓存功能，这个函数会触发
    activated() { }
}
</script>
<style scoped>
.bgContainer {
    width:96%;margin:0 auto;height: 100%;
}
.con {
    width: 100%;
    height: 8%;
    display: flex;
}

.con-center {
    width: 100%;
    height: 92%;
}
.center{
    width: 100%;
    height: 93%;
    overflow-y: scroll;
}
.border-wz{
    width: 100%;
    height: 26%;
    background: rgb(255, 255, 255);
    margin-bottom: 1%;
    border-radius: 5px;
    padding: 10px;
    box-shadow: 0px 2px 10px 0px rgba(107, 117, 134, 0.15)
}
.wz-title{
    width: 98%;
    height: 25%;
    display: flex;
    align-items: center;
    padding:0px 1%;
    margin-bottom: 7px;
}
/deep/.el-input__inner {
    font-family: 'SourceHanSansSCziti';
}
/deep/.el-button span {
    font-family: 'SourceHanSansSCziti';
}
.wz-title .vip {
    margin-top: 5px;
}
.wenzi{
    /* font-size: 14px; */
    font-weight: 700;
    margin-left: 10px;
    margin-right: 10px;
    font-family: 'SourceHanSansSCziti';
    font-size: 20px;
}
.wz-con{
    width: 98%;
    height: 75%;
    display: flex;
    align-items: center;
    padding:0px 1%;
    position: relative;
}
.wenzi1{
    font-family: 'SourceHanSansSCziti';
    font-size: 16px;
    margin-left: 15px;
    margin-right: .5%;
    height: 63px;
    line-height: 25px;
    color: grey;
    width: calc(100% - 30px);
}
.wz-btn{
    font-family: 'SourceHanSansSCziti';
    width: 100%;
    font-size: 16px;
    margin-left: 15px;
    margin-right: .5%;
    /* margin-top: 1%; */
    color: rgb(166, 175, 175);
}
.wz-btn div{
    font-family: 'SourceHanSansSCziti';
}
.andw{
    position: absolute;
    right: 30px;
    bottom: 10%;
}
.quickLook {
    font-family: 'SourceHanSansSCziti';
    font-size: 16px;
    border-radius: 5px;
    background: #409EFF;
    color: #FFFFFF;
    padding: 5px 10px;
}
.bgBtnPosition {
    right: 0px;
    top: 0px;
}
.noDatas {
    height: calc(100% - 50px);
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 20px;
    font-family: 'SourceHanSansSCziti';
    color: #a3a6ab;
}

</style>