import {
    getJgxx
} from "../../../db/zzjgdb";
import {
    getBmzd
} from "../../../db/bmzddb";
import {
    getYz
} from "../../../db/syszjb";
import { getDwxx } from "../../../db/dwxxDb"
import {
    getSmgwgl
} from "../../../db/smgwgldb";
import {
    getsmry
} from "../../../db/smrydb";
import {
    getRyxz
} from "../../../db/ryxzdb";
import {
    getGwbg
} from "../../../db/gwbgdb";
import {
    getLglz
} from "../../../db/lglzdb";
import {
    getCsgl
} from "../../../db/csgldb";
import {
    getSmzttz,
} from "../../../db/smzttzdb";
import {
    getCsbg
} from "../../../db/csbgdb";
import {
    getSmjsj
} from "../../../db/smjsjdb";
import {
    getFsmjsj
} from "../../../db/fsmjsjdb";
import {
    getSmydccjz
} from "../../../db/smydccjzdb";
import {
    getSmbgzdhsb
} from "../../../db/smbgzdhsbdb.js";
import {
    getFsmbgzdhsb
} from "../../../db/fsmbgzdhsbdb";
import {
    getSmwlsb
} from "../../../db/smwlsbdb";
import {
    getFmwlsb
} from "../../../db/fmwlsbdb";
import {
    getAqcp
} from "../../../db/aqcpdb";
import {
    getZczpRiskDatas
} from "../../../db/yjjcdb";
import {
    getdmzrr
} from "../../../db/dmzrrdb.js";
import {
    getdmsq
} from "../../../db/dmsqdb.js";
import {
    getGjmmsx
} from "../../../db/gjmmsxdb.js";
import {
    getdmpx
} from "../../../db/dmpxdb.js";
import {
    getDmqkndtj
} from "../../../db/dmqkndtjdb";
import {
    getDmqsxqdqk
} from "../../../db/bmqsxqdqkdb";
import {
    getZfcgxmqk
} from "../../../db/zfcgxmqkdb";
import {
    addSmgwglHistory,
    addSmryHistory,
    addRyxzhzHistory,
    addRymjbghzHistory,
    addSmcsglHistory,
    addCsbgHistory,
    addLglzHistory,
    reviseDbgzStatus,
    getDbgzStatus,
    addSmjsjHistory,
    addFsmjsjHistory,
    addSmydccjzHistory,
    addBgzdhsbHistory,
    addfBgzdhsbHistory,
    addSmwlsbHistory,
    addFsmwlsbHistory,
    addAqcpHistory,
    addSmztHistory,
    addDmzrrHistory,
    addDmsqHistory,
    addGjmmsxHistory,
    addDmpxHistory,
    addDmqkndtjHistory,
    addBmqsxqdqkListHistory,
    addZfcgxmqkListHistory,
    getSmcsglHistory,
    deleteLsCsgl,
    getSmCsbgHistory,
    deleteLsCsbg,
    getSmgwHistory,
    deleteLsSmgw,
    getSmryHistory,
    deleteLsSmry,
    getRyxzhzHistory,
    deleteLsRyxzhz,
    getRymjbghzHistory,
    deleteLsRymjbghz,
    getLglzHistory,
    deleteLsLglz,
    getSmjsjHistory,
    deleteLsSmjsj,
    getFsmjsjHistory,
    deleteLsFsmjsj,
    getSmydccjzHistory,
    deleteLsSmydccjz,
    getSmBgzdhsbHistory,
    deleteLsSmBgzdhsb,
    getFsmBgzdhsbHistory,
    deleteLsFsmBgzdhsb,
    getSmwlsbHistory,
    deleteLsSmwlsb,
    getFsmwlsbHistory,
    deleteLsFsmwlsb,
    getAqcpHistory,
    deleteLsAqcp,
    getSmzttzHistory,
    deleteLsSmzttz,
    getDmzrrHistory,
    deleteLsDmzrr,
    getDmsqHistory,
    deleteLsDmsq,
    getGjmmsxHistory,
    deleteLsGjmmsx,
    getDmpxHistory,
    deleteLsDmpx,
    getDmqkndtjHistory,
    deleteLsDmqkndtj,
    getBmqsxqdqkHistory,
    deleteLsBmqsxqdqk,
    getZfcgxmqkHistory,
    deleteLsZfcgxmqk
} from "../../../db/dbgzlsdb"
import {
    writeTrajectoryLog
} from '../../../utils/logUtils'
export default {
    data() {
        return {
            statusArr: [],
            nowsYear: 2000, // 当前年
            zcxxIsPerfectShow: false, // 注册信息
            zcxxPerfectCount: 0, // 注册信息需要完善的个数
            dadbShow: false,
            downloadShow: false,
            download1Show: false,
            // 日常各模块
            rcUpdateCounts: 0, // 日常信息需要完善的个数
            zczpUpdateCounts: 0, // 自查自评需要完善的个数
            rcglDivShow: true, // 日常模块显示
            page: 1,
            pageSize: 10,
            zzxxIsPerfectShow: false,
            bmzdIsPerfectShow: false,
            zzjgIsPerfectShow: false,
            ryxxIsPerfectShow: false,
            csxxIsPerfectShow: false,
            sbxxIsPerfectShow: false,
            ztxxIsPerfectShow: false,
            zczp1IsPerfectShow: false,
            zczp2IsPerfectShow: false,
            zczp3IsPerfectShow: false,
            zczp4IsPerfectShow: false,
            // 获取各表数据长度
            smgwListLength: 0,
            zgsmryHzListLength: 0,
            ryxzHzListLength: 0,
            rynjbgHzListLength: 0,
            lghzListLength: 0,
            csglListLength: 0,
            csbgListLength: 0,
            smjsjListLength: 0,
            fsmjsjListLength: 0,
            ydccjzListLength: 0,
            bgzdhsbListLength: 0,
            fsmbgzdhsbListLength: 0,
            wlsbListLength: 0,
            fwlsbListLength: 0,
            aqcpListLength: 0,
            smztListLength: 0,
            dmzrrListLength: 0,
            dmsqListLength: 0,
            gjmmsxListLength: 0,
            dmpxListLength: 0,
            dmqkndtjListLength: 0,
            bmqsxqdqkListLength: 0,
            zfcgxmqkListLength: 0,
            // 重新生成按钮控制
            smryCxscShow: false,
            smcsCxscShow: false,
            smsbCxscShow: false,
            smztCxscShow: false,
            smsxCxscShow: false,
            // 一键生成生成年份
            smrySctime: true,
            smcsSctime: true,
            smsbSctime: true,
            smztSctime: true,
            smsxSctime: true,

            dbgzDateStart: '',
            dbgzDateEnd: ''
        }
    },
    computed: {
        dbscsJcomputed(){
            return `待办工作生成日期区间${this.dbgzDateStart}-${this.dbgzDateEnd},需在系统设置->参数设置->待办工作生成日期区间进行配置`
        },
        // 涉密人员一键生成
        smryTzScShow() {
            return !this.statusArr[0].smgwdj && this.dadbShow == true && this.smgwListLength > 0 || !this.statusArr[0].smryhz && this.dadbShow == true && this.zgsmryHzListLength > 0 || !this.statusArr[0].ryxzhz && this.dadbShow == true && this.ryxzHzListLength > 0 || !this.statusArr[0].rynjbghz && this.dadbShow == true && this.rynjbgHzListLength > 0 || !this.statusArr[0].lghz && this.dadbShow == true && this.lghzListLength > 0
        },
        // 涉密人员重新生成
        cxscSmryTzScShow() {
            return this.dadbShow == true && this.smgwListLength > 0 && this.smryCxscShow ||
                this.dadbShow == true && this.zgsmryHzListLength > 0 && this.smryCxscShow ||
                this.dadbShow == true && this.ryxzHzListLength > 0 && this.smryCxscShow ||
                this.dadbShow == true && this.rynjbgHzListLength > 0 && this.smryCxscShow ||
                this.dadbShow == true && this.lghzListLength > 0 && this.smryCxscShow
        },
        // 涉密场所一键生成
        smcsTzScShow() {
            return !this.statusArr[1].smcsdj && this.dadbShow == true && this.csglListLength > 0 || !this.statusArr[1].csbgdj && this.dadbShow == true && this.csbgListLength > 0
        },
        // 涉密场所重新生成
        cxscSmcsTzScShow() {
            return this.dadbShow == true && this.csglListLength > 0 && this.smcsCxscShow ||
                this.dadbShow == true && this.csbgListLength > 0 && this.smcsCxscShow
        },
        // 涉密设备一键生成
        smsbTzScShow() {
            return !this.statusArr[2].smjsjtz && this.dadbShow == true && this.smjsjListLength > 0 || !this.statusArr[2].fsmjsjtz && this.dadbShow == true && this.fsmjsjListLength > 0 || !this.statusArr[2].ydccjztz && this.dadbShow == true && this.ydccjzListLength > 0 || !this.statusArr[2].bgzdhsbtz && this.dadbShow == true && this.bgzdhsbListLength > 0 || !this.statusArr[2].fsmbgzdhsbtz && this.dadbShow == true && this.fsmbgzdhsbListLength > 0 || !this.statusArr[2].wlsbtz && this.dadbShow == true && this.wlsbListLength > 0 || !this.statusArr[2].fwlsbtz && this.dadbShow == true && this.fwlsbListLength > 0 || !this.statusArr[2].aqcptz && this.dadbShow == true && this.aqcpListLength > 0
        },
        // 涉密设备重新生成
        cxscSmsbTzScShow() {
            return this.dadbShow == true && this.smjsjListLength > 0 && this.smsbCxscShow ||
                this.dadbShow == true && this.fsmjsjListLength > 0 && this.smsbCxscShow ||
                this.dadbShow == true && this.ydccjzListLength > 0 && this.smsbCxscShow ||
                this.dadbShow == true && this.bgzdhsbListLength > 0 && this.smsbCxscShow ||
                this.dadbShow == true && this.fsmbgzdhsbListLength > 0 && this.smsbCxscShow ||
                this.dadbShow == true && this.wlsbListLength > 0 && this.smsbCxscShow ||
                this.dadbShow == true && this.fwlsbListLength > 0 && this.smsbCxscShow ||
                this.dadbShow == true && this.aqcpListLength > 0 && this.smsbCxscShow
        },
        // 涉密载体一键生成
        smztTzScShow() {
            return !this.statusArr[3].smzttz && this.dadbShow == true && this.smztListLength > 0
        },
        // 涉密载体重新生成
        cxscSmztTzScShow() {
            return this.dadbShow == true && this.smztListLength > 0 && this.smztCxscShow
        },
        // 涉密事项一键生成
        smsxTzScShow() {
            return !this.statusArr[4].dmzrr && this.dadbShow == true && this.dmzrrListLength > 0 || !this.statusArr[4].dmsq && this.dadbShow == true && this.dmsqListLength > 0 || !this.statusArr[4].gjmmsx && this.dadbShow == true && this.gjmmsxListLength > 0 || !this.statusArr[4].dmpx && this.dadbShow == true && this.dmpxListLength > 0 || !this.statusArr[4].dmqkndtj && this.dadbShow == true && this.dmqkndtjListLength > 0 || !this.statusArr[4].bmqsxqdqk && this.dadbShow == true && this.bmqsxqdqkListLength > 0 || !this.statusArr[4].zfcgxmqk && this.dadbShow == true && this.zfcgxmqkListLength > 0
        },
        // 涉密事项重新生成
        cxscSmsxTzScShow() {
            return this.dadbShow == true && this.dmzrrListLength > 0 && this.smsxCxscShow ||
                this.dadbShow == true && this.dmsqListLength > 0 && this.smsxCxscShow ||
                this.dadbShow == true && this.gjmmsxListLength > 0 && this.smsxCxscShow ||
                this.dadbShow == true && this.dmpxListLength > 0 && this.smsxCxscShow ||
                this.dadbShow == true && this.dmqkndtjListLength > 0 && this.smsxCxscShow ||
                this.dadbShow == true && this.bmqsxqdqkListLength > 0 && this.smsxCxscShow ||
                this.dadbShow == true && this.zfcgxmqkListLength > 0 && this.smsxCxscShow
        },
        // 涉密人员整体显示
        smryAllShow() {
            let allLength = this.smgwListLength + this.zgsmryHzListLength + this.ryxzHzListLength + this.rynjbgHzListLength + this.lghzListLength
            return allLength == 0 ? false : true
        },
        // 涉密场所整体显示
        smcsAllShow() {
            let allLength = this.csglListLength + this.csbgListLength
            return allLength == 0 ? false : true
        },
        // 涉密设备整体显示
        smsbAllShow() {
            let allLength = this.smjsjListLength + this.fsmjsjListLength + this.ydccjzListLength + this.bgzdhsbListLength + this.fsmbgzdhsbListLength + this.wlsbListLength + this.fwlsbListLength + this.aqcpListLength
            return allLength == 0 ? false : true
        },
        // 涉密事项整体显示
        smsxAllShow() {
            let allLength = this.dmzrrListLength + this.dmsqListLength + this.gjmmsxListLength + this.dmpxListLength + this.dmqkndtjListLength + this.bmqsxqdqkListLength + this.zfcgxmqkListLength
            return allLength == 0 ? false : true
        },
        // 涉密人员上报显示
        smryDownLoadShow() {
            return this.downloadShow == true && this.smgwListLength > 0 || this.downloadShow == true && this.zgsmryHzListLength > 0 || this.downloadShow == true && this.ryxzHzListLength > 0 || this.downloadShow == true && this.rynjbgHzListLength > 0 || this.downloadShow == true && this.lghzListLength > 0
        },
        // 涉密事项上报显示
        smsxDownloadShow() {
            return this.download1Show == true && this.dmzrrListLength > 0 || this.download1Show == true && this.dmsqListLength > 0 || this.download1Show == true && this.gjmmsxListLength > 0 || this.download1Show == true && this.dmpxListLength > 0 || this.download1Show == true && this.dmqkndtjListLength > 0 || this.download1Show == true && this.bmqsxqdqkListLength > 0 || this.download1Show == true && this.zfcgxmqkListLength > 0
        },
    },
    methods: {
        // 获取待办工作状态
        getDbgzSattus() {
            this.statusArr = getDbgzStatus()
        },
        // 一键生成年度涉密人员相关台账
        yjscClick(name, type) {
            let params = {
                page: this.page,
                pageSize: this.pageSize
            }
            let updataItem = {}
            switch (name) {
                case 'smry':
                    if (type == 'yjsc') {
                        // 添加日志
                        let paramsLog = {
                            xyybs: 'mk_dbgzrz',
                            id: '-1',
                            ymngnmc: '一键生成年度涉密人员相关台账'
                        }
                        writeTrajectoryLog(paramsLog)
                        window.localStorage.setItem('smrySctime', new Date().getFullYear().toString())
                        this.smrySctime = false
                    }
                    // 获取涉密岗位数据
                    let smgwList = getSmgwgl(params).list_total
                    addSmgwglHistory(smgwList)
                    // 获取涉密人员汇总数据
                    let smryList = getsmry(params).list_total
                    addSmryHistory(smryList)
                    // 获取人员新增汇总数据
                    let ryxzhzList = getRyxz(params).list_total
                    addRyxzhzHistory(ryxzhzList)
                    // 获取人员密集变更汇总数据
                    let rymjbghzList = getGwbg(params).list_total
                    addRymjbghzHistory(rymjbghzList)
                    // 获取离职离岗数据
                    let lglzList = getLglz(params).list_total
                    addLglzHistory(lglzList)
                    updataItem = {
                        "id": 1,
                        "smgwdj": true,
                        "smryhz": true,
                        "ryxzhz": true,
                        "rynjbghz": true,
                        "lghz": true,
                        "gxsj": "",
                        "dqnf": ""
                    }
                    this.$message({
                        message: '涉密人员相关台账生成成功',
                        type: 'success'
                    });
                    this.smryCxscShow = true
                    break
                case 'smcs':
                    if (type == 'yjsc') {
                        // 添加日志
                        let paramsLog = {
                            xyybs: 'mk_dbgzrz',
                            id: '-1',
                            ymngnmc: '一键生成年度涉密场所相关台账'
                        }
                        writeTrajectoryLog(paramsLog)
                        window.localStorage.setItem('smcsSctime', new Date().getFullYear().toString())
                        this.smcsSctime = false
                    }
                    // 获取涉密场所
                    let smcsList = getCsgl(params).list_total
                    addSmcsglHistory(smcsList)
                    // 获取场所变更数据
                    let csbgList = getCsbg(params).list_total
                    addCsbgHistory(csbgList)
                    updataItem = {
                        "id": 2,
                        "smcsdj": true,
                        "csbgdj": true,
                        "gxsj": "",
                        "dqnf": ""
                    }
                    this.$message({
                        message: '涉密场所相关台账生成成功',
                        type: 'success'
                    });
                    this.smcsCxscShow = true
                    break
                case 'smsb':
                    if (type == 'yjsc') {
                        // 添加日志
                        let paramsLog = {
                            xyybs: 'mk_dbgzrz',
                            id: '-1',
                            ymngnmc: '一键生成年度涉密设备相关台账'
                        }
                        writeTrajectoryLog(paramsLog)
                        window.localStorage.setItem('smsbSctime', new Date().getFullYear().toString())
                        this.smsbSctime = false
                    }
                    // 获取涉密计算机
                    let smjsjList = getSmjsj(params).list_total
                    addSmjsjHistory(smjsjList)
                    // 获取非涉密计算机
                    let fsmjsjList = getFsmjsj(params).list_total
                    addFsmjsjHistory(fsmjsjList)
                    // 获取涉密移动存储介质
                    let smydccjzList = getSmydccjz(params).list_total
                    addSmydccjzHistory(smydccjzList)
                    // 获取办公自动化设备
                    let smBgzdhsbList = getSmbgzdhsb(params).list_total
                    addBgzdhsbHistory(smBgzdhsbList)
                    // 获取非涉密办公自动化设备台账
                    let fsmBgzdhsbList = getFsmbgzdhsb(params).list_total
                    addfBgzdhsbHistory(fsmBgzdhsbList)
                    // 获取涉密网络设备台账
                    let smwlsbList = getSmwlsb(params).list_total
                    addSmwlsbHistory(smwlsbList)
                    // 获取非涉密网络设备台账
                    let fsmwlsbList = getFmwlsb(params).list_total
                    addFsmwlsbHistory(fsmwlsbList)
                    // 获取安全产品台账
                    let aqcpList = getAqcp(params).list_total
                    addAqcpHistory(aqcpList)
                    updataItem = {
                        "id": 3,
                        "smjsjtz": true,
                        "fsmjsjtz": true,
                        "ydccjztz": true,
                        "bgzdhsbtz": true,
                        "fsmbgzdhsbtz": true,
                        "wlsbtz": true,
                        "fwlsbtz": true,
                        "aqcptz": true,
                        "gxsj": "",
                        "dqnf": ""
                    }
                    this.$message({
                        message: '涉密设备相关台账生成成功',
                        type: 'success'
                    });
                    this.smsbCxscShow = true
                    break
                case 'smzt':
                    if (type == 'yjsc') {
                        // 添加日志
                        let paramsLog = {
                            xyybs: 'mk_dbgzrz',
                            id: '-1',
                            ymngnmc: '一键生成年度涉密载体相关台账'
                        }
                        writeTrajectoryLog(paramsLog)
                        window.localStorage.setItem('smztSctime', new Date().getFullYear().toString())
                        this.smztSctime = false
                    }
                    // 获取涉密载体数据
                    let smztList = getSmzttz(params).list_total
                    addSmztHistory(smztList)
                    updataItem = {
                        "id": 4,
                        "smzttz": true,
                        "gxsj": "",
                        "dqnf": ""
                    }
                    this.$message({
                        message: '涉密载体相关台账生成成功',
                        type: 'success'
                    });
                    this.smztCxscShow = true
                    break
                case 'smsx':
                    if (type == 'yjsc') {
                        // 添加日志
                        let paramsLog = {
                            xyybs: 'mk_dbgzrz',
                            id: '-1',
                            ymngnmc: '一键生成年度涉密事项相关台账'
                        }
                        writeTrajectoryLog(paramsLog)
                        window.localStorage.setItem('smsxSctime', new Date().getFullYear().toString())
                        this.smsxSctime = false
                    }
                    // 获取定密责任人
                    let dmzrrList = getdmzrr(params).list_total
                    addDmzrrHistory(dmzrrList)
                    // 获取定密授权
                    let dmsqList = getdmsq(params).list_total
                    addDmsqHistory(dmsqList)
                    // 获取国家秘密事项
                    let gjmmsxList = getGjmmsx({ page: this.page, pageSize: this.pageSize, nd: new Date().getFullYear() }).list_total
                    addGjmmsxHistory(gjmmsxList)
                    // 获取定密培训
                    let dmpxList = getdmpx(params).list_total
                    addDmpxHistory(dmpxList)
                    // 获取定密情况年度统计
                    let dmqkndtjList = getDmqkndtj(params).list_total
                    addDmqkndtjHistory(dmqkndtjList)
                    // 获取不明确事项确定情况
                    let bmqsxqdqkList = getDmqsxqdqk(params).list_total
                    addBmqsxqdqkListHistory(bmqsxqdqkList)
                    // 获取政府采购项目情况
                    let zfcgxmqkList = getZfcgxmqk(params).list_total
                    addZfcgxmqkListHistory(zfcgxmqkList)
                    updataItem = {
                        "id": 5,
                        "dmzrr": true,
                        "dmsq": true,
                        "gjmmsx": true,
                        "dmpx": true,
                        "dmqkndtj": true,
                        "bmqsxqdqk": true,
                        "zfcgxmqk": true,
                        "gxsj": "",
                        "dqnf": ""
                    }
                    this.$message({
                        message: '涉密相关事项台账生成成功',
                        type: 'success'
                    });
                    this.smsxCxscShow = true
                    break
                default:
                    console.log(0)
            }
            reviseDbgzStatus(updataItem)
            this.getDbgzSattus(this.statusArr)
        },
        // 一键生成年度涉密人员相关台账
        cxscClick(name) {
            switch (name) {
                case 'smry':
                    // 添加日志
                    let paramsLog1 = {
                        xyybs: 'mk_dbgzrz',
                        id: '-1',
                        ymngnmc: '重新生成年度涉密人员相关台账'
                    }
                    writeTrajectoryLog(paramsLog1)
                    // 获取当前年份
                    // 先删除
                    getSmgwHistory(new Date().getFullYear().toString()).forEach(function (item) {
                        deleteLsSmgw(item)
                    })
                    getSmryHistory(new Date().getFullYear().toString()).forEach(function (item) {
                        deleteLsSmry(item)
                    })
                    getRyxzhzHistory(new Date().getFullYear().toString()).forEach(function (item) {
                        deleteLsRyxzhz(item)
                    })
                    getRymjbghzHistory(new Date().getFullYear().toString()).forEach(function (item) {
                        deleteLsRymjbghz(item)
                    })
                    getLglzHistory(new Date().getFullYear().toString()).forEach(function (item) {
                        deleteLsLglz(item)
                    })
                    // 再生成
                    this.yjscClick('smry', 'cxsc')
                    break
                case 'smcs':
                    // 添加日志
                    let paramsLog2 = {
                        xyybs: 'mk_dbgzrz',
                        id: '-1',
                        ymngnmc: '重新生成年度涉密场所相关台账'
                    }
                    writeTrajectoryLog(paramsLog2)
                    // 先删除
                    getSmcsglHistory(new Date().getFullYear().toString()).forEach(function (item) {
                        deleteLsCsgl(item)
                    })
                    getSmCsbgHistory(new Date().getFullYear().toString()).forEach(function (item) {
                        deleteLsCsbg(item)
                    })
                    // 再生成
                    this.yjscClick('smcs', 'cxsc')
                    break
                case 'smsb':
                    // 添加日志
                    let paramsLog3 = {
                        xyybs: 'mk_dbgzrz',
                        id: '-1',
                        ymngnmc: '重新生成年度涉密设备相关台账'
                    }
                    writeTrajectoryLog(paramsLog3)
                    // 先删除
                    getSmjsjHistory(new Date().getFullYear().toString()).forEach(function (item) {
                        deleteLsSmjsj(item)
                    })
                    getFsmjsjHistory(new Date().getFullYear().toString()).forEach(function (item) {
                        deleteLsFsmjsj(item)
                    })
                    getSmydccjzHistory(new Date().getFullYear().toString()).forEach(function (item) {
                        deleteLsSmydccjz(item)
                    })
                    getSmBgzdhsbHistory(new Date().getFullYear().toString()).forEach(function (item) {
                        deleteLsSmBgzdhsb(item)
                    })
                    getFsmBgzdhsbHistory(new Date().getFullYear().toString()).forEach(function (item) {
                        deleteLsFsmBgzdhsb(item)
                    })
                    getSmwlsbHistory(new Date().getFullYear().toString()).forEach(function (item) {
                        deleteLsSmwlsb(item)
                    })
                    getFsmwlsbHistory(new Date().getFullYear().toString()).forEach(function (item) {
                        deleteLsFsmwlsb(item)
                    })
                    getAqcpHistory(new Date().getFullYear().toString()).forEach(function (item) {
                        deleteLsAqcp(item)
                    })
                    // 再生成
                    this.yjscClick('smsb', 'cxsc')
                    break
                case 'smzt':
                    // 添加日志
                    let paramsLog4 = {
                        xyybs: 'mk_dbgzrz',
                        id: '-1',
                        ymngnmc: '重新生成年度涉密载体相关台账'
                    }
                    writeTrajectoryLog(paramsLog4)
                    // 先删除
                    getSmzttzHistory(new Date().getFullYear().toString()).forEach(function (item) {
                        deleteLsSmzttz(item)
                    })
                    // 再生成
                    this.yjscClick('smzt', 'cxsc')
                    break
                case 'smsx':
                    // 添加日志
                    let paramsLog5 = {
                        xyybs: 'mk_dbgzrz',
                        id: '-1',
                        ymngnmc: '重新生成年度涉密事项相关台账'
                    }
                    writeTrajectoryLog(paramsLog5)
                    // 先删除
                    getDmzrrHistory(new Date().getFullYear().toString()).forEach(function (item) {
                        deleteLsDmzrr(item)
                    })
                    getDmsqHistory(new Date().getFullYear().toString()).forEach(function (item) {
                        deleteLsDmsq(item)
                    })
                    getGjmmsxHistory(new Date().getFullYear().toString()).forEach(function (item) {
                        deleteLsGjmmsx(item)
                    })
                    getDmpxHistory(new Date().getFullYear().toString()).forEach(function (item) {
                        deleteLsDmpx(item)
                    })
                    getDmqkndtjHistory(new Date().getFullYear().toString()).forEach(function (item) {
                        deleteLsDmqkndtj(item)
                    })
                    getBmqsxqdqkHistory(new Date().getFullYear().toString()).forEach(function (item) {
                        deleteLsBmqsxqdqk(item)
                    })
                    getZfcgxmqkHistory(new Date().getFullYear().toString()).forEach(function (item) {
                        deleteLsZfcgxmqk(item)
                    })
                    // 再生成
                    this.yjscClick('smsx', 'cxsc')
                    break
                default:
                    console.log(0)
            }
        },
        // 注册信息
        zcxxIsPerfect() {
            // 获取当前注册信息
            let resListArr = getDwxx()
            let lock = 0
            for (let key in resListArr[0]) {
                if (resListArr[0][key] == '') {
                    lock = lock + 1
                }
            }
            // 获取需要完善有几项
            this.zcxxPerfectCount = lock
            this.zcxxIsPerfectShow = lock == 1 ? true : false
        },
        // 日常管理---资质信息（资质单位）
        zzxxIsPerfect() {
            // let resListArr = getJgxx()
            // this.zzyhIsPerfectShow =  resListArr.length == 0 ? true : false
        },
        // 日常管理---保密制度
        bmzdIsPerfect() {
            let resListArr = getBmzd({ page: this.page, pageSize: this.pageSize }).list_total
            if (resListArr.length == 0) {
                this.bmzdIsPerfectShow = true
                this.rcUpdateCounts = this.rcUpdateCounts + 1
            }
        },
        // 日常管理---组织机构
        zzjgIsPerfect() {
            let resListArr = getJgxx()
            if (resListArr.length == 0 || resListArr.length == 1) {
                this.zzjgIsPerfectShow = true
                this.rcUpdateCounts = this.rcUpdateCounts + 1
            }
        },
        // 日常管理---人员信息
        ryxxIsPerfect() {
            this.zgsmryHzListLength = getsmry({ page: this.page, pageSize: this.pageSize }).total
            if (this.zgsmryHzListLength == 0 || this.smgwListLength == 0 || this.ryxzHzListLength == 0 || this.rynjbgHzListLength == 0 || this.lghzListLength == 0) {
                this.ryxxIsPerfectShow = true
                this.rcUpdateCounts = this.rcUpdateCounts + 1
            }
        },
        // 日常管理---场所信息
        csxxIsPerfect() {
            this.csglListLength = getCsgl({ page: this.page, pageSize: this.pageSize }).total
            if (this.csglListLength == 0 || this.csbgListLength == 0) {
                this.csxxIsPerfectShow = true
                this.rcUpdateCounts = this.rcUpdateCounts + 1
            }
        },
        // 日常管理---设备信息
        sbxxIsPerfect() {
            this.smjsjListLength = getSmjsj({ page: this.page, pageSize: this.pageSize }).total
            if (this.smjsjListLength == 0 || this.fsmjsjListLength == 0 || this.ydccjzListLength == 0 || this.bgzdhsbListLength == 0 || this.fsmbgzdhsbListLength == 0 || this.wlsbListLength == 0 || this.fwlsbListLength == 0 || this.aqcpListLength == 0) {
                this.sbxxIsPerfectShow = true
                this.rcUpdateCounts = this.rcUpdateCounts + 1
            }
        },
        // 日常管理---载体信息
        ztxxIsPerfect() {
            let resListArr = getSmzttz({ page: this.page, pageSize: this.pageSize }).list_total
            if (resListArr.length == 0) {
                this.ztxxIsPerfectShow = true
                this.rcUpdateCounts = this.rcUpdateCounts + 1
            }
        },
        // 自查自评-第一季度
        zczp1Perfect() {
            let resRiskDatas = getZczpRiskDatas(1)
            let bhgItem = resRiskDatas.some((item) => {
                let { zt } = item
                if (zt != 7) {
                    return item
                }
            });
            console.log(resRiskDatas)
            if (resRiskDatas.length == 0 || bhgItem) {
                this.zczp1IsPerfectShow = true
                this.zczpUpdateCounts = this.zczpUpdateCounts + 1
            }
        },
        // 自查自评-第二季度
        zczp2Perfect() {
            let resRiskDatas = getZczpRiskDatas(2)
            let bhgItem = resRiskDatas.some((item) => {
                let { zt } = item
                if (zt != 7) {
                    return item
                }
            });
            if (resRiskDatas.length == 0 || bhgItem) {
                this.zczp2IsPerfectShow = true
                this.zczpUpdateCounts = this.zczpUpdateCounts + 1
            }
        },
        // 自查自评-第三季度
        zczp3Perfect() {
            let nowMonth = new Date().getMonth() + 1
            let resRiskDatas = getZczpRiskDatas(3)
            let bhgItem = resRiskDatas.some((item) => {
                let { zt } = item
                if (zt != 7) {
                    return item
                }
            });
            if (resRiskDatas.length == 0 || bhgItem) {
                this.zczp3IsPerfectShow = true
                this.zczpUpdateCounts = this.zczpUpdateCounts + 1
            }
        },
        // 自查自评-第四季度
        zczp4Perfect() {
            let resRiskDatas = getZczpRiskDatas(4)
            let bhgItem = resRiskDatas.some((item) => {
                let { zt } = item
                if (zt != 7) {
                    return item
                }
            });
            if (resRiskDatas.length == 0 || bhgItem) {
                this.zczp4IsPerfectShow = true
                this.zczpUpdateCounts = this.zczpUpdateCounts + 1
            }
        },
        getTime(date1) {
            let date = new Date(date1);
            let Y = date.getFullYear() + '-';
            let M = (date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1) : date.getMonth()+1) + '-';
            let D = date.getDate() + ' ';
            let h = date.getHours() + ':';
            let m = date.getMinutes() + ':';
            let s = date.getSeconds(); 
            return Y+M+D+h+m+s
          },
        // 初始化
        initDatas() {
            let params = {
                page: this.page,
                pageSize: this.pageSize
            }
            // 获取各表数据的length
            this.smgwListLength = getSmgwgl(params).total
            this.ryxzHzListLength = getRyxz(params).total
            this.rynjbgHzListLength = getGwbg(params).total
            this.lghzListLength = getLglz(params).total
            this.csbgListLength = getCsbg(params).total
            this.fsmjsjListLength = getFsmjsj(params).total
            this.ydccjzListLength = getSmydccjz(params).total
            this.bgzdhsbListLength = getSmbgzdhsb(params).total
            this.fsmbgzdhsbListLength = getFsmbgzdhsb(params).total
            this.wlsbListLength = getSmwlsb(params).total
            this.fwlsbListLength = getFmwlsb(params).total
            this.aqcpListLength = getAqcp(params).total
            this.smztListLength = getSmzttz(params).total
            this.dmzrrListLength = getdmzrr(params).total
            this.dmsqListLength = getdmsq(params).total
            this.gjmmsxListLength = getGjmmsx({ page: this.page, pageSize: this.pageSize, nd: new Date().getFullYear() }).total
            this.dmpxListLength = getdmpx(params).total
            this.dmqkndtjListLength = getDmqkndtj(params).total
            this.bmqsxqdqkListLength = getDmqsxqdqk(params).total
            this.zfcgxmqkListLength = getZfcgxmqk(params).total

            // 获取台账生成时间范围
            let sTime = new Date().getFullYear() + '/' + getYz('csbs_dbgzscrq')[0].csz[0].month + '/' + getYz('csbs_dbgzscrq')[0].csz[0].day + ' ' + '00:00:00'
            let eTime = new Date().getFullYear() + '/' + getYz('csbs_dbgzscrq')[0].csz[1].month + '/' + getYz('csbs_dbgzscrq')[0].csz[1].day + ' ' + '00:00:00'
            let startTime = new Date(sTime).getTime()
            let endTime = new Date(eTime).getTime()
            this.dbgzDateStart = this.getTime(startTime)
            this.dbgzDateEnd = this.getTime(endTime)
            // 涉密人员上报区间范围
            let smrySbsTime = new Date().getFullYear() + '/' + getYz('csbs_ndsmrysbrq')[0].csz[0].month + '/' + getYz('csbs_ndsmrysbrq')[0].csz[0].day + ' ' + '00:00:00'
            let smrySbeTime = new Date().getFullYear() + '/' + getYz('csbs_ndsmrysbrq')[0].csz[1].month + '/' + getYz('csbs_ndsmrysbrq')[0].csz[1].day + ' ' + '00:00:00'
            let smrySbstartTime = new Date(smrySbsTime).getTime()
            let smrySbendTime = new Date(smrySbeTime).getTime()
            // 定密事项上报区间范围
            let dmsxSbsTime = new Date().getFullYear() + '/' + getYz('csbs_nddmsxsbrq')[0].csz[0].month + '/' + getYz('csbs_nddmsxsbrq')[0].csz[0].day + ' ' + '00:00:00'
            let dmsxSbeTime = new Date().getFullYear() + '/' + getYz('csbs_nddmsxsbrq')[0].csz[1].month + '/' + getYz('csbs_nddmsxsbrq')[0].csz[1].day + ' ' + '00:00:00'
            let dmsxSbstartTime = new Date(dmsxSbsTime).getTime()
            let dmsxSbendTime = new Date(dmsxSbeTime).getTime()
            // 获取当前时间戳
            let nowTime = new Date().getTime()

            this.dadbShow = nowTime > startTime && nowTime < endTime ? true : false
            this.downloadShow = nowTime > smrySbstartTime && nowTime < smrySbendTime ? true : false
            this.download1Show = nowTime > dmsxSbstartTime && nowTime < dmsxSbendTime ? true : false
            let offsetCounts = 0
            // 如果当前时间在区间范围内
            if (nowTime > startTime && nowTime < endTime && this.smryTzScShow || nowTime > startTime && nowTime < endTime && this.smcsTzScShow || nowTime > startTime && nowTime < endTime && this.smsbTzScShow || nowTime > startTime && nowTime < endTime && this.smsxTzScShow || nowTime > startTime && nowTime < endTime && this.smsbTzScShow || nowTime > startTime && nowTime < endTime && this.smztTzScShow) {
                // this.$message({
                //     message: '待办工作未处理，待生成',
                //     type: 'warning'
                // });
                offsetCounts = offsetCounts + 100
                this.$notify({
                    title: '提示',
                    message: '待办工作未处理，待生成',
                    type: 'warning',
                    offset: offsetCounts
                })
            }
            if (nowTime > smrySbstartTime && nowTime < smrySbendTime && this.smryDownLoadShow) {
                offsetCounts = offsetCounts + 100
                this.$notify({
                    title: '提示',
                    message: '年度涉密人员上报待完成',
                    type: 'warning',
                    offset: offsetCounts
                })
            }
            if (nowTime > dmsxSbstartTime && nowTime < dmsxSbendTime && this.smsxDownloadShow) {
                offsetCounts = offsetCounts + 100
                this.$notify({
                    title: '提示',
                    message: '年度定密事项上报待完成',
                    type: 'warning',
                    offset: offsetCounts
                })
            }
            // 如果当前时间大于 区间范围的最后一天 重置dbgz_status 为false
            if (nowTime > endTime) {
                let updataItem1 = {
                    "id": 1,
                    "smgwdj": false,
                    "smryhz": false,
                    "ryxzhz": false,
                    "rynjbghz": false,
                    "lghz": false,
                    "gxsj": "",
                    "dqnf": ""
                }
                let updataItem2 = {
                    "id": 2,
                    "smcsdj": false,
                    "csbgdj": false,
                    "gxsj": "",
                    "dqnf": ""
                }
                let updataItem3 = {
                    "id": 3,
                    "smjsjtz": false,
                    "fsmjsjtz": false,
                    "ydccjztz": false,
                    "bgzdhsbtz": false,
                    "fsmbgzdhsbtz": false,
                    "wlsbtz": false,
                    "fwlsbtz": false,
                    "aqcptz": false,
                    "gxsj": "",
                    "dqnf": ""
                }
                let updataItem4 = {
                    "id": 4,
                    "smzttz": false,
                    "gxsj": "",
                    "dqnf": ""
                }
                let updataItem5 = {
                    "id": 5,
                    "dmzrr": false,
                    "dmsq": false,
                    "gjmmsx": false,
                    "dmpx": false,
                    "dmqkndtj": false,
                    "bmqsxqdqk": false,
                    "zfcgxmqk": false,
                    "gxsj": "",
                    "dqnf": ""
                }
                // 更新数据库状态
                reviseDbgzStatus(updataItem1)
                reviseDbgzStatus(updataItem2)
                reviseDbgzStatus(updataItem3)
                reviseDbgzStatus(updataItem4)
                reviseDbgzStatus(updataItem5)
            }
        },
        // 初始化当前生成状态 一键生成或者重新生成
        initScStatus() {
            // 重新生成状态
            this.smryCxscShow = !this.smryTzScShow ? true : false
            this.smcsCxscShow = !this.smcsTzScShow ? true : false
            this.smsbCxscShow = !this.smsbTzScShow ? true : false
            this.smztCxscShow = !this.smztTzScShow ? true : false
            this.smsxCxscShow = !this.smsxTzScShow ? true : false
            if (window.localStorage.getItem('smrySctime') == new Date().getFullYear().toString()) {
                // 已经进行过一键生成操作
                this.smrySctime = false
                let updataItem1 = {
                    "id": 1,
                    "smgwdj": true,
                    "smryhz": true,
                    "ryxzhz": true,
                    "rynjbghz": true,
                    "lghz": true,
                    "gxsj": "",
                    "dqnf": ""
                }
                reviseDbgzStatus(updataItem1)
            } else {
                // 还未进行一键生成
                this.smrySctime = true
            }
            if (window.localStorage.getItem('smcsSctime') == new Date().getFullYear().toString()) {
                // 已经进行过一键生成操作
                this.smcsSctime = false
                let updataItem2 = {
                    "id": 2,
                    "smcsdj": true,
                    "csbgdj": true,
                    "gxsj": "",
                    "dqnf": ""
                }
                reviseDbgzStatus(updataItem2)
            } else {
                // 还未进行一键生成
                this.smcsSctime = true
            }
            if (window.localStorage.getItem('smsbSctime') == new Date().getFullYear().toString()) {
                // 已经进行过一键生成操作
                this.smsbSctime = false
                let updataItem3 = {
                    "id": 3,
                    "smjsjtz": true,
                    "fsmjsjtz": true,
                    "ydccjztz": true,
                    "bgzdhsbtz": true,
                    "fsmbgzdhsbtz": true,
                    "wlsbtz": true,
                    "fwlsbtz": true,
                    "aqcptz": true,
                    "gxsj": "",
                    "dqnf": ""
                }
                reviseDbgzStatus(updataItem3)
            } else {
                // 还未进行一键生成
                this.smsbSctime = true
            }
            if (window.localStorage.getItem('smztSctime') == new Date().getFullYear().toString()) {
                // 已经进行过一键生成操作
                this.smztSctime = false
                let updataItem4 = {
                    "id": 4,
                    "smzttz": true,
                    "gxsj": "",
                    "dqnf": ""
                }
                reviseDbgzStatus(updataItem4)
            } else {
                // 还未进行一键生成
                this.smztSctime = true
            }
            if (window.localStorage.getItem('smsxSctime') == new Date().getFullYear().toString()) {
                // 已经进行过一键生成操作
                this.smsxSctime = false
                let updataItem5 = {
                    "id": 5,
                    "dmzrr": true,
                    "dmsq": true,
                    "gjmmsx": true,
                    "dmpx": true,
                    "dmqkndtj": true,
                    "bmqsxqdqk": true,
                    "zfcgxmqk": true,
                    "gxsj": "",
                    "dqnf": ""
                }
                reviseDbgzStatus(updataItem5)
            } else {
                // 还未进行一键生成
                this.smsxSctime = true
            }
            // 获取本年度最后一天 
            let lastDay = new Date( new Date().getFullYear() + 1 , 0 , 0) // 年份最后一天
            var date = new Date(Date.parse(lastDay));
            let Y = date.getFullYear() + '-';
            let M = (date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1) : date.getMonth()+1) + '-';
            let D = date.getDate() + ' ';
            let h = '23' + ':';
            let m = '59' + ':';
            let s = '59'; 
            let nowTime = new Date().getTime()
            if(nowTime > Date.parse(Y+M+D+h+m+s)){
                window.localStorage.removeItem('smrySctime')
                window.localStorage.removeItem('smcsSctime')
                window.localStorage.removeItem('smsbSctime')
                window.localStorage.removeItem('smztSctime')
                window.localStorage.removeItem('smsxSctime')
            }
        }
    },
    created() {
        this.getDbgzSattus()
        this.nowsYear = new Date().getFullYear()
    },
    mounted() {
        this.initDatas()
        this.zcxxIsPerfect()
        this.zzxxIsPerfect()
        this.bmzdIsPerfect()
        this.zzjgIsPerfect()
        this.ryxxIsPerfect()
        this.csxxIsPerfect()
        this.sbxxIsPerfect()
        this.ztxxIsPerfect()
        this.zczp1Perfect()
        this.zczp2Perfect()
        this.zczp3Perfect()
        this.zczp4Perfect()
        this.initScStatus()
    }
}