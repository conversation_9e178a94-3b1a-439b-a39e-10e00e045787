export default [
  // {
  //   name: 'zczpsy',
  //   path: '/zczpsy',
  //   component: () => import('../zczptabs.vue'),
  //   meta: {
  //     name: '自查自评',
  //     icon: 'aaa',
  //     hidden: false,
  //     showHeaderMenu: true,
  //     showAsideMenu: true,
  //     menuList: ['/zczpls', '/xjzczp', '/jxzczp', '/nsjggl', '/zcrygl'],
  //   },
  // },
  {
    name: 'zczpls',
    path: '/zczpls',
    component: () => import('../zczpls.vue'),
    meta: {
      name: '自查自评历史',
      icon: 'aaa',
      hidden: false,
      showHeaderMenu: true,
      showAsideMenu: true,
      menuList: [
        '/zczpls',
        '/xjzczp',
        '/jxzczp',
        '/nsjggl',
        '/zcrygl',
        '/ccdnsjg',
        '/ccdnsjgDj',
        '/ccdry',
        '/ccdryDj',
        '/jczj',
        '/jczjLsxx',
        '/ccdryDjXqxx',
        '/ccdnsjgDjXqxx',
      ],
    },
  },
  {
    name: 'xjzczp',
    path: '/xjzczp',
    component: () => import('../xjzczp.vue'),
    meta: {
      name: '新建自查自评',
      icon: 'aaa',
      hidden: false,
      showHeaderMenu: true,
      showAsideMenu: true,
    },
  },
  {
    name: 'jxzczp',
    path: '/jxzczp',
    component: () => import('../jxzczp.vue'),
    meta: {
      name: '继续自查自评',
      icon: 'aaa',
      hidden: false,
      showHeaderMenu: true,
      showAsideMenu: true,
    },
  },
  // {
  //   name: 'nsjggl',
  //   path: '/nsjggl',
  //   component: () => import('../nsjggl.vue'),
  //   meta: {
  //     name: '内设机构管理',
  //     icon: 'aaa',
  //     hidden: false,
  //     showHeaderMenu: true,
  //     showAsideMenu: true,
  //   },
  // },
  // {
  //   name: 'zcrygl',
  //   path: '/zcrygl',
  //   component: () => import('../zcrygl.vue'),
  //   meta: {
  //     name: '自查人员管理',
  //     icon: 'aaa',
  //     hidden: false,
  //     showHeaderMenu: true,
  //     showAsideMenu: true,
  //   },
  // },
  // 不需要菜单显示的页面
  {
    name: 'ccdnsjg',
    path: '/ccdnsjg',
    component: () => import('../childPage/ccdnsjg.vue'),
    meta: {
      name: '抽查的内设机构',
      icon: 'aaa',
      hidden: false,
      showHeaderMenu: true,
      showAsideMenu: true,
    },
  },
  {
    name: 'ccdnsjgDj',
    path: '/ccdnsjgDj',
    component: () => import('../childPage/ccdnsjgDj.vue'),
    meta: {
      name: '内设机构自查自评结果登记',
      icon: 'aaa',
      hidden: false,
      showHeaderMenu: true,
      showAsideMenu: true,
    },
  },
  {
    name: 'ccdry',
    path: '/ccdry',
    component: () => import('../childPage/ccdry.vue'),
    meta: {
      name: '抽查的人员',
      icon: 'aaa',
      hidden: false,
      showHeaderMenu: true,
      showAsideMenu: true,
    },
  },
  {
    name: 'ccdryDj',
    path: '/ccdryDj',
    component: () => import('../childPage/ccdryDj.vue'),
    meta: {
      name: '人员自查自评',
      icon: 'aaa',
      hidden: false,
      showHeaderMenu: true,
      showAsideMenu: true,
    },
  },
  {
    name: 'jczj',
    path: '/jczj',
    component: () => import('../childPage/jczj.vue'),
    meta: {
      name: '检查总结',
      icon: 'aaa',
      hidden: false,
      showHeaderMenu: true,
      showAsideMenu: true,
    },
  },
  // 不能编辑的详情页面
  {
    name: 'jczjLsxx',
    path: '/jczjLsxx',
    component: () => import('../childPage/jczjLsxx.vue'),
    meta: {
      name: '检查总结详情',
      icon: 'aaa',
      hidden: false,
      showHeaderMenu: true,
      showAsideMenu: true,
    },
  },
  {
    name: 'ccdryDjXqxx',
    path: '/ccdryDjXqxx',
    component: () => import('../childPage/ccdryDjXqxx.vue'),
    meta: {
      name: '人员抽查详情',
      icon: 'aaa',
      hidden: false,
      showHeaderMenu: true,
      showAsideMenu: true,
    },
  },
  {
    name: 'ccdnsjgDjXqxx',
    path: '/ccdnsjgDjXqxx',
    component: () => import('../childPage/ccdnsjgDjXqxx.vue'),
    meta: {
      name: '内设机构抽查详情',
      icon: 'aaa',
      hidden: false,
      showHeaderMenu: true,
      showAsideMenu: true,
    },
  },
]
