<template>
  <div style="height: calc(100% - 32px);width: 100%;">
    <!-- 检索条件区域 -->
    <div class="mhcx">
      <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline" style="float:left">
        <el-form-item label="所属模块" style="font-weight: 700;">
          <el-select v-model="formInline.xyybs" clearable>
            <el-option v-for="(item,index) in ssmkList" :key="index" :label="item.name" :value="item.xyybs"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="时间" style="font-weight: 700;">
          <el-date-picker v-model="formInline.cxsj" size="" type="daterange" :default-time="['00:00:00', '23:59:59']" value-format="timestamp" :editable="false" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="getSjrz">查询</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 表格区域 -->
    <div style="height: calc(100% - 34px - 20px);">
      <el-table :data="scList" border stripe :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" style="width: 100%;border:1px solid #EBEEF5;" height="calc(100% - 34px - 10px)">
        <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
        <el-table-column prop="yhm" label="账号" width="90" align="center"></el-table-column>
        <el-table-column prop="xm" label="姓名" width="90" align="center"></el-table-column>
        <el-table-column label="操作模块" width="100" align="center">
          <template slot-scope="scoped">
            <div>{{ translationSsmk(scoped.row.xyybs) }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="ymngnmc" label="操作功能" width="100" align="center"></el-table-column>
        <el-table-column label="标识" width="100" align="center">
          <template slot-scope="scoped">
            <div v-if="scoped.row.extraParams">{{ scoped.row.extraParams.bs }}</div>
          </template>
        </el-table-column>
        <el-table-column label="修改内容">
          <template slot-scope="scoped">
            <div v-if="scoped.row.extraParams && scoped.row.extraParams.modifyArr && scoped.row.extraParams.modifyArr.length > 0">
              <p v-for="(item) in scoped.row.extraParams.modifyArr">
                <span v-if="item.changedTypeName" class="table_span_xgnr" :style="{'--color': tableColorArr[item.changedType]}">[{{ item.changedTypeName }}]</span>
                <span v-if="item.changedFieldName">[{{ item.changedFieldName }}][{{ item.changedField }}]</span>
                <span v-if="item.changedFieldVal">{{ item.changedFieldVal }}</span>
              </p>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="时间" width="200" align="center">
          <template slot-scope="scoped">
            <div>{{ formatDate(scoped.row.time) }}</div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 分页组件区域 -->
    <div style="border: 1px solid #ebeef5">
      <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange" :pager-count="5" :current-page="pageInfo.page" :page-sizes="[5, 10, 20, 30]" :page-size="pageInfo.pageSize" layout="total, prev, pager, sizes,next, jumper" :total="pageInfo.total">
      </el-pagination>
    </div>
    <!---->
  </div>
</template>

<script>
import { parseSystemOperationLogsSj } from '../../../utils/logUtils'

import { getDateTime } from '../../../utils/utils'

import { dateFormatChinese } from '../../../utils/moment'

export default {
  data () {
    return {
      // 表格修改内容列 新增|修改|删除 颜色
      tableColorArr: ['', '#67C23A', '#E6A23C', '#F56C6C'],
      // 查询条件
      formInline: {},
      // 分页信息
      pageInfo: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      // 表格数据
      scList: [],
      // 所属模块列表集合
      ssmkList: [],
      // 日志数据全集
      logsAllList: []
    }
  },
  methods: {
    // 格式化日期
    formatDate (time) {
      return dateFormatChinese(new Date(time))
    },
    // 获取日志信息集合
    getSjrz () {
      this.pageInfo.page = 1
      this.getLogs()
    },
    // 获取所有的操作日志
    getAllOptionsLogs () {
      parseSystemOperationLogsSj(resArr => {
        console.log('操作日志', resArr)
        // let aaa = resArr.sort((a, b) => {
        //   return a.time < b.time
        // })
        // console.log('aaa', aaa)
        this.logsAllList = resArr
        // 加工获取模块集合
        this.initSsmkList(resArr)
        //
        this.getLogs()
      })
    },
    // 加工获取模块集合
    initSsmkList (resArr) {
      let ssmkTempArr = []
      let tempObj
      resArr.forEach(item => {
        tempObj = {}
        if (ssmkTempArr.indexOf(item.xyybs) == -1) {
          ssmkTempArr.push(item.xyybs)
          tempObj.xyybs = item.xyybs
          tempObj.name = this.translationSsmk(item.xyybs)
          this.ssmkList.push(tempObj)
        }
      })
    },
    // 翻译模块
    translationSsmk (ssmk) {
      switch (ssmk) {
        case 'yybs_mmcz':
          return '密码重置'
        case 'yybs_xgmm':
          return '修改密码'
        case 'yybs_cssz':
          return '参数设置'
        case 'yybs_wjljsz':
          return '文件路径设置'
        case 'yybs_sjsb':
          return '数据上报'
        case 'yybs_zcxx':
          return '注册信息维护'
        default:
          return ssmk
      }
    },
    // 页码变更
    handleCurrentChange (val) {
      this.pageInfo.page = val
      this.getLogs()
    },
    // 页面大小变更
    handleSizeChange (val) {
      this.pageInfo.pageSize = val
      this.pageInfo.page = 1
      this.getLogs()
    },
    // 总集日志中进行日志的筛选
    getLogs () {
      let cxsj = this.formInline.cxsj
      let xyybs = this.formInline.xyybs
      // 根据查询条件筛选数据
      let logsAllTempList = JSON.parse(JSON.stringify(this.logsAllList))
      if (xyybs) {
        logsAllTempList = this.logsAllList.filter(item => {
          if (item.xyybs == xyybs) {
            return item
          }
        })
      }
      if (cxsj) {
        logsAllTempList = this.logsAllList.filter(item => {
          if (item.time >= cxsj[0] && item.time <= cxsj[1]) {
            return item
          }
        })
      }
      // 分页
      let page = this.pageInfo.page
      let pageSize = this.pageInfo.pageSize
      this.scList = logsAllTempList.slice(pageSize * (page - 1), pageSize * (page - 1) + pageSize)
      this.pageInfo.total = logsAllTempList.length
    }
  },
  mounted () {
    // // 获取所有的操作日志
    this.getAllOptionsLogs()
  }
}
</script>

<style scoped>
.mhcx :deep(.el-form-item) {
  margin-top: 5px;
  margin-bottom: 5px;
}

/**表格修改内容里标记 新增|修改|删除 的span的样式**/
.table_span_xgnr {
  color: var(--color);
}
</style>