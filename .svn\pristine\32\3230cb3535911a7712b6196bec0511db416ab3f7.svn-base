
    const path=require('path');
    const OSEnum=['Win32','linux or mac'];
const environmentEnum=['development','production'];

export function getOS() {
    if(navigator.platform===OSEnum[0]) {
        return OSEnum[0];
    }
    return OSEnum[1];
}

export function getEnvironment() {
    if(process.env.NODE_ENV===environmentEnum[0]) {
        console.log(environmentEnum[0]);
        return environmentEnum[0];
    }
    console.log(environmentEnum[1]);
    return environmentEnum[1];
}

export function getFileSavePath() {
    if(getOS()===OSEnum[0]) {
        if(getEnvironment()===environmentEnum[0]) {
            return 'C:\\hsoft3\\data\\uploadFile\\';
        }
    }else {
        return '/home/<USER>/data/uploadFile/';
    }
}