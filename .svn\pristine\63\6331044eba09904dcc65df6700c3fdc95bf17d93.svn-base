import db from './adapter/zczpAdaptor'

// 获取所有dm培训年度统计信息(上报数据自选模式专用)
export const getAllDmqkndtjZxms = () => {
  return db.get('dmqkndtj_list').cloneDeep().value()
}

//保密制度-----------------------------------保密制度初始化列表********
export const getDmqkndtj = (params) => {
  let page = params.page
  let pagesize = params.pageSize
  let nd = params.nd
  // let bmbh = params.bmbh
  // let lx = params.lx
  // let mj = params.mj
  // let qyrq = params.qyrq
  let list_total = db.get('dmqkndtj_list').sortBy('cjsj').cloneDeep().value()

  // 模糊查询过滤
  if (nd) {
    list_total = list_total.filter((item) => {
      if (item.nd == nd) {
        return item
      }
    })
  }

  // 手动分页
  console.log(pagesize * (page - 1), pagesize * (page - 1) + pagesize)
  let pageList = list_total.slice(
    pagesize * (page - 1),
    pagesize * (page - 1) + pagesize
  )

  let resList = {
    list: pageList,
    list_total: list_total,
    total: list_total.length,
  }
  console.log('保密制度', resList)
  return resList
}
//保密制度-----------------------------------保密制度添加成员********
export const addDmqkndtj = (params) => {
  let sjc = new Date().getTime()
  params.cjsj = sjc
  params.gxsj = sjc
  db.read().get('dmqkndtj_list').push(params).write()
}
//保密制度-----------------------------------保密制度删除成员********
//修改
export const reviseDmqkndtj = (params) => {
  let dmqkndtjid = params.dmqkndtjid
  console.log('Dmqkndtjid', dmqkndtjid)
  if (!dmqkndtjid || dmqkndtjid == '') {
    return
  }
  params.gxsj = new Date().getTime()
  // 全字段更新方法(传进来的字段都会更新)
  db.read()
    .get('dmqkndtj_list')
    .find({ dmqkndtjid: dmqkndtjid })
    .assign(params)
    .write()
}
//保密制度-----------------------------------保密制度删除成员********
export const deleteDmqkndtj = (params) => {
  db.read().get('dmqkndtj_list').remove(params).write()
}
