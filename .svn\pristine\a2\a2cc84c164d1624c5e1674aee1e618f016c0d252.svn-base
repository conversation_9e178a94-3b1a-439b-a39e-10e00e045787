<template>
  <div class="out">
    <div class="card" @mouseleave="userCardMouseLeave" style="cursor: unset;flex: none;width:120px;justify-content: start;position: relative;">
      <!-- <img src="../../assets/icons/zxh.png" /> -->
      <!-- <span>最小化</span> -->
      <!-- <i class="el-icon-minus" style="color: #FEC13F;"></i> -->
      <!-- <img src="../../assets/icons/zuxiaohua.png" /> -->
      <i @click="showUserCardFlag = !showUserCardFlag" class="el-icon-s-custom" style="background: rgba(255,255,255,0.3);border-radius: 50%;padding: 5px;box-sizing: border-box;font-size: 17px;"></i>
      <div v-show="showUserCardFlag" class="div-user-card">
        <div style="border-bottom: 1px solid #E4E7ED;">
          <!-- <i class="el-icon-caret-top"></i> -->
          <div class="pointer"></div>
          <span>{{currentXm}}</span>
        </div>
      </div>
      <!-- <span style="font-size: 13px;font-style: italic;color: #e8e8e8;">{{currentXm}}</span> -->
      <!-- <el-popover placement="bottom" width="80" trigger="hover">
        <div class="system-setting-out">
          <span style="font-size: 13px;font-style: italic;color: #e8e8e8;">{{currentXm}}</span>
        </div>
        <i slot="reference" class="el-icon-s-custom" style="background: rgba(255,255,255,0.3);border-radius: 50%;padding: 5px;box-sizing: border-box;font-size: 17px;"></i>
      </el-popover> -->
    </div>
    <div class="card" @click="minimizeSystem()">
      <!-- <img src="../../assets/icons/zxh.png" /> -->
      <!-- <span>最小化</span> -->
      <i class="el-icon-minus"></i>
      <!-- <img src="../../assets/icons/zuxiaohua.png" title="最小化" /> -->
      <!-- <span>最小化</span> -->
    </div>
    <div class="card" @click="maximizeSystem()">
      <i v-show="!windowBtnConfig.isMax" class="el-icon-full-screen"></i>
      <i v-show="windowBtnConfig.isMax" class="el-icon-copy-document"></i>
      <!-- <img v-show="!windowBtnConfig.isMax" src="../../assets/icons/zuxiaohua.png" title="最大化" />
      <img v-show="windowBtnConfig.isMax" src="../../assets/icons/zuxiaohua.png" title="窗口化" /> -->
      <!-- <span v-show="!windowBtnConfig.isMax">最大化</span>
      <span v-show="windowBtnConfig.isMax">窗口化</span> -->
    </div>
    <div class="card">
      <!-- <img src="../../assets/icons/header_icon8.png" /> -->
      <el-popover placement="bottom" width="80" trigger="hover">
        <div class="system-setting-out">
          <!-- <div v-for="(item,index) in systemSettingList" v-if="(item.showRoles.indexOf('all') != -1)||(item.showRoles.indexOf(currentYhm) != -1)" :key="index" @click="toPath(item.path)"><i :class="item.icon"></i>{{item.name}}</div> -->
          <div v-for="(item,index) in systemSettingList" v-if="(item.showRoles.indexOf('all') != -1)||(currentYhlx==1?item.showRoles.indexOf(currentYhm)!=-1:currentYhlx==0?true:item.showYhlxs.indexOf(3)!=-1)" :key="index" @click="toPath(item.path)"><i :class="item.icon"></i>{{item.name}}</div>
        </div>
        <!-- <img slot="reference" src="../../assets/icons/header_icon8.png" style="margin-top: 5px;" /> -->
        <!-- <i slot="reference" class="el-icon-setting" style="color: #909399;"></i> -->
        <!-- <i slot="reference" class="el-icon-setting"></i> -->
        <i slot="reference" class="el-icon-s-operation"></i>
        <!-- <div slot="reference">
          <div style="display: flex;">
            <img src="../../assets/icons/shezhi.png" />
            <span>系统设置</span>
          </div>
        </div> -->
      </el-popover>
    </div>
    <div class="card" @click="quitSystem()">
      <!-- <img src="../../assets/icons/header_icon9.png" /> -->
      <!-- <i class="el-icon-close" style="color: #FE645D;"></i> -->
      <i class="el-icon-close"></i>
      <!-- <img src="../../assets/icons/tuichu.png" title="退出系统" /> -->
      <!-- <span>退出</span> -->
    </div>
    <!--数据上报dialog-->
    <el-dialog title="数据上报" :visible.sync="dialogVisibleSjsb" width="35%">
      <div>
        <el-form :model="dialogSjsb" :label-position="'right'" label-width="120px" size="mini">
          <div style="display:flex">
            <el-form-item label="上报数据模式" class="one-line">
              <el-radio-group v-model="sbsjlx">
                <el-radio :label="1">本年数据模式</el-radio>
                <el-radio :label="2">自选数据模式</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
          <div style="display:flex">
            <el-form-item label="上报时间" class="one-line">
              <el-date-picker v-model="dialogSjsb.sbsj" type="year" value-format="yyyy" placeholder="选择上报时间" style="width:calc(100%);">
              </el-date-picker>
            </el-form-item>
          </div>
          <div v-if="sbsjlx == 1" style="display:flex">
            <el-form-item label="上报类型" class="one-line">
              <el-checkbox-group v-model="dialogSjsb.sblx">
                <el-checkbox :label="1" :disabled="dialogSjsb.sblx.length == 1 && dialogSjsb.sblx.indexOf(1) != -1">涉密人员</el-checkbox>
                <el-checkbox :label="2" :disabled="dialogSjsb.sblx.length == 1 && dialogSjsb.sblx.indexOf(2) != -1">定密事项</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="generateSbsj()">保 存</el-button>
        <el-button type="warning" @click="dialogVisibleSjsb = false">关 闭</el-button>
      </span>
    </el-dialog>
    <!---->
    <el-dialog title="上报数据自选模式" :visible.sync="dialogVisibleSjsbZxms" width="90%">
      <div>
        <el-tabs type="card" tab-position="left">
          <el-tab-pane label="涉密岗位" style="height: 403px;">
            <el-table :data="sbsjDataZxmsOld.Smgwgl_list" @select="(selection, row) => tableSelected(selection, row, 'Smgwgl_list')" @select-all="(selection) => tableSelectedAll(selection, 'Smgwgl_list')" border :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" style="width: 100%;border:1px solid #EBEEF5;" height="calc(100%)" stripe>
              <el-table-column type="selection" width="55" align="center"> </el-table-column>
              <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
              <el-table-column prop="bm" label="部门"></el-table-column>
              <el-table-column prop="gwmc" label="岗位名称"></el-table-column>
              <el-table-column prop="smdj" label="涉密等级"></el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="涉密人员" style="height: 403px;">
            <el-table :data="sbsjDataZxmsOld.Smry_list" @select="(selection, row) => tableSelected(selection, row, 'Smry_list')" @select-all="(selection) => tableSelectedAll(selection, 'Smry_list')" border :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" style="width: 100%;border:1px solid #EBEEF5;" height="100%" stripe>
              <el-table-column type="selection" width="55" align="center"> </el-table-column>
              <el-table-column prop="xm" label="姓名"></el-table-column>
              <el-table-column prop="bm" label="部门"></el-table-column>
              <el-table-column prop="gwmc" label="岗位名称"></el-table-column>
              <el-table-column prop="smdj" label="涉密等级"></el-table-column>
              <el-table-column prop="zw" label="职务"></el-table-column>
              <el-table-column prop="sfsc" label="是否审查"></el-table-column>
              <el-table-column prop="sgsj" label="上岗时间"></el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="离岗离职" style="height: 403px;">
            <el-table :data="sbsjDataZxmsOld.Lglz_list" @select="(selection, row) => tableSelected(selection, row, 'Lglz_list')" @select-all="(selection) => tableSelectedAll(selection, 'Lglz_list')" border :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" style="width: 100%;border:1px solid #EBEEF5;" height="calc(100%)" stripe>
              <el-table-column type="selection" width="55" align="center"> </el-table-column>
              <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
              <el-table-column prop="xm" label="姓名"></el-table-column>
              <el-table-column prop="ybm" label="原部门"></el-table-column>
              <el-table-column prop="ygwmc" label="原岗位名称"></el-table-column>
              <el-table-column prop="ysmdj" label="原涉密等级"></el-table-column>
              <el-table-column prop="tmqkssj" label="脱密期开始时间"></el-table-column>
              <el-table-column prop="tmqjssj" label="脱密期结束时间"></el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="定密责任人" style="height: 403px;">
            <el-table :data="sbsjDataZxmsOld.dmzrr_list" @select="(selection, row) => tableSelected(selection, row, 'dmzrr_list')" @select-all="(selection) => tableSelectedAll(selection, 'dmzrr_list')" border :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" style="width: 100%;border:1px solid #EBEEF5;" height="calc(100%)" stripe>
              <el-table-column type="selection" width="55" align="center"> </el-table-column>
              <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
              <el-table-column prop="nd" label="年度"></el-table-column>
              <el-table-column prop="xm" label="姓名"></el-table-column>
              <el-table-column prop="zw" label="职务"></el-table-column>
              <el-table-column prop="dmqx" label="定密权限"></el-table-column>
              <el-table-column prop="dmsx" label="定密事项（范围）"></el-table-column>
              <el-table-column prop="lb" label="类别"></el-table-column>
              <el-table-column prop="qdsj" label="确（指）定时间"></el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="定密授权" style="height: 403px;">
            <el-table :data="sbsjDataZxmsOld.dmsq_list" @select="(selection, row) => tableSelected(selection, row, 'dmsq_list')" @select-all="(selection) => tableSelectedAll(selection, 'dmsq_list')" border :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" style="width: 100%;border:1px solid #EBEEF5;" height="calc(100%)" stripe>
              <el-table-column type="selection" width="55" align="center"> </el-table-column>
              <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
              <el-table-column prop="nd" label="年度"></el-table-column>
              <el-table-column prop="bsqjg" label="被授权机关、单位名称"></el-table-column>
              <el-table-column prop="sqjg" label="授权机关/单位名称"></el-table-column>
              <el-table-column prop="dmqx" label="权限"></el-table-column>
              <el-table-column prop="lksj" label="时间"></el-table-column>
              <el-table-column prop="qxnd" label="期限（年）"></el-table-column>
              <el-table-column prop="sx" label="事项"></el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="国家秘密事项" style="height: 403px;">
            <el-table :data="sbsjDataZxmsOld.Gjmmsx_list" @select="(selection, row) => tableSelected(selection, row, 'Gjmmsx_list')" @select-all="(selection) => tableSelectedAll(selection, 'Gjmmsx_list')" border :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" style="width: 100%;border:1px solid #EBEEF5;" height="calc(100%)" stripe>
              <el-table-column type="selection" width="55" align="center"> </el-table-column>
              <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
              <el-table-column prop="nd" width="60" label="年度"></el-table-column>
              <el-table-column prop="gjmmsxmc" width="140px" label="国家秘密事项名称"></el-table-column>
              <el-table-column prop="mj" label="密级"></el-table-column>
              <el-table-column prop="bmqx" label="保密期限"></el-table-column>
              <el-table-column prop="zxfw" label="知悉范围"></el-table-column>
              <el-table-column prop="dmyj" label="定密依据"></el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="定密培训" style="height: 403px;">
            <el-table :data="sbsjDataZxmsOld.dmpx_list" @select="(selection, row) => tableSelected(selection, row, 'dmpx_list')" @select-all="(selection) => tableSelectedAll(selection, 'dmpx_list')" border :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" style="width: 100%;border:1px solid #EBEEF5;" height="calc(100%)" stripe>
              <el-table-column type="selection" width="55" align="center"> </el-table-column>
              <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
              <el-table-column prop="nd" label="年度"></el-table-column>
              <el-table-column prop="pxsj" label="培训时间"></el-table-column>
              <el-table-column prop="xs" label="学时（小时）"></el-table-column>
              <el-table-column prop="pxrs" label="培训人数（人）"></el-table-column>
              <el-table-column prop="pxdx" label="培训对象"></el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="定密情况年度统计" style="height: 403px;">
            <el-table :data="sbsjDataZxmsOld.dmqkndtj_list" @select="(selection, row) => tableSelected(selection, row, 'dmqkndtj_list')" @select-all="(selection) => tableSelectedAll(selection, 'dmqkndtj_list')" border :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" style="width: 100%;border:1px solid #EBEEF5;" height="calc(100%)" stripe>
              <el-table-column type="selection" width="55" align="center"> </el-table-column>
              <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
              <el-table-column prop="nd" label="年度" width="60" align="center"></el-table-column>
              <el-table-column prop="gjmmzshj" label="国家秘密数量"></el-table-column>
              <el-table-column prop="dmzrrhj" label="定密责任人数量"></el-table-column>
              <el-table-column prop="dmsqxzshj" label="定密授权数量"></el-table-column>
              <el-table-column prop="gjmmsxylbxzzsylbs" label="国家秘密事项数量"></el-table-column>
              <el-table-column prop="dmzdsxczs" label="定密制度数量"></el-table-column>
              <el-table-column prop="pxcs" label="定密培训次数"></el-table-column>
              <el-table-column prop="gzmmqds" label="工作秘密数量"></el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="不明确事项确定情况" style="height: 403px;">
            <el-table :data="sbsjDataZxmsOld.dmqsxqdqk_list" @select="(selection, row) => tableSelected(selection, row, 'dmqsxqdqk_list')" @select-all="(selection) => tableSelectedAll(selection, 'dmqsxqdqk_list')" border :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" style="width: 100%;border:1px solid #EBEEF5;" height="calc(100%)" stripe>
              <el-table-column type="selection" width="55" align="center"> </el-table-column>
              <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
              <el-table-column prop="sxmc" label="事项名称"></el-table-column>
              <el-table-column prop="mj" label="密级"></el-table-column>
              <el-table-column prop="bmqx" label="保密期限"></el-table-column>
              <el-table-column prop="qyrq" label="确认时间"></el-table-column>
              <el-table-column prop="qrly" label="确认理由"></el-table-column>
              <el-table-column prop="djsj" label="登记时间"></el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="政府采购项目情况" style="height: 403px;">
            <el-table :data="sbsjDataZxmsOld.zfcgxmqk_list" @select="(selection, row) => tableSelected(selection, row, 'zfcgxmqk_list')" @select-all="(selection) => tableSelectedAll(selection, 'zfcgxmqk_list')" border :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" style="width: 100%;border:1px solid #EBEEF5;" height="calc(100%)" stripe>
              <el-table-column type="selection" width="55" align="center"> </el-table-column>
              <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
              <el-table-column prop="nd" label="年度"></el-table-column>
              <el-table-column prop="mc" label="项目名称"></el-table-column>
              <el-table-column prop="zl" label="项目种类"></el-table-column>
              <el-table-column prop="mj" label="项目密级"></el-table-column>
              <el-table-column prop="je" label="项目金额"></el-table-column>
              <el-table-column prop="gysmc" label="供应商名称"></el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogVisibleSjsbZxms = false">自选数据确认</el-button>
        <el-button type="warning" @click="dialogVisibleSjsbZxms = false">取 消</el-button>
      </span>
    </el-dialog>
    <!---->
  </div>
</template>

<script>
import { removeWindowLocation, getWindowLocation } from '../../../utils/windowLocation'

import { writeLoginLog, writeSystemOptionsLog } from '../../../utils/logUtils'

import { errorProcessor } from '../../../utils/errorProcessor'

import { getZczpPath } from '../../../utils/pathUtil'

import { encryptAesCBC, decryptAesCBC } from '../../../utils/aesUtils'

import { dateFormatChinese } from '../../../utils/moment'

import { checkArr } from '../../../utils/utils'

//
import { getAllSmgwZxms } from '../../../db/smgwgldb'
import { getAllCgcjZxms } from '../../../db/cgcjdb'
import { getAllSmryZxms } from '../../../db/smrydb'
import { getAllLglzZxms } from '../../../db/lglzdb'
import { getAllDmzrrZxms } from '../../../db/dmzrrdb'
import { getAllDmsqZxms } from '../../../db/dmsqdb'
import { getAllGjmmxsZxms } from '../../../db/gjmmsxdb'
import { getAllDmpxZxms } from '../../../db/dmpxdb'
import { getAllDmqkndtjZxms } from '../../../db/dmqkndtjdb'
import { getAllBmqsxqdqkZxms } from '../../../db/bmqsxqdqkdb'
import { getAllZfcgxmqkZxms } from '../../../db/zfcgxmqkdb'
import { getDwxxListAll } from '../../../db/dwxxDb'

export default {
  data() {
    return {
      // 上报数据自选模式dialog显隐
      dialogVisibleSjsbZxms: false,
      // 上报数据自选模式(备选数据)
      sbsjDataZxmsOld: {
        Smgwgl_list: [],
        Smry_list: [],
        Lglz_list: [],
        dmzrr_list: [],
        dmsq_list: [],
        Gjmmsx_list: [],
        dmpx_list: [],
        dmqkndtj_list: [],
        dmqsxqdqk_list: [],
        zfcgxmqk_list: []
      },
      // 上报数据(已选数据)
      sbsjDataZxms: {},
      // 人员上报 表数组
      rysbTableArr: ['Smgwgl_list', 'Smry_list', 'Lglz_list'],
      // 定密上报 表数组
      dmsbTableArr: ['dmzrr_list', 'dmsq_list', 'Gjmmsx_list', 'dmpx_list', 'dmqkndtj_list', 'dmqsxqdqk_list', 'zfcgxmqk_list'],
      // 数据上报数据类型默认值
      sbsjlx: 1,
      // 数据上报dialog数据
      dialogSjsb: {
        // 默认值
        sblx: [1, 2]
      },
      // 数据上报dialog显隐
      dialogVisibleSjsb: false,
      // 当前登录用户信息显隐
      showUserCardFlag: false,
      // 当前登录用户的姓名
      currentXm: '',
      // 当前登录用户的用户名(目前系统没有角色概念，故直接使用账号进行按钮显隐的判断)（用户名从登录时放入缓存的数据获取）
      currentYhm: '',
      // 当前登录用户的类型
      currentYhlx: '',
      /**
       * 最大化按钮控制配置
       * 窗口化时显示最大化
       * 最大化时显示窗口化
      */
      windowBtnConfig: {
        isMax: false
      },
      // 系统设置菜单集合
      systemSettingList: [
        {
          name: '密码重置',
          path: '/mmczSetting',
          icon: 'el-icon-edit',
          // 判断按钮是否需要隐藏（目前系统没有角色概念，故直接使用账号进行判断）
          showRoles: ['root'],
          showYhlxs: [0]
        },
        {
          name: '修改密码',
          path: '/xgmmSetting',
          icon: 'el-icon-edit',
          showRoles: ['all'],
          showYhlxs: [0, 1, 3]
        },
        {
          name: '参数设置',
          path: '/systemSetting',
          icon: 'el-icon-notebook-2',
          showRoles: ['root', 'sysadmin'],
          showYhlxs: [0, 1]
        },
        {
          name: '文件路径设置',
          path: '/filePathSetting',
          icon: 'el-icon-files',
          showRoles: ['root', 'sysadmin'],
          showYhlxs: [0, 1]
        },
        // {
        //   name: '代码管理',
        //   path: '/dmglSetting',
        //   icon: 'el-icon-c-scale-to-original',
        //   showRoles: ['root', 'sysadmin'],
        //   showYhlxs: [0, 1]
        // },
        {
          name: '数据上报',
          path: '/sjsbSetting',
          icon: 'el-icon-bank-card',
          showRoles: ['root', 'sysadmin'],
          showYhlxs: [0, 1, 3]
        },
        {
          name: '注册信息维护',
          path: '/zcxxSetting',
          icon: 'el-icon-office-building',
          showRoles: ['root', 'sysadmin'],
          showYhlxs: [0, 1, 3]
        },
        {
          name: '关于我们',
          path: '/gywmSetting',
          icon: 'el-icon-message',
          showRoles: ['all'],
          showYhlxs: [0, 1, 3]
        },
        // {
        //   name: '工具箱',
        //   path: '/toolBox',
        //   icon: 'el-icon-message',
        //   showRoles: ['all']
        // },
        {
          name: '退出登录',
          path: '/quit',
          icon: 'el-icon-warning-outline',
          showRoles: ['all']
        }
      ]
    }
  },
  methods: {
    // 表格选择事件
    tableSelected(selection, row, tableName) {
      console.log(selection, row, tableName)
      this.sbsjDataZxms[tableName] = selection
    },
    // 表格全选事件
    tableSelectedAll(selection, tableName) {
      console.log(selection, tableName)
      this.sbsjDataZxms[tableName] = selection
    },
    // 当前登录用户信息鼠标移出事件
    userCardMouseLeave() {
      // console.log('鼠标移出去了')
      this.showUserCardFlag = false
    },
    // 关闭 el-popover 弹出窗
    closeElPopover() {
      console.log('closeElPopover mouseleave')
      this.elPopoverFlag = false
    },
    closeElPopover() {
      console.log('closeElPopover mouseleave')
      this.elPopoverFlag = false
    },
    // 页面跳转方法
    toPath(path) {
      if (path == '/quit') {
        // 写入登录日志
        writeLoginLog(1)
        // 清除缓存
        removeWindowLocation()
        this.$router.push('/')
        return
      }
      if (path == '/sjsbSetting') {
        // this.generateSbsj()
        // 设置上报数据dialog默认数据
        this.dialogSjsb = {
          sbsj: new Date().getFullYear() + '',
          sblx: [1, 2]
        }
        this.dialogVisibleSjsb = true
        return
      }
      this.$router.push(path)
    },
    // 数据上报方法
    generateSbsj() {
      let FS = require('fs')
      let params = this.dialogSjsb
      if (!params) {
        this.$message.warning('上报时间或上报类型数据异常')
        return
      }
      let sbsjlxParams = this.sbsjlx
      let sbYear = params.sbsj
      let sblx = params.sblx
      if (!sbsjlxParams) {
        this.$message.warning('上报数据类型未选择')
        return
      }
      if (!sbYear) {
        this.$message.warning('上报时间未选择')
        return
      }
      if (!sblx || !checkArr(sblx) || sblx.length < 1) {
        this.$message.warning('上报类型未选择')
        return
      }
      // 弹出dialog选择上报数据保存文件位置
      let sbsjFileName = '上报数据-' + sbYear + '-' + (new Date().getTime()) + '.json'
      const { dialog } = require('electron').remote
      let options = {
        title: '选择上报数据保存位置',
        defaultPath: sbsjFileName
      }
      dialog.showSaveDialog(options, result => {
        let zczpCurrentJsonObj = {}
        // 自选模式
        if (sbsjlxParams == 2) {
          console.log('自选模式...')
          zczpCurrentJsonObj = this.handleSbsjZxms(sbYear)
        } else {
          // 自动筛选模式
          console.log('自动筛选模式')
          zczpCurrentJsonObj = this.handleSbsjZdsxms(sbYear, sblx, FS)
        }
        // json对象中加入本次数据所属的年份(共后台区分具体是哪个年份的数据)
        zczpCurrentJsonObj['data_mess'] = [{
          'ssnf': sbYear
        }]
        console.log(zczpCurrentJsonObj)
        // return
        // 密钥年份
        let currentYear = new Date().getFullYear()
        // 生成密钥
        let keyStr = 'hsoft3Banner' + currentYear
        // 数据加密
        let encryptStr
        try {
          encryptStr = encryptAesCBC(JSON.stringify(zczpCurrentJsonObj), keyStr)
        } catch (error) {
          this.$notify({
            title: '系统异常',
            message: '[' + '上报数据加密异常' + ']\n' + error.message,
            type: 'error',
            offset: 100,
            duration: 0
          })
          return
        }
        // 测试解密
        let decryptJsonObj
        try {
          decryptJsonObj = JSON.parse(decryptAesCBC(encryptStr, keyStr))
        } catch (error) {
          this.$notify({
            title: '系统异常',
            message: '[' + '上报数据测试解密异常' + ']\n' + error.message,
            type: 'error',
            offset: 100,
            duration: 0
          })
          return
        }
        // 生成上报数据文件
        try {
          FS.writeFileSync(result, encryptStr, { encoding: 'utf8' })
          // 未加密数据(测试对接时使用)
          // FS.writeFileSync(result.substring(0, result.indexOf('.') + 1) + '(未加密).json', JSON.stringify(zczpCurrentJsonObj), { encoding: 'utf8' })
        } catch (error) {
          error = errorProcessor(error)
          let errObj = JSON.parse(error.message)
          this.$notify({
            title: '系统异常',
            message: '[' + errObj.mark + ']\n',
            type: 'error',
            offset: 100,
            duration: 0
          })
          return
        }
        // 上报数据生成成功
        this.$message.success('上报数据生成成功=>' + result)
        // 写入日志
        let logParams = {
          xyybs: 'yybs_sjsb',
          ymngnmc: '数据上报',
          extraParams: params
        }
        writeSystemOptionsLog(logParams)
        this.dialogVisibleSjsb = false
      })
    },
    // 自选模式数据处理
    handleSbsjZxms() {
      // 获取单位信息加入到json对象中
      this.sbsjDataZxms['dwxx_List'] = getDwxxListAll()
      return this.sbsjDataZxms
    },
    // 自动筛选模式数据处理
    handleSbsjZdsxms(currentYear, sblx, FS) {
      // 获取文件路径
      let zczpJsonPath = getZczpPath()
      if (!zczpJsonPath) {
        this.$notify({
          title: '系统异常',
          message: '[' + '上报数据文件路径获取失败' + ']\n',
          type: 'error',
          offset: 100,
          duration: 0
        })
        return
      }
      // 获取文件内容
      let zczpJsonStr
      try {
        zczpJsonStr = FS.readFileSync(zczpJsonPath, { encoding: 'utf8' })
      } catch (error) {
        console.log(error)
        error = errorProcessor(error)
        let errObj = JSON.parse(error.message)
        this.$notify({
          title: '系统异常',
          message: '[' + errObj.mark + ']\n',
          type: 'error',
          offset: 100,
          duration: 0
        })
        return
      }
      // 简单校验数据是否完整
      let zczpJsonObj
      try {
        zczpJsonObj = JSON.parse(zczpJsonStr)
      } catch (error) {
        this.$notify({
          title: '系统异常',
          message: '[' + '上报数据文件内容异常' + ']\n',
          type: 'error',
          offset: 100,
          duration: 0
        })
        return
      }
      // 筛选今年数据
      // console.log('筛选今年数据')
      let zczpCurrentJsonObj = {}
      let table
      // 今年一月第一天
      let startTime = new Date(currentYear, 0, 1).getTime()
      // 今年最后月最后一天最后时刻
      let endTime = new Date(parseInt(currentYear) + 1, 0, 0, 23, 59, 59, 999).getTime()
      // console.log(startTime, endTime, dateFormatChinese(new Date(startTime)), dateFormatChinese(new Date(endTime)), new Date(), dateFormatChinese(new Date()))
      // 加工上报数据表数组(内置单位信息表)
      let sbTableAllArr = ['dwxx_List']
      sblx.forEach(item => {
        if (item == 1) {
          sbTableAllArr = sbTableAllArr.concat(this.rysbTableArr)
        }
        if (item == 2) {
          sbTableAllArr = sbTableAllArr.concat(this.dmsbTableArr)
        }
      })
      if (sbTableAllArr.length < 1) {
        this.$message.warning('数据异常，未检测到正确的上报类型')
        return
      }
      //
      // console.log('sbTableAllArr', sbTableAllArr)
      Object.keys(zczpJsonObj).forEach(tableName => {
        // console.log(tableName, sbTableAllArr.indexOf(tableName))
        if (sbTableAllArr.indexOf(tableName) == -1) {
          return
        }
        table = zczpJsonObj[tableName]
        if (tableName == 'dwxx_List') {
          zczpCurrentJsonObj[tableName] = table
          return
        }
        // 初始化新json对象对应表数据
        zczpCurrentJsonObj[tableName] = table.filter(item => {
          // console.log(startTime, item.gxsj, endTime, item.gxsj >= startTime, item.gxsj <= endTime)
          if (item.gxsj >= startTime && item.gxsj <= endTime) {
            return item
          }
        })
      })
      return zczpCurrentJsonObj
    },
    // 最小化系统
    minimizeSystem() {
      this.$electron.ipcRenderer.send("hide-window")
    },
    // 最大化系统
    maximizeSystem() {
      let isMax = this.windowBtnConfig.isMax
      console.log('isMax', isMax, 'windowBtnConfig', this.windowBtnConfig)
      // this.$electron.ipcRenderer.send("max-window")
      // return
      this.windowBtnConfig.isMax = !isMax
      if (isMax) {
        console.log('窗口化')
        this.$electron.ipcRenderer.send("unmax-window")
      } else {
        console.log('最大化')
        this.$electron.ipcRenderer.send("max-window")
      }
    },
    // 退出系统
    quitSystem() {
      this.$confirm('是否执行此操作，点击将退出系统？', '是否退出系统？', {
        cancelButtonClass: "btn-custom-cancel",
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        // center: true
      }).then(() => {
        this.$emit('quitClicked', true)
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消退出系统'
        })
      })
    },
    // 初始化表格数据(自选模式)
    initTableDataZxms() {
      //
      this.sbsjDataZxmsOld = {
        Smgwgl_list: [],
        Smry_list: [],
        Lglz_list: [],
        dmzrr_list: [],
        dmsq_list: [],
        Gjmmsx_list: [],
        dmpx_list: [],
        dmqkndtj_list: [],
        dmqsxqdqk_list: [],
        zfcgxmqk_list: []
      }
      // 获取sm岗位信息（Smgwgl_list）(所有)
      this.sbsjDataZxmsOld.Smgwgl_list = this.getAllSmgwList()
      // 获取sm人员信息(Smry_list)(所有)
      this.sbsjDataZxmsOld.Smry_list = this.getAllSmryList()
      // 获取离岗离职信息(Lglz_list)(所有)
      this.sbsjDataZxmsOld.Lglz_list = this.getAllLglzList()
      // 获取dm责任人信息(dmzrr_list)(所有)
      this.sbsjDataZxmsOld.dmzrr_list = this.getAllDmzrrList()
      // 获取dm授权信息(dmsq_list)(所有)
      this.sbsjDataZxmsOld.dmsq_list = this.getAllDmsqList()
      // 获取国家mm事项信息(Gjmmsx_list)(所有)
      this.sbsjDataZxmsOld.Gjmmsx_list = this.getAllGjmmsxList()
      // 获取dm培训信息(dmpx_list)(所有)
      this.sbsjDataZxmsOld.dmpx_list = this.getAllDmpxList()
      // 获取dm情况年度统计信息(dmqkndtj_list)(所有)
      this.sbsjDataZxmsOld.dmqkndtj_list = this.getAllDmqkndtjList()
      // 获取不明确事项确定情况信息(dmqsxqdqk_list)(所有)
      this.sbsjDataZxmsOld.dmqsxqdqk_list = this.getAllBmqsxqdqkList()
      // 获取zf采购项目情况信息(zfcgxmqk_list)(所有)
      this.sbsjDataZxmsOld.zfcgxmqk_list = this.getAllZfcgxmqkList()
      //
    },
    // 获取所有sm岗位信息
    getAllSmgwList() {
      return getAllSmgwZxms()
    },
    // 获取所有sm人员信息
    getAllSmryList() {
      let smryListAll = getAllSmryZxms()
      smryListAll.forEach(item => {
        if (checkArr(item.gwmc)) {
          item.gwmc = item.gwmc.join(',')
        }
      })
      return smryListAll
    },
    // 获取所有离岗离职信息
    getAllLglzList() {
      let lglzListAll = getAllLglzZxms()
      lglzListAll.forEach(item => {
        if (checkArr(item.ygwmc)) {
          item.ygwmc = item.ygwmc.join(',')
        }
      })
      return lglzListAll
    },
    // 获取所有dm责任人信息
    getAllDmzrrList() {
      return getAllDmzrrZxms()
    },
    // 获取所有dm授权信息
    getAllDmsqList() {
      return getAllDmsqZxms()
    },
    // 获取所有国家mm事项信息
    getAllGjmmsxList() {
      return getAllGjmmxsZxms()
    },
    // 获取所有dm培训信息
    getAllDmpxList() {
      return getAllDmpxZxms()
    },
    // 获取所有dm情况年度统计信息
    getAllDmqkndtjList() {
      return getAllDmqkndtjZxms()
    },
    // 获取所有不明确事项确定情况信息
    getAllBmqsxqdqkList() {
      return getAllBmqsxqdqkZxms()
    },
    // 获取所有zf采购项目情况信息
    getAllZfcgxmqkList() {
      return getAllZfcgxmqkZxms()
    },
  },
  mounted() {
  },
  watch: {
    // 每次路由变动都去扫一遍缓存中的用户信息（为了处理账号切换时设置里的菜单未正确显隐问题）
    $route(to, form) {
      console.log('路由变更，校验设置里的菜单')
      // 获取当前登录用户信息
      let localObj = getWindowLocation()
      if (localObj) {
        this.currentYhm = localObj.yhm
        this.currentXm = localObj.xm
        this.currentYhlx = localObj.yhlx
      }
    },
    // 监听数据上报数据类型变动，如是往年数据，则需要弹框给用户提供选择
    sbsjlx(newVal, oldVal) {
      if (newVal == 1) {
        let now = new Date()
        this.dialogSjsb.sbsj = now.getFullYear() + ''
      } else {
        this.dialogSjsb.sbsj = undefined
        this.initTableDataZxms()
        this.dialogVisibleSjsbZxms = true
      }
    }
  }
}
</script>

<style scoped>
.out {
  display: flex;
  height: 100%;
  /* background: red; */
  color: white;
  height: 40px;
  /* background: rgba(255, 255, 255, 0.3); */
  background: url(../../assets/background/head-mall-box.png) no-repeat;
  background-size: cover;
  color: white;
  /* align-self: center; */
  width: 100%;
  /* border-radius: 18px; */
  padding: 0 10px;
}
.out .card {
  flex: 1;
  align-items: center;
  display: flex;
  justify-content: center;
  /* flex-direction: column; */
  cursor: pointer;
}
/* .out .card:hover {
  cursor: pointer;
  background: rgba(255, 255, 255, 0.18);
} */
.out .card:nth-child(odd) {
  /* background: green; */
}
.out .card img {
  /* margin: 5px 10px 5px 0; */
  width: 22px;
  height: 22px;
  align-self: center;
  cursor: pointer;
  /* margin-right: 11.25px; */
  /* background: rgba(255, 255, 255, 0.5); */
  /* padding: 5px; */
  /* box-sizing: border-box; */
  /* border-radius: 50%; */
}
.out .card img:hover {
}
.out .card span {
  font-size: 16px;
  font-weight: 400;
}
.out .card i {
  /* margin: 5px 10px 5px 0; */
  font-size: 20px;
  cursor: pointer;
  margin-right: 5px;
  /* background: rgba(255, 255, 255, 1); */
  /* padding: 5px; */
  /* box-sizing: border-box; */
  /* border-radius: 50%; */
  /* color: red; */
  /* font-weight: bold; */
}
.out .card i:hover {
}
/**系统设置菜单**/
.system-setting-out {
  /* background: red; */
  font-size: 13px;
}
.system-setting-out div {
  border-radius: 6px;
  margin: 3px 0;
  cursor: pointer;
  padding: 3px 0 3px 15px;
}
.system-setting-out div:hover {
  background: #f4f4f5;
  color: #409eff;
}
.system-setting-out > div i {
  margin-right: 10px;
}
/**当前登录用户信息展示卡片样式**/
.div-user-card {
  position: absolute;
  top: calc(27px + 10px);
  background: white;
  color: black;
  padding: 5px 10px;
  border-radius: 5px;
  /* width: 200px; */
  text-align: left;
}
.out .card .div-user-card span {
  font-size: 14px;
}
.out .card .div-user-card .pointer {
  /* background: #3dff00; */
  width: 8px;
  height: 8px;
  display: inline-block;
  border-radius: 50%;
  /* border: 1px solid #909399; */
  background-image: linear-gradient(50deg, white, green);
}
</style>
