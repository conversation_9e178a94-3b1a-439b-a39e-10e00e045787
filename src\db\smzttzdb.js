import db from "./adapter/zczpAdaptor";
// 日志工具类
import { writeTrajectoryLog } from '../utils/logUtils'

//保密制度-----------------------------------保密制度初始化列表********
export const getSmzttz = (params) => {
  // console.log(params);
  let page = params.page;
  let pagesize = params.pageSize;
  let list_total = db
    .get("Smzttz_list")
    .sortBy("cjsj")
    .filter(function (item) {
      return item;
    })
    .cloneDeep()
    .value();

  // 手动分页
  console.log(pagesize * (page - 1), pagesize * (page - 1) + pagesize);
  let pageList = list_total.slice(
    pagesize * (page - 1),
    pagesize * (page - 1) + pagesize
  );

  let resList = {
    list: pageList,
    list_total: list_total,
    total: list_total.length,
  };
  console.log("保密制度", resList);
  return resList;
};
//保密制度-----------------------------------保密制度添加成员********
export const addSmzttz = (params) => {
  console.log('params', params)
  let sjc = new Date().getTime();
  params.cjsj = sjc;
  params.gxsj = sjc;
  db.read().get("Smzttz_list").push(params).write();
  // 添加日志
  let paramsLog = {
    xyybs: 'mk_smzttz',
    id: params.smztid,
    ymngnmc: '新增',
    extraParams: {
      ztbhsj: params.ztbhsj,
      zrr: params.zrr
    }
  }
  writeTrajectoryLog(paramsLog)
};
//添加校验
export const jyaddSmzttz = (params) => {
  let ztbh = params.ztbh;
  let message = 0;
  let pdztbh = db
    .read()
    .get("Smzttz_list")
    .find({ ztbh: ztbh })
    .cloneDeep()
    .value();
    console.log(pdztbh);
  if (pdztbh) {
    message = 2;
  }
  return message;
};
//修改
export const reviseSmzttz = (params) => {
  let scbm = params.scbm.join("/");
  params.scbm = scbm;
  let smztid = params.smztid;
  console.log("smjsjid", smztid);
  if (!smztid || smztid == "") {
    return;
  }
  params.gxsj = new Date().getTime();
  // 全字段更新方法(传进来的字段都会更新)
  db.read().get("Smzttz_list").find({ smztid: smztid }).assign(params).write();
};
//保密制度-----------------------------------保密制度删除成员********
export const deleteSmzttz = (params) => {
  db.read().get("Smzttz_list").remove(params).write();
};

// 更新设备状态
export const updateSbZt = (params, targetObj) => {
  console.log('updateSbZt params', params, targetObj)
  params.forEach((item) => {
    // item的状态旧值（做状态未更改不添加日志的功能）
    let itemZtOld = item.zt
    db.get("Smzttz_list")
      .find({ ztbh: item.ztbh })
      .assign({
        zt: targetObj.zt,
        ztbhsj: targetObj.ztbhsj
      })
      .write()
      // 添加日志
      if(itemZtOld != targetObj.zt) {
        let paramsLog = {
          xyybs: 'mk_smzttz',
          id: item.smztid,
          ymngnmc: targetObj.zt,
          extraParams: {
            zrr: item.zrr,
            ztbhsj: targetObj.ztbhsj
          }
        }
        writeTrajectoryLog(paramsLog)
      }
  })
}

//非是秘密计算机修改
export const xgsmjsjsyzt_zy = (params) => {
  console.log(params);
  params.forEach((item) => {
    db.get("Smzttz_list")
      .find({ ztbh: item.ztbh })
      .assign({
        zt: "在管",
      })
      .write();
      // 添加日志
      let paramsLog = {
        xyybs: 'mk_smzttz',
        id: item.smztid,
        ymngnmc: '在管'
      }
      writeTrajectoryLog(paramsLog)
  });
};
//非是秘密计算机修改
export const xgsmjsjsyzt_ty = (params) => {
  console.log(params);
  params.forEach((item) => {
    db.get("Smzttz_list")
      .find({ ztbh: item.ztbh })
      .assign({
        zt: "借出",
      })
      .write();
      // 添加日志
      let paramsLog = {
        xyybs: 'mk_smzttz',
        id: item.smztid,
        ymngnmc: '借出'
      }
      writeTrajectoryLog(paramsLog)
  });
};
//非是秘密计算机修改
export const xgsmjsjsyzt_bf = (params) => {
  console.log(params);
  params.forEach((item) => {
    db.get("Smzttz_list")
      .find({ ztbh: item.ztbh })
      .assign({
        zt: "报废",
      })
      .write();
      // 添加日志
      let paramsLog = {
        xyybs: 'mk_smzttz',
        id: item.smztid,
        ymngnmc: '报废'
      }
      writeTrajectoryLog(paramsLog)
  });
};
//秘密计算机修改
export const xgsmjsjsyzt_jc = (params) => {
  console.log(params);
  params.forEach((item) => {
    db.get("Smzttz_list")
      .find({ ztbh: item.ztbh })
      .assign({
        zt: "外发",
      })
      .write();
      // 添加日志
      let paramsLog = {
        xyybs: 'mk_smzttz',
        id: item.smztid,
        ymngnmc: '外发'
      }
      writeTrajectoryLog(paramsLog)
  });
};
//秘密计算机修改
export const xgsmjsjsyzt_xh = (params) => {
  console.log(params);
  params.forEach((item) => {
    db.get("Smzttz_list")
      .find({ ztbh: item.ztbh })
      .assign({
        zt: "销毁",
      })
      .write();
      // 添加日志
      let paramsLog = {
        xyybs: 'mk_smzttz',
        id: item.smztid,
        ymngnmc: '销毁'
      }
      writeTrajectoryLog(paramsLog)
  });
};
export const getSmzt = () => {
	let smzt = db.get('Smzttz_list').cloneDeep().value()
	console.log()
	return smzt
}
