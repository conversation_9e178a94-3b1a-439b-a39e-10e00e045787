import db from "./adapter/zczpAdaptor";

//保密制度-----------------------------------保密制度初始化列表********
export const getCsgl = (params) => {
  let page = params.page;
  let pagesize = params.pageSize;
  let csmc = params.csmc;
  let sqrq = params.sqrq;
  let list_total = db
    .get("Csgl_list")
    .sortBy("cjsj")
    .filter(function (item) {
      // 1、名称和创建时间都没有
      if (
        (csmc === undefined || csmc == "") &&
        (sqrq === undefined || sqrq == null)
      ) {
        return item;
        console.log("全都没有", item);
      }
      // 2、名称有，创建时间没有
      else if (csmc && (sqrq === undefined || sqrq == null)) {
        if (item.csmc) {
          if (item.csmc.indexOf(csmc) != -1) {
            console.log("ccc", item);
            return item;
          }
        } else {
          console.log("item.csmc", item.csmc);
        }
      }
      // 3、名称没有，创建时间有
      else if ((csmc === undefined || csmc == "") && sqrq) {
        if (item.sqrq) {
          if (item.sqrq >= sqrq[0] && item.sqrq <= sqrq[1]) {
            return item;
          }
        } else {
          console.log("item.sqrq", item.sqrq);
        }
      }
      // 4、名称有，创建时间有
      else if (csmc && sqrq) {
        if (item.csmc && item.sqrq) {
          if (
            item.csmc.indexOf(csmc) != -1 &&
            item.sqrq >= sqrq[0] &&
            item.sqrq <= sqrq[1]
          ) {
            return item;
          }
        } else {
          console.log("item.csmc", item.csmc, "item.sqrq", item.sqrq);
        }
      }
    })
    .cloneDeep()
    .value();
 
  // 手动分页
  console.log(pagesize * (page - 1), pagesize * (page - 1) + pagesize);
  let pageList = list_total.slice(
    pagesize * (page - 1),
    pagesize * (page - 1) + pagesize
  );

  let resList = {
    list: pageList,
    list_total: list_total,
    total: list_total.length,
  };
  console.log("保密制度", resList);
  return resList;
};
//保密制度-----------------------------------保密制度添加成员********
export const addCsgl = (params) => {
  let sjc = new Date().getTime();
  params.cjsj = sjc;
  params.gxsj = sjc;
  db.read().get("Csgl_list").push(params).write();
};
export const jycsgl = (params) => {
  let csmc = params.csmc;
  let message = 0;
  let pdcsmc = db
    .read()
    .get("Csgl_list")
    .find({ csmc: csmc })
    .cloneDeep()
    .value();
  if (pdcsmc != undefined) {
    message = 1;
  }
  console.log(message);
  return message;
};
//保密制度-----------------------------------保密制度删除成员********
export const deleteCsgl = (params) => {
  let csglid = params.csglid;
  let csgl = db
    .read()
    .get("Csbg_list")
    .filter({ csglid: csglid })
    .cloneDeep()
    .value();
  csgl.forEach((item) => {
    db.read().get("Csbg_list").remove(item).write();
  });
  db.read().get("Csgl_list").remove(params).write();
};
export const updatecsgl = (params) => {
  let zrbm = params.zrbm.join("/");
  params.zrbm = zrbm;
  // 数据校验
  // 校验ID
  let csglid = params.csglid;
  if (!csglid || csglid == "") {
    return;
  }
  params.gxsj = new Date().getTime();
  // 全字段更新方法(传进来的字段都会更新)
  db.read().get("Csgl_list").find({ csglid: csglid }).assign(params).write();
  // // 更新部分字段方法
  // db.read().get('Bmzd_list').find({smryid:smryid}).assign({
  // 	zdmc: params.zdmc,
  // 	...
  // }).write()
};
export const getsmry = (params) => {
  let smry = db
    .get("Smry_list")
    .filter({
      bm: params,
    })
    .cloneDeep()
    .value();
  console.log(smry);
  return smry;
};

export const getSmryList = (params) => {
  let smry = db.get("Smry_list").cloneDeep().value();
  console.log(smry);
  return smry;
};


export const getSzwz = () => {
	let szwz = db.get('Csgl_list').cloneDeep().value()
	console.log()
	return szwz
}
