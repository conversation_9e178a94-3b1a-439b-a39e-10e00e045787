<template>
  <div>
    <hsoft_top_title>
      <template #left>注册信息维护</template>
    </hsoft_top_title>
    <!---->
    <div class="out-card">
      <div class="out-card-div dwxx">
        <div @mouseenter="divMouseEnter('dwmcEdit')" @mouseleave="divMouseLeave('dwmcEdit')">
          <label>单位名称</label>
          <div class="article">
            <span v-show="dwxx.dwmcEdit == 0 || dwxx.dwmcEdit == 1">{{dwxx.dwmc}}</span>
            <el-input v-show="dwxx.dwmcEdit == 2" v-model="dwxx.dwmc" size="mini"></el-input>
          </div>
          <i v-show="dwxx.dwmcEdit == 1" @click="changeFlag('dwmcEdit')" class="el-icon-edit"></i>
          <i v-show="dwxx.dwmcEdit == 2" @click="changeDwxx('dwmcEdit', 'dwmc')" class="el-icon-check" style="color: #67C23A;"></i>
        </div>
        <div @mouseenter="divMouseEnter('dwzchEdit')" @mouseleave="divMouseLeave('dwzchEdit')">
          <label>单位注册号</label>
          <div class="article">
            <span v-show="dwxx.dwzchEdit == 0 || dwxx.dwzchEdit == 1">{{dwxx.dwzch}}</span>
            <el-input v-show="dwxx.dwzchEdit == 2" v-model="dwxx.dwzch" size="mini"></el-input>
          </div>
          <i v-show="dwxx.dwzchEdit == 1" @click="changeFlag('dwzchEdit')" class="el-icon-edit"></i>
          <i v-show="dwxx.dwzchEdit == 2" @click="changeDwxx('dwzchEdit', 'dwzch')" class="el-icon-check" style="color: #67C23A;"></i>
        </div>
        <div @mouseenter="divMouseEnter('xydmEdit')" @mouseleave="divMouseLeave('xydmEdit')">
          <label>社会信用统一代码</label>
          <div class="article">
            <span v-show="dwxx.xydmEdit == 0 || dwxx.xydmEdit == 1">{{dwxx.xydm}}</span>
            <el-input v-show="dwxx.xydmEdit == 2" v-model="dwxx.xydm" size="mini"></el-input>
          </div>
          <i v-show="dwxx.xydmEdit == 1" @click="changeFlag('xydmEdit')" class="el-icon-edit"></i>
          <i v-show="dwxx.xydmEdit == 2" @click="changeDwxx('xydmEdit', 'xydm')" class="el-icon-check" style="color: #67C23A;"></i>
        </div>
        <div @mouseenter="divMouseEnter('dwlxmcEdit')" @mouseleave="divMouseLeave('dwlxmcEdit')">
          <label>单位类型</label>
          <div class="article">
            <span v-show="dwxx.dwlxmcEdit == 0 || dwxx.dwlxmcEdit == 1">{{dwxx.dwlxmc}}</span>
            <el-select v-show="dwxx.dwlxmcEdit == 2" v-model="dwxx.dwlx" size="mini">
              <el-option v-for="(item, index) in dwlxList" :key="index" :value="item.dwlxid" :label="item.dwlxmc"></el-option>
            </el-select>
          </div>
          <i v-show="dwxx.dwlxmcEdit == 1" @click="changeFlag('dwlxmcEdit')" class="el-icon-edit"></i>
          <i v-show="dwxx.dwlxmcEdit == 2" @click="changeDwxx('dwlxmcEdit', 'dwlx')" class="el-icon-check" style="color: #67C23A;"></i>
        </div>
        <div @mouseenter="divMouseEnter('sslymcEdit')" @mouseleave="divMouseLeave('sslymcEdit')">
          <label>所属领域</label>
          <div class="article">
            <span v-show="dwxx.sslymcEdit == 0 || dwxx.sslymcEdit == 1">{{dwxx.sslymc}}</span>
            <el-select v-show="dwxx.sslymcEdit == 2" v-model="dwxx.ssly" size="mini">
              <el-option v-for="(item, index) in sslyList" :key="index" :value="item.sslyid" :label="item.sslymc"></el-option>
            </el-select>
          </div>
          <i v-show="dwxx.sslymcEdit == 1" @click="changeFlag('sslymcEdit')" class="el-icon-edit"></i>
          <i v-show="dwxx.sslymcEdit == 2" @click="changeDwxx('sslymcEdit', 'ssly')" class="el-icon-check" style="color: #67C23A;"></i>
        </div>
        <div @mouseenter="divMouseEnter('ssccmcEdit')" @mouseleave="divMouseLeave('ssccmcEdit')">
          <label>所属层次</label>
          <div class="article">
            <span v-show="dwxx.ssccmcEdit == 0 || dwxx.ssccmcEdit == 1">{{dwxx.ssccmc}}</span>
            <el-select v-show="dwxx.ssccmcEdit == 2" v-model="dwxx.sscc" size="mini">
              <el-option v-for="(item, index) in ssccList" :key="index" :value="item.ssccid" :label="item.ssccmc"></el-option>
            </el-select>
          </div>
          <i v-show="dwxx.ssccmcEdit == 1" @click="changeFlag('ssccmcEdit')" class="el-icon-edit"></i>
          <i v-show="dwxx.ssccmcEdit == 2" @click="changeDwxx('ssccmcEdit', 'sscc')" class="el-icon-check" style="color: #67C23A;"></i>
        </div>
        <div @mouseenter="divMouseEnter('dwlxrEdit')" @mouseleave="divMouseLeave('dwlxrEdit')">
          <label>单位联系人</label>
          <div class="article">
            <span v-show="dwxx.dwlxrEdit == 0 || dwxx.dwlxrEdit == 1">{{dwxx.dwlxr}}</span>
            <el-input v-show="dwxx.dwlxrEdit == 2" v-model="dwxx.dwlxr" size="mini"></el-input>
          </div>
          <i v-show="dwxx.dwlxrEdit == 1" @click="changeFlag('dwlxrEdit')" class="el-icon-edit"></i>
          <i v-show="dwxx.dwlxrEdit == 2" @click="changeDwxx('dwlxrEdit', 'dwlxr')" class="el-icon-check" style="color: #67C23A;"></i>
        </div>
        <div @mouseenter="divMouseEnter('dwlxdhEdit')" @mouseleave="divMouseLeave('dwlxdhEdit')">
          <label>单位联系电话号码</label>
          <div class="article">
            <span v-show="dwxx.dwlxdhEdit == 0 || dwxx.dwlxdhEdit == 1">{{dwxx.dwlxdh}}</span>
            <el-input v-show="dwxx.dwlxdhEdit == 2" v-model="dwxx.dwlxdh" size="mini"></el-input>
          </div>
          <i v-show="dwxx.dwlxdhEdit == 1" @click="changeFlag('dwlxdhEdit')" class="el-icon-edit"></i>
          <i v-show="dwxx.dwlxdhEdit == 2" @click="changeDwxx('dwlxdhEdit', 'dwlxdh')" class="el-icon-check" style="color: #67C23A;"></i>
        </div>
        <div @mouseenter="divMouseEnter('dwlxyxEdit')" @mouseleave="divMouseLeave('dwlxyxEdit')">
          <label>单位联系邮箱</label>
          <div class="article">
            <span v-show="dwxx.dwlxyxEdit == 0 || dwxx.dwlxyxEdit == 1">{{dwxx.dwlxyx}}</span>
            <el-input v-show="dwxx.dwlxyxEdit == 2" v-model="dwxx.dwlxyx" size="mini"></el-input>
          </div>
          <i v-show="dwxx.dwlxyxEdit == 1" @click="changeFlag('dwlxyxEdit')" class="el-icon-edit"></i>
          <i v-show="dwxx.dwlxyxEdit == 2" @click="changeDwxx('dwlxyxEdit', 'dwlxyx')" class="el-icon-check" style="color: #67C23A;"></i>
        </div>
        <div @mouseenter="divMouseEnter('ssqhEdit')" @mouseleave="divMouseLeave('ssqhEdit')">
          <label>单位所在省市区</label>
          <div class="article">
            <span v-show="dwxx.ssqhEdit == 0 || dwxx.ssqhEdit == 1">{{dwxx.province}}/{{dwxx.city}}/{{dwxx.district}}</span>
            <el-select v-show="dwxx.ssqhEdit == 2" v-model="dwxx.province" @change="provinceChanged" size="mini" style="width: 32%;">
              <el-option v-for="(item, index) in provinceList" :key="'province'+index" :label="item.name" :value="item.name"></el-option>
            </el-select>
            <el-select v-show="dwxx.ssqhEdit == 2" v-model="dwxx.city" @change="cityChanged" size="mini" style="width: 32%;">
              <el-option v-for="(item, index) in cityList" :key="'city'+index" :label="item.name" :value="item.name"></el-option>
            </el-select>
            <el-select v-show="dwxx.ssqhEdit == 2" v-model="dwxx.district" @change="districtChanged" size="mini" style="width: 32%;">
              <el-option v-for="(item, index) in districtList" :key="'district'+index" :label="item.name" :value="item.name"></el-option>
            </el-select>
          </div>
          <i v-show="dwxx.ssqhEdit == 1" @click="changeFlag('ssqhEdit')" class="el-icon-edit"></i>
          <i v-show="dwxx.ssqhEdit == 2" @click="changeDwxx('ssqhEdit', '')" class="el-icon-check" style="color: #67C23A;"></i>
        </div>
      </div>
    </div>
    <!---->
    <!---->
    <!---->
    <!---->
  </div>
</template>

<script>
import hsoft_top_title from '../../components/hsoft-top-title/hsoft-top-title.vue'

import { getWindowLocation, setWindowLocation } from '../../../utils/windowLocation'

import { dateFormatChinese } from '../../../utils/moment'

import { writeSystemOptionsLog } from '../../../utils/logUtils'

// 省市区信息
import address from '../../../utils/address.json'

// 单位类型
import { getDmbDwlxDB } from '../../../db/dmbDwlxDb'
// 单位所属领域
import { getDmbSslyDB } from '../../../db/dmbSslyDb'
// 单位所属层次
import { getDmbSsccDB } from '../../../db/dmbSsccDb'
// 单位信息
import { getDwxxListAll,updateDwxx } from '../../../db/dwxxDb'

export default {
  data () {
    return {
      // 单位信息
      dwxx: {},
      dwlxList: [],
      sslyList: [],
      ssccList: [],
      // 省集合
      provinceList: [],
      // 市集合
      cityList: [],
      // 区集合
      districtList: []
    }
  },
  components: {
    hsoft_top_title
  },
  methods: {
    // 格式化时间
    formatTime (time) {
      return dateFormatChinese(new Date(time))
    },
    // 通过ID获取所属层次
    getSsccBySsccId (ssccid) {
      if (ssccid === undefined) {
        this.$message.warning('单位所属层次ID为空')
        return
      }
      let params = {
        ssccid: ssccid
      }
      console.log(getDmbSsccDB(params))
      return getDmbSsccDB(params).list_total[0]
    },
    // 通过ID获取所属领域
    getSslyBySslyId (sslyid) {
      if (sslyid === undefined) {
        this.$message.warning('单位所属领域ID为空')
        return
      }
      let params = {
        sslyid: sslyid
      }
      return getDmbSslyDB(params).list_total[0]
    },
    // 通过ID获取单位类型
    getDwlxByDwlxId (dwlxid) {
      if (dwlxid === undefined) {
        this.$message.warning('单位类型ID为空')
        return
      }
      let params = {
        dwlxid: dwlxid
      }
      return getDmbDwlxDB(params).list_total[0]
    },
    // 通过ID获取所属领域
    // 通过ID获取所属层次
    // 获取单位信息
    getDwxx () {
      // localstore中获取登录单位的信息
      let loginUserDwxx = getDwxxListAll()[0]
      console.log('loginUserDwxx', loginUserDwxx, Object.prototype.toString.call(loginUserDwxx))
      if (!loginUserDwxx) {
        this.$message.error('单位信息获取失败')
        return
      }
      // try {
      //   if (Object.prototype.toString.call(loginUserDwxx) == '[object Object]') {
      //     // 什么也不用做
      //   } else {
      //     loginUserDwxx = JSON.parse(loginUserDwxx)
      //   }
      // } catch (error) {
      //   this.$message.error('单位信息获取解析失败')
      //   return
      // }
      // 获取单位类型名称
      let dwlxObj = this.getDwlxByDwlxId(loginUserDwxx.dwlx)
      if (dwlxObj) {
        loginUserDwxx.dwlxmc = dwlxObj.dwlxmc
      }
      // 获取所属领域名称
      let sslyObj = this.getSslyBySslyId(loginUserDwxx.ssly)
      console.log('sslyObj', sslyObj)
      if (sslyObj) {
        loginUserDwxx.sslymc = sslyObj.sslymc
      }
      // 获取所属层次名称
      let ssccObj = this.getSsccBySsccId(loginUserDwxx.sscc)
      if (ssccObj) {
        loginUserDwxx.ssccmc = ssccObj.ssccmc
      }
      this.setFlagDefault(loginUserDwxx)
      this.dwxx = loginUserDwxx
    },
    divMouseEnter (target) {
      if (this.dwxx[target] == 0) {
        this.dwxx[target] = 1
      }
      // console.log('divMouseEnter', this.dwxx[target])
    },
    divMouseLeave (target) {
      if (this.dwxx[target] == 1) {
        this.dwxx[target] = 0
      }
      // console.log('divMouseLeave', this.dwxx[target])
    },
    changeFlag (target) {
      this.dwxx[target] = 2
    },
    changeDwxx (target, field) {
      let dwid = this.dwxx.dwid
      console.log('this.dwxx', this.dwxx)
      if (!dwid) {
        this.$message.warning('单位ID为空')
        return
      }
      let params = {
        dwid: this.dwxx.dwid
      }
      // 省市区特殊处理
      if (target == 'ssqhEdit') {
        // 省
        params.province = this.dwxx.province
        // 市
        params.city = this.dwxx.city
        // 区
        params.district = this.dwxx.district
        // 区划
        params.regionalNumber = this.dwxx.regionalNumber
        // 数据校验
        if(!params.province || params.province == '') {
          this.$message.warning('单位所在省市区[省]未选择')
          return
        }
        if(!params.city || params.city == '') {
          this.$message.warning('单位所在省市区[市]未选择')
          return
        }
        if(!params.district || params.district == '') {
          this.$message.warning('单位所在省市区[区]未选择')
          return
        }
        if(!params.regionalNumber || params.regionalNumber == '') {
          this.$message.warning('单位所在省市区[区]未选择，所属区划为空')
          return
        }
      } else {
        params[field] = this.dwxx[field]
      }
      console.log('params', params)
      updateDwxx(params)
        // 写入日志
        let logParams = {
          xyybs: 'yybs_zcxx',
          ymngnmc: '注册信息维护',
          extraParams: params
        }
        writeSystemOptionsLog(logParams)
      this.dwxx[target] = 0
      // 刷新缓存数据
      this.refreshWindowLocation(params)
    },
    // 设置显隐控制标记默认值（防止undefined导致双向绑定失效）
    setFlagDefault (obj) {
      obj.dwmcEdit = 0
      obj.dwzchEdit = 0
      obj.xydmEdit = 0
      obj.dwlxmcEdit = 0
      obj.sslymcEdit = 0
      obj.ssccmcEdit = 0
      obj.dwlxrEdit = 0
      obj.dwlxdhEdit = 0
      obj.dwlxyxEdit = 0
      obj.ssqhEdit = 0
    },
    // 获取所有单位类型
    getAllDwlx () {
      this.dwlxList = getDmbDwlxDB().list_total
    },
    // 获取所有所属领域
    getAllSsly () {
      this.sslyList = getDmbSslyDB().list_total
    },
    // 获取所有所属层次
    getAllSscc () {
      this.ssccList = getDmbSsccDB().list_total
    },
    // 初始化省市区数据
    initSsq () {
      // 设置省市区初始化数据
      this.provinceList = address.filter(item => {
        return item.name == '黑龙江省'
      })
      this.cityList = address.filter(item => {
        return item.name == '黑龙江省'
      })[0].children
      this.districtList = this.cityList.filter(item => {
        return item.name == this.dwxx.city
      })[0].children
    },
    // 省改变事件
    provinceChanged(province) {
      // 重新初始化市
      this.cityList = address.filter(item => {
        return item.name == province
      })[0].children
      // 重置区
      this.districtList = []
      // 重置区划
      this.dwxx.regionalNumber = ''
      // 重置市区数据
      this.dwxx.city = ''
      this.dwxx.district = ''
    },
    // 市改变事件
    cityChanged(city) {
      // 重新初始化区
      this.districtList = this.cityList.filter(item => {
        return item.name == city
      })[0].children
      // 重置区划
      this.dwxx.regionalNumber = ''
      // 重置区数据
      this.dwxx.district = ''
    },
    // 区改变事件
    districtChanged(district) {
      this.dwxx.regionalNumber = this.districtList.filter(item => {
        return item.name == district
      })[0].code
    },
    // 刷新缓存数据
    refreshWindowLocation(params) {
      let localObj = getWindowLocation()
      console.log('localObj', localObj)
      Object.keys(params).forEach(item => {
        localObj[item] = params[item]
      })
      setWindowLocation(localObj)
    }
  },
  mounted () {
    // 获取单位信息
    this.getDwxx()
    //
    this.getAllDwlx()
    this.getAllSsly()
    this.getAllSscc()
    // 初始化省市区数据
    this.initSsq()
  }
}
</script>

<style scoped>
.out-card {
  /* margin-bottom: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04); */
  text-align: center;
}
/**单位信息区域**/
.out-card .out-card-div {
  font-size: 13px;
  padding: 5px 20px;
  width: 60%;
  margin: 0 auto;
}
.out-card .out-card-div > div {
  padding: 10px 5px;
  display: flex;
  cursor: pointer;
}
.out-card .dwxx div:hover {
  /* background: #f4f4f5; */
  /* background: rgba(255, 255, 255, 0.6); */
  border-radius: 8px;
}
.out-card .dwxx div label {
  /* background-color: red; */
  /* width: 125px; */
  width: 50%;
  height: 28px;
  line-height: 28px;
  display: inline-block;
  text-align: right;
  font-weight: 600;
  color: #909399;
}
.out-card .dwxx div span {
  /* background-color: rgb(33, 92, 79); */
  /* flex: 1; */
  display: inline-block;
  height: 28px;
  line-height: 28px;
}
.out-card .dwxx div .article {
  padding-left: 20px;
  display: inline-block;
}
.out-card .dwxx div i {
  height: 28px;
  line-height: 28px;
  margin-left: 10px;
  cursor: pointer;
  color: #409eff;
  font-size: 16px;
}
</style>