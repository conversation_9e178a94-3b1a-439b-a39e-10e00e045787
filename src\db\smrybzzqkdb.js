import db from "./adapter/zczpAdaptor";
// 日志工具类
import { writeTrajectoryLog } from '../utils/logUtils'

// 获取所有离岗离职信息(上报数据自选模式专用)
export const getAllSmryzzqkZxms = () => {
  return db.get('Smryzzqk_list').cloneDeep().value()
}

//保密制度-----------------------------------保密制度初始化列表********
export const getLglz = (params) => {
  let page = params.page;
  let pagesize = params.pageSize;
  let xm = params.xm;
  let bm
  if (typeof params.bm == 'object') {
    bm = params.bm.join('/')
  }
  let list_total = db
    .get("Smryzzqk_list")
    .sortBy("cjsj")
    .filter(function (item) {
      if ((xm === undefined || xm == '') && (bm === undefined || bm == '')) {
        return item
        console.log('查空', item)
      } else if (xm && (bm === undefined || bm == '')) {
        if (item.xm) {
          if (item.xm.indexOf(xm) != -1) {
            console.log('名称', item)
            return item
          }
        } else {
          console.log('item.xm', item.xm)
        }
      } else if ((xm === undefined || xm == '') && bm) {
        if (item.bm) {
          if (item.bm.indexOf(bm) != -1) {
            console.log('名称', item)
            return item
          }
        } else {
          console.log('item.bm', item.bm)
        }
      } else if (xm && bm) {
        if (item.xm && item.bm) {
          if (item.xm.indexOf(xm) != -1 && item.bm.indexOf(bm) != -1) {
            console.log('名称', item)
            return item
          }
        } else {
          console.log('item.xm', item.xm, 'item.bm', item.bm)
        }
      }
    })
    .cloneDeep()
    .value();
 
  // 手动分页
  console.log(pagesize * (page - 1), pagesize * (page - 1) + pagesize);
  let pageList = list_total.slice(
    pagesize * (page - 1),
    pagesize * (page - 1) + pagesize
  );

  let resList = {
    list: pageList,
    list_total: list_total,
    total: list_total.length,
  };
  console.log("保密制度", resList);
  return resList;
};
//保密制度-----------------------------------保密制度添加成员********
export const addZzqk = (params) => {
  let sjc = new Date().getTime();
  params.cjsj = sjc;
  params.gxsj = sjc;
  db.read().get("Smryzzqk_list").push(params).write();
  // 添加日志
  // let paramsLog = {
  //   xyybs: 'mk_smry',
  //   id: params.lglzid,
  //   ymngnmc: '出国（境）',
  //   extraParams: {
  //     xm: params.xm,
  //     sfzhm: params.sfzhm,
  //     bm: params.bm,
  //     gwmc: params.gwmc,
  //     smdj: params.smdj,
  //     cjsj: params.cjsj,
  //     fhsj: params.fhsj,
  //     bz: params.bz
  //   }
  // }
  // writeTrajectoryLog(paramsLog)
};
//保密制度-----------------------------------保密制度删除成员********
export const deleteSmrybzz = (params) => {
  /**
   * 使用该离岗离职人员的身份证号码修改其在岗涉密人员(Smry_list)在岗状态(zgzt)为可见
   * 在岗状态(zgzt): 0-删除 1-可见
  */
 if(!params) {
  console.log('出国出境撤销参数为空')
  return
 }
 let sfzhm = params.sfzhm
 let cgcjid = params.cgcjid
 // 参数校验
 if(!sfzhm) {
  console.log('出国出境撤销人员身份证号码为空')
  return
 }
 if(!cgcjid) {
  console.log('出国出境撤销失败，离岗离职ID为空')
  return
 }
 // 更新
 db.get('Smry_list').find({sfzhm: sfzhm}).assign({zgzt:1}).write()
 // 删除该离岗离职记录
 let delTargetObj = db.get('Smryzzqk_list').find({cgcjid:cgcjid}).cloneDeep().value()
  db.get("Smryzzqk_list").remove(delTargetObj).write()
};
export const deleteSmrybzzall = () => {
  db.get("Smryzzqk_list").remove().write()
}
export const updateLglz = (params) => {
  // let bm = params.bm.join('/')
  // params.bm = bm
  let cgcjid = params.cgcjid;
  console.log("lglzid", cgcjid);
  if (!cgcjid || cgcjid == "") {
    return;
  }
  params.gxsj = new Date().getTime();
  // 全字段更新方法(传进来的字段都会更新)
  db.read().get("Smryzzqk_list").find({ cgcjid: cgcjid }).assign(params).write();
};
