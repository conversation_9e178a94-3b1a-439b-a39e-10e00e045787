let CryptoJS = require('crypto-js')

// 加密
export const encryptAes = (data, keyStr) => {
  return CryptoJS.AES.encrypt(data, keyStr).toString()
}

// 解密
export const decryptAes = (data, keyStr) => {
  return CryptoJS.AES.decrypt(data, keyStr).toString(CryptoJS.enc.Utf8)
}

/**
 * 数据上报使用
 * AES CBC
 * iv偏移量： 1234567812345678
*/
const iv = '1234567812345678'

export const encryptAesCBC = (data, keyStr) => {
  let srcs = CryptoJS.enc.Utf8.parse(data)
  let encrypted = CryptoJS.AES.encrypt(srcs, CryptoJS.enc.Utf8.parse(keyStr), {
    iv: CryptoJS.enc.Utf8.parse(iv),
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  })
  return CryptoJS.enc.Base64.stringify(encrypted.ciphertext)
}

export const decryptAesCBC = (data, keyStr) => {
  let baseResult = CryptoJS.enc.Base64.parse(data)
  let ciphertext = CryptoJS.enc.Base64.stringify(baseResult)
  let decryptResult = CryptoJS.AES.decrypt(ciphertext, CryptoJS.enc.Utf8.parse(keyStr), {
    iv: CryptoJS.enc.Utf8.parse(iv),
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  })
  let resData = decryptResult.toString(CryptoJS.enc.Utf8).toString()
  return resData
}

/**
 * 会员机制使用
 * AES CBC
 * iv偏移量： hsoft3Banner2023
*/
const ivHy = 'hsoft3Banner2023'
// data  密钥  keyStr 单位注册号
export const encryptAesCBCHy = (data, keyStr) => {
  let srcs = CryptoJS.enc.Utf8.parse(data)
  let encrypted = CryptoJS.AES.encrypt(srcs, CryptoJS.enc.Utf8.parse(keyStr), {
    iv: CryptoJS.enc.Utf8.parse(ivHy),
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  })
  return CryptoJS.enc.Base64.stringify(encrypted.ciphertext)
}

export const decryptAesCBCHy = (data, keyStr) => {
  let baseResult = CryptoJS.enc.Base64.parse(data)
  let ciphertext = CryptoJS.enc.Base64.stringify(baseResult)
  let decryptResult = CryptoJS.AES.decrypt(ciphertext, CryptoJS.enc.Utf8.parse(keyStr), {
    iv: CryptoJS.enc.Utf8.parse(ivHy),
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  })
  try{
    let resData = decryptResult.toString(CryptoJS.enc.Utf8).toString()
    return JSON.parse(resData.substring(0, resData.lastIndexOf('}')+1))
  }catch (e) {
    console.log(e)
    console.log('dwzchError')
    return 'dwzchError'
  }
}
