<template>
  <div style="height: 100%;">
    <hsoft_top_title>
      <template #left>密码重置</template>
    </hsoft_top_title>
    <!---->
    <div class="article">
      <div>
        <el-form ref="form" :model="form" label-width="0px" size="mini" :label-position="'right'" :rules="rules">
          <el-form-item label="" prop="xm" style="position: relative;">
            <el-select v-model="form.yhid" @change="selectChanged" size="medium" style="width:60%;">
              <el-option v-for="(item, index) in form.yhList" :key="index" :label="item.xm" :value="item.yhid"></el-option>
            </el-select>
            <el-button type="primary" @click="updatePassword" size="medium">确认重置</el-button>
            <el-button type="warning" @click="$router.go(-1)" size="medium">返 回</el-button>
          </el-form-item>
        </el-form>
        <!-- <div style="display:flex;align-items: center;justify-content: flex-end;">
          <el-button type="primary" @click="updatePassword">确认重置</el-button>
          <el-button type="warning" @click="$router.go(-1)">返 回</el-button>
        </div> -->
        <div style="padding: 10% 0;">
          <el-result :icon="resultObj.resultIcon" :title="resultObj.resultTitle" subTitle="">
            <template slot="extra">
              <p v-show="resultObj.showResultPassword">
                <span style="color:#909399;">新密码为：</span>
                <span style="color: #67C23A;">csmm123456</span>
              </p>
            </template>
          </el-result>
        </div>
      </div>
    </div>
    <!---->
    <!---->
    <!---->
    <!---->
  </div>
</template>

<script>
import hsoft_top_title from '../../components/hsoft-top-title/hsoft-top-title.vue'

import MD5 from 'md5'

import { getWindowLocation, removeWindowLocation } from '../../../utils/windowLocation'

import { dateFormatChinese } from '../../../utils/moment'

import { writeSystemOptionsLog } from '../../../utils/logUtils'

import { selectYhByYhm, updateYhByYhid, selectYhNotAdmin } from '../../../db/yhdb'

export default {
  data () {

    // 旧密码验证
    const pwdOldCheck = async (rule, value, callback) => {
      // 验证旧密码与库里的密码是否相同
      if (value) {
        if (MD5(value) == this.form.password) {
          callback()
        } else {
          callback(new Error('请输入正确的旧密码'))
        }
      } else {
        callback(new Error('旧密码不能为空'))
      }
    }

    // 密码验证
    const pwdCheck = async (rule, value, callback) => {
      let reg = /^(?=.*d)(?=.*[a-z])(?=.*[A-Z])(?=.*[~@#$%*-+=:,\?[]{}]).{6,16}$/
      if (value) {
        if (value.length < 6) {
          this.changeFlag = 2;
          return callback(new Error('密码不能少于6位！'));
        } else if (value.length > 16) {
          this.changeFlag = 2;
          return callback(new Error('密码最长不能超过16位！'));
        } else {
          this.changeFlag = 1;
          callback()
        }
      } else {
        callback(new Error('新密码不能为空'))
      }
    }

    // 重复密码验证
    const pwdAgainCheck = async (rule, value, callback) => {
      if (value) {
        if (value.length < 1) {
          this.changeAgainFlag = 2;
          return callback(new Error('重复密码不能为空！'));
        } else if (this.form.dlmm != this.form.qrmm) {
          this.changeAgainFlag = 2;
          return callback(new Error('两次输入密码不一致！'));
        } else {
          this.changeAgainFlag = 1;
          callback()
        }
      } else {
        callback(new Error('请再次输入新密码'))
      }
    }

    return {
      resultObj: {
        resultIcon: 'warning',
        resultTitle: '您正在重置密码',
      },
      // 表单数据
      form: {
        xm: '',
        yhm: '',
        passwordOld: '',
        password: '',
        passwordCheck: '',
        yhList: []
      },
      //表单验证
      rules: {
        passwordOld: [{ required: true, validator: pwdOldCheck, trigger: 'blur' }],
        passwordNew: [{ required: true, validator: pwdCheck, trigger: 'blur' }],
        passwordCheck: [{ required: true, validator: pwdAgainCheck, trigger: 'blur' }],
      }
    }
  },
  components: {
    hsoft_top_title
  },
  methods: {
    selectChanged (val) {
      this.resultObj.resultIcon = 'warning'
      this.resultObj.resultTitle = '您正在重置密码'
      this.resultObj.showResultPassword = false
    },
    // 修改密码
    updatePassword () {
      console.log(this.$refs)
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.yhm == 'root') {
            this.$message.warning('超级管理员账号不能修改密码')
            return
          }
          let params = {
            yhid: this.form.yhid,
            // password: 'csmm123456'
            // 2023.0203 1114修改重置后的密码为 qwer1234
            password: 'qwer1234'
          }
          console.log('重置密码入参', params)
          let updateBool = updateYhByYhid(params)
          if (updateBool) {
            //
            this.resultObj.resultIcon = 'success'
            this.resultObj.resultTitle = '重置密码成功'
            this.resultObj.showResultPassword = true
            // 写入日志
            let logParams = {
              xyybs: 'yybs_mmcz',
              ymngnmc: '确认重置'
            }
            writeSystemOptionsLog(logParams)
            return
          }
          this.resultObj.resultIcon = 'error'
          this.resultObj.resultTitle = '重置密码失败'
          this.resultObj.showResultPassword = false
        }
      })
    },
    // 获取所有非admin用户信息集合
    getAllYhNotAdmin () {
      this.form.yhList = selectYhNotAdmin()
    }
  },
  mounted () {
    // // 5d93ceb70e2bf5daa84ec3d0cd2c731a
    // console.log('qwer1234', MD5('qwer1234'))
    // 获取所有非admin用户信息集合
    this.getAllYhNotAdmin()
  }
}
</script>

<style scoped>
.out-card {
  /* margin-bottom: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04); */
}
/**单位信息区域**/
.out-card .out-card-div {
  font-size: 13px;
  padding: 5px 20px;
}
.out-card .out-card-div div {
  padding: 10px 5px;
  display: flex;
}
.out-card .dwxx div:hover {
  background: #f4f4f5;
  border-radius: 20px;
}
.out-card .dwxx div label {
  /* background-color: red; */
  width: 125px;
  display: inline-block;
  text-align: right;
  font-weight: 600;
  color: #909399;
}
.out-card .dwxx div span {
  /* background-color: rgb(33, 92, 79); */
  flex: 1;
  display: inline-block;
  padding-left: 20px;
}
/****/
.article {
  text-align: center;
  /* background: red; */
  height: calc(100% - 32px);
  display: flex;
}
.article > div {
  width: 50%;
  height: 80%;
  margin: 0 auto;
  align-self: center;
}
</style>