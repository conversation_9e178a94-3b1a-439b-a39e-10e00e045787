import db from './adapter/zczpAdaptor'

// 获取所有dm责任人(上报数据自选模式专用)
export const getAllDmzrrZxms = () => {
  return db.get('dmzrr_list').cloneDeep().value()
}

//定密责任人-----------------------------------定密责任人初始化列表********
export const getdmzrr = (params) => {
  let page = params.page
  let pagesize = params.pageSize
  let xm = params.xm
  let dmqx = params.dmqx
  let nd = params.nd
  let list_total = db
    .get('dmzrr_list')
    .sortBy('cjsj')
    .filter(function (item) {
      return item
    })
    .cloneDeep()
    .value()

  // 模糊查询过滤
  if (xm) {
    list_total = list_total.filter((item) => {
      if (item.xm.indexOf(xm) != -1) {
        return item
      }
    })
  }
  if (dmqx) {
    list_total = list_total.filter((item) => {
      if (item.dmqx == dmqx) {
        return item
      }
    })
  }
  if (nd) {
    list_total = list_total.filter((item) => {
      if (item.nd == nd) {
        return item
      }
    })
  }

  // 手动分页
  console.log(pagesize * (page - 1), pagesize * (page - 1) + pagesize)
  let pageList = list_total.slice(
    pagesize * (page - 1),
    pagesize * (page - 1) + pagesize
  )

  let resList = {
    list: pageList,
    list_total: list_total,
    total: list_total.length,
  }
  console.log('定密责任人', resList)
  return resList
}
//定密责任人-----------------------------------定密责任人添加成员********
export const adddmzrr = (params) => {
  let sjc = new Date().getTime()
  params.cjsj = sjc
  params.gxsj = sjc
  db.read().get('dmzrr_list').push(params).write()
}
//定密责任人-----------------------------------定密责任人删除成员********
export const deletedmzrr = (params) => {
  db.read().get('dmzrr_list').remove(params).write()
}

//修改
export const revisedmzrr = (params) => {
  let dmzrrid = params.dmzrrid
  console.log('dmzrrid', dmzrrid)
  if (!dmzrrid || dmzrrid == '') {
    return
  }
  params.gxsj = new Date().getTime()
  // 全字段更新方法(传进来的字段都会更新)
  db.read().get('dmzrr_list').find({ dmzrrid: dmzrrid }).assign(params).write()
}
