<template>
  <div class="bg_con" style="height: calc(100% - 38px);">
    <div style="width: 100%; position: relative; overflow: hidden; height: 100%;">
      <!-- <div class="xmlb-title" style=" cursor: pointer;">
				<span style="font-size: 24px; cursor: pointer;">涉密人员岗位变更汇总情况</span>
				<span style="" @click="returnSy" class="fhsy">返回</span>
			</div> -->

      <div class="dabg" style="height: 100%;">
        <div class="content" style="height: 100%;">
          <div class="table" style="height: 100%;">
            <!-- -----------------操作区域--------------------------- -->
            <div class="mhcx">
              <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline" style="float:left">
                <el-form-item label="变更时间" style="font-weight: 700;">
                  <el-date-picker v-model="formInline.sgsj" type="daterange" range-separator="至"
                    start-placeholder="查询起始时间" end-placeholder="查询结束日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
                  </el-date-picker>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
                </el-form-item>

              </el-form>
              <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline" style="float:right">
                <el-form-item style="float: right;">
                  <el-button type="danger" size="medium" @click="shanchu" icon="el-icon-delete-solid">
                    删除
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <el-button type="primary" size="medium" icon="el-icon-download" @click="exportList()">导出
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <input type="file" ref="upload"
                    style="display: none;position: absolute;top:10px; right:0;opacity:0; cursor: pointer;height: 32px;width: 56px;z-index:1;"
                    accept=".xls,.xlsx">
                  <el-button type="primary" icon="el-icon-upload2" size="medium" @click="dr_dialog = true">
                    导入
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <el-button type="success" size="medium" @click="dialogVisible = true" icon="el-icon-plus">
                    岗位变更
                  </el-button>
                </el-form-item>

              </el-form>
            </div>

            <!-- -----------------审查组人员列表--------------------------- -->
            <div class="table_content_padding" style="height: 100%;">
              <div class="table_content" style="height: 100%;">
                <el-table :data="gwbgList" border @selection-change="selectRow"
                  :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
                  style="width: 100%;border:1px solid #EBEEF5;" height="calc(100% - 34px - 44px - 10px)" stripe>
                  <el-table-column type="selection" width="55" align="center"> </el-table-column>
                  <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                  <el-table-column prop="xm" label="姓名"></el-table-column>
                  <el-table-column prop="sfzhm" label="身份证号码"></el-table-column>
                  <el-table-column prop="bm" label="变更前部门"></el-table-column>
                  <el-table-column prop="gwmc" label="变更前涉密岗位"></el-table-column>
                  <el-table-column prop="smdj" label="变更前涉密等级"></el-table-column>
                  <el-table-column prop="bgbm" label="变更后部门"></el-table-column>
                  <el-table-column prop="bghgwmc" label="变更后涉密岗位"></el-table-column>
                  <el-table-column prop="bghsmdj" label="变更后涉密等级"></el-table-column>
                  <el-table-column prop="bgsj" label="变更时间"></el-table-column>
                  <!-- <el-table-column prop="bz" label="备注"></el-table-column> -->
                  <el-table-column prop="" label="操作" width="120">
                    <template slot-scope="scoped">
                      <el-button size="medium" type="text" @click="xqyl(scoped.row)">详情
                      </el-button>
                      <el-button size="medium" type="text" @click="updateItem(scoped.row)">修改
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <!-- -------------------------分页区域---------------------------- -->
                <div style="border: 1px solid #ebeef5;">
                  <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange"
                    :pager-count="5" :current-page="page" :page-sizes="[5, 10, 20, 30]" :page-size="pageSize"
                    layout="total, prev, pager, sizes,next, jumper" :total="total">
                  </el-pagination>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 模板下载 -->
        <el-dialog title="开始导入" class="scbg-dialog" width="600px" @close="mbxzgb" :visible.sync="dr_dialog" show-close>
          <div style="padding: 20px;">
            <div class="daochu">
              <div>一、请点击“导出模板”，并参照模板填写信息。</div>
              <el-button type="primary" size="mini" @click="mbdc">
                模板导出
              </el-button>
            </div>
            <div class="daochu">
              <div>二、数据导入方式：</div>
              <el-radio-group v-model="sjdrfs" @change="Radio($event)">
                <el-radio label="1">追加（导入时已有的记录信息不变，只添加新的记录）</el-radio>
                <el-radio label="2">覆盖（导入时更新已有的记录信息，并添加新的记录）</el-radio>
              </el-radio-group>
            </div>
            <div class="daochu">
              <div>三、将按模板填写的文件，导入到系统中。</div>
              <el-button type="primary" size="mini" @click="chooseFile">
                上传导入
              </el-button>
            </div>
          </div>
        </el-dialog>

        <!-- -----------------导入-弹窗--------------------------- -->
        <el-dialog width="1000px" height="800px" title="导入涉密人员岗位变更汇总情况" class="scbg-dialog"
          :visible.sync="dialogVisible_dr" show-close>
          <div style="height: 600px;">
            <el-table :data="dr_cyz_list" ref="multipleTable" @selection-change="handleSelectionChange"
              style="width: 100%;border:1px solid #EBEEF5;" height="100%" stripe>
              <el-table-column type="selection" width="55"> </el-table-column>
              <el-table-column prop="姓名" label="姓名"></el-table-column>
              <el-table-column prop="身份证号码" label="身份证号码"></el-table-column>
              <el-table-column prop="变更后涉密岗位" label="变更后涉密岗位"></el-table-column>
              <el-table-column prop="变更后涉密等级" label="变更后涉密等级"></el-table-column>
              <el-table-column prop="变更时间" label="变更时间"></el-table-column>
              <el-table-column prop="备注" label="备注">
              </el-table-column>
            </el-table>
          </div>

          <div style="height :30px;display: flex;align-items: center;justify-content: center;margin: 10px 0;">
            <el-button type="primary" @click="drcy" size="mini">导 入</el-button>
            <el-button type="warning" @click="dialogVisible_dr = false" size="mini">关 闭</el-button>
          </div>
        </el-dialog>

        <!-- -----------------涉密岗位-弹窗--------------------------- -->
        <el-dialog title="新增涉密人员岗位变更信息" :close-on-click-modal="false" :visible.sync="dialogVisible" width="50%" class="xg"
          :before-close="handleClose" @close="close('formName')">
          <el-form ref="formName" :model="tjlist" :rules="rules" label-width="150px" size="mini">
            <el-form-item label="姓名" prop="xm" class="one-line">
              <!-- <el-input placeholder="姓名" v-model="tjlist.xm" clearable></el-input> -->
              <el-autocomplete class="inline-input" value-key="xm" v-model.trim="tjlist.xm" @blur="dwxxByDwmc1(tjlist.xm)"
                :fetch-suggestions="querySearch" style="width: 100%;" placeholder="请输入姓名" @select="handleSelect">
              </el-autocomplete>
            </el-form-item>
            <el-form-item label="身份证号码" prop="sfzhm" class="one-line">
              <el-input oninput="value=value.replace(/[^\d.]/g,'')" @blur="sfzhm = $event.target.value"
                placeholder="身份证号码" v-model="tjlist.sfzhm" clearable disabled></el-input>
            </el-form-item>
            <el-form-item label="变更前部门" prop="bm" class="one-line">
              <el-input v-model="tjlist.bm" clearable placeholder="部门" disabled></el-input>
            </el-form-item>
            <el-form-item label="变更前岗位名称" prop="gwmc" class="one-line">
              <!-- <el-input v-model="tjlist.gwmc" clearable placeholder="变更前岗位名称" disabled></el-input> -->
              <el-select v-model="tjlist.gwmc" placeholder="变更前岗位名称" multiple disabled style="width:100%;">
                <el-option v-for="(item, label) in gwmc" :label="item.gwmc" :value="item.gwmc" :key="label">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="变更前涉密等级" prop="smdj" class="one-line">
              <el-select v-model="tjlist.smdj" placeholder="请选择变更前涉密等级" disabled style="width:100%;">
                <el-option v-for="item in smdjxz" :label="item.smdjmc" :value="item.smdjmc" :key="item.smdjid">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="变更时间" prop="bgsj" class="one-line">
              <!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
              <el-date-picker v-model="tjlist.bgsj" clearable type="date" placeholder="选择日期" style="width:100%;"
                format="yyyy年MM月dd日" value-format="yyyy年MM月dd日">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="变更后部门" prop="bgbm" class="one-line">
              <el-cascader v-model="tjlist.bgbm" :options="regionOption" :props="regionParams" filterable
                style="width:100%;" ref="cascaderArr" @change="handleChange(1)"></el-cascader>
            </el-form-item>
            <el-form-item label="变更后岗位名称" prop="bghgwmc" class="one-line">
              <!-- 带校验部门和岗位的 -->
              <!-- <el-select v-model="tjlist.bghgwmc" placeholder="请选择变更后岗位名称" style="width:100%;"
                @change="handleSelectBghgwmc(tjlist, $event)" multiple> -->
              <el-select v-model="tjlist.bghgwmc" placeholder="请选择变更后岗位名称" style="width:100%;" multiple>
                <el-option v-for="(item, label) in gwmc" :label="item.gwmc" :value="item.gwmc" :key="label">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="变更后涉密等级" prop="bghsmdj" class="one-line">
              <el-select v-model="tjlist.bghsmdj" placeholder="请选择变更后涉密等级" style="width:100%;">
                <el-option v-for="item in smdjxz" :label="item.smdjmc" :value="item.smdjmc" :key="item.smdjid">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="备注" prop="bz" class="one-line-textarea">
              <el-input type="textarea" v-model="tjlist.bz"></el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitTj('formName')">保 存</el-button>
            <el-button type="warning" @click="dialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>

        <el-dialog title="修改涉密人员岗位变更信息" :close-on-click-modal="false" :visible.sync="xgdialogVisible" width="47%" class="xg"
          @close="close1('form')">
          <el-form ref="form" :model="xglist" label-width="150px" size="mini">
            <el-form-item label="姓名" prop="xm" class="one-line">
              <el-input placeholder="姓名" v-model="xglist.xm" clearable></el-input>
            </el-form-item>
            <el-form-item label="身份证号码" prop="sfzhm" class="one-line">
              <el-input oninput="value=value.replace(/[^\d.]/g,'')" @blur="sfzhm = $event.target.value"
                placeholder="身份证号码" v-model="xglist.sfzhm" clearable></el-input>
            </el-form-item>
            <el-form-item label="变更前部门" prop="bm" class="one-line">
              <el-input v-model="xglist.bm" clearable placeholder="部门"></el-input>
            </el-form-item>
            <el-form-item label="变更前岗位名称" prop="gwmc" class="one-line">
              <el-input v-model="xglist.gwmc" clearable placeholder="变更前岗位名称"></el-input>
            </el-form-item>
            <el-form-item label="变更前涉密等级" prop="smdj" class="one-line">
              <el-select v-model="xglist.smdj" placeholder="请选择变更前涉密等级" style="width:100%;">
                <el-option v-for="item in smdjxz" :label="item.smdjmc" :value="item.smdjmc" :key="item.smdjid">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="变更时间" prop="bgsj" class="one-line">
              <el-date-picker v-model="xglist.bgsj" class="cd" clearable type="date" placeholder="选择日期"
                style="width:100%;" format="yyyy年MM月dd日" value-format="yyyy年MM月dd日">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="变更后部门" prop="bgbm" class="one-line">
              <el-input placeholder="变更后部门" v-model="xglist.bgbm" clearable></el-input>
            </el-form-item>
            <el-form-item label="变更后岗位名称" prop="bghgwmc" class="one-line">
              <el-input v-model="xglist.bghgwmc" clearable></el-input>
            </el-form-item>
            <el-form-item label="变更后涉密等级" prop="bghsmdj" class="one-line">
              <el-select v-model="xglist.bghsmdj" placeholder="请选择变更后涉密等级" style="width:100%;">
                <el-option v-for="item in smdjxz" :label="item.smdjmc" :value="item.smdjmc" :key="item.smdjid">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="备注" prop="bz" class="one-line-textarea">
              <el-input type="textarea" v-model="xglist.bz"></el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="updataDialog('form')">保 存</el-button>
            <el-button type="warning" @click="xgdialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>

        <el-dialog title="涉密人员岗位变更信息详情" :close-on-click-modal="false" :visible.sync="xqdialogVisible" width="50%"
          class="xg">
          <el-form ref="form" :model="xglist" label-width="150px" size="mini" disabled>
            <el-form-item label="姓名" prop="xm" class="one-line">
              <el-input placeholder="姓名" v-model="xglist.xm" clearable></el-input>
            </el-form-item>
            <el-form-item label="身份证号码" prop="sfzhm" class="one-line">
              <el-input oninput="value=value.replace(/[^\d.]/g,'')" @blur="sfzhm = $event.target.value"
                placeholder="身份证号码" v-model="xglist.sfzhm" clearable></el-input>
            </el-form-item>
            <el-form-item label="变更前部门" prop="bm" class="one-line">
              <el-input v-model="xglist.bm" clearable placeholder="部门"></el-input>
            </el-form-item>
            <el-form-item label="变更前岗位名称" prop="gwmc" class="one-line">
              <el-input v-model="xglist.gwmc" clearable placeholder="变更前岗位名称"></el-input>
            </el-form-item>
            <el-form-item label="变更前涉密等级" prop="smdj" class="one-line">
              <el-select v-model="xglist.smdj" placeholder="请选择变更前涉密等级" style="width:100%;">
                <el-option v-for="item in smdjxz" :label="item.smdjmc" :value="item.smdjmc" :key="item.smdjid">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="变更时间" prop="bgsj" class="one-line">
              <!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
              <el-date-picker v-model="xglist.bgsj" class="cd" clearable type="date" placeholder="选择日期"
                style="width:100%;" format="yyyy年MM月dd日" value-format="yyyy年MM月dd日">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="变更后部门" prop="bgbm" class="one-line">
              <el-input placeholder="变更后部门" v-model="xglist.bgbm" clearable></el-input>
            </el-form-item>
            <el-form-item label="变更后岗位名称" prop="bghgwmc" class="one-line">
              <el-input v-model="xglist.bghgwmc" clearable></el-input>

            </el-form-item>
            <el-form-item label="变更后涉密等级" prop="bghsmdj" class="one-line">
              <el-select v-model="xglist.bghsmdj" placeholder="请选择变更后涉密等级" style="width:100%;">
                <el-option v-for="item in smdjxz" :label="item.smdjmc" :value="item.smdjmc" :key="item.smdjid">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="备注" prop="bz" class="one-line-textarea">
              <el-input type="textarea" v-model="xglist.bz"></el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="warning" @click="xqdialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>
      </div>
    </div>
  </div>
</template>
<script>
import {
  getsmdj
} from "../../../db/xzdb"
import {
  //内容管理初mc始化成员列表
  getGwbg,
  //添加内容管理
  addGwbg,
  //删除内容管理
  deleteGwbg,
  //修改
  reviseGwbg
} from "../../../db/gwbgdb";
import {
  getlogin
} from "../../../db/loginyhdb";
import {
  getsmry1,
  getsmry2,
  getsmryxg
} from "../../../db/smrydb";
import {
  //涉密人员管理初始化成员列表
  getsmgw,
  getsmgw1,
  getsmgw2,
  getbmmc
} from "../../../db/smgwgldb";

import {
  exportExcel
} from "../../../utils/exportExcel"; //excel导出工具
import {
  dateFormatNYRChinese
} from "../../../utils/moment"

import {
  getUuid
} from "../../../utils/getUuid"; //获取uuid
import XLSX from "xlsx";
export default {
  components: {},
  props: {},
  data() {
    return {
      bghgwmc: '',
      gwbgjy: 0,
      smdjxz: [],
      gwbgList: [],
      gwmc: [],
      formInline: {

      },
      tjlist: {
        xm: '',
        sfzhm: '',
        xb: '',
        nl: '',
        bm: '',
        bgbm: '',
        gwmc: '',
        smdj: '',
        gwmch: '',
        smdjh: '',
        bgsj: '',
        bghgwmc: '',
        bghsmdj: '',
        gwqdyj: '',
        zgxl: '',
        zw: '',
        zj: '',
        zc: '',
        gwdyjb: '',
        sflx: '',
        yrxs: '',
        sfsc: '',
        crjdjba: '',
        tybgcrjzj: '',
        sgsj: '',
        bz: '',
      },
      page: 1,
      pageSize: 10,
      total: 0,
      selectlistRow: [], //列表的值
      dialogVisible: false, //添加弹窗状态
      //表单验证
      rules: {
        xm: [{
          required: true,
          message: '请输入姓名',
          trigger: ['blur', 'change'],
        },],
        sfzhm: [{
          required: true,
          message: '请输入身份证号码',
          trigger: 'blur'
        },
        {
          min: 18,
          max: 18,
          message: '长度为16个字符',
          trigger: 'blur'
        }
        ],
        xb: [{
          required: true,
          message: '请选择性别',
          trigger: 'blur'
        },],
        nl: [{
          required: true,
          message: '请输入年龄',
          trigger: 'blur'
        },],
        bm: [{
          required: true,
          message: '请输入部门',
          trigger: 'blur'
        },],
        bgbm: [{
          required: true,
          message: '请选择变更后部门',
          trigger: ['blur', 'change'],
        },],
        gwmc: [{
          required: true,
          message: '请输入岗位名称',
          trigger: 'blur'
        },],
        smdj: [{
          required: true,
          message: '请选择涉密等级',
          trigger: 'blur'
        },],
        bgsj: [{
          required: true,
          message: '请选择变更时间',
          trigger: 'blur'
        },],
        bghgwmc: [{
          required: true,
          message: '请输入变更后岗位名称',
          trigger: 'blur'
        },],
        bghsmdj: [{
          required: true,
          message: '请选择变更后涉密等级',
          trigger: 'blur'
        },],
        gwqdyj: [{
          required: true,
          message: '请选择岗位确定依据',
          trigger: 'blur'
        },],
        zgxl: [{
          required: true,
          message: '请选择最高学历',
          trigger: 'blur'
        },],
        zw: [{
          required: true,
          message: '请输入职务',
          trigger: 'blur'
        },],
        zj: [{
          required: true,
          message: '请输入职级',
          trigger: 'blur'
        },],
        zc: [{
          required: true,
          message: '请选择级别职称',
          trigger: 'blur'
        },],
        gwdyjb: [{
          required: true,
          message: '请选择岗位对应级别',
          trigger: 'blur'
        },],
        sflx: [{
          required: true,
          message: '请选择身份类型',
          trigger: 'blur'
        },],
        yrxs: [{
          required: true,
          message: '请选择用人形式',
          trigger: 'blur'
        },],
        sfsc: [{
          required: true,
          message: '请选择是否审查',
          trigger: 'blur'
        },],
        crjdjba: [{
          required: true,
          message: '请选择是否出入境登记备案',
          trigger: 'blur'
        },],
        tybgcrjzj: [{
          required: true,
          message: '请选择是否统一保管出入境证件',
          trigger: 'blur'
        },],
        sgsj: [{
          required: true,
          message: '请选择上岗时间（现涉密岗位）',
          trigger: 'blur'
        },],
        // bz: [{
        // 	required: true,
        // 	message: '请输入文件名',
        // 	trigger: 'blur'
        // },],
      },
      xglist: {},
      restaurants: [],
      restaurantsBghgwmc: [],
      updateItemOld: {},
      xgdialogVisible: false,
      xqdialogVisible: false,
      //导入
      dialogVisible_dr: false, //导入成员组弹窗状态
      dr_cyz_list: [], //待选择导入成员组列表
      multipleTable: [], //已选择导入成员组列表
      regionOption: [], //地域信息
      regionParams: {
        label: 'label', //这里可以配置你们后端返回的属性
        value: 'label',
        children: 'childrenRegionVo',
        expandTrigger: 'click',
        checkStrictly: true,
      }, //地域信息配置参数
      dwmc: '',
      year: '',
      yue: '',
      ri: '',
      Date: '',
      xh: [],
      dclist: [],
      dr_dialog: false,
      //数据导入方式
      sjdrfs: ''

    }
  },
  computed: {},
  mounted() {
    this.dwmc = getlogin()[0].dwmc
    let date = new Date()
    this.year = date.getFullYear() + '年'; //获取完整的年份(4位)
    this.yue = (date.getMonth() + 1) + '月'; //获取当前月份(0-11,0代表1月)
    this.ri = date.getDate() + '日'; //获取当前日(1-31)
    this.Date = this.year + this.yue + this.ri
    this.tjlist.bgsj = this.Date
    //列表初始化\
    this.smdjxz = getsmdj()
    this.smbm()
    this.smry()
    this.gwbg()
    let bmmc = getbmmc()
    let shu = []
    // console.log(bmmc);
    bmmc.forEach(item => {
      let childrenRegionVo = []
      bmmc.forEach(item1 => {
        if (item.bmm == item1.fbmm) {
          // console.log(item, item1);
          childrenRegionVo.push(item1)
          // console.log(childrenRegionVo);
          item.childrenRegionVo = childrenRegionVo
        }
      });
      // console.log(item);
      shu.push(item)
    })
    if (shu[0].childrenRegionVo) {
      shu[0].childrenRegionVo.forEach(item => {
      this.regionOption.push(item)
    })
    }
    this.$refs.upload.addEventListener('change', e => { //绑定监听表格导入事件
      this.readExcel(e);
    })
  },
  methods: {
    Radio(val) {
      this.sjdrfs = val
      console.log("当前选中的值", val)
    },
    mbxzgb() {
      this.sjdrfs = ''
    },
    mbdc() {
      console.log("----导出涉密人员----")
      // console.log(this.selectlistRow);
      // if (this.selectlistRow.length > 0) {
      let filename = "涉密人员岗位变更汇总情况模板" + getUuid() + ".xlsx"

      const {
        dialog
      } = require('electron').remote;
      //弹窗title
      let options = {
        title: "保存文件",
        defaultPath: filename,
      };
      console.log(dialog)
      //导出文件夹选择弹窗
      dialog.showSaveDialog(options, result => {
        console.log('result', result)
        if (result == null || result == "") {
          console.log("取消导出")
          return
        }
        let list = []

        //对导出的数据进行格式，js导出excel要求传入的参数格式为[["列1名称","列2名称","列3名称"],["列11","值21","值31"],["列12","值22","值32"]]
        list.push(["序号", "姓名", "身份证号码",
          "变更后涉密等级", "变更后涉密岗位",
          "变更日期", "备注"
        ]) //确定列名

        let styles = {
          // 列样式
          cols: {
            // 作用sheet页索引（0开始）（-1全sheet页生效）
            scoped: -1,
            style: [
              { wpx: 100 },
              { wpx: 100 },
              { wpx: 100 },
              { wpx: 100 },
              { wpx: 100 },
              { wpx: 100 },
              { wpx: 100 },
            ]
          },
          // 全局样式
          all: {
            alignment: {
              horizontal: 'center', // 水平居中
              vertical: 'center' // 垂直居中
            },
            font: {
              sz: 11, // 字号
              name: '宋体' // 字体
            },
            border: { // 边框
              top: {
                style: 'thin'
              },
              bottom: {
                style: 'thin'
              },
              left: {
                style: 'thin'
              },
              right: {
                style: 'thin'
              }
            }
          },
          // 单元格样式
          cell: []
        }

        exportExcel(result, list, undefined, styles) //list 要求为二维数组
        this.dr_dialog = false
        this.$message('导出成功:' + result)
      })
    },
    //导入
    chooseFile() {
      if (this.sjdrfs != '') {
        if (this.sjdrfs == 1) {
          this.$refs.upload.click()
        } else if (this.sjdrfs == 2) {
          let valArr = this.dclist
          valArr.forEach(function (item) {
            deleteGwbg(item)
          })
          this.$refs.upload.click()
        }
      } else {
        this.$message.warning('请选择导入方式')
      }
    },
    //----成员组选择
    handleSelectionChange(val) {
      this.multipleTable = val
      console.log("选中：", this.multipleTable);
    },
    //---确定导入成员组
    drcy() {
      //遍历已选择导入的成员，进行格式化，然后添加到数据库
      for (var i in this.multipleTable) {
        var cy = {
          xm: this.multipleTable[i]["姓名"],
          sfzhm: this.multipleTable[i]["身份证号码"],
          bghgwmc: this.multipleTable[i]["变更后涉密岗位"],
          bghsmdj: this.multipleTable[i]["变更后涉密等级"],
          bgsj: dateFormatNYRChinese(this.multipleTable[i]["变更时间"]),
          bz: this.multipleTable[i]["备注"],
          gwbgid: getUuid()
        }
        addGwbg(cy)
      }
      this.dialogVisible_dr = false
      this.gwbg()
    },
    //----表格导入方法
    readExcel(e) {
      var that = this;
      const files = e.target.files;
      console.log("files", files);
      var vali = /\.(xls|xlsx)$/
      if (files.length <= 0) { //如果没有文件名
        return false;
      } else if (!vali.test(files[0].name.toLowerCase())) {
        this.$Message.error('上传格式不正确，请上传xls或者xlsx格式');
        return false;
      }
      const fileReader = new FileReader();
      fileReader.onload = (e) => {
        try {
          const data = e.target.result;
          const workdata = XLSX.read(data, {
            type: 'binary',
            cellDates: true, //设为true，将天数的时间戳转为时间格式
          });
          console.log("文件的内容：", workdata) // 文件的内容
          //查看打印能看到 workdata的sheetNames里是文件中每个表的有值的集合；
          const wsname = workdata.SheetNames[0]; //取第一张表
          console.log('wsname', wsname)
          const ws = XLSX.utils.sheet_to_json(workdata.Sheets[wsname]); //生成json表格内容
          console.log(ws); //自第二行开始的内容
          this.dialogVisible_dr = true
          this.dr_cyz_list = ws
          console.log("列表的值:", this.dr_cyz_list)
          // 加工excel读取业务类型为数组
          // this.dr_cyz_list.forEach(function(item) {
          // 	console.log(item[0]['业务类型'].splite(','))
          // })
          this.$refs.upload.value = ''; // 处理完成 清空表单值
          this.dr_dialog = false
        } catch (e) {
          return false;
        }
      };
      fileReader.readAsBinaryString(files[0]);
    },
    //查询
    onSubmit() {
      this.gwbg()
    },
    updateItem(row) {
      this.updateItemOld = JSON.parse(JSON.stringify(row))
      this.xglist = JSON.parse(JSON.stringify(row))
      // this.gwmc = this.xglist.gwmc
      // this.form1.ywlx = row.ywlx
      console.log('old', row)
      console.log("this.xglist.ywlx", this.xglist);
      // this.xglist.bm = this.xglist.bm.split('/')
      this.xgdialogVisible = true
    },
    //修改
    updataDialog(form) {
      this.$refs[form].validate((valid) => {
        if (valid) { // 删除旧的
          /**
           * 修改前部门+岗位名称+涉密等级校验
           * 1、判断部门、岗位名称、涉密等级是否相同
           * 相同：直接修改
           * 不同：判断部门+岗位名称+涉密等级是否重复
           *    a、重复提示
           *    b、不重复修改
          */
          // let bm = this.xglist.bm
          // let gwmc = this.xglist.gwmc
          // let smdj = this.xglist.smdj
          // // 旧数据
          // let bmOld = this.updateItemOld.bm
          // let gwmcOld = this.updateItemOld.gwmc
          // let smdjOld = this.updateItemOld.smdj
          // console.log(bm, bmOld)
          // if (bmOld == bm.join('/') && gwmcOld == gwmc && smdjOld == smdj) {
          //   // '校验数据相同，直接执行修改'
          // } else {
          //   // '校验数据不同，执行重复验证，通过后修改'
          //   // this.onInputBlurXg(1)
          // }
          // if (this.pdgwbm == 0) {
          reviseGwbg(this.xglist)
          // 刷新页面表格数据
          this.gwbg()
          // 关闭dialog
          this.$message.success('修改成功')
          this.xgdialogVisible = false
          // } else {
          //   this.$message.error('本部门已包含本涉密等级对应岗位')
          // }
        } else {
          console.log('error submit!!');
          return false;
        }
      });

    },
    xqyl(row) {
      // this.updateItemOld = JSON.parse(JSON.stringify(row))
      this.xglist = JSON.parse(JSON.stringify(row))
      // this.form1.ywlx = row.ywlx
      console.log('old', row)
      console.log("this.xglist.ywlx", this.xglist);
      this.xqdialogVisible = true
    },
    returnSy() {
      this.$router.push("/tzglsy");
    },
    gwbg() {
      let params = {
        page: this.page,
        pageSize: this.pageSize
      }
      Object.assign(params, this.formInline)
      let resList = getGwbg(params)
      console.log("params", params);
      this.gwbgList = resList.list
      this.dclist = resList.list_total
      this.dclist.forEach((item, label) => {
        this.xh.push(label + 1)
      })
      this.total = resList.total
    },
    //删除
    shanchu(id) {
      if (this.selectlistRow != '') {
        this.$confirm('是否继续删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let valArr = this.selectlistRow
          // console.log("....", val);
          valArr.forEach(function (item) {
            deleteGwbg(item)
            console.log("删除：", item);
            console.log("删除：", item);
          })
          let params = valArr
          this.$message({
            message: '删除成功',
            type: 'success'
          });
          this.gwbg()
        }).catch(() => {
          this.$message('已取消删除')
        })
      } else {
        this.$message({
          message: '未选择删除记录，请选择下列列表',
          type: 'warning'
        });
      }
    },
    //添加
    showDialog() {
      this.dialogVisible = true
    },

    //导出
    exportList() {
      console.log("----导出涉密人员----")
      // console.log(this.selectlistRow);
      // if (this.selectlistRow.length > 0) {
      let filename = "涉密人员岗位变更汇总情况表" + getUuid() + ".xlsx"

      const {
        dialog
      } = require('electron').remote;
      //弹窗title
      let options = {
        title: "保存文件",
        defaultPath: filename,
      };
      console.log(dialog)
      //导出文件夹选择弹窗
      dialog.showSaveDialog(options, result => {
        console.log('result', result)
        if (result == null || result == "") {
          console.log("取消导出")
          return
        }
        let list = []
        list.push(["涉密人员岗位变更汇总表"])

        list.push(["上报单位:", this.dwmc, "", "", "", "", "", "", "", "", "", ""])
        list.push(["统计年度:", this.year, "", "", "", "", "", "", "", "",
          "填报时间:", this.Date
        ])
        //对导出的数据进行格式，js导出excel要求传入的参数格式为[["列1名称","列2名称","列3名称"],["列11","值21","值31"],["列12","值22","值32"]]
        list.push(["序号", "姓名", "性别", "身份证号码", "所在部门", "原涉密岗位",
          "原涉密等级", "变更后涉密岗位", "变更后涉密等级", "入职涉密岗位日期",
          "变更日期", "备注"
        ]) //确定列名

        for (var i in this.dclist) { //每一行的值
          let item = this.dclist[i] //{"name":"XXX","dw":"XXX","zw":"XXX"}

          console.log("导出值:", this.dclist);
          let column = [(parseInt(i) + 1), item["xm"], item["xb"], item["sfzhm"],
          item["bm"], item["gwmc"], item["smdj"],
          item["bghgwmc"], item["bghsmdj"], item["sgsj"],
          item["bgsj"], item["bz"]
          ]
          list.push(column)
        }
        let merges = [{
          s: { //s为开始
            c: 0, //开始列
            r: 0 //开始取值范围
          },
          e: { //e结束
            c: 11, //结束列
            r: 0 //结束范围
          }
        }]
        let styles = {
          // 列样式
          cols: {
            // 作用sheet页索引（0开始）（-1全sheet页生效）
            scoped: -1,
            style: [{
              wpx: 100
            },
            {
              wpx: 300
            },
            {
              wpx: 60
            },
            {
              wpx: 200
            },
            {
              wpx: 200
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 120
            },
            {
              wpx: 120
            },
            {
              wpx: 300
            },
            ]
          },
          // 全局样式
          all: {
            alignment: {
              horizontal: 'center', // 水平居中
              vertical: 'center' // 垂直居中
            },
            font: {
              sz: 11, // 字号
              name: '宋体' // 字体
            },
            border: { // 边框
              top: {
                style: 'thin'
              },
              bottom: {
                style: 'thin'
              },
              left: {
                style: 'thin'
              },
              right: {
                style: 'thin'
              }
            }
          },
          // 单元格样式
          cell: [{
            // 生效sheet页索引（值为 -1 时所有sheet页都生效）
            scoped: -1,
            // 索引
            index: 'A1',
            style: {
              font: {
                name: '宋体',
                sz: 16, // 字号
                bold: true,
              },
              alignment: {
                horizontal: 'center', // 水平居中
                vertical: 'center', // 垂直居中
                wrapText: true, // 文字换行
              }
            }
          }]
        }
        // excel导出方法自定义配置
        let config = {
          // 忽略行索引(从1开始)
          rowIndexArrIgnore: [1]
        }
        exportExcel(result, list, merges, styles, config) //list 要求为二维数组
        this.$message('导出成功:' + result)
      })
    },
    //确定添加成员组
    submitTj(formName) {

      //年
      let year = new Date().getFullYear();
      //月份是从0月开始获取的，所以要+1;
      let month = new Date().getMonth() + 1;
      //日
      let day = new Date().getDate();
      //时
      let hour = new Date().getHours();
      //分
      let minute = new Date().getMinutes();
      //秒
      let second = new Date().getSeconds();
      let bgsjtime = year + '年' + month + '月' + day + '日'
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let params = {
            smryid: this.tjlist.smryid,
            xm: this.tjlist.xm,
            sfzhm: this.tjlist.sfzhm,
            xb: this.tjlist.xb,
            nl: this.tjlist.nl,
            bm: this.tjlist.bm,
            bgbm: this.tjlist.bgbm.join('/'),
            gwmc: this.tjlist.gwmc,
            smdj: this.tjlist.smdj,
            jtbgsj: this.tjlist.bgsj,
            bghgwmc: this.tjlist.bghgwmc,
            bghsmdj: this.tjlist.bghsmdj,
            gwqdyj: this.tjlist.gwqdyj,
            zgxl: this.tjlist.zgxl,
            zw: this.tjlist.zw,
            zj: this.tjlist.zj,
            zc: this.tjlist.zc,
            sflx: this.tjlist.sflx,
            yrxs: this.tjlist.yrxs,
            sfsc: this.tjlist.sfsc,
            tybgcrjzj: this.tjlist.tybgcrjzj,
            sgsj: this.tjlist.sgsj,
            bz: this.tjlist.bz,
            bgsj: bgsjtime,
            gwbgid: getUuid()
          }
          // if (this.gwbgjy == 0) {
          addGwbg(params)
          getsmryxg(params)
          this.dialogVisible = false
          this.$message({
            message: '添加成功',
            type: 'success'
          });
          this.resetForm()
          this.gwbg()
          this.smry()
          // } else {
          //   this.$message.error('变更后岗位与原岗位一致')
          // }

        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    deleteTkglBtn() {

    },
    selectRow(val) {
      console.log(val);
      this.selectlistRow = val;
    },
    //列表分页--跳转页数
    handleCurrentChange(val) {
      this.page = val
      this.gwbg()
    },
    //列表分页--更改每页显示个数
    handleSizeChange(val) {
      this.page = 1
      this.pageSize = val
      this.gwbg()
    },
    //添加重置
    resetForm() {
      this.tjlist.xm = '',
        this.tjlist.sfzhm = '',
        this.tjlist.xb = '',
        this.tjlist.nl = '',
        this.tjlist.bm = '',
        this.tjlist.gwmc = '',
        this.tjlist.smdj = '',
        this.tjlist.bgsj = this.Date,
        this.tjlist.bghgwmc = '',
        this.tjlist.bghsmdj = '',
        this.tjlist.gwqdyj = '',
        this.tjlist.zgxl = '',
        this.tjlist.zw = '',
        this.tjlist.zj = '',
        this.tjlist.zc = '',
        this.tjlist.sflx = '',
        this.tjlist.yrxs = '',
        this.tjlist.sfsc = '',
        this.tjlist.crjdjba = '',
        this.tjlist.tybgcrjzj = '',
        this.tjlist.sgsj = '',
        this.tjlist.bz = ''
    },
    handleClose(done) {
      this.resetForm()
      this.dialogVisible = false
    },
    // 弹框关闭触发
    close(formName) {
      // 清空表单校验，避免再次进来会出现上次校验的记录
      this.$refs[formName].resetFields();
    },
    close1(form) {
      // 清空表单校验，避免再次进来会出现上次校验的记录
      this.$refs[form].resetFields();
    },
    querySearch(queryString, cb) {
      var restaurants = this.restaurants;
      console.log("restaurants", restaurants);
      var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
      console.log("results", results);
      // 调用 callback 返回建议列表的数据

      cb(results);
      console.log("cb(results.dwmc)", results);
    },
    createFilter(queryString) {
      return (restaurant) => {
        return (restaurant.xm.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
      };
    },
    smry() {
      let resList = getsmry1()
      this.restaurants = resList;
      console.log("this.restaurants", this.restaurants);
      console.log(resList)
    },
    handleSelect(item) {
      // console.log(item);
      // 不能用
      // this.dwxxShow = JSON.parse(JSON.stringify(item))
      this.tjlist.xm = item.xm
      this.tjlist.sfzhm = item.sfzhm
      this.tjlist.xb = item.xb
      this.tjlist.nl = item.nl
      this.tjlist.bm = item.bm
      this.tjlist.gwmc = item.gwmc
      this.tjlist.smdj = item.smdj
      this.tjlist.gwqdyj = item.gwqdyj
      this.tjlist.zgxl = item.zgxl
      this.tjlist.zw = item.zw
      this.tjlist.zj = item.zj
      this.tjlist.zc = item.zc
      this.tjlist.sflx = item.sflx
      this.tjlist.yrxs = item.yrxs
      this.tjlist.sfsc = item.sfsc
      this.tjlist.crjdjba = item.crjdjba
      this.tjlist.tybgcrjzj = item.tybgcrjzj
      this.tjlist.sgsj = item.sgsj
      this.tjlist.smryid = item.smryid
      // console.log("this.dwxxShow",this.dwxxShow);
      console.log("item", item);
    },
    dwxxByDwmc1(xm) {
      //单位名称imput失去焦点，通过单位名称获取单位信息
      console.log('单位名称input失去焦点:' + xm);
      const smryDataBase = getsmry2(xm);
      console.log('通过单位名称获取单位信息:');
      console.log(smryDataBase);
      if (smryDataBase === undefined) {
        /**
         * 去掉实际操作表单数据dwxxShow里的单位id
         * 因为这里显示的是数据库里的数据，但是当用户改变了该表单里的单位名称数据后，此时应该把实际操作的单位id dwidPage置为空
         */
        this.dwidPage = '';
        return;
      }
      /**
       * 有旧的数据
       * 直接给到页面显示用的数据dwxxShow
       */
      // this.dwxxShow = smryDataBase;
      //把用户实际操作的表单信息中的dwid设置为数据库里的数据
      // this.dwidPage = smryDataBase.dwid;
    },
    querySearchBghgwmc(queryString, cb) {
      var restaurants = this.restaurantsBghgwmc;
      console.log("restaurants", restaurants);
      var results = queryString ? restaurants.filter(this.createFilterBghgwmc(queryString)) : restaurants;
      console.log("results", results);
      // 调用 callback 返回建议列表的数据

      cb(results);
      console.log("cb(results.dwmc)", results);
    },
    createFilterBghgwmc(queryString) {
      return (restaurant) => {
        return (restaurant.gwmc.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
      };
    },
    smbm() {
      let resList = getsmgw1()
      this.restaurantsbm = resList;
      console.log("this.restaurants", this.restaurantsbm);
      console.log(resList)
    },

    handleSelectBghgwmc(item, i) {
      let dx = []
      let hx = []
      let zy = []
      let yb = []
      i.forEach(item => {
        this.gwmc.forEach(item1 => {
          if (item == item1.gwmc) {
            dx.push(item1)
          }
        })
      })
      console.log(dx);
      dx.forEach(item => {
        if (item.smdj == '核心') {
          hx.push(item)
        } else if (item.smdj == '重要') {
          zy.push(item)
        } else {
          yb.push(item)
        }
      })
      if (hx.length > 0) {
        this.tjlist.bghsmdj = hx[0].smdj
      } else if (zy.length > 0) {
        this.tjlist.bghsmdj = zy[0].smdj
      } else if (yb.length > 0) {
        this.tjlist.bghsmdj = yb[0].smdj
      }

      this.gwmc.forEach(item1 => {
        if (i == item1.gwmc) {
          this.tjlist.bghsmdj = item1.smdj
          // this.tjlist.gwqdyj = item1.gwqdyj
        }
      })
      // this.tjlist = item
      // 不能用
      // this.dwxxShow = JSON.parse(JSON.stringify(item))
      // this.tjlist.bghgwmc = item.gwmc
      console.log(i);
      console.log(this.tjlist.gwmc)
      this.tjlist.bghsmdj = item.bghsmdj
      if (this.tjlist.bm === item.bgbm.join('/')) {
        if (this.tjlist.gwmc.length === i.length && i.sort().toString() === this.tjlist.gwmc.sort().toString()) {
          console.log(1);
          this.$message.error('变更后岗位与原岗位一致')
          this.gwbgjy = 1
        } else {
          this.gwbgjy = 0
        }
      } else {
        this.gwbgjy = 0
      }
      // console.log("this.dwxxShow",this.dwxxShow);
    },
    dwxxByDwmc2(xm) {
      //单位名称imput失去焦点，通过单位名称获取单位信息
      console.log('单位名称input失去焦点:' + xm);
      const smgwDataBase = getsmgw2(xm);
      console.log('通过单位名称获取单位信息:');
      console.log(smgwDataBase);
      if (smgwDataBase === undefined) {
        /**
         * 去掉实际操作表单数据dwxxShow里的单位id
         * 因为这里显示的是数据库里的数据，但是当用户改变了该表单里的单位名称数据后，此时应该把实际操作的单位id dwidPage置为空
         */
        this.dwidPage = '';
        return;
      }
      /**
       * 有旧的数据
       * 直接给到页面显示用的数据dwxxShow
       */
      // this.dwxxShow = smryDataBase;
      //把用户实际操作的表单信息中的dwid设置为数据库里的数据
      // this.dwidPage = smryDataBase.dwid;

    },

    querySearch1(queryString, cb) {
      var restaurants = this.restaurantsbm;
      console.log("restaurants", restaurants);
      var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
      console.log("results", results);
      let results1
      for (var i = 0; i < results.length; i++) {
        for (var j = i + 1; j < results.length; j++) {
          if (results[i].bm === results[j].bm) {
            results.splice(j, 1);
            j--;
          }
        }
      }
      // 调用 callback 返回建议列表的数据
      cb(results);
      console.log("cb(results.dwmc)", results);
    },
    bmxzsj(item) {
      console.log(item.bm);
      let resList = getsmgw(item.bm)
      this.restaurantsBghgwmc = resList;
      console.log("this.restaurants", this.restaurantsBghgwmc);
      console.log(resList)
      this.tjlist.bghgwmc = ''
      this.tjlist.bghsmdj = ''
    },
    handleChange(index) {
      this.gwbgjy = 0
      let resList
      if (index == 1) {
        resList = getsmgw(this.tjlist.bgbm.join('/'))
      } else if (index == 2) (
        resList = getsmgw(this.xglist.bm.join('/'))
      )
      this.restaurantsBghgwmc = resList;
      this.gwmc = resList
      if (this.gwmc.length == 0) {
        this.$message.error('该部门没有添加岗位');
      }
      console.log("this.restaurants", this.restaurantsBghgwmc);
      console.log(resList)
      this.tjlist.bghgwmc = ''
      this.tjlist.bghsmdj = ''
    },
    bghbm() {
      console.log(1);
      this.smbm()
    }
  },
  watch: {

  }
}
</script>

<style scoped>
.bg_con {
  width: 100%;
}

.dabg {
  /* margin-top: 10px; */
  box-shadow: 0px 2px 12px 0px rgba(9, 70, 255, 0.13);
  border-radius: 8px;
  width: 100%;
}

.xmlb-title {
  line-height: 60px;
  width: 100%;
  padding-left: 10px;
  height: 60px;
  background: url(../../assets/background/bg-02.png) no-repeat left;
  background-size: 100% 100%;
  text-indent: 10px;
  /* margin: 0 20px; */
  color: #0646bf;
  font-weight: 700;
}

.fhsy {
  display: inline-block;
  width: 120px;
  margin-top: 10px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-left: 30px;
  padding-top: 4px;
  float: right;
  background: url(../../assets/icons/zzjg_icon1.png) no-repeat center;
  background-size: 100% 100%;
}

.daochu {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

/* /deep/.el-radio {
	display: block;
	margin-top: 10px;
	margin-bottom: 10px;
} */

.item_button {
  height: 100%;
  float: left;
  padding-left: 10px;
  line-height: 50px;
}

.select_wrap {
  /* //padding: 5px; */

  .select_wrap_content {
    float: left;
    width: 100%;
    line-height: 50px;
    /* // padding-left: 20px; */
    /* // padding-right: 20px; */
    height: 100%;
    background: rgba(255, 255, 255, 0.7);

    .item_label {
      padding-left: 10px;
      height: 100%;
      float: left;
      line-height: 50px;
      font-size: 1em;
    }
  }
}

.mhcx1 {
  margin-top: 0px;
}

/deep/.inline-input .el-input--medium .el-input__inner {
  width: 556px;
  height: 28px;
  font-size: 12px;
}

/deep/.el-radio-group {
  width: 184px;
}

/deep/.el-dialog {
  margin-top: 3vh !important;
}

/* /deep/.el-select .el-input__inner{
	width: 587.96px;
} */

.cd {
  width: 184px;
}

/* /deep/.el-form-item__label {
	text-align: left;
} */

/deep/.mhcx .el-form-item {
  margin-top: 5px;
  margin-bottom: 5px;
}

/deep/.el-select .el-select__tags>span {
  display: flex !important;
  flex-wrap: wrap;
}

/deep/.el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
  margin-bottom: 0px;
}

.bz {
  height: 72px !important;
}

.dialog-footer {
  display: block;
  margin-top: 10px;
}

/deep/.el-dialog__body .el-form>div>div {
  width: auto;
  max-width: 100%;
}

/deep/.el-dialog__body .el-form>div .el-form-item__label {
  width: 155px !important;
}
</style>