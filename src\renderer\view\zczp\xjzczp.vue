<template>
  <div style="width: 100%;height: calc(100% - 32px);">
    <!---->
    <div class="mhcx">
      <el-form :inline="true" size="medium" class="demo-form-inline" style="float:left">
        <el-form-item style="float: right;">
          <div>当前审查任务：{{dialogObj.rwmc}}</div>
        </el-form-item>
      </el-form>
      <!---->
      <el-form :inline="true" size="medium" class="demo-form-inline" style="float:right">
        <el-form-item style="float: right;">
          <el-button type="primary" size="medium" icon="el-icon-document-add" @click="saveToNext">
            保存至下一步
          </el-button>
        </el-form-item>
        <el-form-item style="float: right;">
          <el-button type="warning" size="medium" @click="save" icon="el-icon-document">临时保存
          </el-button>
        </el-form-item>
        <el-form-item style="float: right;">
          <el-button type="info" size="medium" @click="newScrw" icon="el-icon-document">新建审查任务
          </el-button>
        </el-form-item>
      </el-form>
      <div style="clear: both;"></div>
    </div>
    <!---->
    <div class="div-table">
      <el-card v-for="(dx, index) in showDxList" :key="index" :id="'table' + index" style="margin-bottom: 1em">
        <div slot="header" class="clearfix">
          {{ dx.dxmc }}（<span>{{ dxdf(dx) }}</span>分）
        </div>
        <el-table :data="dx.xx" :span-method="objectSpanMethod" :header-cell-style="{ 'text-align': 'center' }" border>
          <el-table-column prop="xxmc" label="检查项" width="80"></el-table-column>
          <el-table-column label="检查内容">
            <template slot-scope="scope">
              <div>
                <span :id="dx.xx[scope.$index].mdIndex"></span>{{ dx.xx[scope.$index].nr }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="扣分" width="150" align="center">
            <template slot-scope="scope">
              <div>
                <!--计数器扣分-->
                <div v-if="dx.xx[scope.$index].kffs == 6">
                  <el-checkbox-group v-model="dx.xx[scope.$index].check">
                    <el-checkbox label="" name="checkbox" style="margin-right: 0.5em">
                    </el-checkbox>
                    <el-input-number v-model="dx.xx[scope.$index].jsqkf" :min="dx.xx[scope.$index].zdkffz" :max="dx.xx[scope.$index].zgkffz" :step="dx.xx[scope.$index].kfzf" size="mini" style="width: 100px" @change="handleKfjsq(dx.xx[scope.$index])"></el-input-number>
                  </el-checkbox-group>
                </div>
                <!--固定扣分-->
                <div v-if="dx.xx[scope.$index].kffs == 7">
                  <el-checkbox-group v-model="dx.xx[scope.$index].check">
                    <!-- gdkffz:{{dx.xx[scope.$index].gdkffz}} -->
                    <el-checkbox label="" name="checkbox">{{
                    dx.xx[scope.$index].gdkffz
                  }}</el-checkbox>
                  </el-checkbox-group>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="fz" label="分值" width="50" align="center"></el-table-column>
          <el-table-column label="实有项目" width="80" align="center">
            <template slot-scope="scope">
              <el-checkbox-group v-model="dx.xx[scope.$index].sfsynr">
                <el-checkbox label="是" name="checkboxSynr"></el-checkbox>
              </el-checkbox-group>
            </template>
          </el-table-column>
          <el-table-column prop="" label="实有内容">
            <template slot-scope="scope">
              <el-input type="textarea" v-model.trim="dx.xx[scope.$index].synr" :rows="3" @input="handleSynrTextarea(dx.xx[scope.$index])"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="得分" width="50" align="center">
            <template slot-scope="scope">
              <!-- <div>{{(dx.xx[scope.$index].fz-dx.xx[scope.$index].ykf)>0?(dx.xx[scope.$index].fz-dx.xx[scope.$index].ykf):0}}</div> -->
              <div>{{ dx.xx[scope.$index].df }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="kfbz" label="扣分标准"></el-table-column>
          <el-table-column label="评分说明">
            <template slot-scope="scope">
              <el-input type="textarea" v-model.trim="dx.xx[scope.$index].kfsm" :rows="3" @input="handleKfsmTextarea(dx.xx[scope.$index])"></el-input>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
    <!---->
    <el-card id="sslist" style="margin-bottom: 5px;">
      <span class="sslist_class">实有项目总分：<span style="color: red">{{ syxmzf }}</span></span>
      <span class="sslist_class">实有项目得分：<span style="color: red">{{ syxmdf }}</span></span>
      <span class="sslist_class">实有项目得分占实有项目总分百分比：<span style="color: red">{{ syxmbfb }}%</span></span>
    </el-card>
    <el-card id="sslist">
      <span style="color: red; display: block; width: 100%; font-weight: bold">备注：</span>
      <ol style="margin-left: 2em; list-style: disc">
        <li style="list-style: disc">
          1.单项检查内容存在多起不符合要求行为的，每个单项的总扣分最高不超过该项的总分值。
        </li>
        <li style="list-style: disc">
          2.实行100分评分制,得分为实有项目得分与实有项目总分比值的百分制得分。实有项目总分为实有检查内容各项分值之和，实有项目得分为实有项目总分扣除自查发现问题分值之后的得分。
        </li>
      </ol>
    </el-card>
    <!-- 创建自查自评任务dialog -->
    <el-dialog title="新建自查自评任务" :visible.sync="dialogVisibleXjzczpRw" width="35%">
      <el-form ref="formName" :model="dialogObj" label-width="120px" size="mini">
        <div style="display:flex">
          <el-form-item label="任务名称" class="one-line">
            <el-input v-model="dialogObj.rwmc" clearable placeholder="任务名称"></el-input>
          </el-form-item>
        </div>
        <div style="display:flex">
          <el-form-item label="请选择检查单位名称" class="one-line">
            <el-select v-model="dialogObj.dwid" placeholder="请选择检查单位名称" style="width: 100%;">
              <el-option v-for="(item,index) in dialogObj.dwxxList" :key="index" :label="item.dwmc" :value="item.dwid">
              </el-option>
            </el-select>
          </el-form-item>
        </div>
        <div style="display:flex">
          <el-form-item label="请选择检查季度" class="one-line">
            <el-select v-model="dialogObj.jcjdid" placeholder="请选择检查季度" @change="handleJcjdChanged" style="width: 100%;">
              <el-option v-for="(item,index) in jcjdList" :key="index" :label="item.jcjdmc" :value="item.jcjdid">
              </el-option>
            </el-select>
          </el-form-item>
        </div>
      </el-form>
      <!-- <pre style="font-size: 12px;">{{dialogObj}}</pre>
      <div style="font-size: 12px;">{{aaa}}</div> -->
      <!-- <div>
        <div class="form-out">
          <label class="form-label">任务名称</label>
          <el-input v-model="dialogObj.rwmc" clearable placeholder="任务名称" style="flex: 1;"></el-input>
        </div>
        <div class="form-out">
          <label class="form-label">检查单位</label>
          <el-select v-model="dialogObj.dwid" placeholder="请选择检查单位名称" style="flex: 1;">
            <el-option v-for="(item,index) in dialogObj.dwxxList" :key="index" :label="item.dwmc" :value="item.dwid">
            </el-option>
          </el-select>
        </div>
        <div class="form-out">
          <label class="form-label">检查时间</label>
          <el-select v-model="dialogObj.jcjdid" placeholder="请选择检查季度" style="flex: 1;">
            <el-option v-for="(item,index) in jcjdList" :key="index" :label="item.jcjdmc" :value="item.jcjdid">
            </el-option>
          </el-select>
        </div>
      </div> -->
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" size="medium" @click="createZczpRw()">创 建</el-button>
        <el-button size="medium" @click="closeXjzczpDialog()">取 消</el-button>
      </span>
    </el-dialog>
    <!--锚点索引-->
    <div class="md-menu" @mouseover="mouseoverMdMenu" @mouseout="mouseoutMenu">
      <div class="md-left"></div>
      <transition name="el-fade-in-linear">
        <div v-show="showMdmenu" class="md-right">
          <div class="md-article">
            <el-timeline :reverse="reverse">
              <el-timeline-item v-for="(item, index) in activities" :key="index">
                <div>
                  <h4>{{ item.dxmc }}</h4>
                  <div v-for="(xxItem, xxIndex) in item.children" :key="xxIndex" class="md-article-article">
                    <span v-if="xxItem.xxmc" style="color:#409EFF;">【{{ xxItem.xxmc }}】</span>
                    <a :href="xxItem.href">
                      <span>{{ xxItem.nr }}</span>
                    </a>
                    <span v-if="xxItem.ykf" style="color:#F56C6C;">扣{{ xxItem.ykf }}分<span class="el-icon-caret-right"></span></span>
                  </div>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </div>
      </transition>
      <div class="md-right-margin-div"></div>
    </div>
    <!---->
  </div>
</template>

<script>

import { checkObjIsBlank, machineSendParams, generatorCurrentYearQuarter } from '../../../utils/utils'

import { getWindowLocation, setZczpIdsObj, getZczpIdsObj, initZczpIdsObj } from '../../../utils/windowLocation'

import { writeOptionsLog } from '../../../utils/logUtils'

import { getDwxxListAll } from '../../../db/dwxxDb'

import {
  //获取单位检查字典
  getDwxxzcjlZD,
  // 新建审查任务
  insertScrwList,
  // 更新审查任务
  updateScrwListZt,
  // 插入或更新单位评分记录表
  insertUpdateDwpfjlListByRwid,
  // 通过任务ID获取单位评分记录
  selectDwpfjlListByRwid,
  // 通过任务ID获取审查任务历史信息
  selectScrwByRwid
} from '../../../db/zczpdb'

export default {
  data () {
    return {
      // 新建自查自评任务 dialog
      dialogVisibleXjzczpRw: false,
      dialogObj: {},
      // dialogObj: {
      //   rwmc: '自查任务20221105.1915',
      //   dwid: 'D66215EC-1B9A-408A-BEE8-7A9396165EA7'
      // },
      // 当前年份的检查季度集合
      jcjdList: [],
      ////////////////////
      //页面实际操作的评分数据[dx:{scnr:[]}]
      //各大项得分总和array（实时变化）
      dxdfArr: [],
      //table实际操作的检查评分数据
      showDxList: [],
      //单元格合并规则
      spanArr: [],
      // 时间线排序方向
      reverse: true,
      // 锚点菜单集合
      activities: [],
      // 锚点菜单显隐
      showMdmenu: false,
      aaa: ''
    }
  },
  computed: {
    //实有项目总分
    syxmzf () {
      let zf = 0;
      this.showDxList.forEach((dx) => {
        dx.xx.forEach((xx) => {
          if (xx.sfsynr) {
            zf += xx.fz;
          }
        });
      });
      return zf;
    },
    //实有项目得分
    syxmdf () {
      let df = 0;
      this.showDxList.forEach((dx) => {
        dx.xx.forEach((xx) => {
          if (xx.sfsynr) {
            df += xx.fz - xx.ykf;
          }
        });
      });
      return df;
    },
    //计算实有项目得分百分比
    syxmbfb () {
      // console.log(this.syxmdf);
      // console.log(this.syxmzf);
      if (this.syxmzf == 0) {
        return 0;
      }
      // return (this.syxmdf / this.syxmzf).toFixed(3) * 100;
      let num = this.syxmdf / this.syxmzf;
      return Math.round(num * 1000) / 10;
    },
    //计算大项得分
    dxdf () {
      return function (dx) {
        let resDf = 0
        dx.xx.forEach((xx) => {
          resDf += xx.fz - xx.ykf
        })
        return resDf
      }
    }
  },
  components: {
  },
  methods: {
    // 获取当前年份的检查季度表集合
    getJcjdList () {
      //   let params = {
      //     ssnf: new Date().getFullYear()
      //   }
      //   let jcjdList = selectJcjdList(params)
      //   // 判断当前应选中那个季度
      //   let currentTime = new Date().getTime()
      //   jcjdList.some(item => {
      //     // console.log(item)
      //     if (item.kssj <= currentTime && item.jssj >= currentTime) {
      //       this.dialogObj.jcjdid = item.jcjdid
      //       return true
      //     }
      //   })
      //   //
      //   this.jcjdList = jcjdList
      let quarterObj = generatorCurrentYearQuarter()
      console.log('quarterObj', quarterObj)
      this.dialogObj.jcjdid = quarterObj.currentQuarter
      this.jcjdList = quarterObj.resList
    },
    // 获取单位信息集合（默认选中最后一个，也就是最新的单位信息）
    getDwxxList () {
      const dwxxListDb = getDwxxListAll()
      // console.log('zczp dwxxListDb', dwxxListDb)
      if (dwxxListDb) {
        this.dialogObj.dwxxList = dwxxListDb
        // 默认选中最后一个，也就是最新的单位信息
        this.dialogObj.dwid = dwxxListDb[dwxxListDb.length - 1].dwid
      }
    },
    // 关闭新建自查自评dialog
    closeXjzczpDialog () {
      // 通知tags组件关闭新建zczp tag
      this.dialogVisibleXjzczpRw = false
    },
    // 新建自查自评任务
    async createZczpRw () {
      if (!this.checkXjzczpRwParams()) {
        this.$message.warning('请先填写检查任务名称及选择本次检查单位')
        this.dialogVisibleXjzczpRw = true
        return
      }
      let params = this.dialogObj
      // 任务开始时间
      params.kssj = new Date().getTime()
      // 登记人ID
      params.djr = getWindowLocation().yhm
      // 状态
      params.zt = -1
      try {
        checkObjIsBlank(params, [{ field: 'rwmc', fieldCH: '任务名称' }, { field: 'dwid', fieldCH: '检查单位' }, { field: 'kssj', fieldCH: '开始时间' }, { field: 'djr', fieldCH: '登记人' }, { field: 'zt', fieldCH: '任务状态' }, { field: 'jcjdid', fieldCH: '检查时间' }])
      } catch (error) {
        this.$message.warning('[新建自查自评任务失败]' + error.message)
        return
      }
      let sendParams = machineSendParams(params, ['rwmc', 'dwid', 'kssj', 'djr', 'zt', 'jcjdid'])
      // 加入检查季度名称
      let jcjdObj = generatorCurrentYearQuarter()
      sendParams.jcjdmc = jcjdObj.resList[sendParams.jcjdid - 1].jcjdmc
      // 插入审查任务表
      let resRwid = await insertScrwList(sendParams)
      // 页面保存任务ID
      this.dialogObj.rwid = resRwid
      if (resRwid) {
        //
        this.$message.success('新建审查任务成功')
        // 写入操作日志
        writeOptionsLog('yybs-xjzczp', '新建审查任务', sendParams)
        // 关闭dialog
        this.dialogVisibleXjzczpRw = false
        // 放入localtion
        setZczpIdsObj('rwid', this.dialogObj.rwid)
        setZczpIdsObj('dwid', this.dialogObj.dwid)
        return
      }
      this.$message.error('新建审查任务失败')
    },
    // 保存至下一步
    saveToNext () {
      this.dwjcRK(this.showDxList, 1)
    },
    // 临时保存
    save () {
      this.dwjcRK(this.showDxList, 0)
    },
    // 新建自查自评任务参数校验（为了复用）
    checkXjzczpRwParams () {
      const dwmc = this.dialogObj.rwmc
      const dwid = this.dialogObj.dwid
      if (dwmc && dwid) {
        return true
      }
    },
    /**
     * 单位检查数据入库
     * dwpfjlList：页面上的评分数据
    */
    dwjcRK (dwpfjlList, zt) {
      // 优先检测是否已创建了自查自评任务，已创建才进行自查自评评分记录的提交
      if (!this.checkXjzczpRwParams()) {
        this.$message.warning('请先创建检查任务再保存数据')
        this.dialogVisibleXjzczpRw = true
        return
      }
      //////////////////////
      // 走到这里，rwid不管是新建还是修改都有值
      ////////////////////////
      // 插入或更新单位评分记录表
      let bool = insertUpdateDwpfjlListByRwid(dwpfjlList, this.dialogObj.rwid)
      if (!bool) {
        this.$message.error('单位检查记录录入失败')
        return
      }
      // 更新任务状态码
      let updateScrwListParams = {
        rwid: this.dialogObj.rwid,
        zt: zt
      }
      bool = updateScrwListZt(updateScrwListParams)
      if (bool) {
        // 通过任务ID获取单位评分记录历史信息
        this.getDwpfjlLsxxByRwid()
        this.$message.success('单位检查记录录入成功')
        if (zt == 0) {
          // 写入操作日志
          writeOptionsLog('yybs-xjzczp', '机关单位基本信息临时保存', this.dialogObj)
        }
        if (zt == 1) {
          // 写入操作日志
          writeOptionsLog('yybs-xjzczp', '机关单位基本信息保存成功', this.dialogObj)
        }
      } else {
        this.$message.error('单位检查记录录入失败')
      }
      // 状态为保存至下一步时的操作
      if (zt == 1) {
        // // 插入成功，重置页面数据
        // this.dialogObj = {}
        // 跳转到抽查的内设机构页面
        this.$router.push({
          path: '/ccdnsjg',
          // 任务ID从localtion中去取
          // query: {
          //   // 任务ID
          //   rwid: this.dialogObj.rwid
          // }
        })
      }
    },
    //根据内容id计算内容所在大项、小项索引
    getDxXxIndex (nrid) {
      const _this = this
      //获取个大项小项个数
      let dxXxCountArr = []
      this.showDxList.forEach((dx) => {
        dxXxCountArr.push(dx.xx.length)
      });
      let dxIndex = 0
      let dxNrCount = 0
      let xxIndex = 0
      dxXxCountArr.some((dxXxCount, index) => {
        dxNrCount += dxXxCount
        if (nrid > dxNrCount) {
          return false
        }
        dxIndex = index
        //找到内容所在大项位置，开始计算小项位置
        xxIndex = _this.showDxList[dxIndex].xx.length - (dxNrCount - nrid + 1)
        //找到内容所在小项位置\
        return true
      })
      return {
        dxIndex: dxIndex,
        xxIndex: xxIndex
      }
    },
    //----------------------------用来返回this.spanArr数组的，定义每一行的 rowspan-----------
    getSpanArr (list) {
      let spanArr2 = []
      for (var index = 0; index < list.length; index++) {
        let data = list[index].xx
        let spanArr = []
        for (var i = 0; i < data.length; i++) {
          if (i === 0) {
            spanArr.push(1)
            this.pos = 0
          } else {
            // 判断当前元素与上一个元素是否相同
            if (data[i].xxid == data[i - 1].xxid) {
              spanArr[this.pos] += 1
              spanArr.push(0)
            } else {
              spanArr.push(1)
              this.pos = i
            }
          }
        }
        spanArr.forEach((sa) => {
          spanArr2.push(sa)
        })
      }
      return spanArr2
    },
    objectSpanMethod ({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        //
        const _row = this.spanArr[row.nrid - 1]
        return {
          rowspan: _row,
          colspan: 1,
        }
      }
    },
    // 扣分计数器改变
    handleKfjsq (xx) {
      xx.check = true
    },
    /**
     * 实有内容textarea值改变
     */
    handleSynrTextarea (xx) {
      if (xx.synr !== undefined && xx.synr != "") {
        xx.sfsynr = true
        // xx.check=true;
      } else {
        xx.sfsynr = false
        // xx.check=true;
      }
    },
    /**
     * 评分说明改变
     */
    handleKfsmTextarea (xx) {
      if (xx.kfsm !== undefined && xx.kfsm != "") {
        xx.check = true
      } else {
        xx.check = false
      }
    },
    /**
     * 获取单位详细自查记录字典
     */
    getZD () {
      const zdList = getDwxxzcjlZD()
      /**
       * 在字典里加入:
       * 1、必要字段已扣分【ykf】为0
       * 2、必要字段扣分是否被选中check为false
       * 3、必要字段计数器jsqkf扣分为0
       * 4、必要字段固定扣分分值gdkffz为小项分值fz
       * 5、必要字段是否实有内容sfsynr为false
       */
      zdList.forEach((dx, dxIndex) => {
        dx.xx.forEach((xx, xxIndex) => {
          // //锚点
          // xx.mdIndex = "md" + dxIndex + xxIndex;
          //
          xx.ykf = 0
          xx.check = false
          xx.jsqkf = 0
          xx.gdkffz = xx.fz
          //字典是否实有内容默认选中
          xx.sfsynr = true
        })
      })
      //计算单元格合并规则
      this.spanArr = this.getSpanArr(zdList)
      //
      return zdList
    },
    // 通过任务ID获取单位评分记录历史信息
    getDwpfjlLsxxByRwid () {
      console.log('查询历史信息')
      let resXxzcjl = []
      const dwxxzcjlList = selectDwpfjlListByRwid(this.dialogObj.rwid)
      console.log('赋值页面渲染')
      if (dwxxzcjlList !== undefined && dwxxzcjlList.length > 0) {
        //计算check
        dwxxzcjlList.forEach((dx, dxIndex) => {
          dx.xx.forEach((xx, xxIndex) => {
            //
            xx.gdkffz = xx.fz
            xx.jsqkf = xx.ykf
            if (xx.ykf !== undefined && xx.ykf != 0) {
              xx.check = true
            } else {
              xx.check = false
            }
            if (xx.sfsynr === undefined) {
              xx.sfsynr = false
            }
          })
        })
        resXxzcjl = dwxxzcjlList
      } else {
        resXxzcjl = this.getZD()
      }
      //计算单元格合并规则
      this.spanArr = this.getSpanArr(resXxzcjl)
      this.showDxList = resXxzcjl
    },
    // 新建审查任务
    newScrw () {
      // 清除任务信息
      this.dialogObj.rwid = undefined
      // 初始化缓存数据
      initZczpIdsObj()
      // 重置表格为字典信息
      this.showDxList = this.getZD()
      // 获取单位信息集合（默认选中最后一个，也就是最新的单位信息）
      this.getDwxxList()
      // 弹出新建任务dialog
      this.dialogVisibleXjzczpRw = true
    },
    // 锚点菜单鼠标移入事件
    mouseoverMdMenu () {
      this.showMdmenu = true
    },
    // 锚点菜单鼠标移出事件
    mouseoutMenu () {
      this.showMdmenu = false
    },
    // 检测季度下拉框改变事件
    handleJcjdChanged (val) {
      this.dialogObj = JSON.parse(JSON.stringify(this.dialogObj))
    }
  },
  watch: {
    showDxList: {
      handler (newVal, oldVal) {
        console.log("showDxList changed...")
        const _this = this
        // 清空锚点，防重复
        this.activities = []
        let dxMdObj = {}
        let xxMdObj = {}
        //
        _this.dxdfArr = []
        newVal.forEach((dx, dxIndex) => {
          // 初始化大项锚点
          dxMdObj = {
            dxmc: dx.dxmc,
            children: []
          }
          //
          dx.dxdf = 0
          dx.xx.forEach((xx, xxIndex) => {
            // 锚点
            xx.mdIndex = 'md-' + dxIndex + '-' + xxIndex
            /**
             * 判断是否扣分
             * 1、如果是扣分，则计算两种扣分方式应该扣除的分值
             * 2、不是，则：
             * 2.1 重置已扣分ykf为0，
             * 2.2 得分为该项分值
             */
            if (xx.check) {
              if (xx.kffs == 6) {
                xx.ykf = xx.jsqkf
              }
              if (xx.kffs == 7) {
                xx.ykf = xx.gdkffz
              }
              xx.df = xx.fz - xx.ykf
              // 锚点
              xxMdObj = {
                href: '#' + xx.mdIndex,
                xxmc: xx.xxmc,
                nr: xx.nr,
                ykf: xx.ykf
              }
              dxMdObj.children.push(xxMdObj)
            } else {
              xx.ykf = 0
              xx.jsqkf = 0
              xx.df = xx.fz
            }
            dx.dxdf += xx.df
          })
          // 判断是否需要将大项的锚点放入锚点集合中
          if (dxMdObj.children.length > 0) {
            this.activities.push(dxMdObj)
          }
          //把大项得分总和实时数据push到array数组中
          _this.dxdfArr.push(dx.dxdf)
        })
      },
      deep: true
    }
  },
  mounted () {
    // 获取单位信息集合（默认选中最后一个，也就是最新的单位信息）
    this.getDwxxList()
    // 获取当前年份的检查季度表集合
    this.getJcjdList()
    /**
     * 判断路由是否携带任务ID
     * 携带则获取历史
     * 否则查询字典
    */
    // let routeRwid = this.$route.query.rwid
    let routeRwid = getZczpIdsObj().rwid
    console.log('routeRwid', routeRwid)
    ////////////////
    if (routeRwid) {
      // 获取审查任务表历史信息
      let scrw = selectScrwByRwid(routeRwid)
      this.dialogObj = scrw
      // 获取单位评分记录表历史信息
      this.getDwpfjlLsxxByRwid()
      return
    }
    this.dialogVisibleXjzczpRw = true
    /**
     * 新建自查自评
     * 1、不需要查询历史信息
     * 2、需要新生成一个自查自评任务
     * 3、任务ID很重要，而在上方操作按钮左边的区域实际是查询参数的区域，故这里为了不混淆，提供dialog来填写任务名称和选择该任务关联的单位信息
    */
    console.log('新建自查自评，查询字典信息')
    // 查询字典信息
    const zdList = this.getZD()
    this.showDxList = zdList
  }
}
</script>

<style scoped>
/deep/.mhcx .el-form-item {
  margin-top: 5px;
  margin-bottom: 5px;
}
.widthw {
  width: 100%;
}
/***/
/deep/ .el-dialog .el-dialog__body .form-out {
  display: flex;
  margin-bottom: 22px;
}
/deep/ .el-dialog .el-dialog__body .form-label {
  width: 80px;
  text-align: right;
  vertical-align: middle;
  font-size: 14px;
  color: #606266;
  padding: 0 12px 0 0;
  box-sizing: border-box;
  line-height: 40px;
  /* margin-right: 15px; */
}
/****/
.div-table {
  overflow-y: scroll;
  height: calc(100% - 46px - 64px - 108px - 20px - 10px - 10px);
  margin-bottom: 10px;
}
</style>
