<template>
    <div class="container" id="yjgzContainer">
        <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="迎检材料准备" name="first">
                <div style="width: 99%;margin: 10px auto;overflow-y: scroll;height:100%">
                    <div style="margin-bottom:20px;" v-if="!bmzhIsPerfectShow || !zzyhIsPerfectShow || !smgwIsPerfectShow || !ryhzIsPerfectShow || !xzryxxIsPerfectShow || !gwbgIsPerfectShow || !lglzIsPerfectShow || !csxxIsPerfectShow || !bgxxIsPerfectShow || !jsjtzIsPerfectShow || !ydccjzIsPerfectShow || !bgzdhsbIsPerfectShow || !wlsbIsPerfectShow || !aqcpIsPerfectShow || !zttzIsPerfectShow || zczp1IsPerfectShow || zczp2IsPerfectShow || zczp3IsPerfectShow || zczp4IsPerfectShow">
                        <el-button class="yjscBtn" @click="yjscBtn" type="success" round>一键生成</el-button>
                        <span class="yjscsm">说明：初始化及上报格式发生变更后，自动提醒完善补全信息</span>
                    </div>
                    <!-- 组织机构弹出框 -->
                    <el-dialog title="组织机构图" :visible.sync="dialogVisible" top="0vh" width="100%">
                        <div class="zzDialogContainer">
                            <div id="zzjgt" ref="zzjgpic" style="width:100%; min-height: 100%; overflow: scroll;"> </div>
                        </div>
                        <span slot="footer" class="dialog-footer">
                            <el-button type="primary" @click="dialogVisible = false">关 闭</el-button>
                        </span>
                    </el-dialog>
                    <div class="zdwb">
                        <div class="dbItem" v-if="bmzhIsPerfectShow">
                            <p class="fonts">保密制度检查项</p>
				            <div class="buttons" v-if="bmzhIsPerfectShow" @click="toIndex('1')">完善保密制度</div>
                            <div style="clear:both"></div>
                            <div class="titleDiv">
                                <div class="title" v-if="bmzhIsPerfectShow" @click="toIndex('1')">
                                    <img src="./images/s-icon-03.png" alt="">
                                    <span>保密制度登记</span>
                                </div>
                            </div>
                        </div>
                        <div class="dbItem">
                            <p class="fonts">组织机构检查项</p>
				            <div class="buttons" v-if="zzyhIsPerfectShow"  @click="$router.push('/tzglsy?activeName=zzjg')">完善组织机构</div>
                            <div style="clear:both"></div>
                            <div class="titleDiv">
                                <div class="title" @click="zzjgDialog">
                                    <img src="./images/s-icon-14.png" alt="">
                                    <span>组织机构图</span>
                                </div>
                            </div>
                        </div>
                        <div id="zzjgpicXz" ref="zzjgpicXz" style="width:1000px; height:1000px; position: absolute;z-index: -111;"> </div>
                        
                        <div class="dbItem" v-if="smgwIsPerfectShow || ryhzIsPerfectShow || xzryxxIsPerfectShow || gwbgIsPerfectShow || lglzIsPerfectShow">
                            <p class="fonts">人员信息检查项</p>
				            <div class="buttons" v-if="smgwIsPerfectShow" @click="toIndex('3')">完善涉密岗位信息</div>
				            <div class="buttons" v-if="ryhzIsPerfectShow" @click="toIndex('4')">完善人员汇总信息</div>
				            <div class="buttons" v-if="xzryxxIsPerfectShow" @click="toIndex('5')">完善新增人员信息</div>
				            <div class="buttons" v-if="gwbgIsPerfectShow" @click="toIndex('6')">完善岗位变更信息</div>
				            <div class="buttons" v-if="lglzIsPerfectShow" @click="toIndex('7')">完善离岗离职信息</div>
                            <div style="clear:both"></div>
                            <div class="titleDiv">
                                <div class="title" v-if="smgwIsPerfectShow" @click="toIndex('3')">
                                    <img src="./images/s-icon-09.png" alt="">
                                    <span>涉密岗位登记表</span>
                                </div>
                            </div>
                            <div class="titleDiv">
                                <div class="title" v-if="ryhzIsPerfectShow" @click="toIndex('4')">
                                    <img src="./images/s-icon-10.png" alt="">
                                    <span>涉密人员汇总表</span>
                                </div>
                            </div>
                            <div class="titleDiv">
                                <div class="title" v-if="xzryxxIsPerfectShow" @click="toIndex('5')">
                                    <img src="./images/s-icon-11.png" alt="">
                                    <span>人员新增汇总表</span>
                                </div>
                            </div>
                            <div class="titleDiv">
                                <div class="title" v-if="gwbgIsPerfectShow" @click="toIndex('6')">
                                    <img src="./images/s-icon-12.png" alt="">
                                    <span>人员密级变更汇总表</span>
                                </div>
                            </div>
                            <div class="titleDiv">
                                <div class="title" v-if="lglzIsPerfectShow" @click="toIndex('7')">
                                    <img src="./images/s-icon-13.png" alt="">
                                    <span>离岗汇总表</span>
                                </div>
                            </div>
                        </div>

                        <div class="dbItem" v-if="csxxIsPerfectShow || bgxxIsPerfectShow">
                            <p class="fonts">场所检查项</p>
				            <div class="buttons" v-if="csxxIsPerfectShow" @click="toIndex('8')">完善场所信息</div>
				            <div class="buttons" v-if="bgxxIsPerfectShow" @click="toIndex('9')">完善变更信息</div>
                            <div style="clear:both"></div>
                            <div class="titleDiv">
                                <div class="title" v-if="csxxIsPerfectShow" @click="toIndex('8')">
                                    <img src="./images/s-icon-15.png" alt="">
                                    <span>涉密场所登记表</span>
                                </div>
                            </div>
                            <div class="titleDiv">
                                <div class="title" v-if="bgxxIsPerfectShow" @click="toIndex('9')">
                                    <img src="./images/s-icon-16.png" alt="">
                                    <span>场所变更登记表</span>
                                </div>
                            </div>
                        </div>

                        <div class="dbItem" v-if="jsjtzIsPerfectShow || ydccjzIsPerfectShow || bgzdhsbIsPerfectShow || wlsbIsPerfectShow || aqcpIsPerfectShow">
                            <p class="fonts">设备检查项</p>
				            <div class="buttons" v-if="jsjtzIsPerfectShow" @click="toIndex('10')">完善计算机台账</div>
				            <div class="buttons" v-if="ydccjzIsPerfectShow" @click="toIndex('11')">完善移动存储介质台账</div>
				            <div class="buttons" v-if="bgzdhsbIsPerfectShow" @click="toIndex('13')">完善办公自动化设备台账</div>
				            <div class="buttons" v-if="wlsbIsPerfectShow" @click="toIndex('12')">完善网络设备台账</div>
				            <div class="buttons" v-if="aqcpIsPerfectShow" @click="toIndex('14')">完善安全产品台账</div>
                            <div style="clear:both"></div>
                            <div class="titleDiv">
                                <div class="title" v-if="jsjtzIsPerfectShow" @click="toIndex('10')">
                                    <img src="./images/s-icon-17.png" alt="">
                                    <span>涉密计算机台账</span>
                                </div>
                            </div>
                            <div class="titleDiv">
                                <div class="title" v-if="ydccjzIsPerfectShow" @click="toIndex('11')">
                                    <img src="./images/s-icon-18.png" alt="">
                                    <span>移动存储介质台账</span>
                                </div>
                            </div>
                            <div class="titleDiv">
                                <div class="title" v-if="bgzdhsbIsPerfectShow" @click="toIndex('13')">
                                    <img src="./images/s-icon-19.png" alt="">
                                    <span>办公自动化设备台账</span>
                                </div>
                            </div>
                            <div class="titleDiv">
                                <div class="title" v-if="wlsbIsPerfectShow" @click="toIndex('12')">
                                    <img src="./images/s-icon-20.png" alt="">
                                    <span>网络设备台账</span>
                                </div>
                            </div>
                            <div class="titleDiv">
                                <div class="title" v-if="aqcpIsPerfectShow" @click="toIndex('14')">
                                    <img src="./images/s-icon-21.png" alt="">
                                    <span>安全产品台账</span>
                                </div>
                            </div>
                        </div>

                        <div class="dbItem" v-if="zttzIsPerfectShow">
                            <p class="fonts">载体检查项</p>
				            <div class="buttons" v-if="zttzIsPerfectShow" @click="toIndex('15')">完善载体台账</div>
                            <div style="clear:both"></div>
                            <div class="titleDiv">
                                <div class="title" v-if="zttzIsPerfectShow" @click="toIndex('15')">
                                    <img src="./images/s-icon-08.png" alt="">
                                    <span>涉密载体台账</span>
                                </div>
                            </div>
                        </div>

                        <div class="dbItem" v-if="!zczp1IsPerfectShow && new Date().getMonth() + 1 > 0 && new Date().getMonth() + 1 < 13 || !zczp2IsPerfectShow && new Date().getMonth() + 1 > 3 && new Date().getMonth() + 1 < 13 || !zczp3IsPerfectShow && new Date().getMonth() + 1 > 6 && new Date().getMonth() + 1 < 13 || !zczp4IsPerfectShow && new Date().getMonth() + 1 > 9 && new Date().getMonth() + 1 < 13">
                            <p class="fonts">自查自评检查项</p>
				            <div class="buttons" v-if="!zczp1IsPerfectShow && new Date().getMonth() + 1 > 0 && new Date().getMonth() + 1 < 13" @click="toIndex2">完善第一季度自查自评</div>
				            <div class="buttons" v-if="!zczp2IsPerfectShow && new Date().getMonth() + 1 > 3 && new Date().getMonth() + 1 < 13" @click="toIndex2">完善第二季度自查自评</div>
				            <div class="buttons" v-if="!zczp3IsPerfectShow && new Date().getMonth() + 1 > 6 && new Date().getMonth() + 1 < 13" @click="toIndex2">完善第三季度自查自评</div>
				            <div class="buttons" v-if="!zczp4IsPerfectShow && new Date().getMonth() + 1 > 9 && new Date().getMonth() + 1 < 13" @click="toIndex2">完善第四季度自查自评</div>
                            <div style="clear:both"></div>
                            <div class="titleDiv">
                                <div class="title" v-if="!zczp1IsPerfectShow && new Date().getMonth() + 1 > 0 && new Date().getMonth() + 1 < 13" @click="toIndex2">
                                    <img src="./images/s-icon-22.png" alt="">
                                    <span>第一季度自查自评报告</span>
                                </div>
                            </div>
                            <div class="titleDiv">
                                <div class="title" v-if="!zczp2IsPerfectShow && new Date().getMonth() + 1 > 3 && new Date().getMonth() + 1 < 13" @click="toIndex2">
                                    <img src="./images/s-icon-22.png" alt="">
                                    <span>第二季度自查自评报告</span>
                                </div>
                            </div>
                            <div class="titleDiv">
                                <div class="title" v-if="!zczp3IsPerfectShow && new Date().getMonth() + 1 > 6 && new Date().getMonth() + 1 < 13" @click="toIndex2">
                                    <img src="./images/s-icon-22.png" alt="">
                                    <span>第三季度自查自评报告</span>
                                </div>
                            </div>
                            <div class="titleDiv">
                                <div class="title" v-if="!zczp4IsPerfectShow && new Date().getMonth() + 1 > 9 && new Date().getMonth() + 1 < 13" @click="toIndex2">
                                    <img src="./images/s-icon-22.png" alt="">
                                    <span>第四季度自查自评报告</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </el-tab-pane>
            <!-- 自检评分 -->
            <el-tab-pane label="自检评分" name="second">
                <!-- <div style="height: 76vh;" class="zjpfOnner" v-if="firstShow">
                    <img  src="./images/search.png" alt="">
                    <div class="zjpfRight">
                        <p>暂未扫描，可立即检测</p>
                        <el-button style="" class="zjpfButton" type="primary" round @click="yjjc">一键检测</el-button>
                    </div>
                </div> -->
                <div style="padding:0 20px;overflow-y: scroll;height: 100%;">
                    <div class="sqt">
                        <div  class="header-left" id="echart"> </div>
                        <!-- <div v-if="!isshow1" class="header-left">
                            {{allCounts}}
                        </div> -->
                        <!-- <div v-else class="header-left">
                            无
                        </div> -->
                        <div class="sqt-sm" v-if="isshow1">未扫描，暂无评分</div>
                        <div class="sqt-sm" v-if="isshow2">正在扫描中，请稍后···</div>
                        <div class="sqt-sm" v-if="isshow3">扫描完成，未发现问题</div>
                        <div class="sqt-sm" v-if="isshow4">发现问题，最终分数{{ allCounts }}分，请及时处理！</div>
                        <div class="sqt-btn">
                            <el-button class="qbhlBtn" v-if="isshow4 && allCounts != 100" @click="qbhl" type="warning"
                                round>全部忽略</el-button>
                            <el-button class="yjjcBtn" type="primary" v-if="!isshow2" round @click="yjjc">一键检测</el-button>
                        </div>
                    </div>
                    <div id="progress" v-if="progressShow">
                        <!-- <span :class="[isshow?'jdt':'nojdt']"></span> -->
                        <el-progress :percentage="score" :show-text="false"></el-progress>
                    </div>
                    <div class="zdknr" v-if="checkListDatasShow">
                        <el-collapse v-model="activeNames">
                            <!-- 保密制度 -->
                            <el-collapse-item name="1">
                                <template slot="title">
                                    <img src="./images/img1026_1.png" alt="" />
                                    &nbsp;&nbsp;
                                    保密制度&nbsp;&nbsp;
                                    <!-- <img v-if="statusArr.proJurisdictionLoadingShow" src="./images/loading.gif" alt="" />
                                    <i v-else style="color:#409EFF" class="el-icon-circle-check"></i> -->
                                    <p class="glwtzwfx" v-if="statusArr.proJurisdictionShow1">该类信息暂未发现问题</p>
                                    <p class="glwtzwfx hasProblom" v-if="statusArr.proJurisdictionShow2">检测到该类信息有 <span
                                            class="proSpan">{{ checkCount.proJurisdictionOptimizeCount }}</span> 个可优化项</p>
                                    <p class="glwtzwfx hasProblom" v-if="statusArr.proJurisdictionShow3">检测到该类信息有 <span
                                            class="proSpan">{{ checkCount.proJurisdictionriskCount }}</span> 个风险</p>
                                </template>
                                <!-- 检查时显示 -->
                                <div v-if="statusArr.proJurisdictionLoadingShow" class="kuang">
                                    <img src="./images/loading.gif" alt="">
                                    <div class="bmzd">正在检查保密制度，请稍后...</div>
                                </div>
                                <!-- 无问题时显示 -->
                                <!-- 无问题时显示 -->
                                <div class="kuang" v-if="statusArr.proJurisdictionAllRight1">
                                    <div class="bmzd allRightItem">查看保密制度管理</div>
                                    <i class="el-icon-success" style="color:#409EFF"></i>
                                    <el-button class="ckan look" type="success" size="mini" round
                                        @click="toIndex('1')">查看</el-button>
                                </div>
                                <!-- 有问题时显示--优化 -->
                                <div class="kuang" v-if="statusArr.proJurisdictionShowOptimize1">
                                    <div class="bmzd optimizeItem">长时间未对保密制度进行更新</div>
                                    <i class="el-icon-warning" style="color:#E6A23C"></i>
                                    <el-button class="ckan ignore" type="success" size="mini" @click="hulue('bmopt1')"
                                        round>忽略</el-button>
                                    <el-button class="ckan update" type="success" size="mini" round
                                        @click="toIndex('1')">更新</el-button>
                                </div>
                                <!-- 有问题时显示--风险 -->
                                <div class="kuang" v-if="statusArr.proJurisdictionShowRisk1">
                                    <div class="bmzd riskItem">未建立保密制度清单</div>
                                    <i class="el-icon-error" style="color:#F56C6C"></i>
                                    <el-button class="ckan ignore" type="success" size="mini" @click="hulue('bmrisk1')"
                                        round>忽略</el-button>
                                    <el-button class="ckan update" type="success" size="mini" round
                                        @click="toIndex('1')">录入</el-button>
                                </div>
                            </el-collapse-item>
                            <!-- 保密制度end -->
                            <!-- 组织机构 -->
                            <el-collapse-item name="2">
                                <template slot="title">
                                    <img src="./images/img1026_1.png" alt="">
                                    &nbsp;&nbsp;组织机构&nbsp;&nbsp;
                                    <!-- <img v-if="statusArr.organizationLoadingShow" src="./images/loading.gif" alt="" />
                                    <i v-else style="color:#409EFF" class="el-icon-circle-check"></i> -->
                                    <p class="glwtzwfx" v-if="statusArr.organizationShow1">该类信息暂未发现问题</p>
                                    <p class="glwtzwfx hasProblom" v-if="statusArr.organizationShow2">检测到该类信息有 <span
                                            class="proSpan">{{ checkCount.organizationOptimizeCount }}</span> 个可优化项</p>
                                    <p class="glwtzwfx hasProblom" v-if="statusArr.organizationShow3">检测到该类信息有 <span
                                            class="proSpan">{{ checkCount.organizationriskCount }}</span> 个风险</p>
                                </template>
                                <!-- 检查时显示 -->
                                <div v-if="statusArr.organizationLoadingShow" class="kuang">
                                    <img src="./images/loading.gif" alt="">
                                    <div class="bmzd">正在检查组织机构...</div>
                                </div>
                                <!-- 无问题时显示 -->
                                <div class="kuang" v-if="statusArr.organizationAllRight1">
                                    <div class="bmzd allRightItem">查看组织机构管理</div>
                                    <i class="el-icon-success" style="color:#409EFF"></i>
                                    <el-button class="ckan look" type="success" size="mini" round
                                        @click="toIndex('2s')">查看</el-button>
                                </div>
                                <!-- 风险 -->
                                <div class="kuang" v-if="statusArr.organizationShowRisk1">
                                    <div class="bmzd riskItem">未建立组织机构清单</div>
                                    <i class="el-icon-error" style="color:#F56C6C"></i>
                                    <el-button class="ckan ignore" type="success" size="mini" round
                                        @click="hulue('zzrisk1')">忽略</el-button>
                                    <el-button class="ckan update" type="success" size="mini" round
                                        @click="toIndex('2s')">录入</el-button>
                                </div>
                                <div class="kuang" v-if="statusArr.organizationShowRisk2">
                                    <div class="bmzd riskItem">未按要求设置负责的保密机构</div>
                                    <i class="el-icon-error" style="color:#F56C6C"></i>
                                    <el-button class="ckan ignore" type="success" size="mini" @click="hulue('zzrisk2')"
                                        round>忽略</el-button>
                                    <el-button class="ckan update" type="success" size="mini" round
                                        @click="toIndex('2s')">录入</el-button>
                                </div>
                                <div class="kuang" v-if="statusArr.organizationShowRisk3">
                                    <div class="bmzd riskItem">未按要求设置专职保密总监岗位（甲级资质单位）</div>
                                    <i class="el-icon-error" style="color:#F56C6C"></i>
                                    <el-button class="ckan ignore" type="success" size="mini" @click="hulue('zzrisk3')"
                                        round>忽略</el-button>
                                    <el-button class="ckan update" type="success" size="mini" round
                                        @click="toIndex('2s')">录入</el-button>
                                </div>
                                <!-- 优化 -->
                                <div class="kuang" v-if="statusArr.organizationShowOptimize1">
                                    <div class="bmzd optimizeItem">长时间未对组织机构进行更新</div>
                                    <i class="el-icon-warning" style="color:#E6A23C"></i>
                                    <el-button class="ckan ignore" type="success" size="mini" round
                                        @click="hulue('zzopt1')">忽略</el-button>
                                    <el-button class="ckan update" type="success" size="mini" round
                                        @click="toIndex('2s')">更新</el-button>
                                </div>
                            </el-collapse-item>
                            <!-- 涉密人员 -->
                            <el-collapse-item name="3">
                                <template slot="title">
                                    <img src="./images/img1026_1.png" alt="">
                                    &nbsp;&nbsp;涉密人员&nbsp;&nbsp;
                                    <!-- <img v-if="statusArr.secretPersonnelLoadingShow" src="./images/loading.gif" alt="" />
                                    <i v-else style="color:#409EFF" class="el-icon-circle-check"></i> -->
                                    <p class="glwtzwfx" v-if="statusArr.secretPersonnelShow1">该类信息暂未发现问题</p>
                                    <p class="glwtzwfx hasProblom" v-if="statusArr.secretPersonnelShow2">检测到该类信息有 <span
                                            class="proSpan">{{ checkCount.secretPersonnelOptimizeCount }}</span> 个可优化项</p>
                                    <p class="glwtzwfx hasProblom" v-if="statusArr.secretPersonnelShow3">检测到该类信息有 <span
                                            class="proSpan">{{ checkCount.secretPersonnelriskCount }}</span> 个风险</p>
                                </template>
                                <!-- 检查时显示 -->
                                <div v-if="statusArr.secretPersonnelLoadingShow" class="kuang">
                                    <img src="./images/loading.gif" alt="">
                                    <div class="bmzd">正在检查涉密人员...</div>
                                </div>
                                <!-- 无问题时显示 -->
                                <div class="kuang" v-if="statusArr.secretPersonnelAllRight1">
                                    <div class="bmzd allRightItem">查看涉密岗位</div>
                                    <i class="el-icon-success" style="color:#409EFF"></i>
                                    <el-button class="ckan look" type="success" size="mini" round
                                        @click="toIndex('3')">查看</el-button>
                                </div>
                                <div class="kuang" v-if="statusArr.secretPersonnelAllRight2">
                                    <div class="bmzd allRightItem">查看在岗涉密人员</div>
                                    <i class="el-icon-success" style="color:#409EFF"></i>
                                    <el-button class="ckan look" type="success" size="mini" round
                                        @click="toIndex('4')">查看</el-button>
                                </div>
                                <div class="kuang" v-if="statusArr.secretPersonnelAllRight3">
                                    <div class="bmzd allRightItem">查看人员新增汇总</div>
                                    <i class="el-icon-success" style="color:#409EFF"></i>
                                    <el-button class="ckan look" type="success" size="mini" round
                                        @click="toIndex('5')">查看</el-button>
                                </div>
                                <div class="kuang" v-if="statusArr.secretPersonnelAllRight4">
                                    <div class="bmzd allRightItem">查看岗位变更</div>
                                    <i class="el-icon-success" style="color:#409EFF"></i>
                                    <el-button class="ckan look" type="success" size="mini" round
                                        @click="toIndex('6')">查看</el-button>
                                </div>
                                <div class="kuang" v-if="statusArr.secretPersonnelAllRight5">
                                    <div class="bmzd allRightItem">查看离岗离职</div>
                                    <i class="el-icon-success" style="color:#409EFF"></i>
                                    <el-button class="ckan look" type="success" size="mini" round
                                        @click="toIndex('7')">查看</el-button>
                                </div>
                                <!-- 风险 -->
                                <div class="kuang" v-if="statusArr.secretPersonnelShowRisk1">
                                    <div class="bmzd riskItem">未按要求对涉密人员进行审查</div>
                                    <i class="el-icon-error" style="color:#F56C6C"></i>
                                    <el-button class="ckan ignore" type="success" size="mini" round
                                        @click="hulue('smryrisk1')">忽略</el-button>
                                    <el-button class="ckan update" type="success" size="mini" round
                                        @click="toIndex('4')">录入</el-button>
                                </div>
                                <div class="kuang" v-if="statusArr.secretPersonnelShowRisk2">
                                    <div class="bmzd riskItem">未建立涉密岗位清单</div>
                                    <i class="el-icon-error" style="color:#F56C6C"></i>
                                    <el-button class="ckan ignore" type="success" size="mini" round
                                        @click="hulue('smryrisk2')">忽略</el-button>
                                    <el-button class="ckan update" type="success" size="mini" round
                                        @click="toIndex('3')">录入</el-button>
                                </div>
                                <div class="kuang" v-if="statusArr.secretPersonnelShowRisk3">
                                    <div class="bmzd riskItem">未建立在岗涉密人员台账</div>
                                    <i class="el-icon-error" style="color:#F56C6C"></i>
                                    <el-button class="ckan ignore" type="success" size="mini" round
                                        @click="hulue('smryrisk3')">忽略</el-button>
                                    <el-button class="ckan update" type="success" size="mini" round
                                        @click="toIndex('4')">录入</el-button>
                                </div>
                                <!-- 优化 -->
                                <div class="kuang" v-if="statusArr.secretPersonnelShowOptimize1">
                                    <div class="bmzd optimizeItem">长时间未对涉密岗位进行更新</div>
                                    <i class="el-icon-warning" style="color:#E6A23C"></i>
                                    <el-button class="ckan ignore" type="success" size="mini" round
                                        @click="hulue('smryopt1')">忽略</el-button>
                                    <el-button class="ckan update" type="success" size="mini" round
                                        @click="toIndex('3')">更新</el-button>
                                </div>
                                <div class="kuang" v-if="statusArr.secretPersonnelShowOptimize2">
                                    <div class="bmzd optimizeItem">长时间未对在岗涉密人员进行更新</div>
                                    <i class="el-icon-warning" style="color:#E6A23C"></i>
                                    <el-button class="ckan ignore" type="success" size="mini" round
                                        @click="hulue('smryopt2')">忽略</el-button>
                                    <el-button class="ckan update" type="success" size="mini" round
                                        @click="toIndex('4')">更新</el-button>
                                </div>
                                <div class="kuang" v-if="statusArr.secretPersonnelShowOptimize3">
                                    <div class="bmzd optimizeItem">未建立或长时间未对人员新增汇总进行更新</div>
                                    <i class="el-icon-warning" style="color:#E6A23C"></i>
                                    <el-button class="ckan ignore" type="success" size="mini" round
                                        @click="hulue('smryopt3')">忽略</el-button>
                                    <el-button class="ckan update" type="success" size="mini" round
                                        @click="toIndex('5')">更新</el-button>
                                </div>
                                <div class="kuang" v-if="statusArr.secretPersonnelShowOptimize4">
                                    <div class="bmzd optimizeItem">未建立或长时间未对岗位变更进行更新</div>
                                    <i class="el-icon-warning" style="color:#E6A23C"></i>
                                    <el-button class="ckan ignore" type="success" size="mini" round
                                        @click="hulue('smryopt4')">忽略</el-button>
                                    <el-button class="ckan update" type="success" size="mini" round
                                        @click="toIndex('6')">更新</el-button>
                                </div>
                                <div class="kuang" v-if="statusArr.secretPersonnelShowOptimize5">
                                    <div class="bmzd optimizeItem">未建立或长时间未对离岗离职进行更新</div>
                                    <i class="el-icon-warning" style="color:#E6A23C"></i>
                                    <el-button class="ckan ignore" type="success" size="mini" round
                                        @click="hulue('smryopt5')">忽略</el-button>
                                    <el-button class="ckan update" type="success" size="mini" round
                                        @click="toIndex('7')">更新</el-button>
                                </div>
                            </el-collapse-item>
                            <!-- 培训教育 -->
                            <el-collapse-item name="4">
                                <template slot="title">
                                    <img src="./images/img1026_1.png" alt="">
                                    &nbsp;&nbsp;教育培训&nbsp;&nbsp;
                                    <!-- <img v-if="statusArr.eduTrainLoadingShow" src="./images/loading.gif" alt="" />
                                    <i v-else style="color:#409EFF" class="el-icon-circle-check"></i> -->
                                    <p class="glwtzwfx" v-if="statusArr.eduTrainShow1">该类信息暂未发现问题</p>
                                    <p class="glwtzwfx hasProblom" v-if="statusArr.eduTrainShow2">检测到该类信息有 <span
                                            class="proSpan">{{ checkCount.eduTrainOptimizeCount }}</span> 个可优化项</p>
                                    <p class="glwtzwfx hasProblom" v-if="statusArr.eduTrainShow3">检测到该类信息有 <span
                                            class="proSpan">{{ checkCount.eduTrainriskCount }}</span> 个风险</p>
                                </template>
                                <!-- 检查时显示 -->
                                <div v-if="statusArr.eduTrainLoadingShow" class="kuang">
                                    <img src="./images/loading.gif" alt="">
                                    <div class="bmzd">正在检查教育培训，请稍后...</div>
                                </div>
                                <!-- 无问题时显示 -->
                                <div class="kuang" v-if="statusArr.eduTrainAllRight1">
                                    <div class="bmzd allRightItem">查看培训清单</div>
                                    <i class="el-icon-success" style="color:#409EFF"></i>
                                    <el-button class="ckan look" type="success" size="mini" round
                                        @click="toIndex('20')">查看</el-button>
                                </div>
                                <!-- 有问题时显示--优化--长时间未对培训清单进行更新 -->
                                <div class="kuang" v-if="statusArr.eduTrainShowOptimize1">
                                    <div class="bmzd optimizeItem">长时间未对培训清单进行更新</div>
                                    <i class="el-icon-warning" style="color:#E6A23C"></i>
                                    <el-button class="ckan ignore" type="success" size="mini" @click="hulue('jyopt1')"
                                        round>忽略</el-button>
                                    <el-button class="ckan update" type="success" size="mini" round
                                        @click="toIndex('20')">更新</el-button>
                                </div>
                                <!-- 有问题时显示--风险 -->
                                <div class="kuang" v-if="statusArr.eduTrainShowRisk1">
                                    <div class="bmzd riskItem">教育培训学时未达到要求</div>
                                    <i class="el-icon-error" style="color:#F56C6C"></i>
                                    <span style="color:#999999; margin-left:10px">每个涉密人员教育培训需要到达10学时</span>
                                    <el-button class="ckan ignore" type="success" size="mini" round
                                        @click="hulue('jyrisk1')">忽略</el-button>
                                    <el-button class="ckan update" type="success" size="mini" round
                                        @click="toIndex('20')">录入</el-button>
                                </div>
                            </el-collapse-item>
                            <!-- 涉密场所 -->
                            <el-collapse-item name="5">
                                <template slot="title">
                                    <img src="./images/img1026_1.png" alt="">
                                    &nbsp;&nbsp;涉密场所&nbsp;&nbsp;
                                    <!-- <img v-if="statusArr.secretSitesLoadingShow" src="./images/loading.gif" alt="" />
                                    <i v-else style="color:#409EFF" class="el-icon-circle-check"></i> -->
                                    <p class="glwtzwfx" v-if="statusArr.secretSitesShow1">该类信息暂未发现问题</p>
                                    <p class="glwtzwfx hasProblom" v-if="statusArr.secretSitesShow2">检测到该类信息有 <span
                                            class="proSpan">{{ checkCount.secretSitesOptimizeCount }}</span> 个可优化项</p>
                                    <p class="glwtzwfx hasProblom" v-if="statusArr.secretSitesShow3">检测到该类信息有 <span
                                            class="proSpan">{{ checkCount.secretSitesriskCount }}</span> 个风险</p>
                                </template>
                                <!-- 检查时显示 -->
                                <div v-if="statusArr.secretSitesLoadingShow" class="kuang">
                                    <img src="./images/loading.gif" alt="">
                                    <div class="bmzd">正在检查涉密人员，请稍后...</div>
                                </div>
                                <!-- 无问题时显示 -->
                                <div class="kuang" v-if="statusArr.secretSitesAllRight1">
                                    <div class="bmzd allRightItem">查看场所管理</div>
                                    <i class="el-icon-success" style="color:#409EFF"></i>
                                    <el-button class="ckan look" type="success" size="mini" round
                                        @click="toIndex('8')">查看</el-button>
                                </div>
                                <div class="kuang" v-if="statusArr.secretSitesAllRight2">
                                    <div class="bmzd allRightItem">查看场所变更</div>
                                    <i class="el-icon-success" style="color:#409EFF"></i>
                                    <el-button class="ckan look" type="success" size="mini" round
                                        @click="toIndex('9')">查看</el-button>
                                </div>
                                <!-- 有问题时显示 -->
                                <div class="kuang" v-if="statusArr.secretSitesShowRisk1">
                                    <div class="bmzd riskItem">未建立涉密场所台账</div>
                                    <i class="el-icon-error" style="color:#F56C6C"></i>
                                    <el-button class="ckan ignore" type="success" size="mini" round
                                        @click="hulue('csrisk1')">忽略</el-button>
                                    <el-button class="ckan update" type="success" size="mini" round
                                        @click="toIndex('8')">录入</el-button>
                                </div>
                                <div class="kuang" v-if="statusArr.secretSitesShowOptimize1">
                                    <div class="bmzd optimizeItem">长时间未对场所管理进行更新</div>
                                    <i class="el-icon-warning" style="color:#E6A23C"></i>
                                    <el-button class="ckan ignore" type="success" size="mini" round
                                        @click="hulue('csopt1')">忽略</el-button>
                                    <el-button class="ckan update" type="success" size="mini" round
                                        @click="toIndex('8')">更新</el-button>
                                </div>
                                <div class="kuang" v-if="statusArr.secretSitesShowOptimize2">
                                    <div class="bmzd optimizeItem">未建立或长时间未对场所变更进行更新</div>
                                    <i class="el-icon-warning" style="color:#E6A23C"></i>
                                    <el-button class="ckan ignore" type="success" size="mini" round
                                        @click="hulue('csopt2')">忽略</el-button>
                                    <el-button class="ckan update" type="success" size="mini" round
                                        @click="toIndex('9')">更新</el-button>
                                </div>
                            </el-collapse-item>
                            <!-- 设备信息 -->
                            <el-collapse-item name="6">
                                <template slot="title">
                                    <img src="./images/img1026_1.png" alt="">
                                    &nbsp;&nbsp;设备信息&nbsp;&nbsp;
                                    <!-- <img v-if="statusArr.deviceLoadingShow" src="./images/loading.gif" alt="" />
                                    <i v-else style="color:#409EFF" class="el-icon-circle-check"></i> -->
                                    <p class="glwtzwfx" v-if="statusArr.deviceShow1">该类信息暂未发现问题</p>
                                    <p class="glwtzwfx hasProblom" v-if="statusArr.deviceShow2">检测到该类信息有 <span
                                            class="proSpan">{{ checkCount.deviceOptimizeCount }}</span> 个可优化项</p>
                                    <p class="glwtzwfx hasProblom" v-if="statusArr.deviceShow3">检测到该类信息有 <span
                                            class="proSpan">{{ checkCount.deviceriskCount }}</span> 个风险</p>
                                </template>
                                <!-- 检查时显示 -->
                                <div v-if="statusArr.deviceLoadingShow" class="kuang">
                                    <img src="./images/loading.gif" alt="">
                                    <div class="bmzd">正在检查设备信息，请稍后...</div>
                                </div>
                                <!-- 无问题时显示 -->
                                <div class="kuang" v-if="statusArr.deviceAllRight1">
                                    <div class="bmzd allRightItem">查看涉密计算机</div>
                                    <i class="el-icon-success" style="color:#409EFF"></i>
                                    <el-button class="ckan look" type="success" size="mini" round
                                        @click="toIndex('10')">查看</el-button>
                                </div>
                                <div class="kuang" v-if="statusArr.deviceAllRight2">
                                    <div class="bmzd allRightItem">查看非涉密计算机</div>
                                    <i class="el-icon-success" style="color:#409EFF"></i>
                                    <el-button class="ckan look" type="success" size="mini" round
                                        @click="toIndex('21')">查看</el-button>
                                </div>
                                <div class="kuang" v-if="statusArr.deviceAllRight3">
                                    <div class="bmzd allRightItem">查看涉密移动存储介质</div>
                                    <i class="el-icon-success" style="color:#409EFF"></i>
                                    <el-button class="ckan look" type="success" size="mini" round
                                        @click="toIndex('11')">查看</el-button>
                                </div>
                                <div class="kuang" v-if="statusArr.deviceAllRight4">
                                    <div class="bmzd allRightItem">查看涉密办公自动化设备</div>
                                    <i class="el-icon-success" style="color:#409EFF"></i>
                                    <el-button class="ckan look" type="success" size="mini" round
                                        @click="toIndex('13')">查看</el-button>
                                </div>
                                <div class="kuang" v-if="statusArr.deviceAllRight5">
                                    <div class="bmzd allRightItem">查看非密办公自动化设备</div>
                                    <i class="el-icon-success" style="color:#409EFF"></i>
                                    <el-button class="ckan look" type="success" size="mini" round
                                        @click="toIndex('22')">查看</el-button>
                                </div>
                                <div class="kuang" v-if="statusArr.deviceAllRight6">
                                    <div class="bmzd allRightItem">查看涉密网络设备</div>
                                    <i class="el-icon-success" style="color:#409EFF"></i>
                                    <el-button class="ckan look" type="success" size="mini" round
                                        @click="toIndex('12')">查看</el-button>
                                </div>
                                <div class="kuang" v-if="statusArr.deviceAllRight7">
                                    <div class="bmzd allRightItem">查看非密网络设备</div>
                                    <i class="el-icon-success" style="color:#409EFF"></i>
                                    <el-button class="ckan look" type="success" size="mini" round
                                        @click="toIndex('23')">查看</el-button>
                                </div>
                                <div class="kuang" v-if="statusArr.deviceAllRight8">
                                    <div class="bmzd allRightItem">查看安全产品</div>
                                    <i class="el-icon-success" style="color:#409EFF"></i>
                                    <el-button class="ckan look" type="success" size="mini" round
                                        @click="toIndex('14')">查看</el-button>
                                </div>
                                <!-- 有问题时显示--优化 -->
                                <div class="kuang" v-if="statusArr.deviceShowOptimize1">
                                    <div class="bmzd optimizeItem">长时间未对涉密计算机进行更新</div>
                                    <i class="el-icon-warning" style="color:#E6A23C"></i>
                                    <el-button class="ckan ignore" type="success" size="mini" round
                                        @click="hulue('sbopt1')">忽略</el-button>
                                    <el-button class="ckan update" type="success" size="mini" round
                                        @click="toIndex('10')">更新</el-button>
                                </div>
                                <div class="kuang" v-if="statusArr.deviceShowOptimize2">
                                    <div class="bmzd optimizeItem">长时间未对非涉密计算机进行更新</div>
                                    <i class="el-icon-warning" style="color:#E6A23C"></i>
                                    <el-button class="ckan ignore" type="success" size="mini" round
                                        @click="hulue('sbopt2')">忽略</el-button>
                                    <el-button class="ckan update" type="success" size="mini" round
                                        @click="toIndex('21')">更新</el-button>
                                </div>
                                <div class="kuang" v-if="statusArr.deviceShowOptimize3">
                                    <div class="bmzd optimizeItem">长时间未对涉密移动存储介质进行更新</div>
                                    <i class="el-icon-warning" style="color:#E6A23C"></i>
                                    <el-button class="ckan ignore" type="success" size="mini" round
                                        @click="hulue('sbopt3')">忽略</el-button>
                                    <el-button class="ckan update" type="success" size="mini" round
                                        @click="toIndex('11')">更新</el-button>
                                </div>
                                <div class="kuang" v-if="statusArr.deviceShowOptimize4">
                                    <div class="bmzd optimizeItem">长时间未对涉密办公自动化设备进行更新</div>
                                    <i class="el-icon-warning" style="color:#E6A23C"></i>
                                    <el-button class="ckan ignore" type="success" size="mini" round
                                        @click="hulue('sbopt4')">忽略</el-button>
                                    <el-button class="ckan update" type="success" size="mini" round
                                        @click="toIndex('13')">更新</el-button>
                                </div>
                                <div class="kuang" v-if="statusArr.deviceShowOptimize5">
                                    <div class="bmzd optimizeItem">长时间未对非密办公自动化设备进行更新</div>
                                    <i class="el-icon-warning" style="color:#E6A23C"></i>
                                    <el-button class="ckan ignore" type="success" size="mini" round
                                        @click="hulue('sbopt5')">忽略</el-button>
                                    <el-button class="ckan update" type="success" size="mini" round
                                        @click="toIndex('22')">更新</el-button>
                                </div>
                                <div class="kuang" v-if="statusArr.deviceShowOptimize6">
                                    <div class="bmzd optimizeItem">长时间未对涉密网络设备进行更新</div>
                                    <i class="el-icon-warning" style="color:#E6A23C"></i>
                                    <el-button class="ckan ignore" type="success" size="mini" round
                                        @click="hulue('sbopt6')">忽略</el-button>
                                    <el-button class="ckan update" type="success" size="mini" round
                                        @click="toIndex('12')">更新</el-button>
                                </div>
                                <div class="kuang" v-if="statusArr.deviceShowOptimize7">
                                    <div class="bmzd optimizeItem">长时间未对非密网络设备进行更新</div>
                                    <i class="el-icon-warning" style="color:#E6A23C"></i>
                                    <el-button class="ckan ignore" type="success" size="mini" round
                                        @click="hulue('sbopt7')">忽略</el-button>
                                    <el-button class="ckan update" type="success" size="mini" round
                                        @click="toIndex('23')">更新</el-button>
                                </div>
                                <div class="kuang" v-if="statusArr.deviceShowOptimize8">
                                    <div class="bmzd optimizeItem">长时间未对安全产品进行更新</div>
                                    <i class="el-icon-warning" style="color:#E6A23C"></i>
                                    <el-button class="ckan ignore" type="success" size="mini" round
                                        @click="hulue('sbopt8')">忽略</el-button>
                                    <el-button class="ckan update" type="success" size="mini" round
                                        @click="toIndex('14')">更新</el-button>
                                </div>
                                <!-- 有问题时显示--风险 -->
                                <div class="kuang" v-if="statusArr.deviceShowRisk1">
                                    <div class="bmzd riskItem">未建立涉密计算机台账</div>
                                    <i class="el-icon-error" style="color:#F56C6C"></i>
                                    <el-button class="ckan ignore" type="success" size="mini" round
                                        @click="hulue('sbrisk1')">忽略</el-button>
                                    <el-button class="ckan update" type="success" size="mini" round
                                        @click="toIndex('10')">录入</el-button>
                                </div>
                                <div class="kuang" v-if="statusArr.deviceShowRisk2">
                                    <div class="bmzd riskItem">未建立非涉密计算机台账</div>
                                    <i class="el-icon-error" style="color:#F56C6C"></i>
                                    <el-button class="ckan ignore" type="success" size="mini" round
                                        @click="hulue('sbrisk2')">忽略</el-button>
                                    <el-button class="ckan update" type="success" size="mini" round
                                        @click="toIndex('21')">录入</el-button>
                                </div>
                                <div class="kuang" v-if="statusArr.deviceShowRisk3">
                                    <div class="bmzd riskItem">未建立涉密移动存储介质台账</div>
                                    <i class="el-icon-error" style="color:#F56C6C"></i>
                                    <el-button class="ckan ignore" type="success" size="mini" round
                                        @click="hulue('sbrisk3')">忽略</el-button>
                                    <el-button class="ckan update" type="success" size="mini" round
                                        @click="toIndex('11')">录入</el-button>
                                </div>
                                <div class="kuang" v-if="statusArr.deviceShowRisk4">
                                    <div class="bmzd riskItem">未建立涉密办公自动化设备台账</div>
                                    <i class="el-icon-error" style="color:#F56C6C"></i>
                                    <el-button class="ckan ignore" type="success" size="mini" round
                                        @click="hulue('sbrisk4')">忽略</el-button>
                                    <el-button class="ckan update" type="success" size="mini" round
                                        @click="toIndex('13')">录入</el-button>
                                </div>
                                <div class="kuang" v-if="statusArr.deviceShowRisk5">
                                    <div class="bmzd riskItem">未建立非密办公自动化设备台账</div>
                                    <i class="el-icon-error" style="color:#F56C6C"></i>
                                    <el-button class="ckan ignore" type="success" size="mini" round
                                        @click="hulue('sbrisk5')">忽略</el-button>
                                    <el-button class="ckan update" type="success" size="mini" round
                                        @click="toIndex('22')">录入</el-button>
                                </div>
                                <div class="kuang" v-if="statusArr.deviceShowRisk6">
                                    <div class="bmzd riskItem">未建立涉密网络设备台账</div>
                                    <i class="el-icon-error" style="color:#F56C6C"></i>
                                    <el-button class="ckan ignore" type="success" size="mini" round
                                        @click="hulue('sbrisk6')">忽略</el-button>
                                    <el-button class="ckan update" type="success" size="mini" round
                                        @click="toIndex('12')">录入</el-button>
                                </div>
                                <div class="kuang" v-if="statusArr.deviceShowRisk7">
                                    <div class="bmzd riskItem">未建立非密网络设备台账</div>
                                    <i class="el-icon-error" style="color:#F56C6C"></i>
                                    <el-button class="ckan ignore" type="success" size="mini" round
                                        @click="hulue('sbrisk7')">忽略</el-button>
                                    <el-button class="ckan update" type="success" size="mini" round
                                        @click="toIndex('23')">录入</el-button>
                                </div>
                                <div class="kuang" v-if="statusArr.deviceShowRisk8">
                                    <div class="bmzd riskItem">未建立安全产品台账</div>
                                    <i class="el-icon-error" style="color:#F56C6C"></i>
                                    <el-button class="ckan ignore" type="success" size="mini" round
                                        @click="hulue('sbrisk8')">忽略</el-button>
                                    <el-button class="ckan update" type="success" size="mini" round
                                        @click="toIndex('14')">录入</el-button>
                                </div>
                            </el-collapse-item>
                            <!-- 载体信息 -->
                            <el-collapse-item name="7">
                                <template slot="title">
                                    <img src="./images/img1026_1.png" alt="">
                                    &nbsp;&nbsp;涉密载体&nbsp;&nbsp;
                                    <!-- <img v-if="statusArr.carrierLoadingShow" src="./images/loading.gif" alt="" />
                                    <i v-else style="color:#409EFF" class="el-icon-circle-check"></i> -->
                                    <p class="glwtzwfx" v-if="statusArr.carrierShow1">该类信息暂未发现问题</p>
                                    <p class="glwtzwfx hasProblom" v-if="statusArr.carrierShow2">检测到该类信息有 <span
                                            class="proSpan">{{ checkCount.carrierOptimizeCount }}</span> 个可优化项</p>
                                    <p class="glwtzwfx hasProblom" v-if="statusArr.carrierShow3">检测到该类信息有 <span
                                            class="proSpan">{{ checkCount.carrierriskCount }}</span> 个风险</p>
                                </template>
                                <!-- 检查时显示 -->
                                <div v-if="statusArr.carrierLoadingShow" class="kuang">
                                    <img src="./images/loading.gif" alt="">
                                    <div class="bmzd">正在检查涉密载体，请稍后...</div>
                                </div>
                                <!-- 无问题时显示 -->
                                <div class="kuang" v-if="statusArr.carrierAllRight1">
                                    <div class="bmzd allRightItem">查看载体管理</div>
                                    <i class="el-icon-success" style="color:#409EFF"></i>
                                    <el-button class="ckan look" type="success" size="mini" round
                                        @click="toIndex('15')">查看</el-button>
                                </div>
                                <!-- 有问题时显示--优化 -->
                                <div class="kuang" v-if="statusArr.carrierShowOptimize1">
                                    <div class="bmzd optimizeItem">长时间未对载体管理进行更新</div>
                                    <i class="el-icon-warning" style="color:#E6A23C"></i>
                                    <el-button class="ckan ignore" type="success" size="mini" round
                                        @click="hulue('ztopt1')">忽略</el-button>
                                    <el-button class="ckan update" type="success" size="mini" round
                                        @click="toIndex('15')">更新</el-button>
                                </div>
                                <!-- 有问题时显示--风险 -->
                                <div class="kuang" v-if="statusArr.carrierShowRisk1">
                                    <div class="bmzd riskItem">未建立涉密载体台账</div>
                                    <i class="el-icon-error" style="color:#F56C6C"></i>
                                    <el-button class="ckan ignore" type="success" size="mini" round
                                        @click="hulue('ztrisk1')">忽略</el-button>
                                    <el-button class="ckan update" type="success" size="mini" round
                                        @click="toIndex('15')">录入</el-button>
                                </div>
                            </el-collapse-item>
                            <!-- 自查自评 -->
                            <el-collapse-item name="8">
                                <template slot="title">
                                    <img src="./images/img1026_1.png" alt="">
                                    &nbsp;&nbsp;自查自评&nbsp;&nbsp;
                                    <!-- <img v-if="statusArr.selfEvaluationLoadingShow" src="./images/loading.gif" alt="" />
                                    <i v-else style="color:#409EFF" class="el-icon-circle-check"></i> -->
                                    <p class="glwtzwfx" v-if="statusArr.selfEvaluationShow1">该类信息暂未发现问题</p>
                                    <!-- <p class="glwtzwfx hasProblom" v-if="statusArr.selfEvaluationShow2">检测到该类信息有 <span class="proSpan">{{checkCount.selfEvaluationOptimizeCount}}</span> 个可优化项</p> -->
                                    <p class="glwtzwfx hasProblom" v-if="statusArr.selfEvaluationShow3">检测到该类信息有 <span
                                            class="proSpan">{{ checkCount.selfEvaluationriskCount }}</span> 个风险</p>
                                </template>
                                <!-- 检查时显示 -->
                                <div v-if="statusArr.selfEvaluationLoadingShow" class="kuang">
                                    <img src="./images/loading.gif" alt="">
                                    <div class="bmzd">正在检查自查自评，请稍后...</div>
                                </div>
                                <!-- 无问题时显示 -->
                                <div class="kuang" v-if="statusArr.selfEvaluationAllRight1">
                                    <div class="bmzd allRightItem">查看自查自评</div>
                                    <i class="el-icon-success" style="color:#409EFF"></i>
                                    <el-button class="ckan look" type="success" size="mini" round
                                        @click="toIndex('24')">查看</el-button>
                                </div>
                                <!-- 有问题时显示--风险 -->
                                <div class="kuang" v-if="statusArr.selfEvaluationAShowRisk1 && new Date().getMonth() + 1 > 0 && new Date().getMonth() + 1 < 13">
                                    <div class="bmzd riskItem">第一季度未进行自查自评</div>
                                    <i class="el-icon-error" style="color:#F56C6C"></i>
                                    <el-button class="ckan ignore" type="success" size="mini" round
                                        @click="hulue('zczprisk1')">忽略</el-button>
                                    <el-button class="ckan update" type="success" size="mini" round
                                        @click="toIndex('24')">录入</el-button>
                                </div>
                                <div class="kuang" v-if="statusArr.selfEvaluationAShowRisk2 && new Date().getMonth() + 1 > 3 && new Date().getMonth() + 1 < 13">
                                    <div class="bmzd riskItem">第二季度未进行自查自评</div>
                                    <i class="el-icon-error" style="color:#F56C6C"></i>
                                    <el-button class="ckan ignore" type="success" size="mini" round
                                        @click="hulue('zczprisk2')">忽略</el-button>
                                    <el-button class="ckan update" type="success" size="mini" round
                                        @click="toIndex('24')">录入</el-button>
                                </div>
                                <div class="kuang" v-if="statusArr.selfEvaluationAShowRisk3 && new Date().getMonth() + 1 > 6 && new Date().getMonth() + 1 < 13">
                                    <div class="bmzd riskItem">第三季度未进行自查自评</div>
                                    <i class="el-icon-error" style="color:#F56C6C"></i>
                                    <el-button class="ckan ignore" type="success" size="mini" round
                                        @click="hulue('zczprisk3')">忽略</el-button>
                                    <el-button class="ckan update" type="success" size="mini" round
                                        @click="toIndex('24')">录入</el-button>
                                </div>
                                <div class="kuang" v-if="statusArr.selfEvaluationAShowRisk4 && new Date().getMonth() + 1 > 9 && new Date().getMonth() + 1 < 13">
                                    <div class="bmzd riskItem">第四季度未进行自查自评</div>
                                    <i class="el-icon-error" style="color:#F56C6C"></i>
                                    <el-button class="ckan ignore" type="success" size="mini" round
                                        @click="hulue('zczprisk4')">忽略</el-button>
                                    <el-button class="ckan update" type="success" size="mini" round
                                        @click="toIndex('24')">录入</el-button>
                                </div>
                            </el-collapse-item>
                        </el-collapse>
                    </div>
                </div>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>
<script>
import alone from './index'
import reserve from './reserve'
import { detectZoom } from '../../../utils/detectZoom.js';
import {
    getDwxxDB
} from "../../../db/dwxxDb"
import {
    getJgxx
} from "../../../db/zzjgdb";
import { wrap } from 'module';
export default {
    mixins: [alone, reserve],
    data() {
        return {
            isshow1: true,
            sbrisk: 0,
            addCounts: 0,
            sbisAdd: 0,
            zzaddCounts: 0,
            zzisAdd: 0,
            zzrisk: 0,
            smryaddCounts: 0,
            smryisAdd: 0,
            smryrisk: 0,
            jyaddCounts: 0,
            jysbisAdd: 0,
            jysbrisk: 0,
            isshow2: false,
            isshow3: false,
            isshow4: false,
            activeName: 'first',
            activeNames: ['1', '2', '3', '4', '5', '6', '7', '8'],
            checkListDatasShow: false, // 自检查询结果列表是否显示
            sbxxWtCounts: 0,
            csWtCounts: 0,
            ztWtCounts: 0,
            zzWtCounts: 0,
            smryWtCounts: 0,
            jyWtCounts: 0,
            zczpWtCounts: 0,
            dialogVisible: false,
            oldArr: [],
            newArr: [],
            zzjgList: [], // 组织机构树
        }
    },
    computed: {
    },
    watch: {
        // "checkCount.smrywtzs":{
        //     deep:true,
        //     handler(val){
        //         if(val == 0){
        //             this.statusArr.secretPersonnelAllRight1 = true
        //             this.statusArr.secretPersonnelAllRight2 = true
        //             this.statusArr.secretPersonnelAllRight3 = true
        //             this.statusArr.secretPersonnelAllRight4 = true
        //             this.statusArr.secretPersonnelAllRight5 = true
        //             this.statusArr.secretPersonnelShow1 = true
        //             this.statusArr.secretPersonnelLoadingShow = false
        //             localStorage.setItem('allShowState', JSON.stringify(this.statusArr))
        //         }
        //     }
        // },
        // 设备信息全部忽略恢复全部完成
        sbxxWtCounts(newV) {
            if (newV == this.checkCount.sbxxwtzs) {
                this.statusArr.deviceAllRight1 = true
                this.statusArr.deviceAllRight2 = true
                this.statusArr.deviceAllRight3 = true
                this.statusArr.deviceAllRight4 = true
                this.statusArr.deviceAllRight5 = true
                this.statusArr.deviceAllRight6 = true
                this.statusArr.deviceAllRight7 = true
                this.statusArr.deviceAllRight8 = true
                this.statusArr.deviceShow1 = true
                this.statusArr.deviceLoadingShow = false
                localStorage.setItem('allShowState', JSON.stringify(this.statusArr))
                this.sbxxWtCounts = null
                // if(this.sbxxWtCounts == null){
                //     this.checkCount.deviceriskCount = 0
                //     this.checkCount.deviceOptimizeCount = 0
                //     localStorage.setItem('checkCount', JSON.stringify(this.checkCount))
                // }
            }
        },
        csWtCounts(newV) {
            if (newV == this.checkCount.smcswtzs) {
                this.statusArr.secretSitesAllRight1 = true
                this.statusArr.secretSitesAllRight2 = true
                this.statusArr.secretSitesShow1 = true
                this.statusArr.secretSitesLoadingShow = false
                localStorage.setItem('allShowState', JSON.stringify(this.statusArr))
                this.csWtCounts = null
            }
        },
        ztWtCounts(newV) {
            if (newV == this.checkCount.smztwtzs) {
                this.statusArr.carrierAllRight1 = true
                this.statusArr.carrierAllRight2 = true
                this.statusArr.carrierShow1 = true
                this.statusArr.carrierLoadingShow = false
                localStorage.setItem('allShowState', JSON.stringify(this.statusArr))
                this.ztWtCounts = null
            }
        },
        // zzaddCounts(newV) {
        //     if (newV == this.checkCount.zzwtzs) {
        //         this.statusArr.organizationAllRight1 = true
        //         this.statusArr.organizationShow1 = true
        //         this.statusArr.organizationLoadingShow = false
        //         localStorage.setItem('allShowState', JSON.stringify(this.statusArr))
        //     }
        // },
        zzWtCounts(newV) {
            if (newV == this.checkCount.zzwtzs) {
                this.statusArr.organizationAllRight1 = true
                this.statusArr.organizationShow1 = true
                this.statusArr.organizationLoadingShow = false
                localStorage.setItem('allShowState', JSON.stringify(this.statusArr))
                this.ztWtCounts = null
            }
        },
        smryWtCounts(newV) {
            if (newV == this.checkCount.smrywtzs) {
                this.statusArr.secretPersonnelAllRight1 = true
                this.statusArr.secretPersonnelAllRight2 = true
                this.statusArr.secretPersonnelAllRight3 = true
                this.statusArr.secretPersonnelAllRight4 = true
                this.statusArr.secretPersonnelAllRight5 = true
                this.statusArr.secretPersonnelShow1 = true
                this.statusArr.secretPersonnelLoadingShow = false
                localStorage.setItem('allShowState', JSON.stringify(this.statusArr))
                this.smryWtCounts = null
            }
        },
        jyWtCounts(newV) {
            if (newV == this.checkCount.jypxwtzs) {
                this.statusArr.eduTrainAllRight1 = true
                this.statusArr.eduTrainShow1 = true
                this.statusArr.eduTrainLoadingShow = false
                localStorage.setItem('allShowState', JSON.stringify(this.statusArr))
                this.jyWtCounts = null
            }
        },
        zczpWtCounts(newV) {
            if (newV == this.checkCount.zczpwtzs) {
                this.statusArr.selfEvaluationAllRight1 = true
                this.statusArr.selfEvaluationShow1 = true
                this.statusArr.selfEvaluationLoadingShow = false
                localStorage.setItem('allShowState', JSON.stringify(this.statusArr))
                this.zczpWtCounts = null
            }
        },

    },
    
	created(){
		// 各分辨率下修改zoom
		this.$nextTick(()=>{
			const { screen } = require('electron')
			const primaryDisplay = screen.getPrimaryDisplay()
			const { width } = primaryDisplay.workAreaSize
			const m = detectZoom();
			if(width == 1920){
				document.getElementById("yjgzContainer").style.zoom = 1; 
			}
			if(m != 100){
				document.getElementById("yjgzContainer").style.zoom = 100 / Number(m) - 0.08; 
			}
			if(width == 1600){
				document.getElementById("yjgzContainer").style.zoom = 0.8; 
			}else if(width == 1680){
				document.getElementById("yjgzContainer").style.zoom = 0.9; 
			}else if(width == 1440){
				document.getElementById("yjgzContainer").style.zoom = 0.75; 
			}else if(width == 1400){
				document.getElementById("yjgzContainer").style.zoom = 0.73; 
			}else if(width == 1366){
				document.getElementById("yjgzContainer").style.zoom = 0.65; 
			}else if(width == 1360){
				document.getElementById("yjgzContainer").style.zoom = 0.64; 
			}else if(width == 1024){
				document.getElementById("yjgzContainer").style.zoom = 0.52; 
			}
		})
	},
    methods: {
        // 弹框-组织机构树形图
        zzjgDialog() {
            if(this.zzyhIsPerfectShow == true){
                this.dialogVisible = false
                this.$message({
                    message: '未建立组织机构，请完善组织机构',
                    type: 'warning'
                });
            }else{
                this.dialogVisible = true
                this.$nextTick(() => {
                    this.getZzjgPic('tk')
                })
            }
            
        },
        mathMax(arr) {
            var max = arr[0];
            for(var i = 1; i < arr.length; i++) {
                if(arr[i] > max) {
                max = arr[i];
                }
            }
            return max;
        },
        // 组织结构树
        filters(arr) {
            let newArrUpdate = this.oldArr.map(item => ({
                ...item,
                name: item.label,
            }))
            let lengthArr = []
            newArrUpdate.forEach((oldItem) => {
                lengthArr.push(oldItem.name.length)
            })
            let paddingTopCount
            lengthArr.splice(0,1)
            this.newArr = arr.filter((item, itemIndex) => {
                newArrUpdate.forEach((oldItem, oldIndex) => {
                    oldItem.collapsed = false
                    if(((this.mathMax(lengthArr)) * 25 - oldItem.name.length * 25) / 2 == 0){
                        paddingTopCount = 15
                    }else{
                        paddingTopCount = ((this.mathMax(lengthArr)) * 25 - oldItem.name.length * 25) / 2 + 15
                    }
                    if(oldItem.bmflag == '是'){
                        oldItem.label = {
                            padding: [paddingTopCount ,15,15,15],
                            fontSize: 18,
                            height: (this.mathMax(lengthArr)) * 25 - ((this.mathMax(lengthArr)) * 25 - oldItem.name.length * 25) / 2,
                            lineHeight: 25,
                            formatter(value) {
                                return `{b|${value.name.split('').join('\n')}}`
                            },
                            backgroundColor: '#2196F3',
                            borderColor: '#2196F3',
                            borderWidth: 0.5,
                            rich: {
                                b: {
                                    fontSize: 14,
                                    color: 'yellow'
                                }
                            },
                            borderRadius: 4,
                        }
                    }else{
                        oldItem.label = {
                            padding: [paddingTopCount ,15,15,15],
                            fontSize: 18,
                            height: (this.mathMax(lengthArr)) * 25 - ((this.mathMax(lengthArr)) * 25 - oldItem.name.length * 25) / 2,
                            lineHeight: 25,
                            formatter(value) {
                                return `{b|${value.name.split('').join('\n')}}`
                            },
                            backgroundColor: '#2196F3',
                            borderColor: '#2196F3',
                            borderWidth: 0.5,
                            rich: {
                                b: {
                                    fontSize: 14,
                                    color: '#ffffff',
                                }
                            },
                            borderRadius: 4,
                        }
                    }
                    if (oldItem.fbmm == item.bmm) { //有子节点，oldItem是item的子项
                        item.children.push(oldItem)
                    }
                    if (oldIndex == this.oldArr.length - 1) { //内层循环最后一项处理完毕
                        if (item.children && item.children.length) {//当前层级有子项，子项不为空
                            this.filters(item.children); //调用递归过滤函数
                        }
                    }
                });
                return true //返回过滤后的新数组赋值给this.newArr
            })
            return this.newArr
        },
        // 获取组织结构图echarts
        getZzjgPic(type) {
            this.oldArr = getJgxx()
            let arr = []
            this.oldArr.forEach((item1) => {
                item1.children = []
                if (item1.bmjb == 0) {
                    arr.push(item1)
                }
            });
            this.zzjgList = this.filters(arr)[0].children
            let list = getDwxxDB()
            let dwmc = list.list[0].dwmc
            let lastZzjgList = []
            lastZzjgList[0] = {
                name: dwmc,
                collapsed: false,
                label: {
					// position: [45, 90],
                    padding: 15,
                    fontSize: 20,
                    backgroundColor: '#2196F3',
                    borderColor: '#2196F3',
                    borderWidth: 0.5,
                    borderRadius: 4,
                    color:'#ffffff'
                },
                children: this.zzjgList
            }
            let myChartXz
            let myChart
            if (type == 'xz') {
                myChartXz = this.$echarts.init(
                    document.getElementById("zzjgpicXz")
                )
            } else {
                myChart = this.$echarts.init(
                    document.getElementById("zzjgt")
                )
            }
            let option = {
                tooltip: {
                    trigger: 'item',
                    triggerOn: 'mousemove'
                },
                silent:true,
                series: [
                    {
                        type: 'tree',
                        edgeShape: 'polyline', // 链接线是折现还是曲线
                        orient: 'TB',
                        roam: true,
                        // zoom: 1.5,
                        data: lastZzjgList,
                        top: '5%',
                        // left: '5%',
                        // right: '5%',
                        // bottom: '10%',
                        symbol: 'none',
                        initialTreeDepth: 10,
						label: {
							position: [0, 0],
							verticalAlign: 'middle',
							align: 'middle',
						},
                        lineStyle:{
                            color:'rgba(0,0,0,0.2)',
                            type:'solid',
                            width:3
                        },
						leaves: {
							label: {
							    position: [0, 0],
								verticalAlign: 'middle',
								align: 'middle'
							}
						},
                        emphasis: {
                            focus: 'descendant'
                        },
                        expandAndCollapse: true,
                        animationDuration: 550,
                        animationDurationUpdate: 750
                    }
                ]
            }
            if (type == 'xz') {
                myChartXz.setOption(option)
                myChartXz.resize()
            } else {
                myChart.setOption(option)
                myChart.resize()
            }
        },
        // 页面跳转
        toIndex(type) {
            switch (type) {
                case '1':
                    this.$router.push('/tzglsy?activeName=/bmzd')
                    break
                case '2s':
                    this.$router.push('/tzglsy?activeName=/zzjg')
                    break
                case '3':
                    this.$router.push('/tzglsy?activeName=/smgwgl')
                    break
                case '4':
                    this.$router.push('/tzglsy?activeName=/smry')
                    break
                case '5':
                    this.$router.push('/tzglsy?activeName=/ryxz')
                    break
                case '6':
                    this.$router.push('/tzglsy?activeName=/gwbg')
                    break
                case '7':
                    this.$router.push('/tzglsy?activeName=/lglz')
                    break
                case '8':
                    this.$router.push('/tzglsy?activeName=/csgl')
                    break
                case '9':
                    this.$router.push('/tzglsy?activeName=/csbg')
                    break
                case '10':
                    this.$router.push('/tzglsy?activeName=/smjsj')
                    break
                case '11':
                    this.$router.push('/tzglsy?activeName=/smydccjz')
                    break
                case '12':
                    this.$router.push('/tzglsy?activeName=/smwlsb')
                    break
                case '13':
                    this.$router.push('/tzglsy?activeName=/smbgzdhsb')
                    break
                case '14':
                    this.$router.push('/tzglsy?activeName=/aqcp')
                    break
                case '15':
                    this.$router.push('/tzglsy?activeName=/smzttz')
                    break
                case '20':
                    this.$router.push('/tzglsy?activeName=/pxqd')
                    break
                case '21':
                    this.$router.push('/tzglsy?activeName=/fsmjsj')
                    break
                case '22':
                    this.$router.push('/tzglsy?activeName=/fsmbgzdhsb')
                    break
                case '23':
                    this.$router.push('/tzglsy?activeName=/fmwlsb')
                    break
                case '24':
                    this.$router.push('/tzglsy?activeName=/zczpls')
                    break
                default:
                    this.$router.push('/tzglsy')
                    break
            }
        },
        // 自查自评界面跳转
        toIndex2() {
            this.$router.push('/zczpls')
        },
        //获取当前点击下标
        tabsCode() {
            // 比较结果activeName的值
            if (this.$route.query.activeName != null) {
                this.activeName = this.$route.query.activeName;
            }
        },
        handleClick(tab, event) {
            // console.log(tab, event);
        },
        // 获取echarts分数图
        getLoadEcharts() {
            let colorRange
            let labelColor
            if(this.allCounts < 60){
                colorRange = ['rgba(243,125,68,1)', 'rgba(243,125,68,0.4)']
                labelColor = 'rgba(239,115,55,1)'
            }else {
                colorRange = ['rgba(53,190,228,1)', 'rgba(26,132,220,0.68)']
                labelColor = '#ffffff'
            }
            var myChart = this.$echarts.init(
                document.getElementById("echart")
            );
            var option = {
                series: [{
                    type: 'liquidFill',
                    radius: '90%',
                    data: [this.allCounts / 100, this.allCounts / 100 - 0.1],
                    color: colorRange,
                    label: {
                        normal: {
                            color: '#ffffff',
                            insideColor: 'transparent',
                            formatter: this.allCounts + "分", //显示文本
							// position: [60, 35],
                            textStyle: {
                                fontSize: 30,
                                fontWeight: 'bold',
                                fontFamily: 'Microsoft YaHei'
                            }
                        }
                    },
                    outline: {
                        show: true,
                        borderDistance: 5,
                        itemStyle: {
                            borderColor: 'rgba(0,0,0,0)',
                            borderWidth: 2
                        }
                    },
                    backgroundStyle: {
                        // color: 'rgba(67,209,100,1)' 
                    }
                }]
            };
            myChart.setOption(option);
        },
        // 单条忽略功能
        hulue(name) {
            switch (name) {
                // 保密制度
                case 'bmopt1':
                    this.statusArr.proJurisdictionShowOptimize1 = false
                    this.statusArr.proJurisdictionShow2 = false
                    this.allCounts = this.allCounts + 5
                    this.statusArr.proJurisdictionAllRight1 = true
                    this.statusArr.proJurisdictionAllRight2 = true
                    this.statusArr.proJurisdictionShow1 = true
                    this.checkCount.proJurisdictionOptimizeCount = this.checkCount.proJurisdictionOptimizeCount - 1
                    break
                case 'bmrisk1':
                    this.statusArr.proJurisdictionShowRisk1 = false
                    this.statusArr.proJurisdictionShow3 = false
                    this.allCounts = this.allCounts + 12
                    this.statusArr.proJurisdictionAllRight1 = true
                    this.statusArr.proJurisdictionAllRight2 = true
                    this.statusArr.proJurisdictionShow1 = true
                    this.checkCount.proJurisdictionriskCount = this.checkCount.proJurisdictionriskCount - 1
                    break
                // 场所信息
                case 'csrisk1':
                    this.statusArr.secretSitesShowRisk1 = false
                    this.statusArr.secretSitesShow3 = false
                    this.allCounts = this.allCounts + 12
                    this.statusArr.secretSitesAllRight1 = true
                    this.statusArr.secretSitesAllRight2 = true
                    this.statusArr.secretSitesShow1 = true
                    this.csWtCounts = this.csWtCounts + 1
                    this.checkCount.secretSitesriskCount = this.checkCount.secretSitesriskCount - 1
                    break
                case 'csopt1':
                    this.statusArr.secretSitesShowOptimize1 = false
                    this.statusArr.secretSitesShow2 = false
                    this.allCounts = this.allCounts + 1
                    this.csWtCounts = this.csWtCounts - 1
                    this.checkCount.secretSitesOptimizeCount = this.checkCount.secretSitesOptimizeCount - 1
                    break
                case 'csopt2':
                    this.statusArr.secretSitesShowOptimize2 = false
                    this.statusArr.secretSitesShow2 = false
                    this.allCounts = this.allCounts + 1
                    this.csWtCounts = this.csWtCounts + 1
                    this.checkCount.secretSitesOptimizeCount = this.checkCount.secretSitesOptimizeCount - 1
                    break
                // 载体信息
                case 'ztrisk1':
                    this.statusArr.carrierShowRisk1 = false
                    this.statusArr.carrierShow3 = false
                    this.allCounts = this.allCounts + 12
                    this.ztWtCounts = this.ztWtCounts + 1
                    this.checkCount.carrierriskCount = this.checkCount.carrierriskCount - 1
                    break
                case 'ztopt1':
                    this.statusArr.carrierShowOptimize1 = false
                    this.statusArr.carrierShow2 = false
                    this.allCounts = this.allCounts + 5
                    this.ztWtCounts = this.ztWtCounts + 1
                    this.checkCount.carrierOptimizeCount = this.checkCount.carrierOptimizeCount - 1
                    break
                // 自查自评
                case 'zczprisk1':
                    this.statusArr.selfEvaluationAShowRisk1 = false
                    this.statusArr.selfEvaluationShow3 = false
                    this.allCounts = this.allCounts + 3
                    this.zczpWtCounts = this.zczpWtCounts + 1
                    this.checkCount.selfEvaluationriskCount = this.checkCount.selfEvaluationriskCount - 1
                    break
                case 'zczprisk2':
                    this.statusArr.selfEvaluationAShowRisk2 = false
                    this.statusArr.selfEvaluationShow3 = false
                    this.allCounts = this.allCounts + 3
                    this.zczpWtCounts = this.zczpWtCounts + 1
                    this.checkCount.selfEvaluationriskCount = this.checkCount.selfEvaluationriskCount - 1
                    break
                case 'zczprisk3':
                    this.statusArr.selfEvaluationAShowRisk3 = false
                    this.statusArr.selfEvaluationShow3 = false
                    this.allCounts = this.allCounts + 3
                    this.zczpWtCounts = this.zczpWtCounts + 1
                    this.checkCount.selfEvaluationriskCount = this.checkCount.selfEvaluationriskCount - 1
                    break
                case 'zczprisk4':
                    this.statusArr.selfEvaluationAShowRisk4 = false
                    this.statusArr.selfEvaluationShow3 = false
                    this.allCounts = this.allCounts + 3
                    this.zczpWtCounts = this.zczpWtCounts + 1
                    this.checkCount.selfEvaluationriskCount = this.checkCount.selfEvaluationriskCount - 1
                    break
                // 组织管理
                case 'zzrisk1':
                    this.statusArr.organizationShowRisk1 = false
                    this.statusArr.organizationShow3 = false
                    this.zzaddCounts = this.zzaddCounts + 10
                    this.zzWtCounts = this.zzWtCounts + 1
                    this.checkCount.organizationriskCount = this.checkCount.organizationriskCount - 1
                    if (this.zzisAdd == 1) {
                        this.allCounts = this.allCounts
                    } else if (this.zzaddCounts < 10 || this.zzaddCounts == 10 && this.zzisAdd == 0) {
                        this.allCounts = this.allCounts + 10
                        this.zzrisk = this.zzrisk + 10
                    } else if (this.zzaddCounts > 10) {
                        this.allCounts = this.allCounts + (10 - this.zzrisk)
                        this.zzisAdd = 1
                    }
                    break
                case 'zzrisk2':
                    this.statusArr.organizationShowRisk2 = false
                    this.statusArr.organizationShow3 = false
                    this.zzaddCounts = this.zzaddCounts + 5
                    this.zzWtCounts = this.zzWtCounts + 1
                    this.checkCount.organizationriskCount = this.checkCount.organizationriskCount - 1
                    if (this.zzisAdd == 1) {
                        this.allCounts = this.allCounts
                    } else if (this.zzaddCounts < 10 || this.zzaddCounts == 10 && this.zzisAdd == 0) {
                        this.allCounts = this.allCounts + 5
                        this.zzrisk = this.zzrisk + 5
                    } else if (this.zzaddCounts > 10) {
                        this.allCounts = this.allCounts + (10 - this.zzrisk)
                        this.zzisAdd = 1
                    }
                    break
                case 'zzrisk3':
                    this.statusArr.organizationShowRisk3 = false
                    this.statusArr.organizationShow3 = false
                    this.zzaddCounts = this.zzaddCounts + 5
                    this.zzWtCounts = this.zzWtCounts + 1
                    this.checkCount.organizationriskCount = this.checkCount.organizationriskCount - 1
                    if (this.zzisAdd == 1) {
                        this.allCounts = this.allCounts
                    } else if (this.zzaddCounts < 10 || this.zzaddCounts == 10 && this.zzisAdd == 0) {
                        this.allCounts = this.allCounts + 5
                        this.zzrisk = this.zzrisk + 5
                    } else if (this.zzaddCounts > 10) {
                        this.allCounts = this.allCounts + (10 - this.zzrisk)
                        this.zzisAdd = 1
                    }
                    break
                case 'zzopt1':
                    this.statusArr.organizationShowOptimize1 = false
                    this.statusArr.organizationShow2 = false
                    this.zzaddCounts = this.zzaddCounts + 2
                    this.zzWtCounts = this.zzWtCounts + 1
                    this.checkCount.organizationOptimizeCount = this.checkCount.organizationOptimizeCount - 1
                    if (this.zzisAdd == 1) {
                        this.allCounts = this.allCounts
                    } else if (this.zzaddCounts < 10 || this.zzaddCounts == 10 && this.zzisAdd == 0) {
                        this.allCounts = this.allCounts + 2
                        this.zzrisk = this.zzrisk + 2
                    } else if (this.zzaddCounts > 10) {
                        this.allCounts = this.allCounts + (10 - this.zzrisk)
                        this.zzisAdd = 1
                    }
                    break
                // 涉密人员
                case 'smryrisk1':
                    this.statusArr.secretPersonnelShowRisk1 = false
                    this.statusArr.secretPersonnelShow3 = false
                    this.smryaddCounts = this.smryaddCounts + 5
                    this.smryWtCounts = this.smryWtCounts + 1
                    this.checkCount.secretPersonnelriskCount = this.checkCount.secretPersonnelriskCount - 1
                    if (this.smryisAdd == 1) {
                        this.allCounts = this.allCounts
                    } else if (this.smryaddCounts < 10 || this.smryaddCounts == 10 && this.smryisAdd == 0) {
                        this.allCounts = this.allCounts + 5
                        this.smryrisk = this.smryrisk + 5
                    } else if (this.smryaddCounts > 10) {
                        this.allCounts = this.allCounts + (10 - this.smryrisk)
                        this.smryisAdd = 1
                    }
                    break
                case 'smryrisk2':
                    this.statusArr.secretPersonnelShowRisk2 = false
                    this.statusArr.secretPersonnelShow3 = false
                    this.smryWtCounts = this.smryWtCounts + 1
                    this.smryaddCounts = this.smryaddCounts + 10
                    this.checkCount.secretPersonnelriskCount = this.checkCount.secretPersonnelriskCount - 1
                    if (this.smryisAdd == 1) {
                        this.allCounts = this.allCounts
                    } else if (this.smryaddCounts < 10 || this.smryaddCounts == 10 && this.smryisAdd == 0) {
                        this.allCounts = this.allCounts + 10
                        this.smryrisk = this.smryrisk + 10
                    } else if (this.smryaddCounts > 10) {
                        this.allCounts = this.allCounts + (10 - this.smryrisk)
                        this.smryisAdd = 1
                    }
                    break
                case 'smryrisk3':
                    this.statusArr.secretPersonnelShowRisk3 = false
                    this.statusArr.secretPersonnelShow3 = false
                    this.smryWtCounts = this.smryWtCounts + 1
                    this.smryaddCounts = this.smryaddCounts + 10
                    this.checkCount.secretPersonnelriskCount = this.checkCount.secretPersonnelriskCount - 1
                    if (this.smryisAdd == 1) {
                        this.allCounts = this.allCounts
                    } else if (this.smryaddCounts < 10 || this.smryaddCounts == 10 && this.smryisAdd == 0) {
                        this.allCounts = this.allCounts + 10
                        this.smryrisk = this.smryrisk + 10
                    } else if (this.smryaddCounts > 10) {
                        this.allCounts = this.allCounts + (10 - this.smryrisk)
                        this.smryisAdd = 1
                    }
                    break
                case 'smryopt1':
                    this.statusArr.secretPersonnelShowOptimize1 = false
                    this.statusArr.secretPersonnelShow2 = false
                    this.smryaddCounts = this.smryaddCounts + 2
                    this.smryWtCounts = this.smryWtCounts + 1
                    this.checkCount.secretPersonnelOptimizeCount = this.checkCount.secretPersonnelOptimizeCount - 1
                    if (this.smryisAdd == 1) {
                        this.allCounts = this.allCounts
                    } else if (this.smryaddCounts < 10 || this.smryaddCounts == 10 && this.smryisAdd == 0) {
                        this.allCounts = this.allCounts + 2
                        this.smryrisk = this.smryrisk + 2
                    } else if (this.smryaddCounts > 10) {
                        this.allCounts = this.allCounts + (10 - this.smryrisk)
                        this.smryisAdd = 1
                    }
                    break
                case 'smryopt2':
                    this.statusArr.secretPersonnelShowOptimize2 = false
                    this.statusArr.secretPersonnelShow2 = false
                    this.smryaddCounts = this.smryaddCounts + 2
                    this.smryWtCounts = this.smryWtCounts + 1
                    this.checkCount.secretPersonnelOptimizeCount = this.checkCount.secretPersonnelOptimizeCount - 1
                    if (this.smryisAdd == 1) {
                        this.allCounts = this.allCounts
                    } else if (this.smryaddCounts < 10 || this.smryaddCounts == 10 && this.smryisAdd == 0) {
                        this.allCounts = this.allCounts + 2
                        this.smryrisk = this.smryrisk + 2
                    } else if (this.smryaddCounts > 10) {
                        this.allCounts = this.allCounts + (10 - this.smryrisk)
                        this.smryisAdd = 1
                    }
                    break
                case 'smryopt3':
                    this.statusArr.secretPersonnelShowOptimize3 = false
                    this.statusArr.secretPersonnelShow2 = false
                    this.smryaddCounts = this.smryaddCounts + 5
                    this.smryWtCounts = this.smryWtCounts + 1
                    this.checkCount.secretPersonnelOptimizeCount = this.checkCount.secretPersonnelOptimizeCount - 1
                    if (this.smryisAdd == 1) {
                        this.allCounts = this.allCounts
                    } else if (this.smryaddCounts < 10 || this.smryaddCounts == 10 && this.smryisAdd == 0) {
                        this.allCounts = this.allCounts + 5
                        this.smryrisk = this.smryrisk + 5
                    } else if (this.smryaddCounts > 10) {
                        this.allCounts = this.allCounts + (10 - this.smryrisk)
                        this.smryisAdd = 1
                    }
                    break
                case 'smryopt4':
                    this.statusArr.secretPersonnelShowOptimize4 = false
                    this.statusArr.secretPersonnelShow2 = false
                    this.smryaddCounts = this.smryaddCounts + 5
                    this.smryWtCounts = this.smryWtCounts + 1
                    this.checkCount.secretPersonnelOptimizeCount = this.checkCount.secretPersonnelOptimizeCount - 1
                    if (this.smryisAdd == 1) {
                        this.allCounts = this.allCounts
                    } else if (this.smryaddCounts < 10 || this.smryaddCounts == 10 && this.smryisAdd == 0) {
                        this.allCounts = this.allCounts + 5
                        this.smryrisk = this.smryrisk + 5
                    } else if (this.smryaddCounts > 10) {
                        this.allCounts = this.allCounts + (10 - this.smryrisk)
                        this.smryisAdd = 1
                    }
                    break
                case 'smryopt5':
                    this.statusArr.secretPersonnelShowOptimize5 = false
                    this.statusArr.secretPersonnelShow2 = false
                    this.smryaddCounts = this.smryaddCounts + 5
                    this.smryWtCounts = this.smryWtCounts + 1
                    this.checkCount.secretPersonnelOptimizeCount = this.checkCount.secretPersonnelOptimizeCount - 1
                    if (this.smryisAdd == 1) {
                        this.allCounts = this.allCounts
                    } else if (this.smryaddCounts < 10 || this.smryaddCounts == 10 && this.smryisAdd == 0) {
                        this.allCounts = this.allCounts + 5
                        this.smryrisk = this.smryrisk + 5
                    } else if (this.smryaddCounts > 10) {
                        this.allCounts = this.allCounts + (10 - this.smryrisk)
                        this.smryisAdd = 1
                    }
                    break
                // 设备信息
                case 'sbrisk1':
                    this.statusArr.deviceShowRisk1 = false
                    this.statusArr.deviceShow3 = false
                    this.addCounts = this.addCounts + 10
                    this.sbxxWtCounts = this.sbxxWtCounts + 1
                    this.checkCount.deviceriskCount = this.checkCount.deviceriskCount - 1
                    if (this.sbisAdd == 1) {
                        this.allCounts = this.allCounts
                    } else if (this.addCounts < 23 || this.addCounts == 23 && this.sbisAdd == 0) {
                        this.allCounts = this.allCounts + 10
                        this.sbrisk = this.sbrisk + 10
                    } else if (this.addCounts > 23) {
                        this.allCounts = this.allCounts + (23 - this.sbrisk)
                        this.sbisAdd = 1
                    }
                    break
                case 'sbrisk2':
                    this.statusArr.deviceShowRisk2 = false
                    this.statusArr.deviceShow3 = false
                    this.addCounts = this.addCounts + 10
                    this.sbxxWtCounts = this.sbxxWtCounts + 1
                    this.checkCount.deviceriskCount = this.checkCount.deviceriskCount - 1
                    if (this.sbisAdd == 1) {
                        this.allCounts = this.allCounts
                    } else if (this.addCounts < 23 || this.addCounts == 23 && this.sbisAdd == 0) {
                        this.allCounts = this.allCounts + 10
                        this.sbrisk = this.sbrisk + 10
                    } else if (this.addCounts > 23) {
                        this.allCounts = this.allCounts + (23 - this.sbrisk)
                        this.sbisAdd = 1
                    }
                    break
                case 'sbrisk3':
                    this.statusArr.deviceShowRisk3 = false
                    this.statusArr.deviceShow3 = false
                    this.addCounts = this.addCounts + 1
                    this.sbxxWtCounts = this.sbxxWtCounts + 1
                    this.checkCount.deviceriskCount = this.checkCount.deviceriskCount - 1
                    if (this.sbisAdd == 1) {
                        this.allCounts = this.allCounts
                    } else if (this.addCounts < 23 || this.addCounts == 23 && this.sbisAdd == 0) {
                        this.allCounts = this.allCounts + 1
                        this.sbrisk = this.sbrisk + 1
                    } else if (this.addCounts > 23) {
                        this.allCounts = this.allCounts + (23 - this.sbrisk)
                        this.sbisAdd = 1
                    }
                    break
                case 'sbrisk4':
                    this.statusArr.deviceShowRisk4 = false
                    this.statusArr.deviceShow3 = false
                    this.addCounts = this.addCounts + 1
                    this.sbxxWtCounts = this.sbxxWtCounts + 1
                    this.checkCount.deviceriskCount = this.checkCount.deviceriskCount - 1
                    if (this.sbisAdd == 1) {
                        this.allCounts = this.allCounts
                    } else if (this.addCounts < 23 || this.addCounts == 23 && this.sbisAdd == 0) {
                        this.allCounts = this.allCounts + 1
                        this.sbrisk = this.sbrisk + 1
                    } else if (this.addCounts > 23) {
                        this.allCounts = this.allCounts + (23 - this.sbrisk)
                        this.sbisAdd = 1
                    }
                    break
                case 'sbrisk5':
                    this.statusArr.deviceShowRisk5 = false
                    this.statusArr.deviceShow3 = false
                    this.addCounts = this.addCounts + 1
                    this.sbxxWtCounts = this.sbxxWtCounts + 1
                    this.checkCount.deviceriskCount = this.checkCount.deviceriskCount - 1
                    if (this.sbisAdd == 1) {
                        this.allCounts = this.allCounts
                    } else if (this.addCounts < 23 || this.addCounts == 23 && this.sbisAdd == 0) {
                        this.allCounts = this.allCounts + 1
                        this.sbrisk = this.sbrisk + 1
                    } else if (this.addCounts > 23) {
                        this.allCounts = this.allCounts + (23 - this.sbrisk)
                        this.sbisAdd = 1
                    }
                    break
                case 'sbrisk6':
                    this.statusArr.deviceShowRisk6 = false
                    this.statusArr.deviceShow3 = false
                    this.addCounts = this.addCounts + 1
                    this.sbxxWtCounts = this.sbxxWtCounts + 1
                    this.checkCount.deviceriskCount = this.checkCount.deviceriskCount - 1
                    if (this.sbisAdd == 1) {
                        this.allCounts = this.allCounts
                    } else if (this.addCounts < 23 || this.addCounts == 23 && this.sbisAdd == 0) {
                        this.allCounts = this.allCounts + 1
                        this.sbrisk = this.sbrisk + 1
                    } else if (this.addCounts > 23) {
                        this.allCounts = this.allCounts + (23 - this.sbrisk)
                        this.sbisAdd = 1
                    }
                    break
                case 'sbrisk7':
                    this.statusArr.deviceShowRisk7 = false
                    this.statusArr.deviceShow3 = false
                    this.addCounts = this.addCounts + 1
                    this.sbxxWtCounts = this.sbxxWtCounts + 1
                    this.checkCount.deviceriskCount = this.checkCount.deviceriskCount - 1
                    if (this.sbisAdd == 1) {
                        this.allCounts = this.allCounts
                    } else if (this.addCounts < 23 || this.addCounts == 23 && this.sbisAdd == 0) {
                        this.allCounts = this.allCounts + 1
                        this.sbrisk = this.sbrisk + 1
                    } else if (this.addCounts > 23) {
                        this.allCounts = this.allCounts + (23 - this.sbrisk)
                        this.sbisAdd = 1
                    }
                    break
                case 'sbrisk8':
                    this.statusArr.deviceShowRisk8 = false
                    this.statusArr.deviceShow3 = false
                    this.addCounts = this.addCounts + 10
                    this.sbxxWtCounts = this.sbxxWtCounts + 1
                    this.checkCount.deviceriskCount = this.checkCount.deviceriskCount - 1
                    if (this.sbisAdd == 1) {
                        this.allCounts = this.allCounts
                    } else if (this.addCounts < 23 || this.addCounts == 23 && this.sbisAdd == 0) {
                        this.allCounts = this.allCounts + 10
                        this.sbrisk = this.sbrisk + 10
                    } else if (this.addCounts > 23) {
                        this.allCounts = this.allCounts + (23 - this.sbrisk)
                        this.sbisAdd = 1
                    }
                    break
                case 'sbopt1':
                    this.statusArr.deviceShowOptimize1 = false
                    this.statusArr.deviceShow2 = false
                    this.addCounts = this.addCounts + 1
                    this.sbxxWtCounts = this.sbxxWtCounts + 1
                    this.checkCount.deviceOptimizeCount = this.checkCount.deviceOptimizeCount - 1
                    if (this.sbisAdd == 1) {
                        this.allCounts = this.allCounts
                    } else if (this.addCounts < 23 || this.addCounts == 23 && this.sbisAdd == 0) {
                        this.allCounts = this.allCounts + 1
                        this.sbrisk = this.sbrisk + 1
                    } else if (this.addCounts > 23) {
                        this.allCounts = this.allCounts + (23 - this.sbrisk)
                        this.sbisAdd = 1
                    }
                    break
                case 'sbopt2':
                    this.statusArr.deviceShowOptimize2 = false
                    this.statusArr.deviceShow2 = false
                    this.addCounts = this.addCounts + 1
                    this.sbxxWtCounts = this.sbxxWtCounts + 1
                    this.checkCount.deviceOptimizeCount = this.checkCount.deviceOptimizeCount - 1
                    if (this.sbisAdd == 1) {
                        this.allCounts = this.allCounts
                    } else if (this.addCounts < 23 || this.addCounts == 23 && this.sbisAdd == 0) {
                        this.allCounts = this.allCounts + 1
                        this.sbrisk = this.sbrisk + 1
                    } else if (this.addCounts > 23) {
                        this.allCounts = this.allCounts + (23 - this.sbrisk)
                        this.sbisAdd = 1
                    }
                    break
                case 'sbopt3':
                    this.statusArr.deviceShowOptimize3 = false
                    this.statusArr.deviceShow2 = false
                    this.addCounts = this.addCounts + 1
                    this.sbxxWtCounts = this.sbxxWtCounts + 1
                    this.checkCount.deviceOptimizeCount = this.checkCount.deviceOptimizeCount - 1
                    if (this.sbisAdd == 1) {
                        this.allCounts = this.allCounts
                    } else if (this.addCounts < 23 || this.addCounts == 23 && this.sbisAdd == 0) {
                        this.allCounts = this.allCounts + 1
                        this.sbrisk = this.sbrisk + 1
                    } else if (this.addCounts > 23) {
                        this.allCounts = this.allCounts + (23 - this.sbrisk)
                        this.sbisAdd = 1
                    }
                    break
                case 'sbopt4':
                    this.statusArr.deviceShowOptimize4 = false
                    this.statusArr.deviceShow2 = false
                    this.addCounts = this.addCounts + 1
                    this.sbxxWtCounts = this.sbxxWtCounts + 1
                    this.checkCount.deviceOptimizeCount = this.checkCount.deviceOptimizeCount - 1
                    if (this.sbisAdd == 1) {
                        this.allCounts = this.allCounts
                    } else if (this.addCounts < 23 || this.addCounts == 23 && this.sbisAdd == 0) {
                        this.allCounts = this.allCounts + 1
                        this.sbrisk = this.sbrisk + 1
                    } else if (this.addCounts > 23) {
                        this.allCounts = this.allCounts + (23 - this.sbrisk)
                        this.sbisAdd = 1
                    }
                    break
                case 'sbopt5':
                    this.statusArr.deviceShowOptimize5 = false
                    this.statusArr.deviceShow2 = false
                    this.addCounts = this.addCounts + 1
                    this.sbxxWtCounts = this.sbxxWtCounts + 1
                    this.checkCount.deviceOptimizeCount = this.checkCount.deviceOptimizeCount - 1
                    if (this.sbisAdd == 1) {
                        this.allCounts = this.allCounts
                    } else if (this.addCounts < 23 || this.addCounts == 23 && this.sbisAdd == 0) {
                        this.allCounts = this.allCounts + 1
                        this.sbrisk = this.sbrisk + 1
                    } else if (this.addCounts > 23) {
                        this.allCounts = this.allCounts + (23 - this.sbrisk)
                        this.sbisAdd = 1
                    }
                    break
                case 'sbopt6':
                    this.statusArr.deviceShowOptimize6 = false
                    this.statusArr.deviceShow2 = false
                    this.addCounts = this.addCounts + 1
                    this.sbxxWtCounts = this.sbxxWtCounts + 1
                    this.checkCount.deviceOptimizeCount = this.checkCount.deviceOptimizeCount - 1
                    if (this.sbisAdd == 1) {
                        this.allCounts = this.allCounts
                    } else if (this.addCounts < 23 || this.addCounts == 23 && this.sbisAdd == 0) {
                        this.allCounts = this.allCounts + 1
                        this.sbrisk = this.sbrisk + 1
                    } else if (this.addCounts > 23) {
                        this.allCounts = this.allCounts + (23 - this.sbrisk)
                        this.sbisAdd = 1
                    }
                    break
                case 'sbopt7':
                    this.statusArr.deviceShowOptimize7 = false
                    this.statusArr.deviceShow2 = false
                    this.addCounts = this.addCounts + 1
                    this.sbxxWtCounts = this.sbxxWtCounts + 1
                    this.checkCount.deviceOptimizeCount = this.checkCount.deviceOptimizeCount - 1
                    if (this.sbisAdd == 1) {
                        this.allCounts = this.allCounts
                    } else if (this.addCounts < 23 || this.addCounts == 23 && this.sbisAdd == 0) {
                        this.allCounts = this.allCounts + 1
                        this.sbrisk = this.sbrisk + 1
                    } else if (this.addCounts > 23) {
                        this.allCounts = this.allCounts + (23 - this.sbrisk)
                        this.sbisAdd = 1
                    }
                    break
                case 'sbopt8':
                    this.statusArr.deviceShowOptimize8 = false
                    this.statusArr.deviceShow2 = false
                    this.addCounts = this.addCounts + 1
                    this.sbxxWtCounts = this.sbxxWtCounts + 1
                    this.checkCount.deviceOptimizeCount = this.checkCount.deviceOptimizeCount - 1
                    if (this.sbisAdd == 1) {
                        this.allCounts = this.allCounts
                    } else if (this.addCounts < 23 || this.addCounts == 23 && this.sbisAdd == 0) {
                        this.allCounts = this.allCounts + 1
                        this.sbrisk = this.sbrisk + 1
                    } else if (this.addCounts > 23) {
                        this.allCounts = this.allCounts + (23 - this.sbrisk)
                        this.sbisAdd = 1
                    }
                    break
                // 教育
                case 'jyrisk1':
                    this.statusArr.eduTrainShowRisk1 = false
                    this.statusArr.eduTrainShow3 = false
                    this.addCounts = this.addCounts + 9
                    this.jyWtCounts = this.jyWtCounts + 1
                    this.checkCount.eduTrainriskCount = this.checkCount.eduTrainriskCount - 1
                    if (this.sbisAdd == 1) {
                        this.allCounts = this.allCounts
                    } else if (this.addCounts < 9 || this.addCounts == 9 && this.sbisAdd == 0) {
                        this.allCounts = this.allCounts + 9
                        this.sbrisk = this.sbrisk + 9
                    } else if (this.addCounts > 9) {
                        this.allCounts = this.allCounts + (9 - this.sbrisk)
                        this.sbisAdd = 1
                    }
                    break
                case 'jyopt1':
                    this.statusArr.eduTrainShowOptimize1 = false
                    this.statusArr.eduTrainShow2 = false
                    this.jyaddCounts = this.jyaddCounts + 5
                    this.jyWtCounts = this.jyWtCounts + 1
                    this.checkCount.eduTrainOptimizeCount = this.checkCount.eduTrainOptimizeCount - 1
                    if (this.jysbisAdd == 1) {
                        this.allCounts = this.allCounts
                    } else if (this.jyaddCounts < 9 || this.jyaddCounts == 9 && this.jysbisAdd == 0) {
                        this.allCounts = this.allCounts + 5
                        this.jysbrisk = this.jysbrisk + 5
                    } else if (this.jyaddCounts > 9) {
                        this.allCounts = this.allCounts + (9 - this.jysbrisk)
                        this.jysbisAdd = 1
                    }
                    break
                default:
                    console.log(0)
            }
            // 重新加载分数图以及更新localStorage
            this.getLoadEcharts()
            localStorage.setItem('allShowState', JSON.stringify(this.statusArr))
            localStorage.setItem('allCounts', this.allCounts)
            localStorage.setItem('checkCount', JSON.stringify(this.checkCount))
            this.proNumber = this.checkCount.proJurisdictionOptimizeCount + this.checkCount.proJurisdictionriskCount + this.checkCount.organizationOptimizeCount + this.checkCount.organizationriskCount + this.checkCount.secretSitesriskCount + this.checkCount.secretSitesOptimizeCount + this.checkCount.secretPersonnelOptimizeCount + this.checkCount.secretPersonnelriskCount + this.checkCount.eduTrainOptimizeCount + this.checkCount.eduTrainriskCount + this.checkCount.deviceOptimizeCount + this.checkCount.deviceriskCount + this.checkCount.carrierOptimizeCount + this.checkCount.carrierriskCount + this.checkCount.selfEvaluationriskCount
            localStorage.setItem('proNumber', this.proNumber)
        },
        // 一键检测
        yjjc() {
            this.addCounts = 0
            this.sbisAdd = 0
            this.sbrisk = 0
            this.zzaddCounts = 0
            this.zzisAdd = 0
            this.zzrisk = 0
            this.smryaddCounts = 0
            this.smryisAdd = 0
            this.smryrisk = 0
            localStorage.setItem('checkListDatasShow', true)
            this.progressShow = true
            this.allCounts = 100 // 初始化总分数
            this.score = 0
            // 初始化statusArr 
            Object.keys(this.statusArr).forEach((key) => {
                this.statusArr[key] = false
            })
            // 初始化checkCount
            Object.keys(this.checkCount).forEach((key) => {
                this.checkCount[key] = 0
            })
            this.isshow1 = false
            this.isshow2 = true
            this.isshow3 = false
            this.isshow4 = false
            this.checkListDatasShow = true
            this.getConfidentialitySystem() // 一键检测---获取保密制度检测信息
        },
        // 全部忽略
        qbhl() {
            this.statusArr = {
                // 保密制度
                proJurisdictionShow1: true,
                organizationShow1: true,
                secretPersonnelShow1: true,
                eduTrainShow1: true,
                secretSitesShow1: true,
                deviceShow1: true,
                carrierShow1: true,
                selfEvaluationShow1: true,
                proJurisdictionAllRight1: true,
                organizationAllRight1: true,
                secretPersonnelAllRight1: true,
                secretPersonnelAllRight2: true,
                secretPersonnelAllRight3: true,
                secretPersonnelAllRight4: true,
                secretPersonnelAllRight5: true,
                eduTrainAllRight1: true,
                secretSitesAllRight1: true,
                secretSitesAllRight2: true,
                deviceAllRight1: true,
                deviceAllRight2: true,
                deviceAllRight3: true,
                deviceAllRight4: true,
                deviceAllRight5: true,
                deviceAllRight6: true,
                deviceAllRight7: true,
                deviceAllRight8: true,
                carrierAllRight1: true,
                selfEvaluationAllRight1: true,

                proJurisdictionShow2: false,
                proJurisdictionShow3: false,
                proJurisdictionLoadingShow: false,
                proJurisdictionShowOptimize1: false,
                proJurisdictionShowRisk1: false,
                // 组织机构
                organizationShow2: false,
                organizationShow3: false,
                organizationLoadingShow: false,
                organizationShowOptimize1: false,
                organizationShowRisk1: false,
                organizationShowRisk2: false,
                organizationShowRisk3: false,
                organizationShowRisk4: false,
                // 涉密人员
                secretPersonnelShow2: false,
                secretPersonnelShow3: false,
                secretPersonnelLoadingShow: false,
                secretPersonnelShowRisk1: false,
                secretPersonnelShowRisk2: false,
                secretPersonnelShowRisk3: false,
                secretPersonnelShowOptimize1: false,
                secretPersonnelShowOptimize2: false,
                secretPersonnelShowOptimize3: false,
                secretPersonnelShowOptimize4: false,
                secretPersonnelShowOptimize5: false,
                // 教育培训
                eduTrainShow2: false,
                eduTrainShow3: false,
                eduTrainLoadingShow: false,
                eduTrainShowOptimize1: false,
                eduTrainShowRisk1: false,
                // 涉密场所
                secretSitesShow2: false,
                secretSitesShow3: false,
                secretSitesShowRisk1: false,
                secretSitesShowOptimize1: false,
                secretSitesShowOptimize2: false,
                secretSitesLoadingShow: false,
                // 设备信息
                deviceShow2: false,
                deviceShow3: false,
                deviceLoadingShow: false,
                deviceShowOptimize1: false,
                deviceShowOptimize2: false,
                deviceShowOptimize3: false,
                deviceShowOptimize4: false,
                deviceShowOptimize5: false,
                deviceShowOptimize6: false,
                deviceShowOptimize7: false,
                deviceShowOptimize8: false,
                deviceShowRisk1: false,
                deviceShowRisk2: false,
                deviceShowRisk3: false,
                deviceShowRisk4: false,
                deviceShowRisk5: false,
                deviceShowRisk6: false,
                deviceShowRisk7: false,
                deviceShowRisk8: false,
                // 涉密载体
                carrierShow2: false,
                carrierShow3: false,
                carrierLoadingShow: false,
                carrierShowOptimize1: false,
                carrierShowRisk1: false,
                // 自查自评
                selfEvaluationShow2: false,
                selfEvaluationShow3: false,
                selfEvaluationLoadingShow: false,
                selfEvaluationAShowRisk1: false,
                selfEvaluationAShowRisk2: false,
                selfEvaluationAShowRisk3: false,
                selfEvaluationAShowRisk4: false,
            }
            localStorage.setItem('allShowState', JSON.stringify(this.statusArr))
            localStorage.setItem('allCounts', 100)
            // 初始化checkCount
            Object.keys(this.checkCount).forEach((key) => {
                this.checkCount[key] = 0
            })
            localStorage.setItem('checkCount', JSON.stringify(this.checkCount))
            this.isshow4 = false
            this.isshow3 = true
            this.allCounts = 100
            this.getLoadEcharts()
        }
    },
    mounted() {
        // localStorage.removeItem('allShowState')
        // localStorage.removeItem('checkCount')
        // localStorage.removeItem('checkListDatasShow')
        // localStorage.removeItem('allCounts')
        // localStorage.removeItem('proNumber')
        if (localStorage.getItem('allShowState') != null) {
            this.statusArr = JSON.parse(localStorage.getItem('allShowState'))
        }
        if (localStorage.getItem('checkCount') != null) {
            this.checkCount = JSON.parse(localStorage.getItem('checkCount'))
        }
        if (localStorage.getItem('checkListDatasShow') != null) {
            this.checkListDatasShow = JSON.parse(localStorage.getItem('checkListDatasShow'))
        }
        if (localStorage.getItem('proNumber') != null) {
            this.proNumber = JSON.parse(localStorage.getItem('proNumber'))
        }
        if (localStorage.getItem('allCounts') != null) {
            this.allCounts = Number(localStorage.getItem('allCounts'))
            this.isshow4 = true
            this.isshow1 = false
            this.isshow2 = false
            this.isshow3 = false
        }
        if(this.isshow1 == true){
            this.allCounts = 0
        }
        this.$nextTick(() => {
            this.getLoadEcharts()
            this.getZzjgPic('xz')
        })
        this.tabsCode()
    }
};
</script>
<style scoped>
.container{
  font-family: 'SourceHanSansSCziti';
  height: 100%;
}
/* 样式改版2022/12/13 */
.dbItem {
	padding-bottom: 40px;
	background: #FFFFFF;
	border: 1px solid rgba(219,231,255,1);
	box-shadow: 0px 2px 10px 0px rgba(107,117,134,0.15);
	overflow: hidden;
	margin-bottom: 20px;
	position: relative;
	border-radius: 6px;
}
.dbItem .fonts {
	font-family: 'SourceHanSansSCziti';
	font-size: 20px;
	color: #080808;
	font-weight: 400;
	padding-top: 18px;
	padding-left: 20px;
    float: left;
}
.dbItem .buttons {
    /* position: absolute; */
    /* right: 20px; */
    /* top: 20px; */
    /* width: 100px; */
    height: 32px;
    background: #FFFFFF;
    /* border: 1px solid rgba(2,111,222,1); */
    border: 1px solid rgba(223,143,0,1);
    font-family: 'SourceHanSansSCziti';
    font-size: 16px;
    /* color: #1766D1; */
    color: #DF8F00;
    font-weight: 400;
    border-radius: 50px;
    text-align: center;
    line-height: 32px;
    cursor: pointer;
    padding: 0px 15px;
    float: right;
    margin-right: 10px;
    margin-top: 20px;
}
.dbItem .title {
	font-family: 'SourceHanSansSCziti';
	font-size: 18px;
	color: #666666;
	font-weight: 400;
    padding-top: 32px;
    padding-left: 23px;
    overflow: hidden;
	cursor: pointer;
	float: left;
	margin-left: 40px;
}
.titleDiv {
	margin-left: -40px;
}
.dbItem .title img {
	float: left;
    width: 24px;
    height: 24px;
}
.dbItem .title span {
	display: block;
    float: left;
    margin-left: 12px;
}

/deep/.el-tabs__header {
    margin-left: 10px;
    border-bottom: 5px solid #1B72D8;
    /* padding-left: 20px; */
}
/deep/.el-tabs__item.is-active {
    color: #FFFFFF;
    background: #1B72D8;
    border-radius: 5px 5px 0px 0px;
    text-align: center;
    border: 1px solid #1B72D8;
}

/deep/.el-tabs__active-bar {
    background: none;
}
/deep/.el-tabs__item {
    padding: 0px 20px 0px 20px!important;
    font-size: 22px;
    background: #E0EEFF;
    border: 1px solid rgba(194,214,237,1);
    border-radius: 4px 4px 0px 0px;
    margin-left: 20px;
}
/deep/.el-tabs__nav-wrap {
    margin-bottom: 0px;
}


.header-left {
    width: 150px;
    height: 150px;
    /* line-height: 150px!important; */
    /* background: url(./images/zjpf_2.png) no-repeat center; */
    /* font-family: Helvetica;
        font-size: 40px;
        color: #21A566;
        letter-spacing: 0;
        text-align: center;
        line-height: 40px;
        font-weight: 400; */
}

.kuang {
    height: 36px;
    line-height: 36px;
    overflow: hidden;
    margin-bottom: 5px;
}

.kuang img {
    float: left;
    margin-top: 10px;
    margin-right: 10px;
}

.bmzd {
    display: inline-block;
}

.ckan {
    float: right;
    margin-right: 10px;
    margin-top: 0px;
}

.glwtzwfx {
    font-size: 18px;
    color: #999999;
    letter-spacing: 0;
    line-height: 19.6px;
    font-weight: 400;
    margin-left: 3%;
}

/deep/ .el-collapse-item__header {
    font-size: 20px;
    color: #3D3D3D;
    letter-spacing: 0;
    line-height: 22.4px;
    font-weight: 400;
    background-color: rgba(0, 0, 0, 0);
}

/deep/ .el-collapse-item__wrap {
    background-color: rgba(0, 0, 0, 0);
}
/deep/.el-button--mini {
    font-size: 18px!important;
}
.kuang:hover {
    background-color: rgba(0, 153, 255, 0.1);
}

/deep/ .el-collapse-item__content {
    padding-top: 5px;
    padding-bottom: 5px;
    font-size: 18px!important;
}
/deep/ .el-tabs--top {
    height: 100%;
}
/deep/ .el-tabs__content {
    height: calc(100% - 45px);
}
/deep/ .el-tab-pane {
    height: 100%;
}

.sqt {
    width: 100%;
    height: 20%;
    margin-top: 5px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

.sqt-sm {
    /* margin-right: 40%; */
    font-size: 28px;
    color: #3D3D3D;
    letter-spacing: 0;
    line-height: 28px;
    font-weight: 400;
    float: left;
    width: calc(100% - 450px);
    margin-left: 15px;
}

.yjscsm {
    font-size: 12px;
    color: #666666;
    letter-spacing: 0;
    line-height: 18px;
    font-weight: 400;
}

.zdwb {
    width: 100%;
    /* height: 100%; */
    box-shadow: 0px 1px 12px 0px rgba(0, 0, 0, 0.1);
    /* background: #FFFFFF; */
    /* overflow-y: scroll; */
}

.mk_dbgz {
    width: 100%;
    /* height: 12vw; */
    /* background-color: rgba(255, 255, 1, 0.5); */

}

.mk_bt {
    width: 100%;
    height: 3vw;
    border-bottom: 1px solid rgba(216, 216, 216, 1);

}

.mk_btl {
    display: flex;
    align-items: center;
    margin-left: 20px;
    font-size: .9vw;
    height: 100%;
}

.mk-nr {
    display: flex;
    align-items: center;
    /* height: 9vw; */
    /* margin-bottom: 10px; */
}

.mk-nr-div {
    width: 9vw;
    height: 9vw;
    cursor: pointer;
}

.nr-div {
    margin-top: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.yuan {
    width: 60px;
    height: 60px;
    /* background: url(./images/img1026_18.png) no-repeat center; */
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50px;
    background: #EF6B43;
}

.ym-wz {
    font-size: 0.8vw;
    color: #333333;
    letter-spacing: 0;
    text-align: center;
    line-height: 19.6px;
    font-weight: 400;
    margin-top: .5vw;
}

#progress {
    /* width: 1452px;
        height: 6px; */
    /* border: 1px solid #ccc; */
    /* border-radius: 15px; */
    /* overflow: hidden; */
    /*注意这里*/
    /* box-shadow: 0 0 5px 0px #ddd inset; */
}

#progress .nojdt {}

#progress .jdt {
    display: inline-block;
    width: 100%;
    height: 200%;
    background: linear-gradient(#2989d8 0%, #2989d8 100%);
    /* background-size: 60px 30px; */
    text-align: center;
    color: #fff;
    animation: load 10s ease-in;
}

@keyframes load {
    0% {
        width: 0%;
    }

    100% {
        width: 100%;
    }
}

/* zhang */
.sqt-btn {
    float: right;
    width: 430px;
}

.hasProblom {
    color: "#F59A23" !important;
}

.hasProblom .proSpan {
    color: red;
}

.zjpfOnner {
    height: 76vh;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
}

.zjpfOnner img {
    float: left;
    margin-right: 30px
}

.zjpfOnner .zjpfRight p {
    font-size: 35px;
    font-weight: 600;
    color: #444444;
}

.zjpfOnner .zjpfRight .zjpfButton {
    margin-top: 25px;
    width: 200px;
    height: 55px;
    border-radius: 50px;
    font-size: 23px;
    font-family: "SourceHanSansSCziti";
}

.allRightItem {
    color: #409EFF;
}

.optimizeItem {
    color: #E6A23C;
}

.riskItem {
    color: #F56C6C;
}

.look {
    background: #409EFF;
}

.update {
    background: #67C23A;
}

.ignore {
    background: #909399;
}

.ywcFont {
    background: #21A566;
    text-align: center;
    border-radius: 50px;
    color: #ffffff;
    font-size: 0.7vw;
    width: 80px;
    padding: 3px 0px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 10px;
    /* margin-left: calc(100% - 126px); */
}

.wwcFont {
    background: #EF6B43;
    text-align: center;
    border-radius: 50px;
    color: #ffffff;
    font-size: 0.7vw;
    width: 80px;
    padding: 3px 0px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 10px;
    /* margin-left: calc(100% - 126px); */
}

.dwsBkg {
    background: #EF6B43;
}

.ywcBkg {
    background: #21A566;
}

.yjscBtn {
    margin-right: 20px;
    width: 165px;
    height: 40px;
    background-image: linear-gradient(90deg, rgb(40, 220, 134) 0%, rgb(33, 165, 102) 56%);
    font-size: 20px;
    color: #FFFFFF;
    letter-spacing: 0;
    text-align: center;
    line-height: 18px;
    font-weight: 400;
    border: none;
}

.yjjcBtn {
    float: right;
    margin-right: 20px;
    width: 165px;
    height: 40px;
    background-image: linear-gradient(90deg, rgb(40, 220, 134) 0%, rgb(33, 165, 102) 56%);
    font-size: 20px;
    color: #FFFFFF;
    letter-spacing: 0;
    text-align: center;
    line-height: 18px;
    font-weight: 400;
    border: none;
}

.qbhlBtn {
    float: right;
    margin-right: 20px;
    width: 165px;
    height: 40px;
    background-image: linear-gradient(90deg, #FFA75E 0%, #F68B33 56%);
    font-size: 20px;
    color: #FFFFFF;
    letter-spacing: 0;
    text-align: center;
    line-height: 18px;
    font-weight: 400;
    border: none;
}

.ml20px {
    margin-left: 20px;
}

.zzDialogContainer {
    height: 100%;
}

>>>.el-dialog__body {
    height: calc(100% - 120px);
}

>>>.el-dialog {
    margin: 0px;
    height: 100%;
}
.pfather {
	display: flex;
	justify-content: center;
}
</style>