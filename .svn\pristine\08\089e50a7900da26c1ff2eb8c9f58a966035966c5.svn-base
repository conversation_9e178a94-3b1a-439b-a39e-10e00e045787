<template>
    <div class="dbgzContainer" id="dbgzContainer">
        <div class="zdwb">
            <!-- 注册信息 -->
            <div class="dbItem" v-if="zcxxIsPerfectShow">
                <p class="fonts">注册信息待完善-共{{ zcxxPerfectCount }}项</p>
                <div class="titleDiv">
                    <div class="title" @click="$router.push('/zcdw')">
                        <img src="./images/s-icon-01.png" alt="">
                        <span>注册信息待完善</span>
                    </div>
                </div>
            </div>
            <!-- 日常管理 -->
            <div class="dbItem" v-if="rcUpdateCounts != 0">
                <p class="fonts">日常管理待完善待完善-共{{ rcUpdateCounts }}项</p>
                <div class="titleDiv">
                    <!-- <div class="title" v-if="zzxxIsPerfectShow" @click="toIndex(2)">
						<img src="./images/s-icon-02.png" alt="">
						<span style="margin-top: -10px;">完善资质信息<br />（资质单位）</span>
					</div> -->
                    <div class="title" v-if="bmzdIsPerfectShow" @click="$router.push('/tzglsy?activeName=bmzd')">
                        <img src="./images/s-icon-03.png" alt="">
                        <span>完善保密制度</span>
                    </div>
                    <div class="title" v-if="zzjgIsPerfectShow" @click="$router.push('/tzglsy?activeName=zzjg')">
                        <img src="./images/s-icon-04.png" alt="">
                        <span>完善组织机构</span>
                    </div>
                    <div class="title" v-if="ryxxIsPerfectShow" @click="$router.push('/tzglsy?activeName=smry')">
                        <img src="./images/s-icon-05.png" alt="">
                        <span>完善人员信息</span>
                    </div>
                    <div class="title" v-if="csxxIsPerfectShow" @click="$router.push('/tzglsy?activeName=csgl')">
                        <img src="./images/s-icon-06.png" alt="">
                        <span>完善场所信息</span>
                    </div>
                    <div class="title" v-if="sbxxIsPerfectShow" @click="$router.push('/tzglsy?activeName=smwlsb')">
                        <img src="./images/s-icon-07.png" alt="">
                        <span>完善设备信息</span>
                    </div>
                    <div class="title" v-if="ztxxIsPerfectShow" @click="$router.push('/tzglsy?activeName=smzttz')">
                        <img src="./images/s-icon-08.png" alt="">
                        <span>完善载体信息</span>
                    </div>
                </div>
            </div>
            <!-- 年度涉密人员相关台账 -->
            <div class="dbItem" v-if="smryAllShow">
                <p class="fonts">{{ nowsYear }}年度涉密人员相关台账</p>
                <div class="buttons" v-if="smryTzScShow && smrySctime" @click="yjscClick('smry','yjsc')">
                    一键生成</div>
                    <el-tooltip  v-if="smryTzScShow && !smrySctime" class="item" effect="dark" :content="dbscsJcomputed" placement="bottom-end">
                        <i class="el-icon-info scBtnFont"></i>
                    </el-tooltip>
                <div class="buttons" v-if="cxscSmryTzScShow && !smrySctime" @click="cxscClick('smry')">
                    重新生成</div>
                    
                <div class="titleDiv">
                    <div class="title" v-if="smgwListLength > 0" @click="$router.push('/tzglsy?activeName=smgwgl')">
                        <img src="./images/s-icon-09.png" alt="">
                        <span>涉密岗位登记表</span>
                    </div>
                    <div class="title" v-if="zgsmryHzListLength > 0" @click="$router.push('/tzglsy?activeName=smry')">
                        <img src="./images/s-icon-10.png" alt="">
                        <span>涉密人员汇总表</span>
                    </div>
                    <div class="title" v-if="ryxzHzListLength > 0" @click="$router.push('/tzglsy?activeName=ryxz')">
                        <img src="./images/s-icon-11.png" alt="">
                        <span>人员新增汇总表</span>
                    </div>
                    <div class="title" v-if="rynjbgHzListLength > 0" @click="$router.push('/tzglsy?activeName=gwbg')">
                        <img src="./images/s-icon-12.png" alt="">
                        <span>人员密级变更汇总表</span>
                    </div>
                    <div class="title" v-if="lghzListLength > 0" @click="$router.push('/tzglsy?activeName=lglz')">
                        <img src="./images/s-icon-13.png" alt="">
                        <span>离岗汇总表</span>
                    </div>
                </div>
            </div>
            <!-- 年度涉密场所相关台账 -->
            <div class="dbItem" v-if="smcsAllShow">
                <p class="fonts">{{ nowsYear }}年度涉密场所相关台账</p>
                <div class="buttons" v-if="smcsTzScShow && smcsSctime" @click="yjscClick('smcs','yjsc')">一键生成</div>
                    <el-tooltip  v-if="smcsTzScShow && smcsSctime" class="item" effect="dark" :content="dbscsJcomputed" placement="bottom-end">
                        <i class="el-icon-info scBtnFont"></i>
                    </el-tooltip>
                <div class="buttons" v-if="cxscSmcsTzScShow && !smcsSctime" @click="cxscClick('smcs')">重新生成</div>
                <div class="titleDiv">
                    <div class="title" v-if="csglListLength > 0" @click="$router.push('/tzglsy?activeName=csgl')">
                        <img src="./images/s-icon-15.png" alt="">
                        <span>涉密场所登记表</span>
                    </div>
                    <div class="title" v-if="csbgListLength > 0" @click="$router.push('/tzglsy?activeName=csbg')">
                        <img src="./images/s-icon-16.png" alt="">
                        <span>场所变更登记表</span>
                    </div>
                </div>
            </div>
            <!-- 年度涉密设备相关台账 -->
            <div class="dbItem" v-if="smsbAllShow">
                <p class="fonts">{{ nowsYear }}年度涉密设备相关台账</p>
                <div class="buttons" v-if="smsbTzScShow && smsbSctime" @click="yjscClick('smsb','yjsc')">一键生成</div>
                    <el-tooltip  v-if="smsbTzScShow && smsbSctime" class="item" effect="dark" :content="dbscsJcomputed" placement="bottom-end">
                        <i class="el-icon-info scBtnFont"></i>
                    </el-tooltip>
                <div class="buttons" v-if="cxscSmsbTzScShow && !smsbSctime" @click="cxscClick('smsb')">重新生成</div>
                <div class="titleDiv">
                    <div class="title" v-if="smjsjListLength > 0" @click="$router.push('/tzglsy?activeName=smjsj')">
                        <img src="./images/s-icon-17.png" alt="">
                        <span>涉密计算机</span>
                    </div>
                    <div class="title" v-if="fsmjsjListLength > 0" @click="$router.push('/tzglsy?activeName=fsmjsj')">
                        <img src="./images/s-icon-17.png" alt="">
                        <span>非涉密计算机</span>
                    </div>
                    <div class="title" v-if="ydccjzListLength > 0" @click="$router.push('/tzglsy?activeName=smydccjz')">
                        <img src="./images/s-icon-18.png" alt="">
                        <span>涉密移动存储介质</span>
                    </div>
                    <div class="title" v-if="bgzdhsbListLength > 0"
                        @click="$router.push('/tzglsy?activeName=smbgzdhsb')">
                        <img src="./images/s-icon-19.png" alt="">
                        <span>涉密办公自动化设备</span>
                    </div>
                    <div class="title" v-if="fsmbgzdhsbListLength > 0"
                        @click="$router.push('/tzglsy?activeName=fsmbgzdhsb')">
                        <img src="./images/s-icon-19.png" alt="">
                        <span>非涉密办公自动化设备</span>
                    </div>
                    <div class="title" v-if="wlsbListLength > 0" @click="$router.push('/tzglsy?activeName=smwlsb')">
                        <img src="./images/s-icon-20.png" alt="">
                        <span>涉密网络设备</span>
                    </div>
                    <div class="title" v-if="fwlsbListLength > 0" @click="$router.push('/tzglsy?activeName=fmwlsb')">
                        <img src="./images/s-icon-20.png" alt="">
                        <span>非涉密网络设备</span>
                    </div>
                    <div class="title" v-if="aqcpListLength > 0" @click="$router.push('/tzglsy?activeName=aqcp')">
                        <img src="./images/s-icon-21.png" alt="">
                        <span>安全产品</span>
                    </div>
                </div>
            </div>
            <!-- 年度涉密载体相关台账 -->
            <div class="dbItem" v-if="smztListLength > 0">
                <p class="fonts">{{ nowsYear }}年度涉密载体相关台账</p>
                <div class="buttons" v-if="smztTzScShow && smztSctime" @click="yjscClick('smzt','yjsc')">一键生成</div>
                <el-tooltip  v-if="smztTzScShow && smztSctime" class="item" effect="dark" :content="dbscsJcomputed" placement="bottom-end">
                        <i class="el-icon-info scBtnFont"></i>
                    </el-tooltip>
                <div class="buttons" v-if="cxscSmztTzScShow && !smztSctime" @click="cxscClick('smzt')">重新生成</div>
                <div class="titleDiv">
                    <div class="title" v-if="smztListLength > 0" @click="$router.push('/tzglsy?activeName=smzttz')">
                        <img src="./images/s-icon-09.png" alt="">
                        <span>涉密载体</span>
                    </div>
                </div>
            </div>
            <!-- 年度涉密事项相关台账 -->
            <div class="dbItem" v-if="smsxAllShow">
                <p class="fonts">{{ nowsYear }}年度涉密事项相关台账</p>
                <div class="buttons" v-if="smsxTzScShow && smsxSctime" @click="yjscClick('smsx','yjsc')">一键生成</div>
                <el-tooltip  v-if="smsxTzScShow && smsxSctime" class="item" effect="dark" :content="dbscsJcomputed" placement="bottom-end">
                        <i class="el-icon-info scBtnFont"></i>
                    </el-tooltip>
                <div class="buttons" v-if="cxscSmsxTzScShow && !smsxSctime" @click="cxscClick('smsx')">重新生成</div>
                <div class="titleDiv">
                    <div class="title" v-if="dmzrrListLength > 0" @click="$router.push('/tzglsy?activeName=dmzrr')">
                        <img src="./images/s-icon-09.png" alt="">
                        <span>定密责任人</span>
                    </div>
                    <div class="title" v-if="dmsqListLength > 0" @click="$router.push('/tzglsy?activeName=dmsq')">
                        <img src="./images/s-icon-10.png" alt="">
                        <span>定密授权</span>
                    </div>
                    <div class="title" v-if="gjmmsxListLength > 0" @click="$router.push('/tzglsy?activeName=gjmmsx')">
                        <img src="./images/s-icon-11.png" alt="">
                        <span>国家秘密事项</span>
                    </div>
                    <div class="title" v-if="dmpxListLength > 0" @click="$router.push('/tzglsy?activeName=dmpx')">
                        <img src="./images/s-icon-12.png" alt="">
                        <span>定密培训</span>
                    </div>
                    <div class="title" v-if="dmqkndtjListLength > 0"
                        @click="$router.push('/tzglsy?activeName=dmqkndtj')">
                        <img src="./images/s-icon-13.png" alt="">
                        <span>定密情况年度统计</span>
                    </div>
                    <div class="title" v-if="bmqsxqdqkListLength > 0"
                        @click="$router.push('/tzglsy?activeName=bmqsxqdqk')">
                        <img src="./images/s-icon-13.png" alt="">
                        <span>不明确事项确定情况</span>
                    </div>
                    <div class="title" v-if="zfcgxmqkListLength > 0"
                        @click="$router.push('/tzglsy?activeName=zfcgxmqk')">
                        <img src="./images/s-icon-13.png" alt="">
                        <span>政府采购项目情况</span>
                    </div>
                </div>
            </div>
            <!-- 自检自查待完成 -->
            <div class="dbItem"
                v-if="zczp1IsPerfectShow && new Date().getMonth() + 1 > 0 && new Date().getMonth() + 1 < 13 || zczp2IsPerfectShow && new Date().getMonth() + 1 > 3 && new Date().getMonth() + 1 < 13 || zczp3IsPerfectShow && new Date().getMonth() + 1 > 6 && new Date().getMonth() + 1 < 13 || zczp4IsPerfectShow && new Date().getMonth() + 1 > 9 && new Date().getMonth() + 1 < 13">
                <p class="fonts">自检自查待完成-共{{ zczpUpdateCounts }}项</p>
                <div class="titleDiv">
                    <div class="title" v-if="zczp1IsPerfectShow && new Date().getMonth() + 1 > 0 && new Date().getMonth() + 1 < 13" @click="$router.push('/zczpls?activeName=xjzczp')">
                        <img src="./images/s-icon-22.png" alt="">
                        <span>完成本年第一季度自查自评</span>
                    </div>
                    <div class="title" v-if="zczp2IsPerfectShow && new Date().getMonth() + 1 > 3 && new Date().getMonth() + 1 < 13" @click="$router.push('/zczpls?activeName=xjzczp')">
                        <img src="./images/s-icon-22.png" alt="">
                        <span>完成本年第二季度自查自评</span>
                    </div>
                    <div class="title" v-if="zczp3IsPerfectShow && new Date().getMonth() + 1 > 6 && new Date().getMonth() + 1 < 13" @click="$router.push('/zczpls?activeName=xjzczp')">
                        <img src="./images/s-icon-22.png" alt="">
                        <span>完成本年第三季度自查自评</span>
                    </div>
                    <div class="title" v-if="zczp4IsPerfectShow && new Date().getMonth() + 1 > 9 && new Date().getMonth() + 1 < 13" @click="$router.push('/zczpls?activeName=xjzczp')">
                        <img src="./images/s-icon-22.png" alt="">
                        <span>完成本年第四季度自查自评</span>
                    </div>
                </div>
            </div>
            <!-- 年度涉密人员上报待完成 -->
            <div class="dbItem" v-if="smryDownLoadShow">
                <p class="fonts">年度涉密人员上报待完成</p>
                <div class="titleDiv">
                    <div class="title" @click="smryDatasImport">
                        <img src="./images/s-icon-23.png" alt="">
                        <span>完成上报数据导出</span>
                    </div>
                </div>
            </div>
            <!-- 年度定密事项上报待完成 -->
            <div class="dbItem" v-if="smsxDownloadShow">
                <p class="fonts">年度定密事项上报待完成</p>
                <div class="titleDiv">
                    <div class="title" @click="dmsxDatasImport">
                        <img src="./images/s-icon-23.png" alt="">
                        <span>完成上报数据导出</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import handle from './handle'
import { detectZoom } from '../../../utils/detectZoom.js';
import {
    getlogin
} from "../../../db/loginyhdb";
import {
    //内容管理初始化成员列表
    getSmgwglList,
    getGwbgList,
    getLglzList,
    getZgsmrylList,
    getDwxxDatas,
    getDmzrrYear,
    getDmsqYear,
    getGjmmsxYear,
    getDmpxYear,
    getDmqkndtjYear,
    getBmqsxqdqkYear,
    getZfcgxmqkYear
} from "../../../db/yjjcdb";
// 获取workbook2ArrayBuffer方法
import { workbook2ArrayBuffer } from '../../../utils/exportExcel2'
import { getDirectory } from '../../../utils/pathUtil'
import {
    writeTrajectoryLog
} from '../../../utils/logUtils'
import XLSX from "xlsx"
export default {
    mixins: [handle],
    data() {
        return {
            dwmc: '',
            dwdm: '',
            dwlxr: '',
            dwlxdh: '',
        };
    },
    computed: {

    },
    created() {
        // 各分辨率下修改zoom
        this.$nextTick(() => {
            const { screen } = require('electron')
            const primaryDisplay = screen.getPrimaryDisplay()
            const { width } = primaryDisplay.workAreaSize
            const m = detectZoom();
            if (width == 1920) {
                document.getElementById("dbgzContainer").style.zoom = 1;
            }
            if (m != 100) {
                document.getElementById("dbgzContainer").style.zoom = 100 / Number(m) - 0.08;
            }
            if (width == 1600) {
                document.getElementById("dbgzContainer").style.zoom = 0.8;
            } else if (width == 1680) {
                document.getElementById("dbgzContainer").style.zoom = 0.9;
            } else if (width == 1440) {
                document.getElementById("dbgzContainer").style.zoom = 0.75;
            } else if (width == 1400) {
                document.getElementById("dbgzContainer").style.zoom = 0.73;
            } else if (width == 1366) {
                document.getElementById("dbgzContainer").style.zoom = 0.65;
            } else if (width == 1360) {
                document.getElementById("dbgzContainer").style.zoom = 0.64;
            } else if (width == 1024) {
                document.getElementById("dbgzContainer").style.zoom = 0.52;
            }
        })
    },
    methods: {
        // 涉密人员上报数据导出
        smryDatasImport() {
            // 保密制度登记
            const fs = require('fs')
            // zip 文件名称
            let zipFileName = '年度涉密人员上报数据' + '-' + (new Date().getTime()) + '.zip'
            // 弹出dialog选择保存文件的位置
            const { dialog } = require("electron").remote
            let options = {
                title: "年度涉密人员上报数据", //下载文件名称
                defaultPath: zipFileName //下载文件title
            }
            dialog.showSaveDialog(options, (result) => {
                if (!result) {
                    this.$message.warning('涉密人员上报数据下载任务已取消')
                    return
                }
                // 判断路径是否存在
                let path = getDirectory(result)
                // 判断路径是否存在
                if (!fs.existsSync(path)) {
                    this.$message.warning('所选路径不存在，请重新点击下载按钮进行下载')
                    return
                }
                // 准备下载zip文件
                this.exportZip(result, '', '')
            })
        },
        // 定密事项上报数据导出
        dmsxDatasImport() {
            // 保密制度登记
            const fs = require('fs')
            // zip 文件名称
            let zipFileName = '年度定密事项上报数据' + '-' + (new Date().getTime()) + '.zip'
            // 弹出dialog选择保存文件的位置
            const { dialog } = require("electron").remote
            let options = {
                title: "年度定密事项上报数据", //下载文件名称
                defaultPath: zipFileName //下载文件title
            }
            dialog.showSaveDialog(options, (result) => {
                if (!result) {
                    this.$message.warning('年度定密事项上报数据下载任务已取消')
                    return
                }
                // 判断路径是否存在
                let path = getDirectory(result)
                // 判断路径是否存在
                if (!fs.existsSync(path)) {
                    this.$message.warning('所选路径不存在，请重新点击下载按钮进行下载')
                    return
                }
                // 准备下载zip文件
                this.exportDmsxZip(result, '', '')
            })
        },
        // 导出涉密人员zip
        async exportZip(savePath, pdf, row, rowjd) {
            // 准备生成zip
            var AdmZip = require("adm-zip")
            var zip = new AdmZip()
            zip.writeZip(savePath)
            //#region 
            // 生成涉密岗位统计情况
            if (this.smgwListLength > 0) {
                let smcsExcelBuffer = this.generateSmcslExcel('scsmgwhz')
                if (smcsExcelBuffer) {
                    zip.addFile('涉密岗位统计情况' + ".xlsx", smcsExcelBuffer, "")
                }
            }
            // 生成涉密人员汇总表
            if (this.zgsmryHzListLength > 0) {
                let smcsExcelBuffer = this.generateSmcslExcel('scsmryhz')
                if (smcsExcelBuffer) {
                    zip.addFile('涉密人员统计情况' + ".xlsx", smcsExcelBuffer, "")
                }
            }
            // 生成人员新增汇总
            if (this.ryxzHzListLength > 0) {
                let smcsExcelBuffer = this.generateSmcslExcel('scsmryxzhz')
                if (smcsExcelBuffer) {
                    zip.addFile('涉密人员新增统计情况' + ".xlsx", smcsExcelBuffer, "")
                }
            }
            // 生成涉密人员岗位变更汇总表
            if (this.rynjbgHzListLength > 0) {
                let smcsExcelBuffer = this.generateSmcslExcel('scsmrygwbg')
                if (smcsExcelBuffer) {
                    zip.addFile('涉密人员岗位变更汇总表' + ".xlsx", smcsExcelBuffer, "")
                }
            }
            // 生成涉密人员脱密期管理台账
            if (this.lghzListLength) {
                let smcsExcelBuffer = this.generateSmcslExcel('scsmrytmqgl')
                if (smcsExcelBuffer) {
                    zip.addFile('涉密人员脱密期管理台账' + ".xlsx", smcsExcelBuffer, "")
                }
            }
            // #endregion
            zip.writeZip(savePath, (error) => {
                if (error) {
                    this.$message.error('[下载异常]' + error.message)
                    return
                }
                this.$message.success('涉密人员相关台账下载已完成')
                // 添加日志
                let paramsLog = {
                    xyybs: 'mk_dbgzrz',
                    id: '-1',
                    ymngnmc: '导出年度涉密人员上报数据'
                }
                writeTrajectoryLog(paramsLog)
            })
        },

        // 导出定密事项zip
        async exportDmsxZip(savePath, pdf, row, rowjd) {
            // 准备生成zip
            var AdmZip = require("adm-zip")
            var zip = new AdmZip()
            zip.writeZip(savePath)
            //#region 
            // 生成涉定密定密责任人
            if (this.dmzrrListLength > 0) {
                let smcsExcelBuffer = this.generateSmcslExcel('scdmzrr')
                if (smcsExcelBuffer) {
                    zip.addFile('定密责任人' + ".xlsx", smcsExcelBuffer, "")
                }
            }
            // 生成定密定密授权
            if (this.dmsqListLength > 0) {
                let smcsExcelBuffer = this.generateSmcslExcel('scdmsq')
                if (smcsExcelBuffer) {
                    zip.addFile('定密授权' + ".xlsx", smcsExcelBuffer, "")
                }
            }
            // 生成国家秘密事项
            if (this.gjmmsxListLength > 0) {
                let smcsExcelBuffer = this.generateSmcslExcel('scgjmmsx')
                if (smcsExcelBuffer) {
                    zip.addFile('国家秘密事项' + ".xlsx", smcsExcelBuffer, "")
                }
            }
            // 生成定密培训
            if (this.dmpxListLength > 0) {
                let smcsExcelBuffer = this.generateSmcslExcel('scdmpx')
                if (smcsExcelBuffer) {
                    zip.addFile('定密培训' + ".xlsx", smcsExcelBuffer, "")
                }
            }
            // 生成定密情况年度统计
            if (this.dmqkndtjListLength > 0) {
                let smcsExcelBuffer = this.generateSmcslExcel('scdmqkndtj')
                if (smcsExcelBuffer) {
                    zip.addFile('定密情况年度统计' + ".xlsx", smcsExcelBuffer, "")
                }
            }
            // 生成不明确事项确定情况
            if (this.bmqsxqdqkListLength > 0) {
                let smcsExcelBuffer = this.generateSmcslExcel('scbmqsxqdqk')
                if (smcsExcelBuffer) {
                    zip.addFile('不明确事项确定情况' + ".xlsx", smcsExcelBuffer, "")
                }
            }
            // 生成政府采购项目情况
            if (this.bmqsxqdqkListLength > 0) {
                let smcsExcelBuffer = this.generateSmcslExcel('sczfcgxmqk')
                if (smcsExcelBuffer) {
                    zip.addFile('政府采购项目情况' + ".xlsx", smcsExcelBuffer, "")
                }
            }
            // #endregion
            zip.writeZip(savePath, (error) => {
                if (error) {
                    this.$message.error('[下载异常]' + error.message)
                    return
                }
                this.$message.success('定密事项相关台账下载已完成')
                // 添加日志
                let paramsLog = {
                    xyybs: 'mk_dbgzrz',
                    id: '-1',
                    ymngnmc: '导出年度定密事项上报数据'
                }
                writeTrajectoryLog(paramsLog)
            })
        },
        // 获取文件流
        generateSmcslExcel(name) {
            //sheet页数据
            let list = []
            let year = new Date().getFullYear() // 获取当前年份
            let date = new Date()
            let Y = date.getFullYear() + '-';
            let M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
            let D = date.getDate() + ' ';
            let dqsj = Y + M + D // 获取填报日期
            let sbdw = getDwxxDatas()[0].dwmc // 获取上报单位
            switch (name) {
                case 'scsmgwhz':
                    var smgwglList = getSmgwglList(new Date().getFullYear())
                    list.push(["涉密岗位统计情况"]) //确定列名
                    list.push(["上报单位：", sbdw]) //确定列名
                    list.push(["统计年度：", year + '年', "", "", "", "", "填报时间：", dqsj]) //确定列名
                    list.push(["序号", "部门", "岗位名称", "涉密等级", "岗位确定依据", "级别职称", "该岗位涉密人员人数", "备注"]) //确定列名
                    let lock1 = 0
                    for (let i in smgwglList) { //每一行的值
                        lock1 = lock1 + 1
                        let item = smgwglList[i] //{"name":"XXX","dw":"XXX","zw":"XXX"}
                        let column = [lock1, item["bm"], item["gwmc"], item["smdj"], item["gwqdyj"], item["gwmc"], item["gwmc"], item["bz"],]
                        list.push(column)
                    }
                    //Excel sheet页的名称
                    var sheet_name = "涉密岗位统计情况"
                    var sum = 9 // 列的数量，根据自身项目进行数据的更改
                    break
                case 'scsmryhz':
                    var smgwglList = getZgsmrylList(new Date().getFullYear())
                    list.push(["涉密人员统计情况"]) //确定列名
                    list.push(["上报单位：", sbdw]) //确定列名
                    list.push(["统计年度：", year + '年', "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "填报时间：", dqsj]) //确定列名
                    list.push(["序号", "姓名", "性别", "年龄", "身份证号", "部门", "岗位名称", "涉密等级", "最高学历", "级别职称", "职务", "职级", "身份类型", "用人形式", "是否审查", "是否出入境登记备案", "是否统一保管出入境证件", "上岗时间（现涉密岗位）", "备注"]) //确定列名
                    let lock2 = 0
                    for (let i in smgwglList) { //每一行的值
                        lock2 = lock2 + 1
                        let item = smgwglList[i] //{"name":"XXX","dw":"XXX","zw":"XXX"}
                        let column = [lock2, item["xm"], item["xb"], item["nl"], item["sfzhm"], item["bm"], item["gwmc"], item["smdj"], item["zgxl"], item["zc"], item["zw"], item["zj"], item["sflx"], item["yrxs"], item["sfsc"], item["crjdjba"], item["tybgcrjzj"], item["sgsj"], item["bz"],]
                        list.push(column)
                    }
                    //Excel sheet页的名称
                    var sheet_name = "涉密人员新增统计情况"
                    var sum = 20 // 列的数量，根据自身项目进行数据的更改
                    break
                case 'scsmryxzhz':
                    var smgwglList = getZgsmrylList(new Date().getFullYear())
                    list.push(["涉密人员新增统计情况"]) //确定列名
                    list.push(["上报单位：", sbdw]) //确定列名
                    list.push(["统计年度：", year + '年', "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "填报时间：", dqsj]) //确定列名
                    list.push(["序号", "姓名", "性别", "年龄", "身份证号", "部门", "岗位名称", "涉密等级", "最高学历", "级别职称", "职务", "职级", "身份类型", "用人形式", "是否审查", "是否出入境登记备案", "是否统一保管出入境证件", "上岗时间（现涉密岗位）", "备注"]) //确定列名
                    let lock15 = 0
                    for (let i in smgwglList) { //每一行的值
                        lock15 = lock15 + 1
                        let item = smgwglList[i] //{"name":"XXX","dw":"XXX","zw":"XXX"}
                        let column = [lock15, item["xm"], item["xb"], item["nl"], item["sfzhm"], item["bm"], item["gwmc"], item["smdj"], item["zgxl"], item["zc"], item["zw"], item["zj"], item["sflx"], item["yrxs"], item["sfsc"], item["crjdjba"], item["tybgcrjzj"], item["sgsj"], item["bz"],]
                        list.push(column)
                    }
                    //Excel sheet页的名称
                    var sheet_name = "涉密人员统计情况"
                    var sum = 20 // 列的数量，根据自身项目进行数据的更改
                    break
                case 'scsmrygwbg':
                    var smgwglList = getGwbgList(new Date().getFullYear())
                    list.push(["涉密人员岗位变更汇总表"]) //确定列名
                    list.push(["上报单位：", sbdw]) //确定列名
                    list.push(["统计年度：", year + '年', "", "", "", "", "", "", "", "", "填报时间：", dqsj]) //确定列名
                    list.push(["序号", "姓名", "性别", "身份证号", "所在部门", "原涉密岗位", "原涉密等级", "变更后涉密岗位", "变更后涉密等级", "入职涉密岗位日期", "变更日期", "备注",]) //确定列名
                    let lock3 = 0
                    for (let i in smgwglList) { //每一行的值
                        lock3 = lock3 + 1
                        let item = smgwglList[i] //{"name":"XXX","dw":"XXX","zw":"XXX"}
                        let column = [lock3, item["xm"], item["xb"], item["sfzhm"], item["bm"], item["gwmc"], item["smdj"], item["bghgwmc"], item["bghsmdj"], item["sgsj"], item["bgsj"], item["bz"],]
                        list.push(column)
                    }
                    //Excel sheet页的名称
                    var sheet_name = "涉密人员岗位变更汇总表"
                    var sum = 13 // 列的数量，根据自身项目进行数据的更改
                    break
                case 'scsmrytmqgl':
                    var smgwglList = getLglzList(new Date().getFullYear())
                    list.push(["涉密人员脱密期管理台账"]) //确定列名
                    list.push(["上报单位：", sbdw]) //确定列名
                    list.push(["统计年度：", year + '年', "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "填报时间：", dqsj]) //确定列名
                    list.push(["序号", "姓名", "性别", "身份证号", "年龄", "原部门", "原涉密岗位", "职务", "职级", "级别职称", "原身份类型", "是否到公安机关备案", "是否委托管理", "脱密期开始时间", "脱密期结束时间", "手机号码", "离职离岗类型", "去向单位名称", "备注",]) //确定列名
                    let lock4 = 0
                    for (let i in smgwglList) { //每一行的值
                        lock4 = lock4 + 1
                        let item = smgwglList[i] //{"name":"XXX","dw":"XXX","zw":"XXX"}
                        let column = [lock4, item["xm"], item["xb"], item["sfzhm"], item["nl"], item["ybm"], item["ygwmc"], item["zw"], item["zj"], item["zc"], item["ysflx"], item["gajgcrjba"], item["sfwtgl"], item["tmqkssj"], item["tmqjssj"], item["sjhm"], item["lzlglx"], item["qxdwmc"], item["bz"],]
                        list.push(column)
                    }
                    //Excel sheet页的名称
                    var sheet_name = "涉密人员脱密期管理台账"
                    var sum = 20 // 列的数量，根据自身项目进行数据的更改
                    break
                case 'scdmzrr':
                    var smgwglList = getDmzrrYear(new Date().getFullYear())
                    list.push(["定密责任人名单"])
                    list.push(["上报单位:", this.dwmc, "", "单位代码:", this.dwdm, "", "",
                        "年度:", year])
                    //对导出的数据进行格式，js导出excel要求传入的参数格式为[["列1名称","列2名称","列3名称"],["列11","值21","值31"],["列12","值22","值32"]]
                    list.push(["序号", "姓名", "身份证号码", "职务", "定密权限", "定密事项（范围）", "类别",
                        "确（指）定时间", "备注"]) //确定列名

                    for (var i in smgwglList) { //每一行的值
                        let item = smgwglList[i] //{"name":"XXX","dw":"XXX","zw":"XXX"}
                        let column = [(parseInt(i) + 1), item["xm"], item["sfzhm"], item["zw"],
                        item["dmqx"], item["dmsx"], item["lb"], item["qdsj"],
                        item["bz"]
                        ]
                        list.push(column)
                    }
                    list.push(["填报人:", this.dwlxr, "", "联系方式:", this.dwlxdh, "", "",
                        "填报时间:", dqsj])
                    //Excel sheet页的名称
                    var sheet_name = "定密责任人名单"
                    var sum = 20 // 列的数量，根据自身项目进行数据的更改
                    break
                case 'scdmsq':
                    var smgwglList = getDmsqYear(new Date().getFullYear())
                    list.push(["定密授权名录"])
                    list.push(["上报单位:", this.dwmc,
                        "单位代码:", this.dwdm, "", "",
                        "年度:", year])
                    //对导出的数据进行格式，js导出excel要求传入的参数格式为[["列1名称","列2名称","列3名称"],["列11","值21","值31"],["列12","值22","值32"]]
                    list.push(["序号", "被授权机关、单位名称",
                        "授权机关/单位名称", "权限", "时间", "期限（年）", "事项", "备注"]) //确定列名
                    for (var i in smgwglList) { //每一行的值
                        let item = smgwglList[i] //{"name":"XXX","dw":"XXX","sqjg":"XXX"}
                        let column = [(parseInt(i) + 1), item["bsqjg"], item["sqjg"],
                        item["dmqx"], item["lksj"], item["qxnd"], item["sx"],
                        item["bz"]
                        ]
                        list.push(column)
                    }
                    list.push(["填报人:", this.dwlxr,
                        "联系方式:", this.dwlxdh, "", "",
                        "填报时间:", dqsj])
                    //Excel sheet页的名称
                    var sheet_name = "涉密人员脱密期管理台账"
                    var sum = 20 // 列的数量，根据自身项目进行数据的更改
                    break
                case 'scgjmmsx':
                    var smgwglList = getGjmmsxYear(new Date().getFullYear())
                    list.push(["国家秘密事项一览表（细目）"])

                    list.push(["填报单位:", this.dwmc, "单位代码:", this.dwdm, "",
                        "年度:", year])
                    list.push(["附件名:", this.wjm, "", "", "", "", "",])
                    //对导出的数据进行格式，js导出excel要求传入的参数格式为[["列1名称","列2名称","列3名称"],["列11","值21","值31"],["列12","值22","值32"]]
                    list.push(["序号", "国家秘密事项名称", "密级", "保密期限",
                        "知悉范围", "定密依据", "备注",]) //确定列名

                    for (var i in smgwglList) { //每一行的值
                        let item = smgwglList[i] //{"name":"XXX","dw":"XXX","zw":"XXX"}
                        let column = [(parseInt(i) + 1), item["gjmmsxmc"],
                        item["mj"], item["bmqx"],
                        item["zxfw"], item["dmyj"], item["bz"]
                        ]
                        list.push(column)
                    }
                    list.push(["填报人:", this.dwlxr, "联系方式:", this.dwlxdh, "",
                        "填报时间:", dqsj])
                    //Excel sheet页的名称
                    var sheet_name = "国家秘密事项一览表（细目）"
                    var sum = 20 // 列的数量，根据自身项目进行数据的更改
                    break
                case 'scdmpx':
                    var smgwglList = getDmpxYear(new Date().getFullYear())
                    list.push(["定密培训情况"])

                    list.push(["上报单位:", this.dwmc, "单位代码:", this.dwdm,
                        "年度:", year])
                    //对导出的数据进行格式，js导出excel要求传入的参数格式为[["列1名称","列2名称","列3名称"],["列11","值21","值31"],["列12","值22","值32"]]
                    list.push(["序号", "培训时间", "学时（小时）", "培训人数（人）",
                        "培训对象", "备注"]) //确定列名

                    for (var i in smgwglList) { //每一行的值
                        let item = smgwglList[i] //{"name":"XXX","dw":"XXX","xs":"XXX"}
                        let column = [(parseInt(i) + 1), item["pxsj"], item["xs"],
                        item["pxrs"], item["pxdx"],
                        item["bz"]
                        ]
                        list.push(column)
                    }
                    list.push(["填报人:", this.dwlxr,
                        "联系方式:", this.dwlxdh,
                        "填报时间:", dqsj])
                    //Excel sheet页的名称
                    var sheet_name = "涉密人员脱密期管理台账"
                    var sum = 20 // 列的数量，根据自身项目进行数据的更改
                    break
                case 'scbmqsxqdqk':
                    var smgwglList = getBmqsxqdqkYear(new Date().getFullYear())
                    list.push(["不明确事项确定情况"])

                    list.push(["填报单位:", this.dwmc, "单位代码", this.dwdm, "", "", "年度",
                        new Date().getFullYear().toString() + '年'
                    ])
                    //对导出的数据进行格式，js导出excel要求传入的参数格式为[["列1名称","列2名称","列3名称"],["列11","值21","值31"],["列12","值22","值32"]]
                    list.push(["序号", "不明确事项产生单位", "事项名称", "密级", "保密期限", "确认时间", "确认理由",
                        "备注"
                    ]) //确定列名

                    for (var i in smgwglList) { //每一行的值
                        let item = smgwglList[i] //{"name":"XXX","dw":"XXX","zw":"XXX"}
                        let column = [(parseInt(i) + 1), item["bmqsxcsdw"], item["sxmc"], item["mj"], item["bmqx"],
                        item["qyrq"], item["qrly"], item["bz"]
                        ]
                        list.push(column)
                    }
                    list.push(["填报人", "", "联系方式", "", "", "", "填报时间", ""
                    ])
                    //Excel sheet页的名称
                    var sheet_name = "涉密人员脱密期管理台账"
                    var sum = 20 // 列的数量，根据自身项目进行数据的更改
                    break
                case 'sczfcgxmqk':
                    var smgwglList = getZfcgxmqkYear(new Date().getFullYear())
                    list.push(["涉密政府采购项目情况"])

                    list.push(["填报单位", this.dwmc, "", "", "", "年度", year
                    ])
                    //对导出的数据进行格式，js导出excel要求传入的参数格式为[["列1名称","列2名称","列3名称"],["列11","值21","值31"],["列12","值22","值32"]]
                    list.push(["序号", "项目名称", "项目种类", "项目密级", "项目金额", "供应商名称",
                        "备注"
                    ]) //确定列名

                    for (var i in smgwglList) { //每一行的值
                        let item = smgwglList[i] //{"name":"XXX","dw":"XXX","zw":"XXX"}
                        let column = [(parseInt(i) + 1), item["mc"], item["zl"], item["mj"], item["je"],
                        item["gysmc"], item["bz"]
                        ]
                        list.push(column)
                    }
                    list.push(["填报人", "", "联系方式", "", "", "填报时间", ""
                    ])
                    //Excel sheet页的名称
                    var sheet_name = "涉密人员脱密期管理台账"
                    var sum = 20 // 列的数量，根据自身项目进行数据的更改
                    break
                case 'scdmqkndtj':
                    var smgwglList = getDmqkndtjYear(new Date().getFullYear())
                    list.push(["定密事项综合统计表"])

                    list.push(["填报单位:", this.dwmc, "", "", "", "单位代码", this.dwdm, "", "", "所属层级", "", "", "", "省", "",
                        "市（地、州、盟）", "", "县（市、区、旗）", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "",
                        "", "", "", "", "", "", "", "", "", "",
                    ])
                    list.push(["机关、单位/地方名称", "1国家秘密统计情况", "", "", "", "", "", "", "", "", "", "", "", "", "", "2定密责任人数", "",
                        "", "", "", "", "", "", "", "", "", "", "3定密授权数", "", "", "", "", "", "", "", "4国家秘密事项一览表（细目）数", "",
                        "", "", "5定密制度", "", "定密培训数", "", "", "工作秘密数", "", "", "备注"
                    ])
                    list.push(["", "国家秘密总数", "", "", "", "原始国家秘密数", "", "", "", "派生国家秘密数", "", "", "", "变更数", "解密数", "定密责任人数",
                        "", "", "", "法定定密责任人数", "", "", "", "指定定密责任人数", "", "", "", "现有总数", "", "", "", "新增数", "", "", "",
                        "现有总数", "", "新增数", "", "", "", "", "", "", "", "", "",
                    ])
                    list.push(["", "总数", "绝密", "机密", "秘密", "合计", "绝密", "机密", "秘密", "合计", "绝密", "机密", "秘密", "", "", "总数",
                        "绝、机、秘", "机、秘", "秘", "合计", "绝、机、秘", "机、秘", "秘", "合计", "绝、机、秘", "机、秘", "秘", "合计", "绝、机、秘", "机、秘",
                        "秘", "新增数合计", "绝、机、秘", "机、秘", "秘", "一览表数", "一览表条目数", "一览表数", "一览表条目数", "现在数", "新制定修订数", "培训次数",
                        "总学时数", "总人数", "工作秘密确定数", "工作秘密清单应制定数", "工作秘密清单实制定数"
                    ])
                    //对导出的数据进行格式，js导出excel要求传入的参数格式为[["列1名称","列2名称","列3名称"],["列11","值21","值31"],["列12","值22","值32"]]
                    for (var i in smgwglList) { //每一行的值
                        let item = smgwglList[i] //{"name":"XXX","dw":"XXX","zw":"XXX"}
                        let column = ["", item["gjmmzshj"], item["gjmmzsjm"], item["gjmmzsjjm"], item["gjmmzsmm"], item[
                            "ysgjmmshj"], item["ysgjmmsjm"], item["ysgjmmsjjm"], item["ysgjmmsmm"],
                            item["psgjmmshj"], item["psgjmmsjm"], item["psgjmmsjjm"], item["psgjmmsmm"], item["bgs"], item[
                            "jms"], item["dmzrrhj"], item["dmzrrjjm"], item["dmzrrjm"], item["dmzrrm"], item["fddmzrrhj"],
                            item["fddmzrrjjm"], item["fddmzrrjm"], item["fddmzrrm"], item[
                            "zddmzrrhj"], item["zddmzrrjjm"], item["zddmzrrjm"], item["zddmzrrm"], item["dmsqxczshj"], item[
                            "dmsqxczsjjm"], item["dmsqxczsjm"], item["dmsqxczsm"], item["dmsqxzshj"], item["dmsqxzsjjm"],
                            item["dmsqxzsjm"], item["dmsqxzsm"], item["gjmmsxylbxzzsylbs"], item["gjmmsxylbxzzsylblms"], item[
                            "gjmmsxylbxzsylbs"], item["gjmmsxylbxzsylblms"], item["dmzdsxczs"], item["dmzdsxzdxds"], item["pxcs"], item["zxss"], item["zrs"], item["gzmmqds"], item["gzmmqdyzds"], item["gzmmqdyzds"], item["bz"],
                        ]
                        console.log(column);
                        list.push(column)
                    }
                    list.push(["填报人", "", "", "", "", "", "", "", "联系方式", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "填报时间", ""])

                    //Excel sheet页的名称
                    var sheet_name = "定密事项综合统计表"
                    var sum = 20 // 列的数量，根据自身项目进行数据的更改
                    break
                default:
                    console.log(0)
            }

            var sheet = XLSX.utils.aoa_to_sheet(list)
            // 设置列宽（这里用到列的数量是用来设置不同列的不同宽度的）
            let counts = smgwglList.length + 5
            sheet['!cols'] = []
            sheet['!rows'] = []
            for (let i = 1; i < sum; i++) {
                sheet['!cols'].push({ wpx: 150 }) // 设置列宽，只有最后一列的宽度是不同的
            }
            for (let j = 1; j < counts; j++) {
                sheet['!rows'].push({ hpx: 14 }) // 设置列宽，只有最后一列的宽度是不同的
            }
            sheet['!rows'][0].hpx = 35
            sheet['!rows'][1].hpx = 20
            sheet['!rows'][2].hpx = 20
            sheet['!rows'][3].hpx = 20
            // 所有设置边框字体水平居中等样式
            for (let key in sheet) {
                if (sheet[key] instanceof Object) {
                    sheet[key].s = {
                        alignment: {
                            horizontal: 'center', // 水平居中
                            vertical: 'center' // 垂直居中
                        },
                        font: {
                            sz: 11, // 字号
                            name: '宋体' // 字体
                        },
                        border: {  // 边框
                            top: {
                                style: 'thin'
                            },
                            bottom: {
                                style: 'thin'
                            },
                            left: {
                                style: 'thin'
                            },
                            right: {
                                style: 'thin'
                            }
                        }
                    }
                }
            }
            // 标题样式修改
            sheet.A1.s = {
                font: {
                    name: '宋体',
                    sz: 16, // 字号
                    bold: true,
                },
                alignment: {
                    horizontal: 'center', // 水平居中
                    vertical: 'center' // 垂直居中
                }
            }
            //switch case
            switch (name) {
                case 'scsmgwhz':
                    // 标题以及输入性标题去掉border
                    sheet.A2.s.border = sheet.A3.s.border = sheet.B2.s.border = sheet.B3.s.border = sheet.F3.s.border = sheet.B3.s.border = sheet.C3.s.border = sheet.D3.s.border = sheet.E3.s.border = sheet.G3.s.border = sheet.H3.s.border = {}
                    // 标题以及输入性标题以及excel首行文字加粗
                    sheet.A2.s.font.bold = sheet.A3.s.font.bold = sheet.F3.s.font.bold = sheet.G3.s.font.bold = sheet.A4.s.font.bold = sheet.B4.s.font.bold = sheet.C4.s.font.bold = sheet.D4.s.font.bold = sheet.E4.s.font.bold = sheet.F4.s.font.bold = sheet.G4.s.font.bold = sheet.H4.s.font.bold = true
                    sheet['!merges'] = [
                        { s: { r: 0, c: 0 }, e: { r: 0, c: 7 } }
                    ];
                    break
                case 'scsmryhz':
                    sheet.A2.s.border = sheet.A3.s.border = sheet.B2.s.border = sheet.B3.s.border = sheet.F3.s.border = sheet.B3.s.border = sheet.C3.s.border = sheet.D3.s.border = sheet.E3.s.border = sheet.F3.s.border = sheet.G3.s.border = sheet.H3.s.border = sheet.I3.s.border = sheet.J3.s.border = sheet.K3.s.border = sheet.L3.s.border = sheet.M3.s.border = sheet.N3.s.border = sheet.O3.s.border = sheet.P3.s.border = sheet.Q3.s.border = sheet.R3.s.border = sheet.S3.s.border = {}
                    sheet.A2.s.font.bold = sheet.R3.s.font.bold = sheet.A3.s.font.bold = sheet.F3.s.font.bold = sheet.G3.s.font.bold = sheet.A4.s.font.bold = sheet.B4.s.font.bold = sheet.C4.s.font.bold = sheet.D4.s.font.bold = sheet.E4.s.font.bold = sheet.F4.s.font.bold = sheet.G4.s.font.bold = sheet.H4.s.font.bold = sheet.I4.s.font.bold = sheet.J4.s.font.bold = sheet.K4.s.font.bold = sheet.L4.s.font.bold = sheet.M4.s.font.bold = sheet.N4.s.font.bold = sheet.O4.s.font.bold = sheet.P4.s.font.bold = sheet.Q4.s.font.bold = sheet.R4.s.font.bold = sheet.S4.s.font.bold = true
                    sheet['!merges'] = [
                        { s: { r: 0, c: 0 }, e: { r: 0, c: 18 } }
                    ];
                    break
                case 'scsmryxzhz':
                    sheet.A2.s.border = sheet.A3.s.border = sheet.B2.s.border = sheet.B3.s.border = sheet.F3.s.border = sheet.B3.s.border = sheet.C3.s.border = sheet.D3.s.border = sheet.E3.s.border = sheet.F3.s.border = sheet.G3.s.border = sheet.H3.s.border = sheet.I3.s.border = sheet.J3.s.border = sheet.K3.s.border = sheet.L3.s.border = sheet.M3.s.border = sheet.N3.s.border = sheet.O3.s.border = sheet.P3.s.border = sheet.Q3.s.border = sheet.R3.s.border = sheet.S3.s.border = {}
                    sheet.A2.s.font.bold = sheet.R3.s.font.bold = sheet.A3.s.font.bold = sheet.F3.s.font.bold = sheet.G3.s.font.bold = sheet.A4.s.font.bold = sheet.B4.s.font.bold = sheet.C4.s.font.bold = sheet.D4.s.font.bold = sheet.E4.s.font.bold = sheet.F4.s.font.bold = sheet.G4.s.font.bold = sheet.H4.s.font.bold = sheet.I4.s.font.bold = sheet.J4.s.font.bold = sheet.K4.s.font.bold = sheet.L4.s.font.bold = sheet.M4.s.font.bold = sheet.N4.s.font.bold = sheet.O4.s.font.bold = sheet.P4.s.font.bold = sheet.Q4.s.font.bold = sheet.R4.s.font.bold = sheet.S4.s.font.bold = true
                    sheet['!merges'] = [
                        { s: { r: 0, c: 0 }, e: { r: 0, c: 18 } }
                    ];
                    break
                case 'scsmrygwbg':
                    sheet.A2.s.border = sheet.A3.s.border = sheet.B2.s.border = sheet.B3.s.border = sheet.F3.s.border = sheet.B3.s.border = sheet.C3.s.border = sheet.D3.s.border = sheet.E3.s.border = sheet.F3.s.border = sheet.G3.s.border = sheet.H3.s.border = sheet.I3.s.border = sheet.J3.s.border = sheet.K3.s.border = sheet.L3.s.border = {}
                    sheet.A2.s.font.bold = sheet.K3.s.font.bold = sheet.A3.s.font.bold = sheet.F3.s.font.bold = sheet.G3.s.font.bold = sheet.A4.s.font.bold = sheet.B4.s.font.bold = sheet.C4.s.font.bold = sheet.D4.s.font.bold = sheet.E4.s.font.bold = sheet.F4.s.font.bold = sheet.G4.s.font.bold = sheet.H4.s.font.bold = sheet.I4.s.font.bold = sheet.J4.s.font.bold = sheet.K4.s.font.bold = sheet.L4.s.font.bold = true
                    sheet['!merges'] = [
                        { s: { r: 0, c: 0 }, e: { r: 0, c: 11 } }
                    ];
                    break
                case 'scsmrytmqgl':
                    sheet.A2.s.border = sheet.A3.s.border = sheet.B2.s.border = sheet.B3.s.border = sheet.F3.s.border = sheet.B3.s.border = sheet.C3.s.border = sheet.D3.s.border = sheet.E3.s.border = sheet.F3.s.border = sheet.G3.s.border = sheet.H3.s.border = sheet.I3.s.border = sheet.J3.s.border = sheet.K3.s.border = sheet.L3.s.border = sheet.M3.s.border = sheet.N3.s.border = sheet.O3.s.border = sheet.P3.s.border = sheet.Q3.s.border = sheet.R3.s.border = sheet.S3.s.border = {}
                    sheet.A2.s.font.bold = sheet.R3.s.font.bold = sheet.A3.s.font.bold = sheet.F3.s.font.bold = sheet.G3.s.font.bold = sheet.A4.s.font.bold = sheet.B4.s.font.bold = sheet.C4.s.font.bold = sheet.D4.s.font.bold = sheet.E4.s.font.bold = sheet.F4.s.font.bold = sheet.G4.s.font.bold = sheet.H4.s.font.bold = sheet.I4.s.font.bold = sheet.J4.s.font.bold = sheet.K4.s.font.bold = sheet.L4.s.font.bold = sheet.M4.s.font.bold = sheet.N4.s.font.bold = sheet.O4.s.font.bold = sheet.P4.s.font.bold = sheet.Q4.s.font.bold = sheet.R4.s.font.bold = sheet.S4.s.font.bold = true
                    sheet['!merges'] = [
                        { s: { r: 0, c: 0 }, e: { r: 0, c: 18 } }
                    ];
                    break
                case 'scsmcsb':
                    sheet.A2.s.border = sheet.A3.s.border = sheet.B2.s.border = sheet.B3.s.border = sheet.F3.s.border = sheet.B3.s.border = sheet.C3.s.border = sheet.D3.s.border = sheet.E3.s.border = sheet.F3.s.border = sheet.G3.s.border = sheet.H3.s.border = sheet.I3.s.border = sheet.J3.s.border = {}
                    sheet.A2.s.font.bold = sheet.I3.s.font.bold = sheet.A3.s.font.bold = sheet.F3.s.font.bold = sheet.G3.s.font.bold = sheet.A4.s.font.bold = sheet.B4.s.font.bold = sheet.C4.s.font.bold = sheet.D4.s.font.bold = sheet.E4.s.font.bold = sheet.F4.s.font.bold = sheet.G4.s.font.bold = sheet.H4.s.font.bold = sheet.I4.s.font.bold = sheet.J4.s.font.bold = true
                    sheet['!merges'] = [
                        { s: { r: 0, c: 0 }, e: { r: 0, c: 9 } }
                    ];
                case 'sccsbg':
                    sheet.A2.s.border = sheet.A3.s.border = sheet.B2.s.border = sheet.B3.s.border = sheet.F3.s.border = sheet.B3.s.border = sheet.C3.s.border = sheet.D3.s.border = sheet.E3.s.border = sheet.F3.s.border = sheet.G3.s.border = sheet.H3.s.border = sheet.I3.s.border = {}
                    sheet.A2.s.font.bold = sheet.H3.s.font.bold = sheet.A3.s.font.bold = sheet.F3.s.font.bold = sheet.G3.s.font.bold = sheet.A4.s.font.bold = sheet.B4.s.font.bold = sheet.C4.s.font.bold = sheet.D4.s.font.bold = sheet.E4.s.font.bold = sheet.F4.s.font.bold = sheet.G4.s.font.bold = sheet.H4.s.font.bold = sheet.I4.s.font.bold = true
                    sheet['!merges'] = [
                        { s: { r: 0, c: 0 }, e: { r: 0, c: 8 } }
                    ];
                    break
                case 'scsmjsj':
                    sheet.A2.s.border = sheet.A3.s.border = sheet.B2.s.border = sheet.B3.s.border = sheet.F3.s.border = sheet.B3.s.border = sheet.C3.s.border = sheet.D3.s.border = sheet.E3.s.border = sheet.F3.s.border = sheet.G3.s.border = sheet.H3.s.border = sheet.I3.s.border = sheet.J3.s.border = sheet.K3.s.border = sheet.L3.s.border = sheet.M3.s.border = sheet.N3.s.border = sheet.O3.s.border = sheet.P3.s.border = sheet.Q3.s.border = sheet.R3.s.border = {}
                    sheet.A2.s.font.bold = sheet.Q3.s.font.bold = sheet.A3.s.font.bold = sheet.F3.s.font.bold = sheet.G3.s.font.bold = sheet.A4.s.font.bold = sheet.B4.s.font.bold = sheet.C4.s.font.bold = sheet.D4.s.font.bold = sheet.E4.s.font.bold = sheet.F4.s.font.bold = sheet.G4.s.font.bold = sheet.H4.s.font.bold = sheet.I4.s.font.bold = sheet.J4.s.font.bold = sheet.K4.s.font.bold = sheet.L4.s.font.bold = sheet.M4.s.font.bold = sheet.N4.s.font.bold = sheet.O4.s.font.bold = sheet.P4.s.font.bold = sheet.Q4.s.font.bold = sheet.R4.s.font.bold = true
                    sheet['!merges'] = [
                        { s: { r: 0, c: 0 }, e: { r: 0, c: 17 } }
                    ];
                    break
                case 'scydccjz':
                    sheet.A2.s.border = sheet.A3.s.border = sheet.B2.s.border = sheet.B3.s.border = sheet.F3.s.border = sheet.B3.s.border = sheet.C3.s.border = sheet.D3.s.border = sheet.E3.s.border = sheet.F3.s.border = sheet.G3.s.border = sheet.H3.s.border = sheet.I3.s.border = sheet.J3.s.border = sheet.K3.s.border = sheet.L3.s.border = sheet.M3.s.border = {}
                    sheet.A2.s.font.bold = sheet.L3.s.font.bold = sheet.A3.s.font.bold = sheet.F3.s.font.bold = sheet.G3.s.font.bold = sheet.A4.s.font.bold = sheet.B4.s.font.bold = sheet.C4.s.font.bold = sheet.D4.s.font.bold = sheet.E4.s.font.bold = sheet.F4.s.font.bold = sheet.G4.s.font.bold = sheet.H4.s.font.bold = sheet.I4.s.font.bold = sheet.J4.s.font.bold = sheet.K4.s.font.bold = sheet.L4.s.font.bold = sheet.M4.s.font.bold = true
                    sheet['!merges'] = [
                        { s: { r: 0, c: 0 }, e: { r: 0, c: 12 } }
                    ];
                    break
                case 'scsmbgzdhsb':
                    sheet.A2.s.border = sheet.A3.s.border = sheet.B2.s.border = sheet.B3.s.border = sheet.F3.s.border = sheet.B3.s.border = sheet.C3.s.border = sheet.D3.s.border = sheet.E3.s.border = sheet.F3.s.border = sheet.G3.s.border = sheet.H3.s.border = sheet.I3.s.border = sheet.J3.s.border = sheet.K3.s.border = sheet.L3.s.border = sheet.M3.s.border = sheet.N3.s.border = {}
                    sheet.A2.s.font.bold = sheet.M3.s.font.bold = sheet.A3.s.font.bold = sheet.F3.s.font.bold = sheet.G3.s.font.bold = sheet.A4.s.font.bold = sheet.B4.s.font.bold = sheet.C4.s.font.bold = sheet.D4.s.font.bold = sheet.E4.s.font.bold = sheet.F4.s.font.bold = sheet.G4.s.font.bold = sheet.H4.s.font.bold = sheet.I4.s.font.bold = sheet.J4.s.font.bold = sheet.K4.s.font.bold = sheet.L4.s.font.bold = sheet.M4.s.font.bold = sheet.N4.s.font.bold = true
                    sheet['!merges'] = [
                        { s: { r: 0, c: 0 }, e: { r: 0, c: 13 } }
                    ];
                    break
                case 'scsmwlsb':
                    sheet.A2.s.border = sheet.A3.s.border = sheet.B2.s.border = sheet.B3.s.border = sheet.F3.s.border = sheet.B3.s.border = sheet.C3.s.border = sheet.D3.s.border = sheet.E3.s.border = sheet.F3.s.border = sheet.G3.s.border = sheet.H3.s.border = sheet.I3.s.border = sheet.J3.s.border = sheet.K3.s.border = sheet.L3.s.border = sheet.M3.s.border = sheet.N3.s.border = {}
                    sheet.A2.s.font.bold = sheet.M3.s.font.bold = sheet.A3.s.font.bold = sheet.F3.s.font.bold = sheet.G3.s.font.bold = sheet.A4.s.font.bold = sheet.B4.s.font.bold = sheet.C4.s.font.bold = sheet.D4.s.font.bold = sheet.E4.s.font.bold = sheet.F4.s.font.bold = sheet.G4.s.font.bold = sheet.H4.s.font.bold = sheet.I4.s.font.bold = sheet.J4.s.font.bold = sheet.K4.s.font.bold = sheet.L4.s.font.bold = sheet.M4.s.font.bold = sheet.N4.s.font.bold = true
                    sheet['!merges'] = [
                        { s: { r: 0, c: 0 }, e: { r: 0, c: 13 } }
                    ];
                    break
                case 'scaqcp':
                    sheet.A2.s.border = sheet.A3.s.border = sheet.B2.s.border = sheet.B3.s.border = sheet.F3.s.border = sheet.B3.s.border = sheet.C3.s.border = sheet.D3.s.border = sheet.E3.s.border = sheet.F3.s.border = sheet.G3.s.border = sheet.H3.s.border = {}
                    sheet.A2.s.font.bold = sheet.G3.s.font.bold = sheet.A3.s.font.bold = sheet.F3.s.font.bold = sheet.G3.s.font.bold = sheet.A4.s.font.bold = sheet.B4.s.font.bold = sheet.C4.s.font.bold = sheet.D4.s.font.bold = sheet.E4.s.font.bold = sheet.F4.s.font.bold = sheet.G4.s.font.bold = sheet.H4.s.font.bold = true
                    sheet['!merges'] = [
                        { s: { r: 0, c: 0 }, e: { r: 0, c: 7 } }
                    ];
                    break
                case 'scsmzt':
                    sheet.A2.s.border = sheet.A3.s.border = sheet.B2.s.border = sheet.B3.s.border = sheet.F3.s.border = sheet.B3.s.border = sheet.C3.s.border = sheet.D3.s.border = sheet.E3.s.border = sheet.F3.s.border = sheet.G3.s.border = sheet.H3.s.border = sheet.I3.s.border = sheet.J3.s.border = sheet.K3.s.border = sheet.L3.s.border = sheet.M3.s.border = sheet.N3.s.border = sheet.O3.s.border = {}
                    sheet.A2.s.font.bold = sheet.N3.s.font.bold = sheet.A3.s.font.bold = sheet.F3.s.font.bold = sheet.G3.s.font.bold = sheet.A4.s.font.bold = sheet.B4.s.font.bold = sheet.C4.s.font.bold = sheet.D4.s.font.bold = sheet.E4.s.font.bold = sheet.F4.s.font.bold = sheet.G4.s.font.bold = sheet.H4.s.font.bold = sheet.I4.s.font.bold = sheet.J4.s.font.bold = sheet.K4.s.font.bold = sheet.L4.s.font.bold = sheet.M4.s.font.bold = sheet.N4.s.font.bold = sheet.O4.s.font.bold = true
                    sheet['!merges'] = [
                        { s: { r: 0, c: 0 }, e: { r: 0, c: 14 } }
                    ];
                    break
                case 'scsmryxz':
                    sheet.A2.s.border = sheet.A3.s.border = sheet.B2.s.border = sheet.B3.s.border = sheet.F3.s.border = sheet.B3.s.border = sheet.C3.s.border = sheet.D3.s.border = sheet.E3.s.border = sheet.F3.s.border = sheet.G3.s.border = sheet.H3.s.border = sheet.I3.s.border = sheet.J3.s.border = sheet.K3.s.border = {}
                    sheet.A2.s.font.bold = sheet.J3.s.font.bold = sheet.A3.s.font.bold = sheet.F3.s.font.bold = sheet.G3.s.font.bold = sheet.A4.s.font.bold = sheet.B4.s.font.bold = sheet.C4.s.font.bold = sheet.D4.s.font.bold = sheet.E4.s.font.bold = sheet.F4.s.font.bold = sheet.G4.s.font.bold = sheet.H4.s.font.bold = sheet.I4.s.font.bold = sheet.J4.s.font.bold = sheet.K4.s.font.bold = true
                    sheet['!merges'] = [
                        { s: { r: 0, c: 0 }, e: { r: 0, c: 10 } }
                    ];
                    break
                case 'scbmzddj':
                    sheet.A2.s.border = sheet.A3.s.border = sheet.B2.s.border = sheet.B3.s.border = sheet.F3.s.border = sheet.B3.s.border = sheet.C3.s.border = sheet.D3.s.border = sheet.E3.s.border = sheet.F3.s.border = {}
                    sheet.A2.s.font.bold = sheet.E3.s.font.bold = sheet.A3.s.font.bold = sheet.F3.s.font.bold = sheet.A4.s.font.bold = sheet.B4.s.font.bold = sheet.C4.s.font.bold = sheet.D4.s.font.bold = sheet.E4.s.font.bold = sheet.F4.s.font.bold = true
                    sheet['!merges'] = [
                        { s: { r: 0, c: 0 }, e: { r: 0, c: 5 } }
                    ];
                    break
                case 'scdmzrr':
                    sheet['!merges'] = [{
                        s: { //s为开始
                            c: 0, //开始列
                            r: 0 //开始取值范围
                        },
                        e: { //e结束
                            c: 8, //结束列
                            r: 0 //结束范围
                        }
                    }]
                    break
                case 'scdmsq':
                    sheet['!merges'] = [{
                        s: { //s为开始
                            c: 0, //开始列
                            r: 0 //开始取值范围
                        },
                        e: { //e结束
                            c: 7, //结束列
                            r: 0 //结束范围
                        }
                    }]
                    break
                case 'scgjmmsx':
                    sheet['!merges'] = [{
                        s: { //s为开始
                            c: 0, //开始列
                            r: 0 //开始取值范围
                        },
                        e: { //e结束
                            c: 6, //结束列
                            r: 0 //结束范围
                        }
                    }]
                    break
                case 'scdmpx':
                    sheet['!merges'] = [{
                        s: { //s为开始
                            c: 0, //开始列
                            r: 0 //开始取值范围
                        },
                        e: { //e结束
                            c: 5, //结束列
                            r: 0 //结束范围
                        }
                    }]
                    break
                case 'scbmqsxqdqk':
                    sheet['!merges'] = [{
                        s: { //s为开始
                            c: 0, //开始列
                            r: 0 //开始取值范围
                        },
                        e: { //e结束
                            c: 7, //结束列
                            r: 0 //结束范围
                        }
                    },
                    ]
                    break
                case 'sczfcgxmqk':
                    sheet['!merges'] = [{
                        s: { //s为开始
                            c: 0, //开始列
                            r: 0 //开始取值范围
                        },
                        e: { //e结束
                            c: 6, //结束列
                            r: 0 //结束范围
                        }
                    }]
                    break
                case 'scdmqkndtj':
                    sheet['!merges'] = [{
                        s: { //s为开始
                            c: 0, //开始列
                            r: 0 //开始取值范围
                        },
                        e: { //e结束
                            c: 11, //结束列
                            r: 0 //结束范围
                        }
                    },
                    {
                        s: {
                            c: 1,
                            r: 1
                        },
                        e: {
                            c: 4,
                            r: 1
                        }
                    },
                    {
                        s: {
                            c: 6,
                            r: 1
                        },
                        e: {
                            c: 8,
                            r: 1
                        }
                    },
                    {
                        s: {
                            c: 10,
                            r: 1
                        },
                        e: {
                            c: 12,
                            r: 1
                        }
                    },
                    {
                        s: {
                            c: 0,
                            r: 2
                        },
                        e: {
                            c: 0,
                            r: 4
                        }
                    },
                    {
                        s: {
                            c: 1,
                            r: 2
                        },
                        e: {
                            c: 14,
                            r: 2
                        }
                    },
                    {
                        s: {
                            c: 15,
                            r: 2
                        },
                        e: {
                            c: 26,
                            r: 2
                        }
                    },
                    {
                        s: {
                            c: 27,
                            r: 2
                        },
                        e: {
                            c: 34,
                            r: 2
                        }
                    },
                    {
                        s: {
                            c: 35,
                            r: 2
                        },
                        e: {
                            c: 38,
                            r: 2
                        }
                    },
                    {
                        s: {
                            c: 39,
                            r: 2
                        },
                        e: {
                            c: 40,
                            r: 2
                        }
                    },
                    {
                        s: {
                            c: 41,
                            r: 2
                        },
                        e: {
                            c: 43,
                            r: 2
                        }
                    },
                    {
                        s: {
                            c: 44,
                            r: 2
                        },
                        e: {
                            c: 46,
                            r: 2
                        }
                    },
                    {
                        s: {
                            c: 1,
                            r: 3
                        },
                        e: {
                            c: 4,
                            r: 3
                        }
                    },
                    {
                        s: {
                            c: 5,
                            r: 3
                        },
                        e: {
                            c: 8,
                            r: 3
                        }
                    },
                    {
                        s: {
                            c: 9,
                            r: 3
                        },
                        e: {
                            c: 12,
                            r: 3
                        }
                    },
                    {
                        s: {
                            c: 13,
                            r: 3
                        },
                        e: {
                            c: 13,
                            r: 4
                        }
                    },
                    {
                        s: {
                            c: 14,
                            r: 3
                        },
                        e: {
                            c: 14,
                            r: 4
                        }
                    },
                    {
                        s: {
                            c: 15,
                            r: 3
                        },
                        e: {
                            c: 18,
                            r: 3
                        }
                    },
                    {
                        s: {
                            c: 19,
                            r: 3
                        },
                        e: {
                            c: 22,
                            r: 3
                        }
                    },
                    {
                        s: {
                            c: 23,
                            r: 3
                        },
                        e: {
                            c: 26,
                            r: 3
                        }
                    },
                    {
                        s: {
                            c: 27,
                            r: 3
                        },
                        e: {
                            c: 30,
                            r: 3
                        }
                    },
                    {
                        s: {
                            c: 31,
                            r: 3
                        },
                        e: {
                            c: 34,
                            r: 3
                        }
                    },
                    {
                        s: {
                            c: 35,
                            r: 3
                        },
                        e: {
                            c: 36,
                            r: 3
                        }
                    },
                    {
                        s: {
                            c: 37,
                            r: 3
                        },
                        e: {
                            c: 38,
                            r: 3
                        }
                    },
                    {
                        s: {
                            c: 39,
                            r: 3
                        },
                        e: {
                            c: 40,
                            r: 3
                        }
                    },
                    {
                        s: {
                            c: 41,
                            r: 3
                        },
                        e: {
                            c: 43,
                            r: 3
                        }
                    },
                    {
                        s: {
                            c: 44,
                            r: 3
                        },
                        e: {
                            c: 46,
                            r: 3
                        }
                    },
                    {
                        s: {
                            c: 39,
                            r: 2
                        },
                        e: {
                            c: 39,
                            r: 3
                        }
                    },
                    {
                        s: {
                            c: 40,
                            r: 2
                        },
                        e: {
                            c: 40,
                            r: 3
                        }
                    },
                    {
                        s: {
                            c: 41,
                            r: 2
                        },
                        e: {
                            c: 41,
                            r: 3
                        }
                    },
                    {
                        s: {
                            c: 42,
                            r: 2
                        },
                        e: {
                            c: 42,
                            r: 3
                        }
                    },
                    {
                        s: {
                            c: 43,
                            r: 2
                        },
                        e: {
                            c: 43,
                            r: 3
                        }
                    },
                    {
                        s: {
                            c: 44,
                            r: 2
                        },
                        e: {
                            c: 44,
                            r: 3
                        }
                    },
                    {
                        s: {
                            c: 45,
                            r: 2
                        },
                        e: {
                            c: 45,
                            r: 3
                        }
                    },
                    {
                        s: {
                            c: 46,
                            r: 2
                        },
                        e: {
                            c: 46,
                            r: 3
                        }
                    },
                    {
                        s: {
                            c: 47,
                            r: 2
                        },
                        e: {
                            c: 47,
                            r: 4
                        }
                    },


                    ]
                    break
                default:
                    console.log(0)
            }
            //新建book
            var work_book = XLSX.utils.book_new()
            //将数据添加到工作薄
            XLSX.utils.book_append_sheet(work_book, sheet, sheet_name)
            const workbookArrayBuffer = workbook2ArrayBuffer(work_book)
            //arrayBuffer转Buffer
            var buf = new Buffer(workbookArrayBuffer.byteLength)
            var view = new Uint8Array(workbookArrayBuffer)
            for (var i = 0; i < buf.length; ++i) {
                buf[i] = view[i]
            }
            return buf
        },
    },
    watch: {},
    mounted() {

        this.dwmc = getlogin()[0].dwmc
        this.dwdm = getlogin()[0].xydm
        this.dwlxr = getlogin()[0].dwlxr
        this.dwlxdh = getlogin()[0].dwlxdh
    }
};
</script>
<style scoped>
/* 样式改版2022/12/13 */
.dbItem {
    padding-bottom: 40px;
    background: #FFFFFF;
    border: 1px solid rgba(219, 231, 255, 1);
    box-shadow: 0px 2px 10px 0px rgba(107, 117, 134, 0.15);
    overflow: hidden;
    margin-bottom: 20px;
    position: relative;
    border-radius: 6px;
}

.dbItem .fonts {
    font-family: 'SourceHanSansSCziti';
    font-size: 20px;
    color: #080808;
    font-weight: 400;
    padding-top: 18px;
    padding-left: 20px;
}

.dbItem .buttons {
    position: absolute;
    right: 40px;
    top: 25px;
    width: 100px;
    height: 32px;
    background: #FFFFFF;
    border: 1px solid rgba(2, 111, 222, 1);
    font-family: 'SourceHanSansSCziti';
    font-size: 16px;
    color: #1766D1;
    font-weight: 400;
    border-radius: 50px;
    text-align: center;
    line-height: 32px;
    cursor: pointer;
}

.dbItem .title {
    font-family: 'SourceHanSansSCziti';
    font-size: 18px;
    color: #666666;
    font-weight: 400;
    padding-top: 32px;
    padding-left: 23px;
    overflow: hidden;
    cursor: pointer;
    float: left;
    margin-left: 40px;
}

.titleDiv {
    margin-left: -40px;
}

.dbItem .title img {
    float: left;
    width: 24px;
    height: 24px;
}

.dbItem .title span {
    display: block;
    float: left;
    margin-left: 12px;
}


.zdwb {
    width: 100%;
    height: 100%;
    /* box-shadow: 0px 1px 12px 0px rgba(0, 0, 0, 0.1); */
    /* background: #FFFFFF; */
    /* overflow-y: scroll; */
}

.mk_dbgz {
    width: 100%;
    /* height: 12vw; */
    /* background-color: rgba(255, 255, 1, 0.5); */

}

.mk_bt {
    width: 100%;
    height: 3vw;
    border-bottom: 1px solid rgba(216, 216, 216, 1);

}

.mk_btl {
    display: flex;
    align-items: center;
    margin-left: 20px;
    font-size: .9vw;
    height: 100%;
}

.mk-nr {
    display: flex;
    align-items: center;
    padding: 15px 0px;
    /* margin-bottom: 10px; */
}

.mk-nr-div {
    width: 9vw;
    /* height: 9vw; */
    cursor: pointer;
}

.nr-div {
    margin-top: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.yuan {
    width: 60px;
    height: 60px;
    /* background: url(./images/img1026_18.png) no-repeat center; */
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50px;
    background: #EF6B43;
}
.scBtnFont {
    float: right;
    /* margin-bottom: 10px; */
    position: absolute;
    right: 20px;
    /* bottom: 10px; */
    top: 35px;
    color: #195BC7;
    cursor: pointer;
}
.ym-wz {
    font-size: 14px;
    color: #333333;
    letter-spacing: 0;
    text-align: center;
    line-height: 19.6px;
    font-weight: 400;
    margin-top: .5vw;
}

.dbTitle {
    margin-left: 10px;
}

.ywcFont {
    background: #21A566;
    text-align: center;
    border-radius: 50px;
    color: #ffffff;
    font-size: 0.7vw;
    width: 80px;
    padding: 3px 0px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 10px;
    /* margin-left: calc(100% - 126px); */
}

.wwcFont {
    background: #EF6B43;
    text-align: center;
    border-radius: 50px;
    color: #ffffff;
    font-size: 0.7vw;
    width: 80px;
    padding: 3px 0px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 10px;
    /* margin-left: calc(100% - 126px); */
}

.dwsBkg {
    background: #EF6B43;
}

.ywcBkg {
    background: #21A566;
}

.dbgzContainer {
    height: 81vh;
    width: 99%;
    margin: 20px auto;
    height: 100%;
    /* box-shadow: 0px 1px 12px 0px rgba(0,0,0,0.1); */
    overflow-y: scroll;
    margin-top: 20px;
    font-family: 'SourceHanSansSCziti';
}

.pfather {
    display: flex;
    justify-content: center;
}
</style>