import Vue from 'vue'
import axios from 'axios'

import App from './App'
import router from './router'
import store from './store'
import "./assets/style/commonStyle.css";

// 引入elementUI
import ElementUI from 'element-ui'
import { Message } from 'element-ui'

console.log(ElementUI)


Vue.use(ElementUI)

/**
 * 重写message
*/
let messageInstance
Vue.prototype.$message = function(options) {
  // console.log('options', options, Object.prototype.toString.call(options))
  if(messageInstance) {
    messageInstance.close()
  }
  if(Object.prototype.toString.call(options) == '[object Object]') {
    options.offset = 100
    messageInstance = Message(options)
  } else {
    messageInstance = Message({
      type: 'warning',
      message: options,
      offset: 100
    })
  }
  // console.log(messageInstance)
  return messageInstance
}
Vue.prototype.$message.success = function(options) {
  if(messageInstance) {
    messageInstance.close()
  }
  messageInstance = Message({
    type: 'success',
    message: options,
    offset: 100
  })
  return messageInstance
}
Vue.prototype.$message.error = function(options) {
  if(messageInstance) {
    messageInstance.close()
  }
  messageInstance = Message({
    type: 'error',
    message: options,
    offset: 100
  })
  return messageInstance
}
Vue.prototype.$message.warning = function(options) {
  if(messageInstance) {
    messageInstance.close()
  }
  messageInstance = Message({
    type: 'warning',
    message: options,
    offset: 100
  })
  return messageInstance
}
Vue.prototype.$message.info = function(options) {
  if(messageInstance) {
    messageInstance.close()
  }
  messageInstance = Message({
    type: 'info',
    message: options,
    offset: 100
  })
  return messageInstance
}
Vue.prototype.$message.close = function (id, userOnClose) {
  return ElementUI.Message.close(id, userOnClose)
}
Vue.prototype.$message.closeAll = function () {
  return ElementUI.Message.closeAll()
}

// 引入elementUI样式表
import 'element-ui/lib/theme-chalk/index.css'
// 引入公共样式表-锚点菜单
import '../style/md_menu.css'
// 引入公共样式表-dialog自定义样式
import '../style/dialog_customize.css'

// // 用户屏幕尺寸
// const { screen } = require('electron')
// const primaryDisplay = screen.getPrimaryDisplay()
// const { width, height } = primaryDisplay.workAreaSize
// console.log('屏幕尺寸',width, height)
// const allDisplay = screen.getAllDisplays()
// console.log('屏幕尺寸', allDisplay)

// // 用户行为数据
// import bPoint from '../utils/setPoint'
// Vue.prototype.bPoint = bPoint

// // 埋点指令
// Vue.directive('bpoint', {
//   bind: (el, binding, vnode) => {
//     el.addEventListener('click', () => {
//       const data = binding.value
//       bPoint.setPoint(data)
//     }, false)
//   }
// })

import * as echarts from 'echarts'
import 'echarts-liquidfill'
Vue.prototype.$echarts = echarts //挂载到Vue实例上面

if (!process.env.IS_WEB) Vue.use(require('vue-electron'))
Vue.http = Vue.prototype.$http = axios
Vue.config.productionTip = false

/**
 * 解决如下警告
 * Electron Security Warning (Node.js Integration with Remote Content) This renderer process has Node.js integration enabled and attempted to load remote content. This exposes users of this app to severe security risks.
 */
process.env['ELECTRON_DISABLE_SECURITY_WARNINGS'] = 'true'

/* eslint-disable no-new */
new Vue({
  components: { App },
  router,
  store,
  template: '<App/>',
}).$mount('#app')
// 在主进程的main.js或类似文件中
const { app, BrowserWindow } = require('electron');

app.whenReady().then(() => {
  const mainWindow = new BrowserWindow({
    webPreferences: {
      enableRemoteModule: true, // 启用remote模块
      nodeIntegration: true,
      contextIsolation: false
    }
  });

  mainWindow.loadFile('index.html');
});