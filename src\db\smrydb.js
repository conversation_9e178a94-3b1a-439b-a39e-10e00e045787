import db from './adapter/zczpAdaptor'

import { getUuid } from '../utils/getUuid'
// 日志工具类
import { writeTrajectoryLog } from '../utils/logUtils'

// 获取所有sm人员信息(上报数据自选模式专用)
export const getAllSmryZxms = () => {
  return db.get('Smry_list').find({ zgzt: 1 }).cloneDeep().value()
}

// 通过人员ID获取人员信息
export const getSmryBySmryid = (smryid) => {
  if (!smryid) {
    console.log('通过人员ID获取人员信息失败，smryid为空')
    return
  }
  return db.get('Smry_list').find({ smryid: smryid }).cloneDeep().value()
}

export const getsmryxg = (params) => {
  console.log(params)
  let sfzhm = params.sfzhm
  db.get('Smry_list')
    .find({ sfzhm: sfzhm })
    .assign({
      bm: params.bgbm,
      bz: params.bz,
      gwmc: params.bghgwmc,
      gwqdyj: params.gwqdyj,
      smdj: params.bghsmdj,
    })
    .write()
}
export const deletesmry1 = (params) => {
  let sfzhm = params.sfzhm
  db.get('Smry_list').find({ sfzhm: sfzhm }).assign({ zgzt: 0 }).write()
}
//涉密人员管理-----------------------------------涉密人员管理初始化列表********
export const getsmry1 = () => {
  let smry = db.get('Smry_list').filter(function(item){
    if (item.zgzt == 1) {
      return item
    }
  }).cloneDeep().value()
  console.log()
  return smry
}
export const getsmry2 = (xm1) => {
  console.log(xm1)
  /**
   * 这里的参数需要拷贝一份来进行查询
   * 因为：当对象不变的情况下，如果只是值变了，lowDB会认为这是同一个对象，此时会提取之前的查询结果，可以认为是缓存，而没有真正的去查询
   */
  const xm = xm1
  return db
    .get('Smry_list')
    .find({
      xm: xm,
    })
    .cloneDeep()
    .value()
}
export const getsmry = (params) => {
  let page = params.page
  let pagesize = params.pageSize
  let xm = params.xm
  let bm
  if (typeof params.bm == 'object') {
    bm = params.bm.join('/')
  }
  let list_total = db
    .get('Smry_list')
    .sortBy('cjsj')
    .filter(function (item) {
      // console.log(item)
      if (item.zgzt == 1) {
        if ((xm === undefined || xm == '') && (bm === undefined || bm == '')) {
          return item
          console.log('查空', item)
        } else if (xm && (bm === undefined || bm == '')) {
          if (item.xm) {
            if (item.xm.indexOf(xm) != -1) {
              console.log('名称', item)
              return item
            }
          } else {
            console.log('item.xm', item.xm)
          }
        } else if ((xm === undefined || xm == '') && bm) {
          if (item.bm) {
            if (item.bm.indexOf(bm) != -1) {
              console.log('名称', item)
              return item
            }
          } else {
            console.log('item.bm', item.bm)
          }
        } else if (xm && bm) {
          if (item.xm && item.bm) {
            if (item.xm.indexOf(xm) != -1 && item.bm.indexOf(bm) != -1) {
              console.log('名称', item)
              return item
            }
          } else {
            console.log('item.xm', item.xm, 'item.bm', item.bm)
          }
        }
      }

    })
    .cloneDeep()
    .value()

  // 手动分页
  // console.log(pagesize * (page - 1), pagesize * (page - 1) + pagesize)
  let pageList = list_total.slice(
    pagesize * (page - 1),
    pagesize * (page - 1) + pagesize
  )

  let resList = {
    list: pageList,
    list_total: list_total,
    total: list_total.length,
  }
  console.log('涉密人员管理', resList)
  return resList
}
//涉密人员管理-----------------------------------涉密人员管理添加成员********
export const addsmry = (params) => {
  let sfzhm = params.sfzhm
  let checkObj = db.get('Smry_list').find({sfzhm:sfzhm}).cloneDeep().value()
  console.log('checkObj', checkObj)
  if(checkObj) {
    throw new Error('已存在该人员['+sfzhm+']')
  }
  // 增加涉密人员ID
  params.smryid = getUuid()
  let sjc = new Date().getTime()
  params.zgzt = 1
  params.cjsj = sjc
  params.gxsj = sjc
  db.read().get('Smry_list').push(params).write()

  // db.read().get("Smry_list").push(params).write();
  // 添加日志
  let paramsLog = {
    xyybs: 'mk_smry',
    id: params.smryid,
    ymngnmc: '新增',
    extraParams: {
      xm: params.xm,
      sfzhm: params.sfzhm,
      bm: params.bm,
      gwmc: params.gwmc,
      smdj: params.smdj,
      bz: params.bz
    }
  }
  writeTrajectoryLog(paramsLog)
}
export const jxsfzhm = (params) => {
  let sfzhm = params
  let message = 0
  console.log(sfzhm)
  let pd = db.read().get('Smry_list').find({ sfzhm: sfzhm }).cloneDeep().value()
  if (pd == undefined) {
    console.log(0)
    message = 0
  } else {
    console.log(2)
    message = 2
  }
  return message
}
//涉密人员管理-----------------------------------涉密人员管理删除成员********
export const deletesmry = (params) => {
  console.log(params)
  let sfzhm = params.sfzhm
  let ryxz = db
    .read()
    .get('Ryxz_list')
    .filter({ sfzhm: sfzhm })
    .cloneDeep()
    .value()
  let gwbg = db
    .read()
    .get('Gwbg_list')
    .filter({ sfzhm: sfzhm })
    .cloneDeep()
    .value()
  console.log(ryxz)
  console.log(gwbg)
  ryxz.forEach((item) => {
    db.read().get('Ryxz_list').remove(item).write()
  })
  gwbg.forEach((item) => {
    db.read().get('Gwbg_list').remove(item).write()
  })
  db.read().get('Smry_list').remove(params).write()
}
export const deletesmry2 = (params) => {
  // if(!params) {
  //   console.log('离岗离职撤销参数为空')
  //   return
  //  }
  //  let sfzhm = params.sfzhm
  //  // 参数校验
  //  if(!sfzhm) {
  //   console.log('离岗离职撤销人员身份证号码为空')
  //   return
  //  }
   // 删除该离岗离职记录
  //  let delTargetObj = db.get('Smry_list').find({sfzhm:sfzhm}).cloneDeep().value()
    db.get("Smry_list").remove().write()
}
//涉密人员管理-----------------------------------涉密人员管理修改成员********
export const updatesmry = (params) => {
  let bm = params.bm.join('/')
  params.bm = bm
  // 数据校验
  // 校验ID
  let smryid = params.smryid
  if (!smryid || smryid == '') {
    return
  }
  params.gxsj = new Date().getTime()
  // 全字段更新方法(传进来的字段都会更新)
  db.read().get('Smry_list').find({ smryid: smryid }).assign(params).write()
  // // 更新部分字段方法
  // db.read().get('Bmzd_list').find({smryid:smryid}).assign({
  // 	zdmc: params.zdmc,
  // 	...
  // }).write()
}
export const getZw = () => {
  let zw = db.get('Smry_list').filter(function(item){
    if (item.zgzt == 1) {
      return item
    }
  }).cloneDeep().value()
  console.log()
  return zw
}
export const getZj = () => {
  let zw = db.get('Smry_list').filter(function(item){
    if (item.zgzt == 1) {
      return item
    }
  }).cloneDeep().value()
  console.log()
  return zw
}
