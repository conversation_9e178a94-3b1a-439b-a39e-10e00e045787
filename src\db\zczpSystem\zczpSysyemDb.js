import DB from '../adapter/zczpSystemAdaptor'

import { getUuid } from '../../utils/getUuid'

// 获取全部文件相关
export const getAllWjxgList = () => {
  return DB.get('wjxg_list').cloneDeep().value()[0]
}

/*****
 * 系统参数设置表
 * ******/
// 插入参数设置表
export const insertSettingList = (params) => {
  params.settingid = getUuid()
  DB.get('setting_list').push(params).write()
}

// 通过参数标识获取参数信息
export const selectSettingListByCsbs = (csbs) => {
  if(!csbs) {
    console.log('参数标识为空', csbs)
    return
  }
  return DB.get('setting_list').find({csbs:csbs}).cloneDeep().value()
}

// 获取参数设置表集合
export const selectSettingList = (params) => {
  let settingid
  let page
  let pageSize
  if (params) {
    settingid = params.settingid
    page = params.page
    pageSize = params.pageSize
  }
  let list_total = DB.get('setting_list')
    .filter((item) => {
      if (!settingid) {
        return item
      }
      if (item.settingid == settingid) {
        return item
      }
    })
    .cloneDeep()
    .sortBy('fzh')
    .value()
  // 手动分页
  let pageList = list_total
  if (page && pageSize && list_total) {
    pageList = list_total.slice(
      pageSize * (page - 1),
      pageSize * (page - 1) + pageSize
    )
  }
  let resList = {
    list: pageList,
    list_total: list_total,
    total: list_total.length,
  }
  console.log('系统参数查询结果', resList)
  return resList
}

// 删除参数设置表
export const deleteSettingList = (params) => {
  let settingid
  if (params) {
    settingid = params.settingid
  }
  if (!settingid) {
    console.log('参数设置ID为空')
    return
  }
  console.log('settingid', settingid)
  DB.get('setting_list').remove({ settingid: settingid }).write()
}

// 更新参数设置表
export const updateSettingList = (params) => {
  let settingid
  if (params) {
    settingid = params.settingid
  }
  if (!settingid) {
    console.log('参数设置ID为空')
    return
  }
  console.log('settingid', settingid)
  DB.get('setting_list').find({ settingid: settingid }).assign(params).write()
}

/*****
 * 文件路径设置表
 * ******/
// 获取文件路径设置表集合
export const selectWjxgList = (params) => {
  let filesettingid
  let page
  let pageSize
  if (params) {
    filesettingid = params.filesettingid
    page = params.page
    pageSize = params.pageSize
  }
  let list_total = DB.get('wjxg_list')
    .filter((item) => {
      if (!filesettingid) {
        return item
      }
      if (item.filesettingid == filesettingid) {
        return item
      }
    })
    .cloneDeep()
    .sortBy('fzh')
    .value()
  // 手动分页
  let pageList = list_total
  if (page && pageSize && list_total) {
    pageList = list_total.slice(
      pageSize * (page - 1),
      pageSize * (page - 1) + pageSize
    )
  }
  let resList = {
    list: pageList,
    list_total: list_total,
    total: list_total.length,
  }
  console.log('文件路径查询结果', resList)
  return resList
}

// 插入文件路径设置表
export const insertWjxgList = (params) => {
  // 加入ID
  params.filesettingid = getUuid()
  params.gxsj = new Date().getTime()
  DB.get('wjxg_list').push(params).write()
}

// 删除文件路径设置表
export const deleteWjxgList = (params) => {
  // 校验ID
  let filesettingid = params.filesettingid
  if (!filesettingid) {
    console.log('文件相关设置ID为空', params)
    return
  }
  DB.get('wjxg_list').remove({ filesettingid: filesettingid }).write()
}

// 修改文件路径设置表
export const updateWjxgList = (params) => {
  // 校验ID
  let filesettingid
  if (params) {
    filesettingid = params.filesettingid
  }
  if (!filesettingid) {
    console.log('文件相关设置ID为空', params)
    return
  }
  params.gxsj = new Date().getTime()
  DB.get('wjxg_list')
    .find({ filesettingid: filesettingid })
    .assign(params)
    .write()
}

// 通过路径标识获取文件路径设置
export const selectWjxgListByCsbs = (params) => {
  // 校验标识
  let csbs
  if (params) {
    csbs = params.csbs
  }
  if (!csbs) {
    console.log('文件相关设置参数标识为空', params)
    return
  }
  return DB.get('wjxg_list').find({ csbs: csbs }).cloneDeep().value()
}
