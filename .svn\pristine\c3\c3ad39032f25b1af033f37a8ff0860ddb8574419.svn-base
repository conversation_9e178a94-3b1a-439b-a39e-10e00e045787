<template>
  <div class="bg_con" style="height: calc(100% - 38px);">
    <div style="width: 100%; position: relative; overflow: hidden;height: 100%; ">
      <!-- <div class="xmlb-title" style=" cursor: pointer;">
				<span style="font-size: 24px; cursor: pointer;">涉密载体台账</span>
				<span style="" @click="returnSy" class="fhsy">返回</span>
			</div> -->

      <div class="dabg" style="height: 100%;">
        <div class="content" style="height: 100%;">
          <div class="table" style="height: 100%;">
            <!-- -----------------操作区域--------------------------- -->
            <div class="mhcx">
              <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline" style="float:left">
                <el-form-item style="font-weight: 700;">
                  <el-input v-model="formInline.ztbh" clearable placeholder="编号" class="widths">
                  </el-input>
                </el-form-item>
                <el-form-item style="font-weight: 700;">
                  <el-input v-model="formInline.zrr" clearable placeholder="责任人" class="widths">
                  </el-input>
                </el-form-item>
                <el-form-item style="font-weight: 700;">
                  <el-cascader v-model="formInline.scbm" :options="regionOption" clearable class="widths" :props="regionParams" filterable ref="cascaderArr" placeholder="部门"></el-cascader>
                </el-form-item>
                <el-form-item style="font-weight: 700;">
                  <el-select v-model="formInline.lx" clearable placeholder="类型" class="widthx">
                    <el-option v-for="item in sblxxz" :label="item.sblxmc" :value="item.sblxmc" :key="item.sblxid">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item style="font-weight: 700;">
                  <el-select v-model="formInline.mj" clearable placeholder="密级" class="widthx">
                    <el-option v-for="item in sbmjxz" :label="item.sbmjmc" :value="item.sbmjmc" :key="item.sbmjid">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item style="font-weight: 700;">
                  <!-- <el-date-picker v-model="formInline.bmqx" type="daterange" range-separator="至"
										start-placeholder="查询起始时间" end-placeholder="查询结束日期" format="yyyy年MM月dd日"
										value-format="yyyy年MM月dd日">
									</el-date-picker> -->
                  <el-input v-model="formInline.bmqx" clearable placeholder="保密期限" class="widths">
                  </el-input>
                </el-form-item>
                <!-- <el-form-item label="生成原因" style="font-weight: 700;">
                  <el-select v-model="formInline.scyy" clearable placeholder="请选择原因" class="widthx">
                    <el-option label="制作" value="制作"></el-option>
                    <el-option label="复制" value="复制"></el-option>
                    <el-option label="接收" value="接收"></el-option>
                    <el-option label="其他" value="其他"></el-option>
                  </el-select>
                </el-form-item> -->
                <el-form-item style="font-weight: 700;">
                  <el-select v-model="formInline.zt" clearable placeholder="状态" class="widthx">
                    <el-option v-for="item in sbsyqkxz" :label="item.ztztmc" :value="item.ztztmc" :key="item.ztztid">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
                </el-form-item>
                <el-form-item>
                  <el-button type="warning" icon="el-icon-circle-close" @click="cz">重置</el-button>
                </el-form-item>
                <!-- <el-form-item>
                  <el-button type="warning" icon="el-icon-circle-close" @click="resetFilter">重置筛选</el-button>
                </el-form-item> -->
                <div style="float:right">
                  <el-form-item style="float: right;margin-left: 5px;">
                    <el-button type="danger" size="medium" @click="shanchu" icon="el-icon-delete-solid">
                      删除
                    </el-button>
                  </el-form-item>
                  <el-form-item style="float: right;margin-left: 5px;">
                    <el-button type="primary" size="medium" icon="el-icon-download" @click="exportList()">导出
                    </el-button>
                  </el-form-item>
                  <el-form-item style="float: right;margin-left: 5px;">
                    <input type="file" ref="upload" style="display: none;position: absolute;top:10px; right:0;opacity:0; cursor: pointer;height: 32px;width: 56px;z-index:1;" accept=".xls,.xlsx">
                    <el-button type="primary" icon="el-icon-upload2" size="medium" @click="dr_dialog = true">
                      导入
                    </el-button>
                  </el-form-item>
                  <el-form-item style="float: right;margin-left: 5px;">
                    <el-button type="danger" size="medium" icon="el-icon-delete" @click="updateSbZtBtn('销毁')">销毁
                    </el-button>
                  </el-form-item>
                  <!-- <el-form-item style="float: right;margin-left: 5px;">
                    <el-button type="primary" size="medium" icon="el-icon-position" @click="jcsb">销毁
                    </el-button>
                  </el-form-item> -->
                  <el-form-item style="float: right;margin-left: 5px;">
                    <el-button type="danger" size="medium" icon="el-icon-circle-close" @click="updateSbZtBtn('外发')">外发
                    </el-button>
                  </el-form-item>
                  <el-form-item style="float: right;margin-left: 5px;">
                    <el-button type="warning" size="medium" icon="el-icon-remove-outline" @click="updateSbZtBtn('借出')">
                      借出
                    </el-button>
                  </el-form-item>
                  <el-form-item style="float: right;margin-left: 5px;">
                    <el-button type="success" size="medium" icon="el-icon-circle-check" @click="updateSbZtBtn('在管')">在管
                    </el-button>
                  </el-form-item>
                  <el-form-item style="float: right;">
                    <el-button type="success" size="medium" @click="xzsmsb()" icon="el-icon-plus">
                      新增
                    </el-button>
                  </el-form-item>
                </div>
              </el-form>

            </div>

            <!-- -----------------审查组人员列表--------------------------- -->
            <div class="table_content_padding" style="height: 100%;">
              <div class="table_content" style="height: 100%;">
                <el-table :data="smzttzList" ref="tableDiv" border @selection-change="selectRow" @mousedown.native="mouseDownTableHandler" @mouseup.native="mouseUpTableHandler" @mousemove.native="mouseMoveTableHandler" :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" style="width: 100%;border:1px solid #EBEEF5;" height="calc(100% - 34px - 85px)" stripe>
                  <el-table-column type="selection" width="55" align="center" fixed> </el-table-column>
                  <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                  <el-table-column prop="mc" width="200" label="载体名称" :filters="mc_filters" :filter-method="filterMc">
                  </el-table-column>
                  <el-table-column prop="ztbh" label="载体编号" width="150" fixed></el-table-column>
                  <el-table-column prop="xmbh" label="项目编号" width="150" fixed :filters="xmbh_filters" :filter-method="filterXmbh"></el-table-column>
                  <el-table-column prop="lx" label="载体类型" width="90" :filters="lx_filters" :filter-method="filterLx">
                  </el-table-column>
                  <el-table-column prop="scyy" label="生成原因" width="90" :filters="scyy_filters" :filter-method="filterScyy"></el-table-column>
                  <el-table-column prop="mj" label="密级" width="140" :filters="mj_filters" :filter-method="filterMj">
                  </el-table-column>
                  <el-table-column prop="bmqx" label="保密期限" width="90" :filters="bmqx_filters" :filter-method="filterBmqx"></el-table-column>
                  <el-table-column prop="fs" label="份数" width="70"></el-table-column>
                  <el-table-column prop="ys" label="页数" width="70"></el-table-column>
                  <el-table-column prop="zxfw" label="知悉范围" width="180" :filters="zxfw_filters" :filter-method="filterZxfw"></el-table-column>
                  <el-table-column prop="scrq" label="生成日期" width="120" :filters="scrq_filters" :filter-method="filterScrq"></el-table-column>
                  <el-table-column prop="scbm" label="生成部门" width="200" :filters="scbm_filters" :filter-method="filterScbm"></el-table-column>
                  <el-table-column prop="zrr" label="责任人" width="80" :filters="zrr_filters" :filter-method="filterZrr">
                  </el-table-column>
                  <el-table-column prop="bmwz" label="保管位置" width="140" :filters="bmwz_filters" :filter-method="filterBmwz"></el-table-column>
                  <el-table-column prop="zt" label="使用状态" width="90" :filters="zt_filters" :filter-method="filterZt">
                  </el-table-column>
                  <el-table-column prop="ztbhsj" label="状态变化时间" width="120" :filters="ztbhsj_filters" :filter-method="filterZtbhsj"></el-table-column>
                  <el-table-column prop="" label="操作" width="140" fixed="right">
                    <template slot-scope="scoped">
                      <el-button size="medium" type="text" @click="getTrajectory(scoped.row)">轨迹
                      </el-button>
                      <!-- <el-button size="medium" type="text" @click="xqyl(scoped.row)">详情
                      </el-button> -->
                      <el-button size="medium" type="text" @click="updateItem(scoped.row)">修改
                      </el-button>
                    </template>
                  </el-table-column>

                </el-table>

                <!-- -------------------------分页区域---------------------------- -->
                <div style="border: 1px solid #ebeef5;">
                  <!-- <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange"
                    :pager-count="5" :current-page="page" :page-sizes="[5, 10, 20, 30]" :page-size="pageSize"
                    layout="total, prev, pager, sizes,next, jumper" :total="total">
                  </el-pagination> -->
                  <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange" :pager-count="5" :current-page="page" :page-sizes="[5, 10, 20, 30]" :page-size="pageSize" layout="total" :total="total">
                  </el-pagination>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 模板下载 -->
        <el-dialog title="开始导入" class="scbg-dialog" width="600px" @close="mbxzgb" :visible.sync="dr_dialog" show-close>
          <div style="padding: 20px;">
            <div class="daochu">
              <div>一、请点击“导出模板”，并参照模板填写信息。</div>
              <el-button type="primary" size="mini" @click="mbdc">
                模板导出
              </el-button>
            </div>
            <div class="daochu">
              <div>二、数据导入方式：</div>
              <el-radio-group v-model="sjdrfs" @change="Radio($event)">
                <el-radio label="1">追加（导入时已有的记录信息不变，只添加新的记录）</el-radio>
                <el-radio label="2">覆盖（导入时更新已有的记录信息，并添加新的记录）</el-radio>
              </el-radio-group>
            </div>
            <div class="daochu">
              <div>三、将按模板填写的文件，导入到系统中。</div>
              <el-button type="primary" size="mini" @click="chooseFile">
                上传导入
              </el-button>
            </div>
          </div>
        </el-dialog>
        <!-- -----------------导入-弹窗--------------------------- -->
        <el-dialog width="1000px" height="800px" title="导入涉密载体台账" class="scbg-dialog" :visible.sync="dialogVisible_dr" show-close>
          <div style="height: 600px;">
            <el-table :data="dr_cyz_list" ref="multipleTable" @selection-change="handleSelectionChange" style="width: 100%;border:1px solid #EBEEF5;" height="100%" stripe>
              <el-table-column type="selection" width="55" align="center" fixed> </el-table-column>
              <el-table-column prop="载体名称" label="载体名称"></el-table-column>
              <el-table-column prop="载体编号" label="载体编号" fixed></el-table-column>
              <el-table-column prop="项目编号" label="项目编号" fixed></el-table-column>
              <el-table-column prop="载体类型" label="载体类型"></el-table-column>
              <el-table-column prop="生成原因" label="生成原因"></el-table-column>
              <el-table-column prop="密级" label="密级"></el-table-column>
              <el-table-column prop="保密期限" label="保密期限"></el-table-column>
              <el-table-column prop="份数" label="份数"></el-table-column>
              <el-table-column prop="页数" label="页数"></el-table-column>
              <el-table-column prop="知悉范围" label="知悉范围"></el-table-column>
              <el-table-column prop="生成日期" label="生成日期"></el-table-column>
              <el-table-column prop="生成部门" label="生成部门"></el-table-column>
              <el-table-column prop="责任人" label="责任人"></el-table-column>
              <el-table-column prop="保管位置" label="保管位置"></el-table-column>
              <el-table-column prop="使用状态" label="使用状态"></el-table-column>
              <el-table-column prop="状态变化时间" label="状态变化时间"></el-table-column>
            </el-table>
          </div>

          <div style="height :30px;display: flex;align-items: center;justify-content: center;margin: 10px 0;">
            <el-button type="primary" @click="drcy" size="mini">导 入</el-button>
            <el-button type="warning" @click="dialogVisible_dr = false" size="mini">关 闭</el-button>
          </div>
        </el-dialog>
        <!-- -----------------涉密人员离岗离职信息-弹窗--------------------------- -->

        <el-dialog title="涉密载体详细信息" :close-on-click-modal="false" :visible.sync="dialogVisible" width="47%" class="xg" :before-close="handleClose" @close="close('formName')">
          <el-form ref="formName" :model="tjlist" :rules="rules" label-width="120px" size="mini">

            <el-form-item label="涉密载体名称" prop="mc" class="one-line ztgl">
              <el-input placeholder="涉密载体名称" v-model="tjlist.mc" clearable></el-input>
            </el-form-item>
            <div style="display:flex">
              <el-form-item label="载体编号" prop="ztbh">
                <el-input placeholder="载体编号" v-model="tjlist.ztbh" clearable @blur="onInputBlur(1)">
                </el-input>
              </el-form-item>
              <el-form-item label="项目编号">
                <el-input placeholder="项目编号" v-model="tjlist.xmbh" clearable>
                </el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="生成原因" prop='scyy'>
                <el-select v-model="tjlist.scyy" clearable placeholder="请选择生成原因" style="width: 100%;">
                  <el-option v-for="item in ztscyyxz" :label="item.ztscyymc" :value="item.ztscyymc" :key="item.ztscyyid"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="保密期限（年）" prop="bmqx">
                <el-input v-model="tjlist.bmqx" clearable placeholder="保密期限" @blur="bmqx = $event.target.value" type="number"></el-input>
                <!-- <el-date-picker v-model="tjlist.bmqx" class="cd" clearable type="date"
									placeholder="选择日期" format="yyyy年MM月dd日" value-format="yyyy年MM月dd日">
								</el-date-picker> -->
              </el-form-item>
            </div>
            <el-form-item label="密级" prop="mj" class="one-line ztgl">
              <el-radio-group v-model="tjlist.mj">
                <el-radio v-for="item in sbmjxz" :label="item.sbmjmc" :value="item.sbmjmc" :key="item.sbmjid">
                </el-radio>
              </el-radio-group>
            </el-form-item>

            <div style="display:flex">
              <el-form-item label="类型" prop="lx">
                <el-select v-model="tjlist.lx" clearable placeholder="请选择涉密机类型" style="width: 100%;">
                  <el-option v-for="item in sblxxz" :label="item.sblxmc" :value="item.sblxmc" :key="item.sblxid">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="份数" prop="fs">
                <el-input placeholder="份数" v-model="tjlist.fs" clearable></el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="页数" prop="ys">
                <el-input placeholder="页数" v-model="tjlist.ys" clearable></el-input>
              </el-form-item>
              <el-form-item label="知悉范围" prop="zxfw">
                <!-- <el-autocomplete class="inline-input" value-key="zxfw" v-model.trim="tjlist.zxfw" style="width: 100%;"
									:fetch-suggestions="querySearchzxfw" placeholder="知悉范围">
								</el-autocomplete> -->
                <el-input type="textarea" :rows="1" placeholder="知悉范围" v-model="tjlist.zxfw" clearable style="width:65%"></el-input>
                <el-button type="success" @click="zxfw()" size="mini" style="float:right;">
                  添 加
                </el-button>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="生成日期" prop="scrq">
                <!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
                <el-date-picker v-model="tjlist.scrq" clearable type="date" style="width: 100%;" placeholder="选择日期" format="yyyy年MM月dd日" value-format="yyyy年MM月dd日">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="生成部门" prop="scbm">
                <el-cascader v-model="tjlist.scbm" :options="regionOption" style="width: 100%;" :props="regionParams" filterable ref="cascaderArr" @change="handleChange(1)">
                </el-cascader>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="责任人" prop="zrr">
                <el-autocomplete class="inline-input" value-key="xm" v-model.trim="tjlist.zrr" style="width: 100%;" :fetch-suggestions="querySearch" placeholder="请输入责任人">
                </el-autocomplete>
              </el-form-item>
              <el-form-item label="保管位置" prop="bmwz">
                <el-autocomplete class="inline-input" value-key="bmwz" v-model.trim="tjlist.bmwz" style="width: 100%;" :fetch-suggestions="querySearchbmwz" placeholder="保管位置">
                </el-autocomplete>
              </el-form-item>
            </div>
            <el-form-item label="状态" prop="zt" class="one-line ztgl">
              <el-radio-group v-model="tjlist.zt">
                <el-radio v-for="item in sbsyqkxz" :label="item.ztztmc" :value="item.ztztmc" :key="item.ztztid">
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="状态变化时间" class="one-line">
              <el-date-picker v-model="tjlist.ztbhsj" clearable type="date" style="width: 100%;" placeholder="选择日期" format="yyyy年MM月dd日" value-format="yyyy年MM月dd日">
              </el-date-picker>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitTj('formName')">保 存</el-button>
            <el-button type="warning" @click="handleClose()">关 闭</el-button>
          </span>
        </el-dialog>

        <el-dialog title="修改涉密载体详细信息" :close-on-click-modal="false" :visible.sync="xgdialogVisible" width="47%" class="xg" @close="close1('form')">
          <el-form ref="form" :model="xglist" :rules="rules" label-width="120px" size="mini">

            <el-form-item label="涉密载体名称" prop="mc" class="one-line ztgl">
              <el-input placeholder="涉密载体名称" v-model="xglist.mc" clearable></el-input>
            </el-form-item>
            <div style="display:flex">
              <el-form-item label="载体编号" prop="ztbh">
                <el-input placeholder="载体编号" v-model="xglist.ztbh" clearable @blur="onInputBlur(2)" disabled>
                </el-input>
              </el-form-item>
              <el-form-item label="项目编号">
                <el-input placeholder="项目编号" v-model="xglist.xmbh" clearable>
                </el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="生成原因" prop='scyy'>
                <el-select v-model="xglist.scyy" clearable placeholder="请选择生成原因" style="width: 100%;">
                  <el-option v-for="item in ztscyyxz" :label="item.ztscyymc" :value="item.ztscyymc" :key="item.ztscyyid"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="保密期限（年）" prop="bmqx">
                <!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
                <!-- <el-date-picker v-model="xglist.bmqx" class="cd" clearable type="date"
									placeholder="选择日期" format="yyyy年MM月dd日" value-format="yyyy年MM月dd日">
								</el-date-picker> -->
                <el-input v-model="xglist.bmqx" clearable placeholder="保密期限" @blur="bmqx = $event.target.value" type="number"></el-input>
              </el-form-item>
            </div>
            <el-form-item label="密级" prop="mj" class="one-line ztgl">
              <el-radio-group v-model="xglist.mj">
                <el-radio v-for="item in sbmjxz" :label="item.sbmjmc" :value="item.sbmjmc" :key="item.sbmjid">
                </el-radio>
              </el-radio-group>
            </el-form-item>

            <div style="display:flex">
              <el-form-item label="类型" prop="lx">
                <el-select v-model="xglist.lx" clearable placeholder="请选择涉密机类型" style="width: 100%;">
                  <el-option v-for="item in sblxxz" :label="item.sblxmc" :value="item.sblxmc" :key="item.sblxid">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="份数" prop="fs">
                <el-input placeholder="份数" v-model="xglist.fs" clearable disabled></el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="页数" prop="ys">
                <el-input placeholder="页数" v-model="xglist.ys" clearable></el-input>
              </el-form-item>
              <el-form-item label="知悉范围" prop="zxfw">
                <!-- <el-autocomplete class="inline-input" value-key="zxfw" v-model.trim="xglist.zxfw" style="width: 100%;"
									:fetch-suggestions="querySearchzxfw" placeholder="知悉范围">
								</el-autocomplete> -->
                <el-input type="textarea" :rows="1" placeholder="知悉范围" v-model="xglist.zxfw" clearable style="width:65%"></el-input>
                <el-button type="success" @click="zxfw()" size="mini" style="float:right;">
                  保 存
                </el-button>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="生成日期" prop="scrq">
                <el-date-picker v-model="xglist.scrq" clearable type="date" style="width: 100%;" placeholder="选择日期" format="yyyy年MM月dd日" value-format="yyyy年MM月dd日">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="生成部门" prop="scbm">
                <el-cascader v-model="xglist.scbm" :options="regionOption" style="width: 100%;" :props="regionParams" filterable ref="cascaderArr" @change="handleChange(2)">
                </el-cascader>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="责任人" prop="zrr">
                <el-autocomplete class="inline-input" value-key="xm" v-model.trim="xglist.zrr" style="width: 100%;" :fetch-suggestions="querySearch" placeholder="请输入责任人">
                </el-autocomplete>
              </el-form-item>
              <el-form-item label="保管位置" prop="bmwz">
                <el-autocomplete class="inline-input" value-key="bmwz" v-model.trim="xglist.bmwz" style="width: 100%;" :fetch-suggestions="querySearchbmwz" placeholder="保管位置">
                </el-autocomplete>
              </el-form-item>
            </div>
            <el-form-item label="状态" prop="zt" class="one-line ztgl">
              <el-radio-group v-model="xglist.zt">
                <el-radio v-for="item in sbsyqkxz" :label="item.ztztmc" :value="item.ztztmc" :key="item.ztztid">
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="状态变化时间" class="one-line">
              <el-date-picker v-model="xglist.ztbhsj" clearable type="date" style="width: 100%;" placeholder="选择日期" format="yyyy年MM月dd日" value-format="yyyy年MM月dd日">
              </el-date-picker>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="updataDialog('form')">保 存</el-button>
            <el-button type="warning" @click="xgdialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>
        <!-- 详情 -->
        <el-dialog title="涉密载体详细信息详情" :close-on-click-modal="false" :visible.sync="xqdialogVisible" width="47%" class="xg">
          <el-form ref="form" :model="xglist" label-width="120px" size="mini" disabled>

            <el-form-item label="涉密载体名称" prop="mc" class="one-line">
              <el-input placeholder="涉密载体名称" v-model="xglist.mc" clearable></el-input>
            </el-form-item>
            <div style="display:flex">
              <el-form-item label="载体编号" prop="ztbh">
                <el-input placeholder="载体编号" v-model="xglist.ztbh" clearable></el-input>
              </el-form-item>
              <el-form-item label="项目编号">
                <el-input placeholder="项目编号" v-model="xglist.xmbh" clearable>
                </el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="生成原因" prop='scyy'>
                <el-select v-model="xglist.scyy" clearable placeholder="请选择生成原因" style="width: 100%;">
                  <el-option v-for="item in ztscyyxz" :label="item.ztscyymc" :value="item.ztscyymc" :key="item.ztscyyid"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="保密期限" prop="bmqx">
                <!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
                <!-- <el-date-picker v-model="xglist.bmqx" class="cd" clearable type="date"
									placeholder="选择日期" format="yyyy年MM月dd日" value-format="yyyy年MM月dd日">
								</el-date-picker> -->
                <el-input v-model="xglist.bmqx" clearable placeholder="保密期限" oninput="value=value.replace(/[^\d.]/g,'')"></el-input>
              </el-form-item>
            </div>
            <el-form-item label="密级" prop="mj" class="one-line">
              <el-radio-group v-model="xglist.mj">
                <el-radio v-for="item in sbmjxz" :label="item.sbmjmc" :value="item.sbmjmc" :key="item.sbmjid">
                </el-radio>
              </el-radio-group>
            </el-form-item>

            <div style="display:flex">
              <el-form-item label="类型" prop="lx">
                <el-select v-model="xglist.lx" clearable placeholder="请选择涉密机类型" style="width: 100%;">
                  <el-option v-for="item in sblxxz" :label="item.sblxmc" :value="item.sblxmc" :key="item.sblxid">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="份数" prop="fs">
                <el-input placeholder="份数" v-model="xglist.fs" clearable></el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="页数" prop="ys">
                <el-input placeholder="页数" v-model="xglist.ys" clearable></el-input>
              </el-form-item>
              <el-form-item label="知悉范围" prop="zxfw">
                <el-input placeholder="知悉范围" v-model="xglist.zxfw" clearable></el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="生成日期" prop="scrq">
                <!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
                <el-date-picker v-model="xglist.scrq" class="cd" clearable type="date" style="width: 100%;" placeholder="选择日期" format="yyyy年MM月dd日" value-format="yyyy年MM月dd日">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="生成部门" prop="scbm">
                <el-cascader v-model="xglist.scbm" :options="regionOption" style="width: 100%;" :props="regionParams" filterable ref="cascaderArr" @change="handleChange(2)">
                </el-cascader>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="责任人" prop="zrr">
                <el-autocomplete class="inline-input" value-key="xm" v-model.trim="xglist.zrr" style="width: 100%;" :fetch-suggestions="querySearch" placeholder="请输入责任人">
                </el-autocomplete>
              </el-form-item>
              <el-form-item label="保管位置" prop="bmwz">
                <el-input placeholder="保管位置" v-model="xglist.bmwz" clearable></el-input>
              </el-form-item>
            </div>
            <el-form-item label="状态" prop="zt" class="one-line">
              <el-radio-group v-model="xglist.zt">
                <el-radio v-for="item in sbsyqkxz" :label="item.ztztmc" :value="item.ztztmc" :key="item.ztztid">
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">

            <el-button type="warning" @click="xqdialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>
        <el-dialog title="培训人员清单" :close-on-click-modal="false" :visible.sync="rydialogVisible" width="54%" class="xg" style="margin-top:4vh">
          <el-row type="flex">
            <el-col :span="12" style="height:500px">
              <div style="height:96%;border: 1px solid #dee5e7;">
                <div style="padding-top: 10px;padding-left: 10px;width: 97%;height: 68px;background: #fafafa;">
                  <el-row>待选人员列表</el-row>
                  <el-form :inline="true" :model="formInlinery" size="medium" class="demo-form-inline" style="display:flex;margin-bottom: -3%;">
                    <div class="dialog-select-div">
                      <span class="title">部门</span>
                      <el-cascader v-model="formInlinery.bm" :options="regionOption" clearable class="widths" style="width:14vw" :props="regionParams" filterable ref="cascaderArr">
                      </el-cascader>
                      <el-button type="primary" icon="el-icon-search" @click="onSubmitry">查询
                      </el-button>
                    </div>
                    <!-- <el-form-item label="部门" class="dialog-select-div">
											<el-cascader v-model="formInlinery.bm" :options="regionOption" clearable class="widths"
												style="width:14vw" :props="regionParams" filterable ref="cascaderArr">
											</el-cascader>
										</el-form-item>
										<el-form-item>
											<el-button type="primary" icon="el-icon-search" @click="onSubmitry">查询
											</el-button>
										</el-form-item> -->
                  </el-form>
                </div>
                <el-table :data="table1Data" style="width: 100%;margin-top:1%;" height="400" ref="table1" @selection-change="onTable1Select" @row-click="handleRowClick">
                  <el-table-column type="selection" width="55">
                  </el-table-column>
                  <el-table-column prop="xm" label="姓名">
                  </el-table-column>
                </el-table>
              </div>
              <!-- 分页区 -->
              <!-- <div style="border: 1px solid #ebeef5;">
								<el-pagination background @current-change="handleCurrentChange1" @size-change="handleSizeChange1"
									:pager-count="5" :current-page="page1" :page-sizes="[5, 10, 20, 30]" :page-size="pageSize1"
									layout="total, prev, pager, sizes,next, jumper" :total="total1">
								</el-pagination>
							</div> -->
            </el-col>
            <!-- <el-col :span="4" style="margin-left: 18px;
    margin-top: 70px;display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;">
							<el-button type="primary" @click="onAdd">添 加</el-button>
							<el-button type="danger" @click="onDelete" style="margin-top: 50px;
    margin-left: 0;">删 除</el-button>
						</el-col> -->
            <el-col :span="12" style="margin-left:10px;height:500px">
              <div style="height:96%;
    										border: 1px solid #dee5e7;
    										">
                <div style="padding-top: 10px;
    										padding-left: 10px;
    										width: 97%;
    										height: 68px;
    										background: #fafafa;">
                  <el-row>已选人员列表</el-row>
                  <div style="float:right;">
                    <el-button type="primary" @click="addpxry">保 存</el-button>
                    <el-button type="warning" @click="pxrygb">关 闭</el-button>
                  </div>

                </div>
                <el-table :data="table2Data" style="width: 100%;" height="404" ref="table2">
                  <el-table-column prop="xm" label="姓名">
                    <template slot-scope="scope">
                      <div style="display:flex;justify-content: space-between;
    														align-items: center;">
                        <div>
                          {{ scope.row.xm }}
                        </div>
                        <i class="el-icon-circle-close btn" @click="onTable2Select(scope.row)"></i>
                      </div>
                    </template>
                  </el-table-column>
                  <!-- <el-table-column type="selection" width="55">
										</el-table-column> -->

                </el-table>
              </div>

            </el-col>
          </el-row>
        </el-dialog>
        <!-- 历史轨迹 dialog -->
        <el-dialog title="历史轨迹" :close-on-click-modal="false" :visible.sync="lsgjDialogVisible" width="46%" class="xg">
          <div style="padding-left: 10px;background: #EBEEF5;height: 40px;line-height: 40px;font-size: 16px;border-radius: 5px;margin-bottom: 5px;">
            <span>载体编号：<span style="font-size: 14px;">{{ lsgjDialogData.ztbh }}</span></span>
          </div>
          <div style="max-height: 400px;overflow-y: scroll;padding: 10px;">
            <el-timeline>
              <el-timeline-item v-for="(activity, index) in lsgjDialogData.timelineList" :key="index" :icon="activity.icon" :color="activity.color" :size="'large'" :timestamp="'操作时间：'+activity.time">
                <div>
                  <p>{{ activity.ymngnmc }}</p>
                  <p>操作人：{{ activity.xm }}</p>
                  <p v-if="activity.extraParams && activity.extraParams.ztbhsj">状态变化时间：{{ activity.extraParams.ztbhsj }}</p>
                  <p v-if="activity.extraParams && activity.extraParams.zrr">责任人：{{ activity.extraParams.zrr }}</p>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button type="warning" @click="lsgjDialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>
        <!-- 状态变化时间确认 dialog -->
        <el-dialog title="状态变更确认(变更后)" :close-on-click-modal="false" :visible.sync="dialogVisibleZtbhsjQr" width="70%" class="xg">
          <el-table :data="ztbhsjQrList" ref="tableDiv" border @selection-change="selectRow" @mousedown.native="mouseDownTableHandler" @mouseup.native="mouseUpTableHandler" @mousemove.native="mouseMoveTableHandler" :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" style="width: 100%;border:1px solid #EBEEF5;" max-height="400px;" stripe>
            <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
            <el-table-column prop="mc" width="" label="载体名称">
            </el-table-column>
            <el-table-column prop="ztbh" label="载体编号" width="150"></el-table-column>
            <el-table-column prop="xmbh" label="项目编号" width="150"></el-table-column>
            <el-table-column prop="zt" label="使用状态" width="90" align="center">
            </el-table-column>
            <el-table-column prop="ztbhsj" label="状态变化时间" width="120" align="center"></el-table-column>
            <el-table-column prop="" label="操作" width="80" align="center" fixed="right">
              <template slot-scope="scoped">
                <el-button size="medium" type="text" @click="removeDialogTableZtbhqrQrBtn(scoped.row)" style="color:#F56C6C;">移除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div style="width: 100%;height:10px;"></div>
          <el-form ref="formZtbhsjQr" :model="dialogObjZtbhsjQr" :rules="rules" label-width="120px" size="mini">
            <el-form-item label="状态" prop="zt" class="one-line ztgl">
              <el-radio-group v-model="dialogObjZtbhsjQr.zt">
                <el-radio v-for="item in sbsyqkxz" :label="item.ztztmc" :value="item.ztztmc" :key="item.ztztid">
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="状态变化时间" prop="ztbhsj" class="one-line">
              <el-date-picker v-model="dialogObjZtbhsjQr.ztbhsj" clearable type="date" style="width: 100%;" placeholder="选择日期" format="yyyy年MM月dd日" value-format="yyyy年MM月dd日">
              </el-date-picker>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="ztbhsjQrBtn">确 认</el-button>
            <el-button type="warning" @click="dialogVisibleZtbhsjQr = false">关 闭</el-button>
          </span>
        </el-dialog>

      </div>
    </div>
  </div>

</template>
<script>
import {
  getsbmj,
  getztlx,
  getztscyy,
  getztzt
} from "../../../db/xzdb"
import {
  getpxqdry
} from "../../../db/pxqdry"
import {
  dateFormatNYRChinese
} from "../../../utils/moment"
import {
  getlogin
} from "../../../db/loginyhdb";
import {
  getbmmc,
  getsmry
} from "../../../db/smgwgldb";
import {
  getsmry1
} from "../../../db/smrydb"
import {
  //内容管理初始化成员列表
  getSmzttz,
  //添加内容管理
  addSmzttz,
  //删除内容管理
  deleteSmzttz,
  reviseSmzttz,
  jyaddSmzttz,
  xgsmjsjsyzt_zy,
  xgsmjsjsyzt_ty,
  xgsmjsjsyzt_bf,
  xgsmjsjsyzt_jc,
  xgsmjsjsyzt_xh,
  getSmzt,
  // 更新设备状态
  updateSbZt
} from "../../../db/smzttzdb";

import {
  exportExcel
} from "../../../utils/exportExcel"; //excel导出工具

import {
  writeTrajectoryLog,
  parseTrajectoryLogs,
  setTrajectoryIcons
} from '../../../utils/logUtils'

import {
  getUuid
} from "../../../utils/getUuid"; //获取uuid
import XLSX from "xlsx";
import { decideChange, getDateTime } from '../../../utils/utils';

export default {
  components: {},
  props: {},
  data () {
    return {
      // 状态变化时间确认dialog显隐
      dialogVisibleZtbhsjQr: false,
      dialogObjZtbhsjQr: {},
      // 状态变化时间确认dialog表格数据
      ztbhsjQrList: [],
      // 标记鼠标是否是按下状态
      isTableMouseDown: false,
      // 表格鼠标按下移动偏移计算基准点
      tableMouseOffset: 0,
      // 历史轨迹dialog显隐
      lsgjDialogVisible: false,
      // 历史轨迹dialog数据
      lsgjDialogData: {
        ztbh: '',
        xmbh: '',
        // 历史轨迹时间线数据
        timelineList: [],
      },
      //数据筛选
      mc_filters: [],
      xmbh_filters: [],
      lx_filters: [],
      scyy_filters: [],
      mj_filters: [],
      bmqx_filters: [],
      zxfw_filters: [],
      scrq_filters: [],
      scbm_filters: [],
      zrr_filters: [],
      bmwz_filters: [],
      zt_filters: [],
      ztbhsj_filters: [],
      //
      rydialogVisible: false,
      ztbh: '',
      pdsmzt: 0,
      sbmjxz: [],
      ztscyyxz: [],
      sblxxz: [],
      sbsyqkxz: [],
      smzttzList: [],
      xglist: {},
      tableDataCopy: [],
      // 表格修改按钮点击时备份的旧数据
      updateItemOld: {},
      xgdialogVisible: false,
      xqdialogVisible: false,
      formInline: {

      },
      formInlinery: {
        bm: ''
      },
      table1Data: [],
      table2Data: [],
      selectedTable1Data: [], // table1已选数据
      selectedTable2Data: [], // table2已选数据
      selectedTableData: [], //备份数据
      tjlist: {
        mc: '',
        ztbh: '',
        xmbh: '',
        scyy: '',
        mj: '',
        bmqx: '',
        lx: '',
        fs: '',
        ys: '',
        zxfw: '',
        scrq: '',
        scbm: '',
        zrr: '',
        bmwz: '',
        zt: '',
        ztbhsj: ''
      },
      page: 1,
      pageSize: 10,
      total: 0,
      selectlistRow: [], //列表的值
      dialogVisible: false, //添加弹窗状态
      //表单验证
      rules: {
        mc: [{
          required: true,
          message: '请输入涉密载体名称',
          trigger: 'blur'
        },],
        ztbh: [{
          required: true,
          message: '请输入载体编号',
          trigger: 'blur'
        },],
        scyy: [{
          required: true,
          message: '请选择生成原因',
          trigger: 'blur'
        },],
        mj: [{
          required: true,
          message: '请选择密级',
          trigger: 'blur'
        },],
        bmqx: [{
          required: true,
          message: '请选择保密期限',
          trigger: 'blur'
        },],
        lx: [{
          required: true,
          message: '请选择类型',
          trigger: 'blur'
        },],
        fs: [{
          required: true,
          message: '请输入份数',
          trigger: 'blur'
        },],
        zxfw: [{
          required: true,
          message: '请输入知悉范围',
          trigger: ['blur', 'change'],
        },],
        scrq: [{
          required: true,
          message: '请选择生成日期',
          trigger: 'blur'
        },],
        scbm: [{
          required: true,
          message: '请输入生成部门',
          trigger: ['blur', 'change'],
        },],
        zrr: [{
          required: true,
          message: '请输入责任人',
          trigger: ['blur', 'change'],
        },],
        bmwz: [{
          required: true,
          message: '请输入保管位置',
          trigger: ['blur', 'change'],
        },],
        zt: [{
          required: true,
          message: '请选择状态',
          trigger: 'blur'
        },],
        ztbhsj: [{
          required: true,
          message: '请选择状态变化时间',
          trigger: 'blur'
        },],
      },
      //导入
      dialogVisible_dr: false, //导入成员组弹窗状态
      dr_cyz_list: [], //待选择导入成员组列表
      multipleTable: [], //已选择导入成员组列表
      regionOption: [], //地域信息
      regionParams: {
        label: 'label', //这里可以配置你们后端返回的属性
        value: 'label',
        children: 'childrenRegionVo',
        expandTrigger: 'click',
        checkStrictly: true,
      }, //地域信息配置参数
      dwmc: '',
      year: '',
      yue: '',
      ri: '',
      Date: '',
      xh: [],
      dclist: [],
      dr_dialog: false,
      //数据导入方式
      sjdrfs: ''
    }
  },
  computed: {},
  mounted () {
    // return
    this.ry()
    //列表初始化
    this.smzttz()
    this.zxfwlist()
    this.dwmc = getlogin()[0].dwmc
    let date = new Date()
    this.year = date.getFullYear() + '年'; //获取完整的年份(4位)
    this.yue = (date.getMonth() + 1) + '月'; //获取当前月份(0-11,0代表1月)
    this.ri = date.getDate() + '日'; //获取当前日(1-31)
    this.Date = this.year + this.yue + this.ri
    // this.tjlist.bmqx = this.Date
    // this.tjlist.scrq = this.Data
    this.tjlist.mj = '秘密'
    this.tjlist.lx = '纸介质'
    this.tjlist.zt = '在管'
    this.smry()
    this.sbmjxz = getsbmj()
    this.ztscyyxz = getztscyy()
    this.sblxxz = getztlx()
    this.sbsyqkxz = getztzt()

    this.$refs.upload.addEventListener('change', e => { //绑定监听表格导入事件
      this.readExcel(e);
    })
    let bmmc = getbmmc()
    let shu = []
    // console.log(bmmc);
    bmmc.forEach(item => {
      let childrenRegionVo = []
      bmmc.forEach(item1 => {
        if (item.bmm == item1.fbmm) {
          // console.log(item, item1);
          childrenRegionVo.push(item1)
          // console.log(childrenRegionVo);
          item.childrenRegionVo = childrenRegionVo
        }
      });
      // console.log(item);
      shu.push(item)
    })
    console.log(shu[0]);
    if (shu[0].childrenRegionVo) {
      shu[0].childrenRegionVo.forEach(item => {
      this.regionOption.push(item)
    })
    }
    // console.log(this.dclist);
    this.dclist.forEach(item => {
      // console.log(item);
      this.mc_filters.push({
        text: item.mc,
        value: item.mc
      })
      this.xmbh_filters.push({
        text: item.xmbh,
        value: item.xmbh
      })
      this.lx_filters.push({
        text: item.lx,
        value: item.lx
      })
      this.scyy_filters.push({
        text: item.scyy,
        value: item.scyy
      })
      this.mj_filters.push({
        text: item.mj,
        value: item.mj
      })
      this.bmqx_filters.push({
        text: item.bmqx,
        value: item.bmqx
      })
      this.zxfw_filters.push({
        text: item.zxfw,
        value: item.zxfw
      })
      this.scrq_filters.push({
        text: item.scrq,
        value: item.scrq
      })
      this.scbm_filters.push({
        text: item.scbm,
        value: item.scbm
      })
      this.zrr_filters.push({
        text: item.zrr,
        value: item.zrr
      })
      this.bmwz_filters.push({
        text: item.bmwz,
        value: item.bmwz
      })
      this.zt_filters.push({
        text: item.zt,
        value: item.zt
      })
      this.ztbhsj_filters.push({
        text: item.ztbhsj,
        value: item.ztbhsj
      })

    })
    console.log(this.mc_filters);
    var mc = {};
    this.mc_filters = this.mc_filters.reduce(function (item, next) {
      mc[next.text] ? '' : mc[next.text] = true && item.push(next);
      return item;
    }, []);
    var xmbh = {};
    this.xmbh_filters = this.xmbh_filters.reduce(function (item, next) {
      xmbh[next.text] ? '' : xmbh[next.text] = true && item.push(next);
      return item;
    }, []);
    var lx = {};
    this.lx_filters = this.lx_filters.reduce(function (item, next) {
      lx[next.text] ? '' : lx[next.text] = true && item.push(next);
      return item;
    }, []);
    var scyy = {};
    this.scyy_filters = this.scyy_filters.reduce(function (item, next) {
      scyy[next.text] ? '' : scyy[next.text] = true && item.push(next);
      return item;
    }, []);
    var mj = {};
    this.mj_filters = this.mj_filters.reduce(function (item, next) {
      mj[next.text] ? '' : mj[next.text] = true && item.push(next);
      return item;
    }, []);
    var bmqx = {};
    this.bmqx_filters = this.bmqx_filters.reduce(function (item, next) {
      bmqx[next.text] ? '' : bmqx[next.text] = true && item.push(next);
      return item;
    }, []);
    var zxfw = {};
    this.zxfw_filters = this.zxfw_filters.reduce(function (item, next) {
      zxfw[next.text] ? '' : zxfw[next.text] = true && item.push(next);
      return item;
    }, []);
    var scrq = {};
    this.scrq_filters = this.scrq_filters.reduce(function (item, next) {
      scrq[next.text] ? '' : scrq[next.text] = true && item.push(next);
      return item;
    }, []);
    var scbm = {};
    this.scbm_filters = this.scbm_filters.reduce(function (item, next) {
      scbm[next.text] ? '' : scbm[next.text] = true && item.push(next);
      return item;
    }, []);
    var zrr = {};
    this.zrr_filters = this.zrr_filters.reduce(function (item, next) {
      zrr[next.text] ? '' : zrr[next.text] = true && item.push(next);
      return item;
    }, []);
    var bmwz = {};
    this.bmwz_filters = this.bmwz_filters.reduce(function (item, next) {
      bmwz[next.text] ? '' : bmwz[next.text] = true && item.push(next);
      return item;
    }, []);
    var zt = {};
    this.zt_filters = this.zt_filters.reduce(function (item, next) {
      zt[next.text] ? '' : zt[next.text] = true && item.push(next);
      return item;
    }, []);
    var ztbhsj = {};
    this.ztbhsj_filters = this.ztbhsj_filters.reduce(function (item, next) {
      ztbhsj[next.text] ? '' : ztbhsj[next.text] = true && item.push(next);
      return item;
    }, []);
  },
  methods: {
    // 状态变化时间dialog移除表格某行
    removeDialogTableZtbhqrQrBtn(row) {
      let rowIndex = this.ztbhsjQrList.indexOf(row)
      console.log('rowIndex', rowIndex)
      this.ztbhsjQrList.splice(rowIndex, 1)
    },
    // 状态变化时间dialog确认按钮点击事件
    ztbhsjQrBtn () {
      // console.log('this.dialogObjZtbhsjQr', this.dialogObjZtbhsjQr)
      // 数据加工
      this.$refs['formZtbhsjQr'].validate((valid) => {
        if(valid) {
          // 更新数据
          updateSbZt(this.ztbhsjQrList, this.dialogObjZtbhsjQr)
          this.dialogVisibleZtbhsjQr = false
          this.smzttz()
        }
      })
    },
    // 表格鼠标按下事件
    mouseDownTableHandler (e) {
      this.tableMouseOffset = e.clientX
      this.isTableMouseDown = true
    },
    // 表格鼠标按下移动事件
    mouseMoveTableHandler (e) {
      // console.log(this.$refs['tableDiv'])
      let tableDiv = this.$refs['tableDiv'].bodyWrapper
      // console.log(tableDiv)
      if (this.isTableMouseDown) {
        tableDiv.scrollLeft -= (-this.tableMouseOffset + (this.tableMouseOffset = e.clientX))
      }
    },
    // 表格鼠标左键抬起事件
    mouseUpTableHandler (e) {
      this.isTableMouseDown = false
    },
    // 重置筛选条件
    resetFilter () {
      console.log(this.$refs['tableDiv'])
      this.$refs['tableDiv'].columns[2].filteredValue = []
      // this.smzttz()
      let temp = JSON.parse(JSON.stringify(this.smzttzList))
      console.log('temp', temp)
      this.smzttzList = temp
      this.$nextTick(() => {
        this.$refs['tableDiv'].doLayout()
      })
    },
    // 表格鼠标按下事件
    mouseDownTableHandler (e) {
      this.tableMouseOffset = e.clientX
      this.isTableMouseDown = true
    },
    // 表格鼠标按下移动事件
    mouseMoveTableHandler (e) {
      // console.log(this.$refs['tableDiv'])
      let tableDiv = this.$refs['tableDiv'].bodyWrapper
      // console.log(tableDiv)
      if (this.isTableMouseDown) {
        tableDiv.scrollLeft -= (- this.tableMouseOffset + (this.tableMouseOffset = e.clientX))
      }
    },
    // 表格鼠标左键抬起事件
    mouseUpTableHandler (e) {
      this.isTableMouseDown = false
    },
    //数据筛选
    filterMc (value, row, column) {
      const property = column['property'];
      return row[property] === value;
    },
    filterXmbh (value, row, column) {
      const property = column['property'];
      return row[property] === value;
    },
    filterLx (value, row, column) {
      const property = column['property'];
      return row[property] === value;
    },
    filterScyy (value, row, column) {
      const property = column['property'];
      return row[property] === value;
    },
    filterMj (value, row, column) {
      const property = column['property'];
      return row[property] === value;
    },
    filterBmqx (value, row, column) {
      const property = column['property'];
      return row[property] === value;
    },
    filterBmqx (value, row, column) {
      const property = column['property'];
      return row[property] === value;
    },
    filterZxfw (value, row, column) {

    },
    filterScrq (value, row, column) {
      const property = column['property'];
      return row[property] === value;
    },
    filterScbm (value, row, column) {
      const property = column['property'];
      return row[property] === value;
    },
    filterZrr (value, row, column) {
      const property = column['property'];
      return row[property] === value;
    },
    filterBmwz (value, row, column) {
      const property = column['property'];
      return row[property] === value;
    },
    filterZt (value, row, column) {
      const property = column['property'];
      return row[property] === value;
    },
    filterZtbhsj (value, row, column) {
      const property = column['property'];
      return row[property] === value;
    },
    // 获取轨迹日志
    getTrajectory (row) {
      console.log(row)
      let params = {
        id: row.smztid,
        xyybs: 'mk_smzttz',
      }
      parseTrajectoryLogs(params, resArr => {
        console.log(resArr)
        if (resArr.length <= 0) {
          this.$message.warning('暂无轨迹')
          return
        }
        // icon图标处理
        setTrajectoryIcons(resArr)
        //
        this.lsgjDialogData.ztbh = row.ztbh
        this.lsgjDialogData.xmbh = row.xmbh
        this.lsgjDialogData.timelineList = resArr
        //
        this.lsgjDialogVisible = true
      })
    },
    xzsmsb () {
      this.dialogVisible = true
      this.smzttz()
      this.tjlist.scrq = this.Date
      // this.tjlist.scbm = this.tjlist.scbm.split('/')
    },
    Radio (val) {
      this.sjdrfs = val
      console.log("当前选中的值", val)
    },
    mbxzgb () {
      this.sjdrfs = ''
    },
    mbdc () {
      console.log("----导出涉密人员----")
      // console.log(this.selectlistRow);
      // if (this.selectlistRow.length > 0) {
      let filename = "涉密载体台账模板" + getUuid() + ".xlsx"

      const {
        dialog
      } = require('electron').remote;
      //弹窗title
      let options = {
        title: "保存文件",
        defaultPath: filename,
      };
      console.log(dialog)
      //导出文件夹选择弹窗
      dialog.showSaveDialog(options, result => {
        console.log('result', result)
        if (result == null || result == "") {
          console.log("取消导出")
          return
        }
        let list = []

        //对导出的数据进行格式，js导出excel要求传入的参数格式为[["列1名称","列2名称","列3名称"],["列11","值21","值31"],["列12","值22","值32"]]
        list.push(["序号", "载体名称", "载体编号", "项目编号", "载体类型", "生成原因",
          "密级", "保密期限", "份数", "页数", "知悉范围", "生成日期",
          "生成部门", "责任人", "保管位置", "使用状态", "状态变化时间"
        ]) //确定列名


        let styles = {
          // 列样式
          cols: {
            // 作用sheet页索引（0开始）（-1全sheet页生效）
            scoped: -1,
            style: [{
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 120
            }
            ]
          },
          // 全局样式
          all: {
            alignment: {
              horizontal: 'center', // 水平居中
              vertical: 'center' // 垂直居中
            },
            font: {
              sz: 11, // 字号
              name: '宋体' // 字体
            },
            border: { // 边框
              top: {
                style: 'thin'
              },
              bottom: {
                style: 'thin'
              },
              left: {
                style: 'thin'
              },
              right: {
                style: 'thin'
              }
            }
          },
          // 单元格样式
          cell: []
        }

        exportExcel(result, list, undefined, styles) //list 要求为二维数组
        this.dr_dialog = false
        this.$message('导出成功:' + result)
      })
    },
    //导入
    chooseFile () {
      if (this.sjdrfs != '') {
        if (this.sjdrfs == 1) {
          this.$refs.upload.click()
        } else if (this.sjdrfs == 2) {
          let valArr = this.dclist
          valArr.forEach(function (item) {
            deleteSmzttz(item)
          })
          this.$refs.upload.click()
        }
      } else {
        this.$message.warning('请选择导入方式')
      }
    },
    //----成员组选择
    handleSelectionChange (val) {
      this.multipleTable = val
      console.log("选中：", this.multipleTable);
    },
    //---确定导入成员组
    drcy () {
      //遍历已选择导入的成员，进行格式化，然后添加到数据库
      for (var i in this.multipleTable) {
        var cy = {
          mc: this.multipleTable[i]["载体名称"],
          ztbh: this.multipleTable[i]["载体编号"],
          xmbh: this.multipleTable[i]["项目编号"],
          lx: this.multipleTable[i]["载体类型"],
          scyy: this.multipleTable[i]["生成原因"],
          mj: this.multipleTable[i]["密级"],
          bmqx: this.multipleTable[i]["保密期限"],
          fs: this.multipleTable[i]["份数"],
          ys: this.multipleTable[i]["页数"],
          zxfw: this.multipleTable[i]["知悉范围"],
          scrq: dateFormatNYRChinese(this.multipleTable[i]["生成日期"]),
          scbm: this.multipleTable[i]["生成部门"],
          zrr: this.multipleTable[i]["责任人"],
          bmwz: this.multipleTable[i]["保管位置"],
          zt: this.multipleTable[i]["使用状态"],
          // ztbhsj: dateFormatNYRChinese(this.multipleTable[i]["状态变化时间"]),
          smztid: getUuid()
        }
        // 生成日期单独处理（可能存在带汉字时间字符串的问题）
        let scrqExcel = this.multipleTable[i]["生成日期"]
        if (scrqExcel) {
          let scrqExcelTime = getDateTime(scrqExcel)
          try {
            if (scrqExcelTime == 'Invalid date') {
              this.$message.warning('序号' + (item['序号']) + ' 数据生成日期解析错误，请设置为日期格式或日期格式的文本格式重试')
              return
            }
            cy.scrq = dateFormatNYRChinese(scrqExcelTime)
          } catch (error) {
            this.$message.warning(error.message)
            return
          }
        }
        // 状态变化时间单独处理（可能存在带汉字时间字符串的问题）
        let ztbhsjExcel = this.multipleTable[i]["状态变化时间"]
        if (ztbhsjExcel) {
          let ztbhsjExcelTime = getDateTime(ztbhsjExcel)
          try {
            if (ztbhsjExcelTime == 'Invalid date') {
              this.$message.warning('序号' + (item['序号']) + ' 条数据状态变化时间解析错误，请设置为日期格式或日期格式的文本格式重试')
              return
            }
            cy.ztbhsj = dateFormatNYRChinese(ztbhsjExcelTime)
          } catch (error) {
            this.$message.warning(error.message)
            return
          }
        }
        // 处理份数问题
        let ztbh = cy.ztbh
        // let exp = /^[+-]?\d*(\.\d*)?(e[+-]?\d+)?$/;
        let exp = /[0-9]+$/;
        let matchArr = ztbh.match(exp)
        let mwslen
        let prefix
        if (matchArr != null) {
          mwslen = matchArr[0].length
          prefix = ztbh.substring(0, ztbh.lastIndexOf(matchArr[0]))
        }
        let mws = 0
        // let bl;
        // 份数循环次数
        let cycleFsCount = cy.fs
        for (let j = 1; j <= cycleFsCount; j++) {
          console.log('ztbh', ztbh, 'j', j)
          mws = 0
          cy.ztbh = ztbh
          cy.fs = 1
          cy.smztid = getUuid()
          // 判定载体编号是否重复，是否可以插入
          this.onInputBlur(3, cy)
          if (this.pdsmzt == 0) {
            addSmzttz(cy)
            this.dialogVisible = false
            this.$message({
              message: '添加成功',
              type: 'success'
            })
          }
          console.log(ztbh.match(exp))
          if (ztbh.match(exp) != null) {
            if (ztbh.match(exp)[0] != '') {
              mws = ztbh.match(exp)[0] * 1
              mws++
              // bl = mwslen - mws.toString().length
              mws = (mws.toString()).padStart(mwslen, '0')
              ztbh = prefix + mws
            }
          } else {
            mws++
            ztbh = ztbh + mws
          }
        }
        //
        // addSmzttz(cy)
      }
      this.dialogVisible_dr = false
      this.smzttz()
    },
    //----表格导入方法
    readExcel (e) {
      var that = this;
      const files = e.target.files;
      console.log("files", files);
      var vali = /\.(xls|xlsx)$/
      if (files.length <= 0) { //如果没有文件名
        return false;
      } else if (!vali.test(files[0].name.toLowerCase())) {
        this.$Message.error('上传格式不正确，请上传xls或者xlsx格式');
        return false;
      }
      const fileReader = new FileReader();
      fileReader.onload = (e) => {
        try {
          const data = e.target.result;
          const workdata = XLSX.read(data, {
            type: 'binary',
            cellDates: true,
          });
          console.log("文件的内容：", workdata) // 文件的内容
          //查看打印能看到 workdata的sheetNames里是文件中每个表的有值的集合；
          const wsname = workdata.SheetNames[0]; //取第一张表
          console.log('wsname', wsname)
          const ws = XLSX.utils.sheet_to_json(workdata.Sheets[wsname]); //生成json表格内容
          console.log(ws); //自第二行开始的内容
          // return
          this.dialogVisible_dr = true
          ws.forEach((item, index) => {
            // 生成日期单独处理（可能存在带汉字时间字符串的问题）
            let scrqExcel = item["生成日期"]
            if (scrqExcel) {
              let scrqExcelTime = getDateTime(scrqExcel)
              try {
                if (scrqExcelTime == 'Invalid date') {
                  this.$message.warning('序号' + (item['序号']) + ' 数据生成日期解析错误，请设置为日期格式或日期格式的文本格式重试')
                  return
                }
                item["生成日期"] = dateFormatNYRChinese(scrqExcelTime)
              } catch (error) {
                this.$message.warning(error.message)
                return
              }
            }
            // 状态变化时间单独处理（可能存在带汉字时间字符串的问题）
            let ztbhsjExcel = item["状态变化时间"]
            if (ztbhsjExcel) {
              let ztbhsjExcelTime = getDateTime(ztbhsjExcel)
              try {
                if (ztbhsjExcelTime == 'Invalid date') {
                  this.$message.warning('序号' + (item['序号']) + ' 数据状态变化时间解析错误，请设置为日期格式或日期格式的文本格式重试')
                  return
                }
                item["状态变化时间"] = dateFormatNYRChinese(ztbhsjExcelTime)
              } catch (error) {
                this.$message.warning(error.message)
                return
              }
            }
          })
          // 赋值dialog表格数据
          this.dr_cyz_list = ws
          console.log("列表的值:", this.dr_cyz_list)
          // 加工excel读取业务类型为数组
          // this.dr_cyz_list.forEach(function(item) {
          // 	console.log(item[0]['业务类型'].splite(','))
          // })
          this.$refs.upload.value = ''; // 处理完成 清空表单值
          this.dr_dialog = false
        } catch (e) {
          return false;
        }
      };
      fileReader.readAsBinaryString(files[0]);
    },
    //修改
    updataDialog (form) {
      this.$refs[form].validate((valid) => {
        if (valid) {
          if (this.pdsmzt == 0) {
            // 判定修改情况
            // // 状态变化标记（如果状态变化，则记录轨迹日志）
            // let ztbhsjFlag = false
            // // 责任人变化标记
            // let zrrFlag = false
            // 状态变化标记和责任人变化标记合在一起处理，只要有一个变化了就添加日志
            let changedFieldArr = ['zt', 'zrr']
            let changedFlag = false
            //
            let decideResArr = decideChange(this.updateItemOld, this.xglist, [])
            console.log('decideResArr', decideResArr)
            decideResArr.some(item => {
              /**
               * 注意：状态变化时间可能不是用户点击按钮时的当前时间
              */
              if (item.changedType == 2 &&  changedFieldArr.indexOf(item.changedField) != -1) {
                changedFlag = true
                return true
              } else {
                changedFlag = false
              }
            })
            //
            reviseSmzttz(this.xglist)
            if (changedFlag) {
              // 加入状态变化时间及责任人到轨迹日志额外参数中
              let paramsLogs = {
                xyybs: 'mk_smzttz',
                id: this.xglist.smztid,
                ymngnmc: this.xglist.zt,
                extraParams: {
                  ztbhsj: this.xglist.ztbhsj,
                  zrr: this.xglist.zrr
                }
              }
              writeTrajectoryLog(paramsLogs)
            }
            // 刷新页面表格数据
            this.smzttz()
            this.zxfwlist()
            // 关闭dialog
            this.$message.success('修改成功')
            this.xgdialogVisible = false
          } else if (this.pdsmzt == 2) {
            this.$message.error('载体编号已存在');
          }

        } else {
          console.log('error submit!!');
          return false;
        }
      });

    },
    xqyl (row) {
      this.updateItemOld = JSON.parse(JSON.stringify(row))

      this.xglist = JSON.parse(JSON.stringify(row))
      // this.form1.ywlx = row.ywlx
      console.log('old', row)
      console.log("this.xglist.ywlx", this.xglist);
      this.xglist.scbm = this.xglist.scbm.split('/')
      this.xqdialogVisible = true
    },

    updateItem (row) {
      this.updateItemOld = JSON.parse(JSON.stringify(row))

      this.xglist = JSON.parse(JSON.stringify(row))
      this.ztbh = this.xglist.ztbh
      // this.form1.ywlx = row.ywlx
      console.log('old', row)
      console.log("this.xglist.ywlx", this.xglist);
      this.xglist.scbm = this.xglist.scbm.split('/')
      this.xgdialogVisible = true
    },
    //查询
    onSubmit () {
      // this.smzttz()

      //  form是查询条件
      console.log(this.formInline);
      // 备份了一下数据
      let arr = this.tableDataCopy
      // 通过遍历key值来循环处理
      Object.keys(this.formInline).forEach((e, label) => {
        // 调用自己定义好的筛选方法
        if (typeof (this.formInline[e]) == 'object') {
          // console.log(this.formInline[e].length);
          if (this.formInline[e] == null || this.formInline[e].length == 0) {
            arr = this.filterFunc(this.formInline[e], e, arr)
            return
          }
          let timeArr1 = this.formInline[e][0].replace(/[\u4e00-\u9fa5]/g, '/')

          if (!(isNaN(timeArr1) && !isNaN(Date.parse(timeArr1)))) {
            this.formInline[e] = this.formInline[e].join('/')
            arr = this.filterFunc(this.formInline[e], e, arr)
            this.formInline[e] = this.formInline[e].split('/')
          } else {
            arr = this.filterFunc(this.formInline[e], e, arr)
          }
        } else {
          arr = this.filterFunc(this.formInline[e], e, arr)
        }
      })
      // 为表格赋值
      this.smzttzList = arr
    },
    filterFunc (val, target, filterArr) {
      // 参数不存在或为空时，就相当于查询全部
      if (val == undefined || val == '') return filterArr
      return filterArr.filter(p => {
        let bool
        let resP
        if (Object.prototype.toString.call(val) == '[object Array]') {
          console.log('是数组')
          if (val.length > 1) {
            let timeArr1 = val[1].replace(/[\u4e00-\u9fa5]/g, '/')
            let date = new Date(timeArr1)
            if ('Invalid Date' != date) {
              // 时间
              if (new Date(p[target].replace(/[\u4e00-\u9fa5]/g, '/')).getTime() <= date
                .getTime()) {
                console.log('找到小于范围内是记录')
                resP = p
                let timeArr0 = val[0].replace(/[\u4e00-\u9fa5]/g, '/')
                if (new Date(p[target].replace(/[\u4e00-\u9fa5]/g, '/')).getTime() >= new Date(
                  timeArr0)
                  .getTime()) {
                  console.log('找到大于范围内是记录')
                  resP = p
                } else {
                  resP = undefined
                }
              }
            } else {
              console.log('非法时间')
            }
            if (resP) {
              console.log('不是时间，通过时间校验')
              bool = true
            }
          } else {
            if (new Date(p[target]).getTime() <= new Date(timeArr0).getTime()) {
              resP = p
            } else {
              resP = undefined
            }
            if (resP) {
              bool = true
            }
          }
          return bool
        }
        return p[target].indexOf(val) > -1
        // return bool
      }) // 可以自己加一个.toLowerCase()来兼容一下大小
    },

    returnSy () {
      this.$router.push("/tzglsy");
    },
    smzttz () {
      let params = {
        page: this.page,
        pageSize: this.pageSize
      }
      Object.assign(params, this.formInline)
      let resList = getSmzttz(params)
      console.log("params", params);

      this.smzttzList = resList.list_total
      this.dclist = resList.list_total
      if (resList.list_total.length != 0) {
        this.tjlist = resList.list_total[resList.list_total.length - 1]
        this.tjlist.scbm = this.tjlist.scbm.split('/')
      }
      this.dclist.forEach((item, label) => {
        this.xh.push(label + 1)
      })
      this.tableDataCopy = resList.list
      this.total = this.smzttzList.length
    },
    //删除
    shanchu (id) {
      if (this.selectlistRow != '') {
        this.$confirm('是否继续删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let valArr = this.selectlistRow
          // console.log("....", val);
          valArr.forEach(function (item) {
            if (Array.isArray(item.scbm)) {
             item.scbm = item.scbm.join('/')
            }
            deleteSmzttz(item)
            console.log("删除：", item);
            console.log("删除：", item);
          })
          let params = valArr
          this.$message({
            message: '删除成功',
            type: 'success'
          });
          this.smzttz()
          this.zxfwlist()
        }).catch(() => {
          this.$message('已取消删除')
        })
      } else {
        this.$message({
          message: '未选择删除记录，请选择下列列表',
          type: 'warning'
        });
      }
    },
    //添加
    showDialog () {
      this.resetForm()
      this.dialogVisible = true
    },

    //导出
    exportList () {
      console.log("----导出涉密人员----")
      // console.log(this.selectlistRow);
      // if (this.selectlistRow.length > 0) {
      let filename = "涉密载体台账" + getUuid() + ".xlsx"

      const {
        dialog
      } = require('electron').remote;
      //弹窗title
      let options = {
        title: "保存文件",
        defaultPath: filename,
      };
      console.log(dialog)
      //导出文件夹选择弹窗
      dialog.showSaveDialog(options, result => {
        console.log('result', result)
        if (result == null || result == "") {
          console.log("取消导出")
          return
        }
        let list = []
        list.push(["涉密载体台账"])
        list.push(["上报单位:", this.dwmc, "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""])
        list.push(["统计年度:", this.year, "", "", "", "", "", "", "", "", "", "", "", "", "",
          "填报时间:", this.Date
        ])
        //对导出的数据进行格式，js导出excel要求传入的参数格式为[["列1名称","列2名称","列3名称"],["列11","值21","值31"],["列12","值22","值32"]]
        list.push(["序号", "载体名称", "载体编号", "项目编号", "载体类型", "生成原因",
          "密级", "保密期限", "份数", "页数", "知悉范围", "生成日期",
          "生成部门", "责任人", "保管位置", "使用状态", "状态变化时间"
        ]) //确定列名

        for (var i in this.dclist) { //每一行的值
          let item = this.dclist[i] //{"name":"XXX","dw":"XXX","zw":"XXX"}

          console.log("导出值:", this.dclist);
          let column = [(parseInt(i) + 1), item["mc"], item["ztbh"], item["xmbh"], item["lx"],
          item["scyy"],
          item["mj"], item["bmqx"], item["fs"], item["ys"],
          item["zxfw"], item["scrq"], item["scbm"], item["zrr"],
          item["bmwz"], item["zt"], item["ztbhsj"],
          ]
          list.push(column)
        }
        let merges = [{
          s: { //s为开始
            c: 0, //开始列
            r: 0 //开始取值范围
          },
          e: { //e结束
            c: 16, //结束列
            r: 0 //结束范围
          }
        }]
        let styles = {
          // 列样式
          cols: {
            // 作用sheet页索引（0开始）（-1全sheet页生效）
            scoped: -1,
            style: [{
              wpx: 100
            },
            {
              wpx: 300
            },
            {
              wpx: 150
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            }, {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 200
            },
            {
              wpx: 100
            },
            {
              wpx: 200
            },
            {
              wpx: 100
            },
            {
              wpx: 200
            },
            {
              wpx: 120
            },
            {
              wpx: 120
            }
            ]
          },
          // 全局样式
          all: {
            alignment: {
              horizontal: 'center', // 水平居中
              vertical: 'center', // 垂直居中
              wrapText: true, // 文字换行
            },
            font: {
              sz: 11, // 字号
              name: '宋体' // 字体
            },
            border: { // 边框
              top: {
                style: 'thin'
              },
              bottom: {
                style: 'thin'
              },
              left: {
                style: 'thin'
              },
              right: {
                style: 'thin'
              }
            }
          },
          // 单元格样式
          cell: [{
            // 生效sheet页索引（值为 -1 时所有sheet页都生效）
            scoped: -1,
            // 索引
            index: 'A1',
            style: {
              font: {
                name: '宋体',
                sz: 16, // 字号
                bold: true,
              },
              alignment: {
                horizontal: 'center', // 水平居中
                vertical: 'center' // 垂直居中
              }
            }
          }]
        }
        // excel导出方法自定义配置
        let config = {
          // 忽略行索引(从1开始)
          rowIndexArrIgnore: [1]
        }
        exportExcel(result, list, merges, styles, config) //list 要求为二维数组
        this.$message('导出成功:' + result)
      })
    },
    //   //编号自增
    //   setInterval(num,len) {
    //   var len = len //显示的长度，如果以0001则长度为4
    //   num = parseInt(num, 10) + 1//转数据类型，以十进制自增
    //   num = num.toString()//转为字符串
    //   while (num.length < len) {//当字符串长度小于设定长度时，在前面加0
    //     num = "0" + num
    //   }
    //   //如果字符串长度超过设定长度只做自增处理。
    //   return num
    // },
    //确定添加成员组
    submitTj (formName) {
      console.log('this.tjlist', this.tjlist)
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let ztbh = this.tjlist.ztbh
          // let exp = /^[+-]?\d*(\.\d*)?(e[+-]?\d+)?$/;
          let exp = /[0-9]+$/;
          let matchArr = ztbh.match(exp)
          let mwslen
          let prefix
          if (matchArr != null) {
            mwslen = matchArr[0].length;
            prefix = ztbh.substring(0, ztbh.lastIndexOf(matchArr[0]))
          }
          let mws = 0;
          let bl;
          for (let i = 1; i <= this.tjlist.fs; i++) {
            mws = 0
            let uuid = getUuid()
            let params = {
              mc: this.tjlist.mc,
              ztbh: ztbh,
              xmbh: this.tjlist.xmbh,
              scyy: this.tjlist.scyy,
              mj: this.tjlist.mj,
              bmqx: this.tjlist.bmqx,
              lx: this.tjlist.lx,
              fs: 1,
              ys: this.tjlist.ys,
              zxfw: this.tjlist.zxfw,
              scrq: this.tjlist.scrq,
              scbm: this.tjlist.scbm.join('/'),
              zrr: this.tjlist.zrr,
              bmwz: this.tjlist.bmwz,
              zt: this.tjlist.zt,
              ztbhsj: this.tjlist.ztbhsj,
              smztid: uuid
            }
            console.log(params);
            this.onInputBlur(3, params)
            if (this.pdsmzt == 0) {
              addSmzttz(params)
              this.dialogVisible = false
              this.$message({
                message: '添加成功',
                type: 'success'
              });
               this.resetForm()
        this.smzttz()
        this.zxfwlist()
            }
            console.log(ztbh.match(exp));
            if (ztbh.match(exp) != null) {
              if (ztbh.match(exp)[0] != '') {
                mws = ztbh.match(exp)[0] * 1
                mws++
                bl = mwslen - mws.toString().length
                mws = (mws.toString()).padStart(mwslen, '0')
                ztbh = prefix + mws
              }
            } else {
              mws++
              ztbh = ztbh + mws
            }

          }
        } else {
          console.log('error submit!!');
          return false;
        }
       
      });

    },
    deleteTkglBtn () {

    },

    selectRow (val) {
      console.log(val);
      this.selectlistRow = val;
    },
    //列表分页--跳转页数
    handleCurrentChange (val) {
      this.page = val
      this.smzttz()
    },
    //列表分页--更改每页显示个数
    handleSizeChange (val) {
      this.page = 1
      this.pageSize = val
      this.smzttz()
    },
    //添加重置
    resetForm () {
      this.tjlist.mc = ''
      this.tjlist.scyy = ''
      this.tjlist.mj = '秘密'
      this.tjlist.bmqx = this.Date
      this.tjlist.lx = '纸介质'
      this.tjlist.fs = ''
      this.tjlist.ys = ''
      this.tjlist.zxfw = ''
      this.tjlist.scrq = this.Date
      this.tjlist.scbm = ''
      this.tjlist.zrr = ''
      this.tjlist.bmwz = ''
      this.tjlist.zt = '在管'
      this.tjlist.ztbhsj = ''
    },
    handleClose (done) {
      // this.resetForm()
      this.dialogVisible = false
      this.tjlist.scbm = this.tjlist.scbm.join('/')
      this.smzttz()
    },
    // 弹框关闭触发
    close (formName) {
      // 清空表单校验，避免再次进来会出现上次校验的记录
      this.$refs[formName].clearValidate();
    },
    close1 (form) {
      // 清空表单校验，避免再次进来会出现上次校验的记录
      this.$refs[form].clearValidate();
    },
    // 设备在管状态更新
    updateSbZtBtn (zt) {
      if (!this.selectlistRow) {
        this.$message({
          message: '请选择需要操作的数据',
          type: 'warning'
        })
        return
      }
      if (this.selectlistRow.length <= 0) {
        this.$message({
          message: '尚未选择需要操作的数据',
          type: 'warning'
        })
        return
      }
      // 弹出状态变化时间选择dialog
      console.log('弹出状态变化时间选择dialog')
      this.ztbhsjQrList = JSON.parse(JSON.stringify(this.selectlistRow))
      this.dialogObjZtbhsjQr = {
        zt: zt,
        ztbhsj: null
      }
      this.dialogVisibleZtbhsjQr = true
      return
      let params = this.selectlistRow
      updateSbZt(params, zt)
      this.smzttz()
      this.$message({
        message: '操作成功',
        type: 'success'
      })
    },
    zysb () {
      if (this.selectlistRow.length == 0) {
        this.$message({
          message: '操作失败',
          type: 'warning'
        });
      } else {
        let params = this.selectlistRow
        xgsmjsjsyzt_zy(params)
        this.smzttz()
        this.$message({
          message: '操作成功',
          type: 'success'
        });
      }
    },
    tysb () {
      if (this.selectlistRow.length == 0) {
        this.$message({
          message: '操作失败',
          type: 'warning'
        });
      } else {
        let params = this.selectlistRow
        xgsmjsjsyzt_ty(params)
        this.smzttz()
        this.$message({
          message: '操作成功',
          type: 'success'
        });
      }

    },
    bfsb () {
      if (this.selectlistRow.length == 0) {
        this.$message({
          message: '操作失败',
          type: 'warning'
        });
      } else {
        let params = this.selectlistRow
        xgsmjsjsyzt_bf(params)
        this.smzttz()
        this.$message({
          message: '操作成功',
          type: 'success'
        });
      }
    },
    jcsb () {
      if (this.selectlistRow.length == 0) {
        this.$message({
          message: '操作失败',
          type: 'warning'
        });
      } else {
        let params = this.selectlistRow
        xgsmjsjsyzt_jc(params)
        this.smzttz()
        this.$message({
          message: '操作成功',
          type: 'success'
        });
      }
    },
    xhsb () {
      if (this.selectlistRow.length == 0) {
        this.$message({
          message: '操作失败',
          type: 'warning'
        });
      } else {
        let params = this.selectlistRow
        xgsmjsjsyzt_xh(params)
        this.smzttz()
        this.$message({
          message: '操作成功',
          type: 'success'
        });
      }
    },
    onInputBlur (index, param) {
      if (index == 1) {
        this.pdsmzt = jyaddSmzttz(this.tjlist)
        if (this.pdsmzt == 2) {
          this.$message.error('载体编号已存在');
        }
      } else if (index == 2) {
        this.pdsmzt = 0
        if (this.ztbh != this.xglist.ztbh) {
          this.pdsmzt = jyaddSmzttz(this.xglist)
          if (this.pdsmzt == 2) {
            this.$message.error('载体编号已存在');
          }
        }
      } else if (index == 3) {
        this.pdsmzt = jyaddSmzttz(param)
        if (this.pdsmzt == 2) {
          this.$message.error('载体编号已存在');
        }
      }
    },
    querySearch (queryString, cb) {
      var restaurants = this.restaurants;
      console.log("restaurants", restaurants);
      var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
      console.log("results", results);
      // 调用 callback 返回建议列表的数据
      cb(results);
      console.log("cb(results.dwmc)", results);
    },
    createFilter (queryString) {
      return (restaurant) => {
        return (restaurant.xm.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
      };
    },
    smry () {
      this.restaurants = getsmry1()
    },
    handleChange (index) {
      let resList
      if (index == 1) {
        resList = getsmry(this.tjlist.scbm.join('/'))
      } else if (index == 2) (
        resList = getsmry(this.xglist.scbm.join('/'))
      )
      this.restaurants = resList;
      this.tjlist.zrr = "";
      this.xglist.zrr = "";
    },
    //模糊查询品牌型号
    querySearchzxfw (queryString, cb) {
      var restaurants = this.restaurantszxfw;
      console.log("restaurants", restaurants);
      var results = queryString ? restaurants.filter(this.createFilterzxfw(queryString)) : restaurants;
      console.log("results", results);
      // 调用 callback 返回建议列表的数据
      for (var i = 0; i < results.length; i++) {
        for (var j = i + 1; j < results.length; j++) {
          if (results[i].zxfw === results[j].zxfw) {
            results.splice(j, 1);
            j--;
          }
        }
      }
      cb(results);
      console.log("cb(results.zw)", results);
    },
    createFilterzxfw (queryString) {
      return (restaurant) => {
        return (restaurant.zxfw.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
      };
    },
    //模糊查询操作系统
    querySearchbmwz (queryString, cb) {
      var restaurants = this.restaurantszxfw;
      console.log("restaurants", restaurants);
      var results = queryString ? restaurants.filter(this.createFilterbmwz(queryString)) : restaurants;
      console.log("results", results);
      // 调用 callback 返回建议列表的数据
      for (var i = 0; i < results.length; i++) {
        for (var j = i + 1; j < results.length; j++) {
          if (results[i].bmwz === results[j].bmwz) {
            results.splice(j, 1);
            j--;
          }
        }
      }
      cb(results);
      console.log("cb(results.zw)", results);
    },
    createFilterbmwz (queryString) {
      return (restaurant) => {
        return (restaurant.bmwz.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
      };
    },
    zxfwlist () {
      let resList = getSmzt()
      this.restaurantszxfw = resList;
      console.log("this.restaurants", this.restaurantszxfw);
      console.log(resList)
    },
    cz () {
      this.formInline = {}
    },
    zxfw () {
      this.rydialogVisible = true
    },
    onSubmitry () {
      this.ry()
    },
    async ry () {
      let params = {
        // page: this.page1,
        // pageSize: this.pageSize1
      }
      console.log(this.formInlinery.bm);
      Object.assign(params, this.formInlinery)
      let list = getpxqdry(params)
      this.table1Data = list.list_total
      this.pxtjlist = list.list_total
      this.sxry = this.pxtjlist.length;
      this.pxtjlist.forEach((item, label) => {
        this.xh.push(label + 1)
      })
      this.total1 = list.total
      console.log(this.table1Data);
    },
    onTable1Select (rows) {
      console.log(rows);
      this.table2Data = rows
      this.selectlistRow = rows
      // this.selectedTable1Data = [...rows];
      // this.filterAdd(this.selectedTable1Data, this.table2Data, 'sfzhm');
      // this.selectedTable1Data = [];
      // this.$refs.table1.clearSelection();
    },

    /**
     * table2选择事件处理函数
     * @param {array} rows 已勾选的数据
     */
    onTable2Select (rows) {
      console.log(rows);
      console.log(this.table2Data);
      console.log(this.$refs.table1.selection);
      this.$refs.table1.selection.forEach((item, label) => {
        if (item == rows) {
          this.$refs.table1.selection.splice(label, 1)
        }
      })
      this.table2Data.forEach((item, label) => {
        if (item == rows) {
          console.log(label);
          this.table2Data.splice(label, 1)
        }
      })
      // this.selectedTable2Data = [...rows];
      // this.table2Data = this.filterDelete(this.selectedTable2Data, this.table2Data, 'sfzhm');
      // this.selectedTable2Data = [];
    },
    handleRowClick (row, column, event) {
      this.$refs.table1.toggleRowSelection(row);
    },
    addpxry () {
      // this.tianjiaryList = this.table2Data
      // this.xglist.ry = this.table2Data
      // this.rydialogVisible = false
      let ry = []
      this.table2Data.forEach(item => {
        ry.push(item.xm)
        // console.log(item);
      })
      console.log(ry);
      this.tjlist.zxfw = ry.join(',')
      this.rydialogVisible = false
      this.$refs.table1.clearSelection()
      this.table2Data = []
    },
    pxrygb () {
      this.rydialogVisible = false
      this.$refs.table1.clearSelection()
      this.table2Data = []
    },
  },
  watch: {

  }
}
</script>

<style scoped>
.bg_con {
  width: 100%;
}

.dabg {
  /* margin-top: 10px; */
  box-shadow: 0px 2px 12px 0px rgba(9, 70, 255, 0.13);
  border-radius: 8px;
  width: 100%;
}

.xmlb-title {
  line-height: 60px;
  width: 100%;
  padding-left: 10px;
  height: 60px;
  background: url(../../assets/background/bg-02.png) no-repeat left;
  background-size: 100% 100%;
  text-indent: 10px;
  /* margin: 0 20px; */
  color: #0646bf;
  font-weight: 700;
}

.fhsy {
  display: inline-block;
  width: 120px;
  margin-top: 10px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-left: 30px;
  padding-top: 4px;
  float: right;
  background: url(../../assets/icons/zzjg_icon1.png) no-repeat center;
  background-size: 100% 100%;
}

.item_button {
  height: 100%;
  float: left;
  padding-left: 10px;
  line-height: 50px;
}

.select_wrap {
  /* //padding: 5px; */

  .select_wrap_content {
    float: left;
    width: 100%;
    line-height: 50px;
    /* // padding-left: 20px; */
    /* // padding-right: 20px; */
    height: 100%;
    background: rgba(255, 255, 255, 0.7);

    .item_label {
      padding-left: 10px;
      height: 100%;
      float: left;
      line-height: 50px;
      font-size: 1em;
    }
  }
}

.daochu {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

/* /deep/.el-radio {
	display: block;
	margin-top: 10px;
	margin-bottom: 10px;
} */

.mhcx1 {
  margin-top: 0px;
}

.widths {
  width: 6vw;
}

.widthx {
  width: 8vw;
}

.cd {
  width: 191px;
}

/deep/.mhcx .el-form-item {
  /* margin-top: 5px; */
  margin-bottom: 5px;
}

.dialog-footer {
  display: block;
  margin-top: 10px;
}
/deep/.el-table__body-wrapper::-webkit-scrollbar {
  display: block !important;
  width: 8px; /*滚动条宽度*/
  height: 8px; /*滚动条高度*/
}
/deep/.el-table__body-wrapper::-webkit-scrollbar-track {
  border-radius: 10px; /*滚动条的背景区域的圆角*/
  -webkit-box-shadow: inset 0 0 6px rgba(238, 238, 238, 0.3);
  background-color: #eeeeee; /*滚动条的背景颜色*/
}
/deep/.el-table__body-wrapper::-webkit-scrollbar-thumb {
  border-radius: 10px; /*滚动条的圆角*/
  -webkit-box-shadow: inset 0 0 6px rgba(145, 143, 0143, 0.3);
  background-color: rgb(145, 143, 143); /*滚动条的背景颜色*/
}
</style>