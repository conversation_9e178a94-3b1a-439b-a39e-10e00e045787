<template>
  <div class="bgglContainer" v-loading="pageLoading">
    <div class="bgglDiv" onselectstart="return false" ref="contentData">
      <!-- 存放文章的容器 -->
      <!-- word -->
      <div v-if="wordShow" class="wordContainer" :class="gdt ? 'gdt' : ''" id="bodyContainer" ref="file"></div>
      <!-- pdf -->
      <div class="dwheader" v-if="pdfShow"></div>
      <iframe style="pointer-events: none;" :scrolling="iframeScrolling" ref="myiframe" id="myiframe" width="1000"
        height="100%" :src="embedUrl" frameborder="0"> </iframe>
      <!-- <webview v-if="pdfShow" style="width:1000px;height:1000px" src="D:\\workspace\\zczp0103\\src\\renderer\\view\\bggl\\MyBatisPlus（SpringBoot版）.pdf" plugins></webview> -->
      <!-- 会员提示性文字 -->
      <div class="bgglhyDiv" v-if="hylb">
        <img src="./img/st.png" class="cursorClick" alt="" @click="ewmdialog">
        <div class="erweima cursorClick" @click="ewmdialog">开通会员输入口令继续观看
        </div>
        <el-button type="success" round class="klBtn text-center" @click="kldialog">输入口令</el-button>
      </div>
    </div>
    <!-- 界面功能按钮 -->
    <div class="gnbutton">
      <div class="backBtn cursorClick text-center fl" @click="fhsy">
        <p>返回</p>
        <img src="./img/backbtn.png" alt="">
      </div>
      <div class="downloadBtn cursorClick text-center" @click="downloadFile($route.query.item)" v-if="gdt">
        <p>下载</p>
        <img src="./img/download.png" alt="">
      </div>
    </div>
    <!-- 扫描二维码弹框 -->
    <el-dialog title="扫面二维码" :close-on-click-modal="false" :visible.sync="ewmdialogVisible" width="30%" class="xg"
      :before-close="handleClose">
      <img src="./img/ecode.png" class="xgImg" alt="">
      <span slot="footer" class="dialog-footer">
        <el-button type="warning" @click="ewmdialogVisible = false">关 闭</el-button>
      </span>
    </el-dialog>
    <!-- 输入口令弹框 -->
    <el-dialog title="输入会员口令" :close-on-click-modal="false" :visible.sync="kldialogVisible" width="30%" class="xg"
      :before-close="klhandleClose" @close="close1('form')">
      <el-form ref="form" :model="tjlist" label-width="120px" size="mini" :rules="rules">
        <el-form-item label="口令" prop="kl" class="one-line">
          <el-input placeholder="口令" v-model="tjlist.kl" clearable></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="updataDialog('form')">保 存</el-button>
        <el-button type="warning" @click="kldialogVisible = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getDeportConfigPathDev, getPdfNormal } from '../../../utils/pathUtil'
import { decryptAes } from '../../../utils/aesUtils'
import { decryptAesCBCHy, encryptAesCBCHy } from '../../../utils/aesUtils'
import {
  getlogin
} from "../../../db/loginyhdb"
import {
  reviseHyKl
} from "../../../db/bgwdgldb.js";
// 异常处理工具
import {
  errorProcessor
} from '../../../utils/errorProcessor'
// 引入docx-preview插件将word文档显示到界面
let { renderAsync } = require("docx-preview");
import MD5 from 'md5'
export default {
  //import引入的组件需要注入到对象中才能使用
  components: {},
  props: {},
  data() {
    //这里存放数据
    return {
      pageLoading: false, // 界面整体loading
      wordShow: false, // word文件显隐
      pdfShow: false, // pdf文件显隐
      iframeScrolling: 'no', // pdf是否可以滚动
      embedUrl: '', // pdf文件路径
      ewmdialogVisible: false, // 扫描二维码弹框
      kldialogVisible: false, // 输入口令弹框
      gdt: false, // 下载按钮
      hylb: true, // 非会员提示性文字
      allFileDatas: [], // 获取所有带id的文件信息
      tjlist: {
        kl: ''
      }, // 口令提交
      rules: {
        kl: [{
          required: true,
          message: '请输入会员口令',
          trigger: 'blur'
        },],
      } //验证规则
    }
  },
  //计算属性 类似于data概念
  computed: {},
  //监控data中数据变化
  watch: {},
  //方法集合
  methods: {
    // 开通会员输入口令继续观看
    ewmdialog() {
      this.ewmdialogVisible = true
    },
    // 扫描二维码关闭
    handleClose() {
      this.ewmdialogVisible = false
    },
    // 输入口令方法
    kldialog() {
      this.kldialogVisible = true
    },
    // 输入会员口令弹框关闭
    klhandleClose() {
      this.kldialogVisible = false
    },
    // 查看文件的所有内容
    allFileShow(){
      if (this.$route.query.item.fileType == '.docx') {
        // word
        this.kldialogVisible = false
        this.gdt = true
        this.hylb = false
        this.$refs.file.style.padding = '20px 10px 10px 10px'
      } else if (this.$route.query.item.fileType == '.pdf') {
        this.kldialogVisible = false
        this.gdt = true
        this.hylb = false
        this.iframeScrolling = 'yes'
        this.pdfShow = false
        let that = this
        this.pageLoading = true
        setTimeout(function () {
          that.pdfShow = true
          that.$refs.myiframe.style.pointerEvents = ''
        }, 1000);
      }
    },
    // 验证口令
    updataDialog(form) {
      this.$refs[form].validate((valid) => {
        if (valid) {
          // 单位注册号
          let dwzch = getlogin()[0].dwzch
          let keyStr = MD5(dwzch).substring(8, 24)
          // 如果有，则验证密钥是否有效或是否在有限期内
          let backStr = decryptAesCBCHy(this.tjlist.kl, keyStr)
          // 获取口令里的单位注册号
          let mDwzch = backStr.dwzch
          // 验证本地单位注册号与解密单位注册号是否相同
          if (mDwzch == dwzch) {
            console.log('验证此key中单位注册号与本地单位注册号一致')
            if (!backStr.isZcStatus) {
              // 未注册
              console.log('未注册')
              if (Date.now() > backStr.qfrq && Date.now() < backStr.qfrq + backStr.yxqqz * 86399915) {
                // 当前系统时间是否在签发日期+注册阈值内
                backStr.isZcStatus = 1
                backStr.zcDate = Date.now()
                encryptAesCBCHy(backStr, keyStr)
                let params = {
                  "id": "1",
                  "kl": encryptAesCBCHy(JSON.stringify(backStr), keyStr)
                }
                reviseHyKl(params)
                console.log('注册成功可访问全部！')
                // 如果正确并且在有效期内  可查看全部文章并可以下载全文
                this.allFileShow()
              } else {
                console.log('密钥串已经失效')
                this.$message.error('密钥串已经失效')
              }
            } else {
              console.log('已经注册')
              // 已经注册
              // let bsItem = backStr.yxrq.find((item)=>{
              //     return item.bs == "测试标识"
              // })
              let bsItem = backStr.yxrq[0]
              if (backStr.zcDate > backStr.qfrq && backStr.zcDate < bsItem.yxq) {
                console.log('已经注册 在有效期内  可查看全部文章并可以下载全文')
                // 如果正确并且在有效期内  可查看全部文章并可以下载全文
                this.allFileShow()
              } else {
                console.log('已经注册 密钥不匹配，查看首页并提示提示相关信息')
                // 如果没有密钥或者密钥不匹配，查看首页并提示提示相关信息
                this.$notify({
                  title: '提示',
                  message: '会员已到期，请续费继续查看',
                  type: 'warning',
                  offset: 100
                })
                this.ewmdialogVisible = true
              }
            }

          } else { // 提示非法密钥串
            console.log('非法密钥串')
            this.$message.error('非法密钥串')
          }
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    close1(form) {
      // 清空表单校验，避免再次进来会出现上次校验的记录
      this.$refs[form].resetFields();
    },
    // 将buffer转换成ArrayBuffer
    toArrayBuffer(buf) {
      var ab = new ArrayBuffer(buf.length);
      var view = new Uint8Array(ab);
      for (var i = 0; i < buf.length; ++i) {
        view[i] = buf[i];
      }
      return ab;
    },
    // 显示word文档到界面
    handlePreview(item) {
      // word文件回显
      if (item.fileType == '.docx') {
        this.wordShow = true
        this.pdfShow = false
        var fs = require('fs');
        let that = this
        let arrayBufferDatas
        let file = getDeportConfigPathDev(item.wdmc)
        // 读取本地文件获取buffer
        fs.readFile(file, async function (err, data) {
          if (err) {
            console.log(err);
          } else {
            var buf = Buffer.from(data);
            let jmkey = item.id
            let encryptStr = buf
            let jmdatas = await decryptAes(encryptStr.toString(), jmkey)
            arrayBufferDatas = that.toArrayBuffer(new Buffer(JSON.parse(jmdatas)))
            that.docxRender(arrayBufferDatas);
          }
        });
      } else if (item.fileType == '.pdf') { // pdf文件回显
        this.pdfShow = true
        this.wordShow = false
        var fs = require('fs');
        let that = this
        let file = getDeportConfigPathDev(item.wdmc)
        // 读取本地文件获取buffer
        fs.readFile(file, async function (err, data) {
          if (err) {
            console.log(err);
          } else {
            var buf = Buffer.from(data);
            let jmkey = item.id
            let encryptStr = buf
            let jmdatas = await decryptAes(encryptStr.toString(), jmkey)
            let result = getPdfNormal(item.wdmc)
            fs.writeFileSync(result, new Buffer(JSON.parse(jmdatas)))
            that.embedUrl = result + '#scrollbars=0&toolbar=0&statusbar=0&view=FitH&messages=0&navpanes=0'
            that.$nextTick(() => {
              that.$refs.myiframe.onload = () => {
                document.getElementsByTagName("iframe")[0].contentDocument.body.childNodes[7].style.display = 'none'
                that.pageLoading = false
              }
            })
          }
        });
      }
    },
    // 渲染docx
    docxRender(buffer) {
      renderAsync(
        buffer, // Blob | ArrayBuffer | Uint8Array, 可以是 JSZip.loadAsync 支持的任何类型
        this.$refs.file, // HTMLElement 渲染文档内容的元素
      ).then(res => {
        this.pageLoading = false
      })
    },
    // 返回
    fhsy() {
      this.$router.push({
        path: '/bgglsy'
      })
    },
    // 下载功能
    downloadFile(fileInfo) {
      console.log('fileInfo', fileInfo)
      // let wjm = fileInfo.fileName // 获取文件名
      let wjm = fileInfo.wdmc // 获取文件名
      // let fromPath = getDeportConfigPathDev(fileInfo.fileName) // 获取文件路径
      let fromPath = getDeportConfigPathDev(wjm) // 获取文件路径
      // 获取文件流
      const FS = require('fs')
      const {
        dialog
      } = require('electron').remote
      let options = {
        title: '请选择文件保存路径',
        properties: ['openDirectory'],
        defaultPath: wjm
      }
      dialog.showSaveDialog(options, result => {
        try {
          let buffer = FS.readFileSync(fromPath)
          if (buffer) {
            // 解密文档
            let jmkey = fileInfo.id
            console.log('jmkey', jmkey)
            let jmdatas = decryptAes(buffer.toString(), jmkey)
            // 保存文件到新路径下
            // FS.writeFileSync(result, buffer)
            FS.writeFileSync(result, new Buffer(JSON.parse(jmdatas)))
          }
          this.$message.success('文件[' + wjm + ']下载成功')
        } catch (error) {
          error = errorProcessor(error)
          let errObj = JSON.parse(error.message)
          this.$notify({
            title: '系统异常',
            message: '[' + errObj.mark + ']\n',
            type: 'error',
            offset: 100,
            duration: 0
          })
          return
        }
      })
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.pageLoading = true
    // 根据传入的文章id或唯一标识判断当前显示的文章
    this.allFileDatas = this.$route.query.allFileDatas
  },
  created() {
    this.handlePreview(this.$route.query.item)
    let item = this.$route.query.item
    if (this.$route.query.klIsRight == "true" && item.fileType == '.docx') {
      this.gdt = true
      this.hylb = false
      this.wordShow = true
      this.$nextTick(() => {
        this.$refs.file.style.padding = '20px 10px 10px 10px'
      })
    } else if (this.$route.query.klIsRight == "true" && item.fileType == '.pdf') {
      this.gdt = true
      this.hylb = false
      this.iframeScrolling = 'yes'
      this.pdfShow = false
      let that = this
      this.pageLoading = true
      setTimeout(function () {
        that.pdfShow = true
        that.$refs.myiframe.style.pointerEvents = ''
      }, 1000);
    }
  },
  //生命周期-创建之前
  beforeCreated() { },
  //生命周期-挂载之前
  beforeMount() { },
  //生命周期-更新之前
  beforUpdate() { },
  //生命周期-更新之后
  updated() { },
  //生命周期-销毁之前
  beforeDestory() { },
  //生命周期-销毁完成
  destoryed() { },
  //如果页面有keep-alive缓存功能，这个函数会触发
  activated() { }
}
</script>
<style scoped>
.bgglContainer {
  height: 100%;
  position: relative;
}

.wordContainer {
  padding: 20px;
  background: #525659;
}

.bgglDiv {
  width: 1000px;
  margin: 0 auto;
  background-color: #fff;
  height: 100%
}

.bgglDiv img {
  width: 20px;
  height: 20px;
  margin-right: 20px
}

.bgglDiv .erweima {
  color: #e4745a;
  font-weight: 700;
  margin-right: 20px
}

.xg .xgImg {
  display: block;
  width: 200px;
  margin: auto;
}

.dwheader {
  height: 15px;
  background: #525659;
}

.bgglhyDiv {
  width: 1000px;
  margin: 0px auto;
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  bottom: 31px;
  height: 52px;
  background: linear-gradient(to bottom, transparent, #fff);
}

.backBtn {
  width: 70px;
  height: 35px;
  background: #409EFF;
  color: #FFFFFF;
  line-height: 35px;
  border-radius: 3px;
  overflow: hidden;
}

.backBtn p {
  float: left;
  margin-left: 6px;
}

.backBtn img {
  float: left;
  width: 22px;
  margin-top: 6px;
  /* position: absolute; */
  margin-left: 5px;
}

.downloadBtn {
  width: 70px;
  height: 35px;
  background: #FFFFFF;
  color: #409EFF;
  line-height: 35px;
  border-radius: 3px;
  float: left;
  margin-left: 10px;
}

.downloadBtn p {
  float: left;
  margin-left: 6px;
}

.downloadBtn img {
  float: left;
  width: 22px;
  margin-top: 6px;
  /* position: absolute; */
  margin-left: 5px;
}

/* @import url(); 引入css类 */
.klBtn {
  width: 165px;
  height: 40px;
  background-image: linear-gradient(90deg, rgb(40, 220, 134) 0%, rgb(33, 165, 102) 56%);
  font-size: 20px;
  color: #FFFFFF;
  letter-spacing: 0;
  line-height: 18px;
  font-weight: 400;
  border: none;
}

.dialog-footer {
  display: block;
  margin-top: 10px;
}

.gdt {
  height: calc(100% - 38px);
  overflow-y: scroll
}

::-webkit-scrollbar {
  display: block !important;
}

::-webkit-scrollbar {
  width: 15px;
  height: 15px;
}

::-webkit-scrollbar-track-piece {
  background-color: #F1F1F1;
  -webkit-border-radius: 0px;
}

::-webkit-scrollbar-thumb {
  height: 5px;
  background-color: #C1C1C1;
  -webkit-border-radius: 0px;
}

.gnbutton {
  position: absolute;
  right: 0px;
  top: 0px;
  overflow: hidden;
}

/* 修改word文档显示的样式 */
>>>.docx {
  padding: 0px !important;
}

>>>.docx-wrapper {
  background: none !important;
  padding: 0 !important;
  /* display:block!important; */
}

>>>.docx-wrapper>section.docx {
  background: white;
  width: 100% !important;
  box-shadow: none;
}

/deep/.el-dialog__body {
  padding: 30px 20px;
}
</style>