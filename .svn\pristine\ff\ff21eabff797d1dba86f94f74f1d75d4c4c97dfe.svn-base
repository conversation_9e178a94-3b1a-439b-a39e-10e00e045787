<template>
  <div class="bg_con" style="height: calc(100% - 38px);">
    <div style="width: 100%; position: relative; overflow: hidden; height: 100%;">

      <div class="dabg" style="height: 100%;">
        <div class="content" style="height: 100%;">
          <div class="table" style="height: 100%;">
            <!-- -----------------操作区域--------------------------- -->
            <div class="mhcx">
              <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline" style="float:left">
                <el-form-item label="涉密人员" style="font-weight: 700;">
                  <el-input v-model="formInline.xm" clearable placeholder="涉密人员" class="widthw">
                  </el-input>
                </el-form-item>
                <el-form-item label="脱密期限" style="font-weight: 700;">
                  <el-date-picker v-model="formInline.tmqjssj" type="daterange" range-separator="至" style="width:294px;" start-placeholder="查询起始时间" end-placeholder="查询结束日期" format="yyyy年MM月dd日" value-format="yyyy年MM月dd日">
                  </el-date-picker>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
                </el-form-item>
                <el-form-item>
                  <el-button type="warning" icon="el-icon-circle-close" @click="cz">重置</el-button>
                </el-form-item>

              </el-form>
              <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline" style="float:right">
                <el-form-item style="float: right;">
                  <el-button type="danger" size="medium" @click="shanchu" icon="el-icon-delete-solid">
                    删除
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <el-button type="primary" size="medium" icon="el-icon-download" @click="exportList()">导出
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <input type="file" ref="upload" style="display: none;position: absolute;top:10px; right:0;opacity:0; cursor: pointer;height: 32px;width: 56px;z-index:1;" accept=".xls,.xlsx">
                  <el-button type="primary" icon="el-icon-upload2" size="medium" @click="dr_dialog = true">
                    导入
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <el-button type="success" size="medium" @click="dialogVisible = true" icon="el-icon-plus">
                    离职离岗
                  </el-button>
                </el-form-item>
              </el-form>
            </div>

            <!-- -----------------审查组人员列表--------------------------- -->
            <div class="table_content_padding" style="height: 100%;">
              <div class="table_content" style="height: 100%;">
                <el-table :data="smryList" border @selection-change="selectRow" :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" style="width: 100%;border:1px solid #EBEEF5;" height="calc(100% - 34px - 44px - 10px)" stripe>
                  <el-table-column type="selection" width="55" align="center"> </el-table-column>
                  <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                  <el-table-column prop="xm" label="姓名"></el-table-column>
                  <el-table-column prop="ybm" label="原部门"></el-table-column>
                  <!-- <el-table-column prop="ygwmc" label="原岗位名称"></el-table-column> -->
                  <el-table-column prop="ysmdj" label="原涉密等级"></el-table-column>
                  <el-table-column prop="tmqkssj" label="脱密期开始时间"></el-table-column>
                  <el-table-column prop="tmqjssj" label="脱密期结束时间"></el-table-column>
                  <!-- <el-table-column prop="bz" label="备注"></el-table-column> -->
                  <el-table-column prop="" label="操作" width="140">
                    <template slot-scope="scoped">
                      <el-button size="medium" type="text" @click="getTrajectory(scoped.row)">轨迹
                      </el-button>
                      <el-button size="medium" type="text" @click="xqyl(scoped.row)">详情
                      </el-button>
                      <el-button size="medium" type="text" @click="updateItem(scoped.row)">修改
                      </el-button>
                    </template>
                  </el-table-column>

                </el-table>

                <!-- -------------------------分页区域---------------------------- -->
                <div style="border: 1px solid #ebeef5;">
                  <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange" :pager-count="5" :current-page="page" :page-sizes="[5, 10, 20, 30]" :page-size="pageSize" layout="total, prev, pager, sizes,next, jumper" :total="total">
                  </el-pagination>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 模板下载 -->
        <el-dialog title="开始导入" class="scbg-dialog" width="600px" @close="mbxzgb" :visible.sync="dr_dialog" show-close>
          <div style="padding: 20px;">
            <div class="daochu">
              <div>一、请点击“导出模板”，并参照模板填写信息。</div>
              <el-button type="primary" size="mini" @click="mbdc">
                模板导出
              </el-button>
            </div>
            <div class="daochu">
              <div>二、数据导入方式：</div>
              <el-radio-group v-model="sjdrfs" @change="Radio($event)">
                <el-radio label="1">追加（导入时已有的记录信息不变，只添加新的记录）</el-radio>
                <el-radio label="2">覆盖（导入时更新已有的记录信息，并添加新的记录）</el-radio>
              </el-radio-group>
            </div>
            <div class="daochu">
              <div>三、将按模板填写的文件，导入到系统中。</div>
              <el-button type="primary" size="mini" @click="chooseFile">
                上传导入
              </el-button>
            </div>
          </div>
        </el-dialog>
        <!-- -----------------导入-弹窗--------------------------- -->
        <el-dialog width="1000px" height="800px" title="导入涉密人员离岗离职汇总情况" class="scbg-dialog" :visible.sync="dialogVisible_dr" show-close>
          <div style="height: 600px;">
            <el-table :data="dr_cyz_list" ref="multipleTable" @selection-change="handleSelectionChange" style="width: 100%;border:1px solid #EBEEF5;" height="100%" stripe>
              <el-table-column type="selection" width="55"> </el-table-column>
              <el-table-column prop="__EMPTY" label="姓名"></el-table-column>
              <el-table-column prop="__EMPTY_2" label="身份证号码"></el-table-column>
              <el-table-column prop="__EMPTY_10" label="是否到公安机关出入境备案"></el-table-column>
              <el-table-column prop="__EMPTY_11" label="是否委托管理"></el-table-column>
              <el-table-column prop="__EMPTY_12" label="脱密期开始时间"></el-table-column>
              <el-table-column prop="__EMPTY_13" label="脱密期结束时间"></el-table-column>
              <el-table-column prop="__EMPTY_14" label="手机号码"></el-table-column>
              <el-table-column prop="__EMPTY_15" label="离岗离职类型"></el-table-column>
              <el-table-column prop="__EMPTY_16" label="去向单位名称"></el-table-column>
              <el-table-column prop="__EMPTY_17" label="备注">
              </el-table-column>
            </el-table>
          </div>

          <div style="height :30px;display: flex;align-items: center;justify-content: center;margin: 10px 0;">
            <el-button type="primary" @click="drcy" size="mini">导 入</el-button>
            <el-button type="warning" @click="dialogVisible_dr = false" size="mini">关 闭</el-button>
          </div>
        </el-dialog>

        <!-- -----------------涉密人员离岗离职信息-弹窗--------------------------- -->
        <el-dialog title="涉密人员离岗离职信息" :close-on-click-modal="false" :visible.sync="dialogVisible" width="60%" class="xg" :before-close="handleClose" @close="close('formName')">
          <el-form ref="formName" :model="tjlist" :rules="rules" label-width="194px" size="mini">
            <div style="display:flex">
              <el-form-item label="姓名" prop="xm">
                <!-- <el-input placeholder="姓名" v-model="tjlist.xm" clearable></el-input> -->
                <el-autocomplete class="inline-input" value-key="xm" v-model.trim="tjlist.xm" style="width: 100%;" @blur="dwxxByDwmc(tjlist.xm)" :fetch-suggestions="querySearch" size="medium" placeholder="请输入姓名" @select="handleSelect">
                </el-autocomplete>
              </el-form-item>
              <el-form-item label="身份证号码" prop="sfzhm">
                <el-input placeholder="身份证号码" v-model="tjlist.sfzhm" clearable disabled></el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="性别" prop="xb">
                <el-radio-group v-model="tjlist.xb" disabled>
                  <el-radio label="男"></el-radio>
                  <el-radio label="女"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="年龄" prop="nl">
                <el-input oninput="value=value.replace(/[^\d.]/g,'')" @blur="nl = $event.target.value" placeholder="年龄" v-model="tjlist.nl" clearable disabled>
                </el-input>
              </el-form-item>
            </div>
            <el-form-item label="原部门" prop="ybm" class="one-line" >
                <el-input v-model="tjlist.ybm" clearable placeholder="原部门" disabled></el-input>
              </el-form-item>
            <!-- <div style="display:flex">

             
              <el-form-item label="原岗位名称" prop="ygwmc">
                <el-select v-model="tjlist.ygwmc" placeholder="请选择岗位" multiple disabled style="width: 100%;">
                  <el-option v-for="(item, label) in gwmc" :label="item.gwmc" :value="item.gwmc" :key="label">
                  </el-option>
                </el-select>
              </el-form-item>
            </div> -->
            <div style="display:flex">
              <el-form-item label="原涉密等级" prop="ysmdj">
                <el-select v-model="tjlist.ysmdj" placeholder="请选择涉密等级" disabled style="width: 100%;">
                  <el-option v-for="item in smdjxz" :label="item.smdjmc" :value="item.smdjmc" :key="item.smdjid">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="职务" prop="zw">
                <el-input v-model="tjlist.zw" clearable placeholder="职务" disabled></el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="职级" prop="zj">
                <el-input v-model="tjlist.zj" clearable placeholder="职级" disabled></el-input>
              </el-form-item>
              <el-form-item label="级别职称" prop="zc">
                <el-select v-model="tjlist.zc" placeholder="请选择级别职称" disabled style="width: 100%;">
                  <el-option v-for="item in jbzcxz" :label="item.jbzcmc" :value="item.jbzcmc" :key="item.jbzcid">
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="原身份类型" prop="ysflx">
                <el-select v-model="tjlist.ysflx" placeholder="请选择身份类型" disabled style="width: 100%;">
                  <el-option v-for="item in sflxxz" :label="item.sflxmc" :value="item.sflxmc" :key="item.sflxid">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="是否到公安机关出入境备案" prop="gajgcrjba">
                <el-radio-group v-model="tjlist.gajgcrjba">
                  <el-radio label="是"></el-radio>
                  <el-radio label="否"></el-radio>
                </el-radio-group>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="是否委托管理" prop="sfwtgl">
                <el-radio-group v-model="tjlist.sfwtgl">
                  <el-radio label="是"></el-radio>
                  <el-radio label="否"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="脱密期开始时间" prop="tmqkssj">
                <!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
                <el-date-picker v-model="tjlist.tmqkssj" class="cd" clearable type="date" style="width:100%;" placeholder="选择日期" format="yyyy年MM月dd日" value-format="yyyy年MM月dd日" @change="tmkssj">
                </el-date-picker>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="脱密期结束时间" prop="tmqjssj">
                <!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
                <el-date-picker v-model="tjlist.tmqjssj" class="cd" clearable type="date" style="width:100%;" placeholder="选择日期" format="yyyy年MM月dd日" value-format="yyyy年MM月dd日">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="手机号码" prop="sjhm">
                <el-input v-model="tjlist.sjhm" clearable placeholder="手机号码" maxlength="11"></el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="离岗离职类型" prop="lzlglx">
                <el-select v-model="tjlist.lzlglx" placeholder="请选择离岗离职类型" @change="lzlglxBlur" style="width: 100%;">
                  <el-option v-for="item in lzlglxxz" :label="item.lzlglxmc" :value="item.lzlglxmc" :key="item.lzlglxid"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="去向单位名称" prop="qxdwmc">
                <el-input v-model="tjlist.qxdwmc" clearable placeholder="去向单位名称"></el-input>
              </el-form-item>
            </div>
            <el-form-item label="邮箱"  class="one-line">
                <el-input placeholder="邮箱" v-model="tjlist.yx" clearable style="width: 100%;">
                </el-input>
              </el-form-item>
            <el-form-item label="备注" prop="bz" class="one-line-textarea">
              <el-input type="textarea" v-model="tjlist.bz"></el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitTj('formName')">保 存</el-button>
            <el-button type="warning" @click="dialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>

        <el-dialog title="修改涉密人员离岗离职信息" :close-on-click-modal="false" :visible.sync="xgdialogVisible" width="50%" class="xg" @close="close1('form')">
          <el-form ref="form" :model="xglist" :rules="rules" label-width="194px" size="mini">
            <div style="display:flex">
              <el-form-item label="姓名" prop="xm">
                <!-- <el-input placeholder="姓名" v-model="xglist.xm" clearable></el-input> -->
                <el-autocomplete class="inline-input" value-key="xm" v-model.trim="xglist.xm" disabled style="width: 100%;" @blur="dwxxByDwmc(xglist.xm)" :fetch-suggestions="querySearch" size="medium" placeholder="请输入姓名" @select="handleSelect">
                </el-autocomplete>
              </el-form-item>
              <el-form-item label="身份证号码" prop="sfzhm">
                <el-input placeholder="身份证号码" v-model="xglist.sfzhm" clearable disabled></el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="性别" prop="xb">
                <el-radio-group v-model="xglist.xb" disabled>
                  <el-radio label="男"></el-radio>
                  <el-radio label="女"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="年龄" prop="nl">
                <el-input oninput="value=value.replace(/[^\d.]/g,'')" @blur="nl = $event.target.value" placeholder="年龄" v-model="xglist.nl" clearable disabled>
                </el-input>
              </el-form-item>
            </div>
            <el-form-item label="原部门" prop="ybm" class="one-line" >
                <el-input v-model="xglist.ybm" clearable placeholder="原部门" disabled></el-input>
              </el-form-item>
            <div style="display:flex">
              <el-form-item label="原涉密等级" prop="ysmdj">
                <el-select v-model="xglist.ysmdj" placeholder="请选择涉密等级" disabled style="width: 100%;">
                  <el-option v-for="item in smdjxz" :label="item.smdjmc" :value="item.smdjmc" :key="item.smdjid">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="职务" prop="zw">
                <el-input v-model="xglist.zw" clearable placeholder="职务" disabled></el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="职级" prop="zj">
                <el-input v-model="xglist.zj" clearable placeholder="职级" disabled></el-input>
              </el-form-item>
              <el-form-item label="级别职称" prop="zc">
                <el-select v-model="xglist.zc" placeholder="请选择级别职称" disabled style="width: 100%;">
                  <el-option v-for="item in jbzcxz" :label="item.jbzcmc" :value="item.jbzcmc" :key="item.jbzcid">
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="原身份类型" prop="ysflx">
                <el-select v-model="xglist.ysflx" placeholder="请选择身份类型" disabled style="width: 100%;">
                  <el-option v-for="item in sflxxz" :label="item.sflxmc" :value="item.sflxmc" :key="item.sflxid">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="是否到公安机关出入境备案" prop="gajgcrjba">
                <el-radio-group v-model="xglist.gajgcrjba">
                  <el-radio label="是"></el-radio>
                  <el-radio label="否"></el-radio>
                </el-radio-group>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="是否委托管理" prop="sfwtgl">
                <el-radio-group v-model="xglist.sfwtgl">
                  <el-radio label="是"></el-radio>
                  <el-radio label="否"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="脱密期开始时间" prop="tmqkssj">
                <!-- <el-input v-model="xglist.sgsj" clearable></el-input> -->
                <el-date-picker v-model="xglist.tmqkssj" class="cd" clearable type="date" style="width:100%;" placeholder="选择日期" format="yyyy年MM月dd日" value-format="yyyy年MM月dd日" @change="tmkssj">
                </el-date-picker>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="脱密期结束时间" prop="tmqjssj">
                <!-- <el-input v-model="xglist.sgsj" clearable></el-input> -->
                <el-date-picker v-model="xglist.tmqjssj" class="cd" clearable type="date" style="width:100%;" placeholder="选择日期" format="yyyy年MM月dd日" value-format="yyyy年MM月dd日">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="手机号码" prop="sjhm">
                <el-input v-model="xglist.sjhm" clearable placeholder="手机号码" maxlength="11"></el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="离岗离职类型" prop="lzlglx">
                <el-select v-model="xglist.lzlglx" placeholder="请选择离岗离职类型" @change="lzlglxBlur" style="width: 100%;">
                  <el-option v-for="item in lzlglxxz" :label="item.lzlglxmc" :value="item.lzlglxmc" :key="item.lzlglxid"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="去向单位名称" prop="qxdwmc">
                <el-input v-model="xglist.qxdwmc" clearable placeholder="去向单位名称"></el-input>
              </el-form-item>
            </div>
            <el-form-item label="邮箱"  class="one-line">
                <el-input placeholder="邮箱" v-model="xglist.yx" clearable style="width: 100%;">
                </el-input>
              </el-form-item>
            <el-form-item label="备注" prop="bz" class="one-line-textarea">
              <el-input type="textarea" v-model="xglist.bz"></el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="updataDialog('form')">保 存</el-button>
            <el-button type="warning" @click="xgdialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>

        <el-dialog title="涉密人员离岗离职信息详情" :close-on-click-modal="false" :visible.sync="xqdialogVisible" width="50%" class="xg">
          <el-form ref="form" :model="xglist" label-width="194px" size="mini" disabled>
            <div style="display:flex">
              <el-form-item label="姓名" prop="xm">
                <!-- <el-input placeholder="姓名" v-model="xglist.xm" clearable></el-input> -->
                <el-autocomplete class="inline-input" value-key="xm" v-model.trim="xglist.xm" disabled style="width: 100%;" @blur="dwxxByDwmc(xglist.xm)" :fetch-suggestions="querySearch" size="medium" placeholder="请输入姓名" @select="handleSelect">
                </el-autocomplete>
              </el-form-item>
              <el-form-item label="身份证号码" prop="sfzhm">
                <el-input placeholder="身份证号码" v-model="xglist.sfzhm" clearable disabled></el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="性别" prop="xb">
                <el-radio-group v-model="xglist.xb" disabled>
                  <el-radio label="男"></el-radio>
                  <el-radio label="女"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="年龄" prop="nl">
                <el-input oninput="value=value.replace(/[^\d.]/g,'')" @blur="nl = $event.target.value" placeholder="年龄" v-model="xglist.nl" clearable disabled>
                </el-input>
              </el-form-item>
            </div>
            <el-form-item label="原部门" prop="ybm" class="one-line" >
                <el-input v-model="xglist.ybm" clearable placeholder="原部门" disabled></el-input>
              </el-form-item>
            <div style="display:flex">
              <el-form-item label="原涉密等级" prop="ysmdj">
                <el-select v-model="xglist.ysmdj" placeholder="请选择涉密等级" disabled style="width: 100%;">
                  <el-option v-for="item in smdjxz" :label="item.smdjmc" :value="item.smdjmc" :key="item.smdjid">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="职务" prop="zw">
                <el-input v-model="xglist.zw" clearable placeholder="职务" disabled></el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="职级" prop="zj">
                <el-input v-model="xglist.zj" clearable placeholder="职级" disabled></el-input>
              </el-form-item>
              <el-form-item label="级别职称" prop="zc">
                <el-select v-model="xglist.zc" placeholder="请选择级别职称" disabled style="width: 100%;">
                  <el-option v-for="item in jbzcxz" :label="item.jbzcmc" :value="item.jbzcmc" :key="item.jbzcid">
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="原身份类型" prop="ysflx">
                <el-select v-model="xglist.ysflx" placeholder="请选择身份类型" disabled style="width: 100%;">
                  <el-option v-for="item in sflxxz" :label="item.sflxmc" :value="item.sflxmc" :key="item.sflxid">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="是否到公安机关出入境备案" prop="gajgcrjba">
                <el-radio-group v-model="xglist.gajgcrjba">
                  <el-radio label="是"></el-radio>
                  <el-radio label="否"></el-radio>
                </el-radio-group>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="是否委托管理" prop="sfwtgl">
                <el-radio-group v-model="xglist.sfwtgl">
                  <el-radio label="是"></el-radio>
                  <el-radio label="否"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="脱密期开始时间" prop="tmqkssj">
                <!-- <el-input v-model="xglist.sgsj" clearable></el-input> -->
                <el-date-picker v-model="xglist.tmqkssj" class="cd" clearable type="date" style="width:100%;" placeholder="选择日期" format="yyyy年MM月dd日" value-format="yyyy年MM月dd日" @change="tmkssj">
                </el-date-picker>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="脱密期结束时间" prop="tmqjssj">
                <!-- <el-input v-model="xglist.sgsj" clearable></el-input> -->
                <el-date-picker v-model="xglist.tmqjssj" class="cd" clearable type="date" style="width:100%;" placeholder="选择日期" format="yyyy年MM月dd日" value-format="yyyy年MM月dd日">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="手机号码" prop="sjhm">
                <el-input v-model="xglist.sjhm" clearable placeholder="手机号码" maxlength="11"></el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="离岗离职类型" prop="lzlglx">
                <el-select v-model="xglist.lzlglx" placeholder="请选择离岗离职类型" @change="lzlglxBlur" style="width: 100%;">
                  <el-option v-for="item in lzlglxxz" :label="item.lzlglxmc" :value="item.lzlglxmc" :key="item.lzlglxid"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="去向单位名称" prop="qxdwmc">
                <el-input v-model="xglist.qxdwmc" clearable placeholder="去向单位名称"></el-input>
              </el-form-item>
            </div>
            <el-form-item label="邮箱"  class="one-line">
                <el-input placeholder="邮箱" v-model="xglist.yx" clearable style="width: 100%;">
                </el-input>
              </el-form-item>
            <el-form-item label="备注" prop="bz" class="one-line-textarea">
              <el-input type="textarea" v-model="xglist.bz"></el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="warning" @click="xqdialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>
        <!-- 历史轨迹 dialog -->
        <el-dialog title="历史轨迹" :close-on-click-modal="false" :visible.sync="lsgjDialogVisible" width="46%" class="xg">
          <div style="padding-left: 10px;background: #EBEEF5;height: 40px;line-height: 40px;font-size: 16px;border-radius: 5px;margin-bottom: 5px;">
            <span>姓名：<span style="font-size: 14px;">{{ lsgjDialogData.xm }}</span></span>
            <span>身份证号码：<span style="font-size: 14px;">{{ lsgjDialogData.sfzhm }}</span></span>
          </div>
          <div style="max-height: 400px;overflow-y: scroll;padding: 10px;">
            <el-timeline>
              <el-timeline-item v-for="(activity, index) in lsgjDialogData.timelineList" :key="index" :icon="activity.icon" :color="activity.color" :size="'large'" :timestamp="activity.time">
                <div>
                  <p>{{ activity.ymngnmc }}</p>
                  <p>
                  <p>姓名：{{ activity.extraParams.xm }}</p>
                  <p>部门：{{ activity.extraParams.bm }}</p>
                  <p v-if="activity.extraParams">岗位名称：{{ Object.prototype.toString.call(activity.extraParams.gwmc)== "[object Array]"?activity.extraParams.gwmc.join(','):activity.extraParams.gwmc }}</p>
                  <p>涉密等级：{{ activity.extraParams.smdj }}</p>
                  <p v-if="activity.extraParams.lzlglx">离岗离职类型：{{ activity.extraParams.lzlglx }}</p>
                  <p v-if="activity.extraParams.tmqkssj">脱密期开始时间：{{ activity.extraParams.tmqkssj }}</p>
                  <p v-if="activity.extraParams.tmqjssj">脱密期结束时间：{{ activity.extraParams.tmqjssj }}</p>
                  <p>备注：{{ activity.extraParams.bz }}</p>
                  </p>
                  <p>操作人：{{ activity.xm }}</p>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button type="warning" @click="lsgjDialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>
        <!---->
      </div>
    </div>
  </div>

</template>
<script>
import { getDateTime } from "../../../utils/utils";
import {
  getsmdj,
  getjbzc,
  getsflx,
  getlzlglx
} from "../../../db/xzdb"
import {
  //内容管理初始化成员列表
  getLglz,
  //添加内容管理
  addLglz,
  //删除内容管理
  deleteLglz,
  updateLglz
} from "../../../db/lglzdb";
import {
  getlogin
} from "../../../db/loginyhdb";
import { getDoclglzMbPath } from "../../../utils/pathUtil";
import {
  getsmry1,
  getsmry2,
  deletesmry1,
  getSmryBySmryid
} from "../../../db/smrydb";
import {
  exportExcel
} from "../../../utils/exportExcel"; //excel导出工具

import { parseTrajectoryLogs } from '../../../utils/logUtils'

import {
  getUuid
} from "../../../utils/getUuid"; //获取uuid
import XLSX from "xlsx";
import {
  dateFormatNYRChinese
} from "../../../utils/moment"

export default {
  components: {},
  props: {},
  data () {
    var isMobileNumber = (rule, value, callback) => {
      if (!value) {
        return new Error('请输入电话号码')
      } else {
        const reg = /^((13[0-9])|(14[5-9])|(15([0-3]|[5-9]))|(16[6-7])|(17[1-8])|(18[0-9])|(19[1|3])|(19[5|6])|(19[8|9]))\d{8}$/
        const isPhone = reg.test(value)
        value = Number(value) //转换为数字
        if (typeof value === 'number' && !isNaN(value)) {
          //判断是否为数字
          value = value.toString() //转换成字符串
          if (value.length < 0 || value.length > 12 || !isPhone) {
            //判断是否为11位手机号
            callback(new Error('手机号格式:138xxxx8754'))
          } else {
            callback()
          }
        } else {
          callback(new Error('请输入电话号码'))
        }
      }
    }
    return {
      // 历史轨迹dialog显隐
      lsgjDialogVisible: false,
      // 历史轨迹dialog数据
      lsgjDialogData: {
        bmbh: '',
        zcbh: '',
        // 历史轨迹时间线数据
        timelineList: [],
      },
      gwmc: [],
      smdjxz: [],
      jbzcxz: [],
      sflxxz: [],
      lzlglxxz: [],
      smryList: [],
      xglist: {},
      updateItemOld: {},
      xgdialogVisible: false,
      xqdialogVisible: false,
      formInline: {

      },
      tjlist: {
        xm: '',
        sfzhm: '',
        xb: '',
        nl: '',
        ybm: '',
        ygwmc: '',
        ysmdj: '',
        zw: '',
        zj: '',
        zc: '',
        ysflx: '',
        gajgcrjba: '是',
        sfwtgl: '是',
        tmqkssj: '',
        tmqjssj: '',
        sjhm: '',
        lzlglx: '',
        qxdwmc: '',
        yx:'',
        bz: ''
      },
      page: 1,
      pageSize: 10,
      total: 0,
      selectlistRow: [], //列表的值
      dialogVisible: false, //添加弹窗状态
      //表单验证
      rules: {
        xm: [{
          required: true,
          message: '请输入姓名',
          trigger: ['blur', 'change'],
        },],
        sfzhm: [{
          required: true,
          message: '请输入身份证号码',
          trigger: 'blur'
        },],
        xb: [{
          required: true,
          message: '请选择性别',
          trigger: 'blur'
        },],
        nl: [{
          required: true,
          message: '请输入年龄',
          trigger: 'blur'
        },],
        ybm: [{
          required: true,
          message: '请输入原部门',
          trigger: 'blur'
        },],
        ygwmc: [{
          required: true,
          message: '请输入原岗位名称',
          trigger: 'blur'
        },],
        ysmdj: [{
          required: true,
          message: '请选择原涉密等级',
          trigger: 'blur'
        },],
          zw: [{
            required: true,
            message: '请输入职务',
            trigger: 'blur'
          },],
          zj: [{
            required: true,
            message: '请输入职级',
            trigger: 'blur'
          },],
          zc: [{
            required: true,
            message: '请选择级别职称',
            trigger: 'blur'
          },],
        ysflx: [{
          required: true,
          message: '请选择原身份类型',
          trigger: 'blur'
        },],
        gajgcrjba: [{
          required: true,
          message: '请选择是否到公安机关出入境备案',
          trigger: 'blur'
        },],
        sfwtgl: [{
          required: true,
          message: '请选择是否委托管理',
          trigger: 'blur'
        },],
        tmqkssj: [{
          required: true,
          message: '请选择脱密期开始时间',
          trigger: 'blur'
        },],
        tmqjssj: [{
          required: true,
          message: '请选择脱密期结束时间',
          trigger: 'blur'
        },],
        sjhm: [{
          required: true,
          message: '请输入手机号码',
          trigger: 'blur',
        }, {
          validator: isMobileNumber,
          trigger: 'blur'
        }],
        lzlglx: [{
          required: true,
          message: '请选择离岗离职类型',
          trigger: 'blur'
        },],
        // yx: [{
        //   required: true,
        //   message: '请输入邮箱',
        //   trigger: 'blur'
        // },],
        // qxdwmc: [{
        //   required: true,
        //   message: '请选择去向单位名称',
        //   trigger: 'blur'
        // },],
        // bz: [{
        // 	required: true,
        // 	message: '请输入备注',
        // 	trigger: 'blur'
        // },],
      },
      //导入
      dialogVisible_dr: false, //导入成员组弹窗状态
      dr_cyz_list: [], //待选择导入成员组列表
      multipleTable: [], //已选择导入成员组列表
      dwmc: '',
      year: '',
      yue: '',
      ri: '',
      Date: '',
      xh: [],
      dclist: [], dr_dialog: false,
      //数据导入方式
      sjdrfs: ''
    }
  },
  computed: {},
  mounted () {
    this.dwmc = getlogin()[0].dwmc
    let date = new Date()
    this.year = date.getFullYear() + '年'; //获取完整的年份(4位)
    this.yearwn = date.getFullYear(); //获取完整的年份(4位)
    this.yue = (date.getMonth() + 1) + '月'; //获取当前月份(0-11,0代表1月)
    this.ri = date.getDate() + '日'; //获取当前日(1-31)
    this.Date = this.year + this.yue + this.ri
    this.smdjxz = getsmdj()
    this.jbzcxz = getjbzc()
    this.sflxxz = getsflx()
    this.lzlglxxz = getlzlglx()
    //列表初始化
    this.smry()
    this.lglz()
    this.$refs.upload.addEventListener('change', e => { //绑定监听表格导入事件
      this.readExcel(e);
    })
  },
  methods: {
    /**
     * 获取轨迹日志
     * 通过涉密人员ID获取（日志文件中标记的ID为涉密人员ID，并非离岗离职ID）
    */
    getTrajectory (row) {
      console.log(row)
      let params = {
        // id: row.smryid,
        id: row.lglzid,
        xyybs: 'mk_smry',
      }
      parseTrajectoryLogs(params, resArr => {
        console.log('resArr', resArr)
        if (resArr.length <= 0) {
          this.$message.warning('暂无轨迹')
          return
        }
        // icon图标处理
        resArr.forEach(item => {
          switch (item.ymngnmc) {
            case '新增':
              item.icon = 'el-icon-plus'
              item.color = '#67C23A'
              break
            case '在用':
              item.icon = 'el-icon-circle-check'
              item.color = '#67C23A'
              break
            case '停用':
              item.icon = 'el-icon-remove-outline'
              item.color = '#E6A23C'
              break
            case '报废':
              item.icon = 'el-icon-circle-close'
              item.color = '#F56C6C'
              break
            case '借出':
              item.icon = 'el-icon-position'
              item.color = '#409EFF'
              break
            case '销毁':
              item.icon = 'el-icon-delete'
              item.color = '#F56C6C'
              break
            case '变更':
              item.icon = 'el-icon-remove-outline'
              item.color = '#E6A23C'
              break
            case '离岗离职':
              item.icon = 'el-icon-delete'
              item.color = '#F56C6C'
              break
          }
        })
        // // 通过人员ID获取人员信息（可能涉及到该涉密人员离岗离职后从涉密人员列表中删除的问题，这里应该从日志文件中进行解析）
        // let smryMess = getSmryBySmryid(row.smryid)
        // if(!smryMess) {
        //   this.$message.warning('未能获取到该人员的')
        //   return
        // }
        // 中间人员改名的问题（最上方显示的是离岗离职信息录入时该人员的名字，下方时间轴中显示的名字是该人员在入职后离岗离职前日志文件中记录的名字）
        this.lsgjDialogData.xm = row.xm
        this.lsgjDialogData.sfzhm = row.sfzhm
        this.lsgjDialogData.timelineList = resArr
        //
        this.lsgjDialogVisible = true
      })
    },
    Radio (val) {
      this.sjdrfs = val
      console.log("当前选中的值", val)
    },

    mbxzgb () {
      this.sjdrfs = ''
    },
    mbdc(){
      let filename = "离岗离职模板" + ".xlsx";

const { dialog } = require("electron").remote;

const FS = require("fs");

//弹窗title
let options = {
  title: "保存文件",
  defaultPath: filename,
};
console.log(dialog);
//导出文件夹选择弹窗
dialog.showSaveDialog(options, (result) => {
  console.log("result", result);
  if (result == null || result == "") {
    console.log("取消导出");
    return;
  }
  // 直接读取模板下载
  // FS.copyFileSync(getDocDmsxzhtjbMbPath(), result, FS.constants.COPYFILE_EXCL)
  FS.writeFileSync(result, FS.readFileSync(getDoclglzMbPath()));
  this.dr_dialog = false;
  this.$message("导出成功:" + result);
  this.dialogVisibleSjdc = false;
});
    },
    //导入
    chooseFile () {
      if (this.sjdrfs != '') {
        if (this.sjdrfs == 1) {
          this.$refs.upload.click()
        }
        else if (this.sjdrfs == 2) {
          let valArr = this.dclist
          valArr.forEach(function (item) {
            deleteLglz(item)
          })
          this.$refs.upload.click()
        }
      } else {
        this.$message.warning('请选择导入方式')
      }
    },
    //----成员组选择
    handleSelectionChange (val) {
      this.multipleTable = val
      console.log("选中：", this.multipleTable);
    },
    //---确定导入成员组
    drcy () {
      //遍历已选择导入的成员，进行格式化，然后添加到数据库
      for (var i in this.multipleTable) {
        var cy = {
          xm: this.multipleTable[i]["__EMPTY"],
          sfzhm: this.multipleTable[i]["__EMPTY_2"],
          xb: this.multipleTable[i]["__EMPTY_1"],
          nl: this.multipleTable[i]["__EMPTY_3"],
          ybm: this.multipleTable[i]["__EMPTY_4"],
          ysmdj: this.multipleTable[i]["__EMPTY_5"],
          zw: this.multipleTable[i]["__EMPTY_6"],
          zj: this.multipleTable[i]["__EMPTY_7"],
          zc: this.multipleTable[i]["__EMPTY_8"],
          ysflx: this.multipleTable[i]["__EMPTY_9"],
          gajgcrjba: this.multipleTable[i]["__EMPTY_10"],
         
          sfwtgl: this.multipleTable[i]["__EMPTY_11"],
          tmqkssj: this.multipleTable[i]["__EMPTY_12"],
          tmqjssj: this.multipleTable[i]["__EMPTY_13"],
          sjhm: this.multipleTable[i]["__EMPTY_14"],
          lzlglx: this.multipleTable[i]["__EMPTY_15"],
          qxdwmc: this.multipleTable[i]["__EMPTY_16"],
          bz: this.multipleTable[i]["__EMPTY_17"],
          lglzid: getUuid()
        }
        addLglz(cy)
      }
      this.dialogVisible_dr = false
      this.smry()
    },
    //----表格导入方法
    readExcel (e) {
      var that = this;
      const files = e.target.files;
      console.log("files", files);
      var vali = /\.(xls|xlsx)$/
      if (files.length <= 0) { //如果没有文件名
        return false;
      } else if (!vali.test(files[0].name.toLowerCase())) {
        this.$Message.error('上传格式不正确，请上传xls或者xlsx格式');
        return false;
      }
      const fileReader = new FileReader();
      fileReader.onload = (e) => {
        try {
          const data = e.target.result;
          const workdata = XLSX.read(data, {
            type: 'binary',
            cellDates: true,//设为true，将天数的时间戳转为时间格式
          });
          console.log("文件的内容：", workdata) // 文件的内容
          //查看打印能看到 workdata的sheetNames里是文件中每个表的有值的集合；
          const wsname = workdata.SheetNames[0]; //取第一张表
          console.log('wsname', wsname)
          const ws = XLSX.utils.sheet_to_json(workdata.Sheets[wsname]); //生成json表格内容
          console.log(ws); //自第二行开始的内容
          this.dialogVisible_dr = true
          this.dr_cyz_list = ws.slice(4)
          console.log("列表的值:", this.dr_cyz_list)
          // 加工excel读取业务类型为数组
          // this.dr_cyz_list.forEach(function(item) {
          // 	console.log(item[0]['业务类型'].splite(','))
          // })
          this.dr_cyz_list.forEach((item) => {
            console.log(item);
            // item['上岗时间'] = dateFormatNYRChinese(item['上岗时间'].getTime())
            item["__EMPTY_12"] = dateFormatNYRChinese(
              getDateTime(item["__EMPTY_12"])
            );
            item["__EMPTY_13"] = dateFormatNYRChinese(
              getDateTime(item["__EMPTY_13"])
            );
          });
          this.$refs.upload.value = ''; // 处理完成 清空表单值
          this.dr_dialog = false
        } catch (e) {
          return false;
        }
      };
      fileReader.readAsBinaryString(files[0]);
    },
    //导出
    exportList () {
      console.log("----导出涉密人员----")
      // console.log(this.selectlistRow);
      // if (this.selectlistRow.length > 0) {
      let filename = "涉密人员离岗离职汇总情况表" + getUuid() + ".xlsx"

      const {
        dialog
      } = require('electron').remote;
      //弹窗title
      let options = {
        title: "保存文件",
        defaultPath: filename,
      };
      console.log(dialog)
      //导出文件夹选择弹窗
      dialog.showSaveDialog(options, result => {
        console.log('result', result)
        if (result == null || result == "") {
          console.log("取消导出")
          return
        }
        let list = []
        list.push(["附件5-4"])
        list.push(["基础表4 涉密人员脱密期管理台帐"])
        let dwmc = "(填报单位名称、印章)" + this.dwmc
let year = "统计年度："+this.yearwn
let date = "填报时间："+this.Date
        list.push([dwmc, "", "", "", "", "", "", "", "", "", "", "", "", "","", "", "", "", ""])
        list.push([year, "",  "", "", "", "", "", "", "","",
          "", "", "", "", "",  "",date, ""
        ])
        //对导出的数据进行格式，js导出excel要求传入的参数格式为[["列1名称","列2名称","列3名称"],["列11","值21","值31"],["列12","值22","值32"]]
        list.push(["序号", "姓名", "性别", "身份证号", "年龄", "原部门",
          "原涉密等级", "职务", "职级", "级别职称", "原身份类型",
          "是否到公安机关出入境备案", "是否委托管理", "脱密期开始时间",
          "脱密期结束时间", "手机号码", "离职离岗类型", "去向单位名称","备注"
        ]) //确定列名

        for (var i in this.dclist) { //每一行的值
          let item = this.dclist[i] //{"name":"XXX","dw":"XXX","zw":"XXX"}

          console.log("导出值:", this.dclist);
          let column = [(parseInt(i) + 1), item["xm"], item["xb"], item["sfzhm"], item["nl"],
          item["ybm"], item["ysmdj"], item["zw"], item["zj"], item["zc"],
          item["ysflx"], item["gajgcrjba"], item["sfwtgl"], item["tmqkssj"],
          item["tmqjssj"],
          item["sjhm"], item["lzlglx"], item["qxdwmc"],item["bz"]
          ]
          list.push(column)
        }
        let merges = [{
          s: { //s为开始
            c: 0, //开始列
            r: 1 //开始取值范围
          },
          e: { //e结束
            c: 18, //结束列
            r: 1 //结束范围
          }
        },
        {
          s: { //s为开始
            c: 0, //开始列
            r: 2 //开始取值范围
          },
          e: { //e结束
            c: 1, //结束列
            r: 2 //结束范围
          }
        },
        {
          s: { //s为开始
            c: 0, //开始列
            r: 3 //开始取值范围
          },
          e: { //e结束
            c: 2, //结束列
            r: 3 //结束范围
          }
        },
        {
          s: { //s为开始
            c: 16, //开始列
            r: 3 //开始取值范围
          },
          e: { //e结束
            c: 18, //结束列
            r: 3 //结束范围
          }
        },
      
      ]
        let styles = {
          // 列样式
          cols: {
            // 作用sheet页索引（0开始）（-1全sheet页生效）
            scoped: -1,
            style: [
              { wpx: 100 },
              { wpx: 300 },
              { wpx: 60 },
              { wpx: 200 },
              { wpx: 60 },
              { wpx: 200 },
              { wpx: 100 },
              { wpx: 100 },
              { wpx: 100 },
              { wpx: 100 },
              { wpx: 100 },
              { wpx: 150 },
              { wpx: 100 },
              { wpx: 100 },
              { wpx: 100 },
              { wpx: 100 },
              { wpx: 150 },
              { wpx: 150 },
              { wpx: 300 },
              { wpx: 300 },
            ]
          },
          // 全局样式
          all: {
            alignment: {
              horizontal: 'center', // 水平居中
              vertical: 'center', // 垂直居中
              wrapText: true, // 文字换行
            },
            font: {
              sz: 11, // 字号
              name: '宋体', // 字体
              bold: true,
            },
            border: {  // 边框
              top: {
                style: 'thin'
              },
              bottom: {
                style: 'thin'
              },
              left: {
                style: 'thin'
              },
              right: {
                style: 'thin'
              }
            }
          },
          // 单元格样式
          cell: [
            {
              // 生效sheet页索引（值为 -1 时所有sheet页都生效）
              scoped: -1,
              // 索引
              index: 'A2',
              style: {
                font: {
                  name: '宋体',
                  sz: 16, // 字号
                  bold: true,
                },
                alignment: {
                  horizontal: 'center', // 水平居中
                  vertical: 'center' // 垂直居中
                }
              }
            }
          ]
        }
        // excel导出方法自定义配置
        let config = {
          // 忽略行索引(从1开始)
          rowIndexArrIgnore: [1]
        }
        exportExcel(result, list, merges, styles, config) //list 要求为二维数组
        this.$message('导出成功:' + result)
      })
    },
    //修改
    updataDialog (form) {
      this.$refs[form].validate((valid) => {
        if (valid) {
          // 删除旧的
          // deleteLglz(this.updateItemOld)
          // 插入新的
          updateLglz(this.xglist)
          // 刷新页面表格数据
          this.smry()
          // 关闭dialog
          this.$message.success('修改成功')
          this.xgdialogVisible = false
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    xqyl (row) {
      this.updateItemOld = JSON.parse(JSON.stringify(row))

      this.xglist = JSON.parse(JSON.stringify(row))
      // this.form1.ywlx = row.ywlx
      console.log('old', row)
      console.log("this.xglist.ywlx", this.xglist);
      this.xqdialogVisible = true
    },

    updateItem (row) {
      this.updateItemOld = JSON.parse(JSON.stringify(row))

      this.xglist = JSON.parse(JSON.stringify(row))
      // this.form1.ywlx = row.ywlx
      console.log('old', row)
      console.log("this.xglist.ywlx", this.xglist);
      this.xgdialogVisible = true
    },

    //查询
    onSubmit () {
      this.smry()
    },
    cz () {
      this.formInline = {}
    },

    returnSy () {
      this.$router.push("/tzglsy");
    },
    smry () {
      let params = {
        page: this.page,
        pageSize: this.pageSize
      }
      Object.assign(params, this.formInline)
      let resList = getLglz(params)
      console.log("params", params);
      this.smryList = resList.list
      this.dclist = resList.list_total
      this.dclist.forEach((item, label) => {
        this.xh.push(label + 1)
      })
      this.total = resList.total
    },
    //删除
    shanchu (id) {
      if (this.selectlistRow != '') {
        this.$confirm('是否继续删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let valArr = this.selectlistRow
          // console.log("....", val);
          valArr.forEach(function (item) {
            deleteLglz(item)
            // console.log("删除：", item);
            // console.log("删除：", item);
          })
          let params = valArr
          this.$message({
            message: '删除成功',
            type: 'success'
          });
          this.smry()
        }).catch(() => {
          this.$message('已取消删除')
        })
      } else {
        this.$message({
          message: '未选择删除记录，请选择下列列表',
          type: 'warning'
        });
      }
    },
    //添加
    showDialog () {
      this.resetForm()
      this.dialogVisible = true
    },
    //确定添加成员组
    submitTj (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          console.log('this.tjlist', this.tjlist)
          this.returnSy
          let params = {
            smryid: this.tjlist.smryid,
            xm: this.tjlist.xm,
            sfzhm: this.tjlist.sfzhm,
            xb: this.tjlist.xb,
            nl: this.tjlist.nl,
            ybm: this.tjlist.ybm,
            ygwmc: this.tjlist.ygwmc,
            ysmdj: this.tjlist.ysmdj,
            zw: this.tjlist.zw,
            zj: this.tjlist.zj,
            zc: this.tjlist.zc,
            ysflx: this.tjlist.ysflx,
            gajgcrjba: this.tjlist.gajgcrjba,
            sfwtgl: this.tjlist.sfwtgl,
            tmqkssj: this.tjlist.tmqkssj,
            tmqjssj: this.tjlist.tmqjssj,
            sjhm: this.tjlist.sjhm,
            lzlglx: this.tjlist.lzlglx,
            qxdwmc: this.tjlist.qxdwmc,
            yx: this.tjlist.yx,
            bz: this.tjlist.bz,
            lglzid: getUuid()
          }
          deletesmry1(params)
          let a = addLglz(params)
          if (a) {
            this.$message({
            message: '添加成功',
            type: 'success'
          });
          }else{
            this.$message.error('人员已经离岗');
          }
          this.dialogVisible = false
          this.$message({
            message: '添加成功',
            type: 'success'
          });
          this.resetForm()
          this.smry()
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    deleteTkglBtn () {

    },
    selectRow (val) {
      console.log(val);
      this.selectlistRow = val;
    },
    //列表分页--跳转页数
    handleCurrentChange (val) {
      this.page = val
      this.smry()
    },
    //列表分页--更改每页显示个数
    handleSizeChange (val) {
      this.page = 1
      this.pageSize = val
      this.smry()
    },
    //添加重置
    resetForm () {
      this.tjlist.xm = ''
      this.tjlist.sfzhm = ''
      this.tjlist.xb = ''
      this.tjlist.nl = ''
      this.tjlist.ybm = ''
      this.tjlist.ygwmc = ''
      this.tjlist.ysmdj = ''
      this.tjlist.zw = ''
      this.tjlist.zj = ''
      this.tjlist.zc = ''
      this.tjlist.ysflx = ''
      this.tjlist.gajgcrj = ''
      this.tjlist.sfwtgl = ''
      this.tjlist.tmqkssj = ''
      this.tjlist.tmqjssj = ''
      this.tjlist.sjhm = ''
      this.tjlist.lzlglx = ''
      this.tjlist.qxdwmc = ''
      this.tjlist.bz = ''
    },
    handleClose (done) {
      this.resetForm()
      this.dialogVisible = false
    },
    // 弹框关闭触发
    close (formName) {
      // 清空表单校验，避免再次进来会出现上次校验的记录
      this.$refs[formName].resetFields();
    },
    close1 (form) {
      // 清空表单校验，避免再次进来会出现上次校验的记录
      this.$refs[form].resetFields();
    },

    querySearch (queryString, cb) {
      var restaurants = this.restaurants;
      console.log("restaurants", restaurants);
      var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
      console.log("results", results);
      // 调用 callback 返回建议列表的数据
      cb(results);
      console.log("cb(results.dwmc)", results);
    },
    createFilter (queryString) {
      return (restaurant) => {
        return (restaurant.xm.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
      };
    },
    lglz () {
      let resList = getsmry1()
      this.restaurants = resList;
      console.log("this.restaurants", this.restaurants);
      console.log(resList)
    },
    handleSelect (item) {
      console.log(item);
      // console.log(item);
      // 不能用
      // this.dwxxShow = JSON.parse(JSON.stringify(item))
      this.tjlist.sfzhm = item.sfzhm
      this.tjlist.xb = item.xb
      this.tjlist.nl = item.nl
      this.tjlist.ybm = item.bm
      this.tjlist.ygwmc = item.gwmc
      this.tjlist.ysmdj = item.smdj
      this.tjlist.gwqdyj = item.gwqdyj
      this.tjlist.zgxl = item.zgxl
      this.tjlist.zw = item.zw
      this.tjlist.zj = item.zj
      this.tjlist.zc = item.zc
      this.tjlist.gwdyjb = item.gwdyjb
      this.tjlist.ysflx = item.sflx
      this.tjlist.yrxs = item.yrxs
      this.tjlist.sfsc = item.sfsc
      this.tjlist.crjdjba = item.crjdjba
      this.tjlist.tybgcrjzj = item.tybgcrjzj
      this.tjlist.sgsj = item.sgsj
      this.tjlist.sjhm = item.lxdh
      // 增加涉密人员ID，配合轨迹使用
      this.tjlist.smryid = item.smryid
      this.tjlist.yx = item.yx
      this.tjlist.bz = item.yx
      // console.log("this.dwxxShow",this.dwxxShow);
      console.log("item", item);
    },
    dwxxByDwmc (xm) {
      //单位名称imput失去焦点，通过单位名称获取单位信息
      console.log('单位名称input失去焦点:' + xm);
      const smryDataBase = getsmry2(xm);
      console.log('通过单位名称获取单位信息:');
      console.log(smryDataBase);
      if (smryDataBase === undefined) {
        /**
         * 去掉实际操作表单数据dwxxShow里的单位id
         * 因为这里显示的是数据库里的数据，但是当用户改变了该表单里的单位名称数据后，此时应该把实际操作的单位id dwidPage置为空
         */
        this.dwidPage = '';
        return;
      }
      /**
       * 有旧的数据
       * 直接给到页面显示用的数据dwxxShow
       */
      // this.dwxxShow = smryDataBase;
      //把用户实际操作的表单信息中的dwid设置为数据库里的数据
      // this.dwidPage = smryDataBase.dwid;
    },

    lzlglxBlur () {
      console.log(this.tjlist.lzlglx);
      if (this.tjlist.lzlglx == '本单位其他非涉密岗') {
        this.tjlist.qxdwmc = this.dwmc
      }
    },
    tmkssj (val) {
      console.log('val', val, val.replace(/[\u4e00-\u9fa5]/g, '/'))
      // 格式化时间
      val = val.replace(/[\u4e00-\u9fa5]/g, '/')
      console.log(new Date(val))
      let tmqjssjDate = new Date(val)
      if (this.tjlist.ysmdj == '核心' || this.tjlist.ysmdj == 1) {
        tmqjssjDate.setFullYear(tmqjssjDate.getFullYear() + 3)
      } else if (this.tjlist.ysmdj == '重要' || this.tjlist.ysmdj == 2) {
        tmqjssjDate.setFullYear(tmqjssjDate.getFullYear() + 2)
      } else if (this.tjlist.ysmdj == '一般' || this.tjlist.ysmdj == 3) {
        tmqjssjDate.setFullYear(tmqjssjDate.getFullYear() + 1)
      }
      console.log('tmqjssjDate', tmqjssjDate)
      this.tjlist.tmqjssj = dateFormatNYRChinese(tmqjssjDate)
      // return
      // // console.log(val.slice(4,11));
      // let yr = val.slice(4, 11)
      // let nian
      // console.log(nian);
      // if (this.tjlist.ysmdj == '核心') {
      // 	nian = val.slice(0, 4) * 1 + 3
      // } else if (this.tjlist.ysmdj == '重要') {
      // 	nian = val.slice(0, 4) * 1 + 2
      // } else if (this.tjlist.ysmdj == '一般') {
      // 	nian = val.slice(0, 4) * 1 + 1
      // }
      // this.tjlist.tmqjssj = nian + yr
    },
    tmkssj1 (val) {
      // console.log(val.slice(4,11));
      let yr = val.slice(4, 11)
      let nian
      console.log(nian);
      if (this.xglist.ysmdj == '核心') {
        nian = val.slice(0, 4) * 1 + 3
      } else if (this.xglist.ysmdj == '重要') {
        nian = val.slice(0, 4) * 1 + 2
      } else if (this.xglist.ysmdj == '一般') {
        nian = val.slice(0, 4) * 1 + 1
      }
      this.xglist.tmqjssj = nian + yr
    }
  },
  watch: {

  }
}
</script>

<style scoped>
.bg_con {
  width: 100%;
}

.dabg {
  /* margin-top: 10px; */
  box-shadow: 0px 2px 12px 0px rgba(9, 70, 255, 0.13);
  border-radius: 8px;
  /* padding: 20px 20px; */
  width: 100%;
}

.daochu {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

/* /deep/.el-radio {
	display: block;
	margin-top: 10px;
	margin-bottom: 10px;
} */

/deep/.mhcx .el-form-item {
  margin-top: 5px;
  margin-bottom: 5px;
}

.xmlb-title {
  line-height: 60px;
  width: 100%;
  padding-left: 10px;
  height: 60px;
  background: url(../../assets/background/bg-02.png) no-repeat left;
  background-size: 100% 100%;
  text-indent: 10px;
  /* margin: 0 20px; */
  color: #0646bf;
  font-weight: 700;
}

.fhsy {
  display: inline-block;
  width: 120px;
  margin-top: 10px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-left: 30px;
  padding-top: 4px;
  float: right;
  background: url(../../assets/icons/zzjg_icon1.png) no-repeat center;
  background-size: 100% 100%;
}

.item_button {
  height: 100%;
  float: left;
  padding-left: 10px;
  line-height: 50px;
}

.select_wrap {
  /* //padding: 5px; */

  .select_wrap_content {
    float: left;
    width: 100%;
    line-height: 50px;
    /* // padding-left: 20px; */
    /* // padding-right: 20px; */
    height: 100%;
    background: rgba(255, 255, 255, 0.7);

    .item_label {
      padding-left: 10px;
      height: 100%;
      float: left;
      line-height: 50px;
      font-size: 1em;
    }
  }
}

.mhcx1 {
  margin-top: 0px;
}

.widthw {
  width: 6vw;
}

.cd {
  width: 164px;
}

/deep/.el-dialog {
  margin-top: 6vh !important;
}

/deep/.inline-input .el-input--medium .el-input__inner {
  /* width: 164px; */
  height: 28px;
  font-size: 12px;
}

/deep/.el-select .el-select__tags > span {
  display: flex !important;
  flex-wrap: wrap;
}

.bz {
  height: 72px !important;
}

.dialog-footer {
  display: block;
  margin-top: 10px;
}

/deep/.el-dialog__body .el-form > div > div {
  /* width: auto; */
  max-width: 100%;
}


/deep/.el-dialog__body .el-form > div .el-form-item__label {
  width: 194px !important;
}
</style>
