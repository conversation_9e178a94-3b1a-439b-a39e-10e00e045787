export default [
  {
    name: 'tzglls',
    path: '/tzglls',
    component: () => import('../tzgltabs.vue'),
    meta: {
      name: '历史台账管理',
      icon: 'aaa',
      hidden: false,
      showHeaderMenu: true,
      showAsideMenu: true,
      menuList: ['/lsSmgwgl', '/lsSmry', '/lsRyxz', '/lsGwbg', '/lsLglz' ,'/lsCsgl' , '/lsCsbg', '/lsSmjsj', '/lsFsmjsj', '/lsSmydccjz', '/lsSmbgzdhsb', '/lsFsmbgzdhsb', '/lsSmwlsb', '/lsFmwlsb', '/lsAqcp', '/lsSmzttz', '/lsDmzrr', '/lsDmsq', '/lsGjmmsx', '/lsDmpx', '/lsDmqkndtj', '/lsBmqsxqdqk', '/lsZfcgxmqk']
    }
  },
  {
    name: 'lsSmgwgl',
    path: '/lsSmgwgl',
    component: () => import('../lsSmgwgl.vue'),
    meta: {
      name: '涉密岗位历史台账',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'lsSmry',
    path: '/lsSmry',
    component: () => import('../lsSmry.vue'),
    meta: {
      name: '在岗涉密人员历史台账',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'lsRyxz',
    path: '/lsRyxz',
    component: () => import('../lsRyxz.vue'),
    meta: {
      name: '人员新增汇总历史台账',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'lsGwbg',
    path: '/lsGwbg',
    component: () => import('../lsGwbg.vue'),
    meta: {
      name: '涉密岗位变更历史台账',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'lsLglz',
    path: '/lsLglz',
    component: () => import('../lsLglz.vue'),
    meta: {
      name: '离岗离职历史台账',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'lsCsgl',
    path: '/lsCsgl',
    component: () => import('../lsCsgl.vue'),
    meta: {
      name: '场所管理历史台账',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'lsCsbg',
    path: '/lsCsbg',
    component: () => import('../lsCsbg.vue'),
    meta: {
      name: '场所变更历史台账',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'lsSmjsj',
    path: '/lsSmjsj',
    component: () => import('../lsSmjsj.vue'),
    meta: {
      name: '涉密计算机历史台账',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'lsFsmjsj',
    path: '/lsFsmjsj',
    component: () => import('../lsFsmjsj.vue'),
    meta: {
      name: '非涉密计算机历史台账',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'lsSmydccjz',
    path: '/lsSmydccjz',
    component: () => import('../lsSmydccjz.vue'),
    meta: {
      name: '涉密移动存储介质历史台账',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'lsSmbgzdhsb',
    path: '/lsSmbgzdhsb',
    component: () => import('../lsSmbgzdhsb.vue'),
    meta: {
      name: '涉密办公自动化设备历史台账',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'lsFsmbgzdhsb',
    path: '/lsFsmbgzdhsb',
    component: () => import('../lsFsmbgzdhsb.vue'),
    meta: {
      name: '非涉密办公自动化设备历史台账',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'lsSmwlsb',
    path: '/lsSmwlsb',
    component: () => import('../lsSmwlsb.vue'),
    meta: {
      name: '涉密网络设备历史台账',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'lsFmwlsb',
    path: '/lsFmwlsb',
    component: () => import('../lsFmwlsb.vue'),
    meta: {
      name: '非密网络设备历史台账',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'lsAqcp',
    path: '/lsAqcp',
    component: () => import('../lsAqcp.vue'),
    meta: {
      name: '安全产品历史台账',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'lsSmzttz',
    path: '/lsSmzttz',
    component: () => import('../lsSmzttz.vue'),
    meta: {
      name: '载体管理历史台账',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  // {
  //   name: 'pxqd',
  //   path: '/pxqd',
  //   component: () => import('../pxqd.vue'),
  //   meta: {
  //     name: '培训清单',
  //     icon: 'aaa',
  //     hidden: true,
  //     showHeaderMenu: true,
  //     showAsideMenu: true
  //   }
  // },
  {
    name: 'lsDmzrr',
    path: '/lsDmzrr',
    component: () => import('../lsDmzrr.vue'),
    meta: {
      name: '定密责任人历史台账',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'lsDmsq',
    path: '/lsDmsq',
    component: () => import('../lsDmsq.vue'),
    meta: {
      name: '定密授权历史台账',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'lsGjmmsx',
    path: '/lsGjmmsx',
    component: () => import('../lsGjmmsx.vue'),
    meta: {
      name: '国家秘密事项历史台账',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'lsDmpx',
    path: '/lsDmpx',
    component: () => import('../lsDmpx.vue'),
    meta: {
      name: '定密培训历史台账',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'lsDmqkndtj',
    path: '/lsDmqkndtj',
    component: () => import('../lsDmqkndtj.vue'),
    meta: {
      name: '定密情况年度统计历史台账',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'lsBmqsxqdqk',
    path: '/lsBmqsxqdqk',
    component: () => import('../lsBmqsxqdqk.vue'),
    meta: {
      name: '不明确事项确定情况历史台账',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'lsZfcgxmqk',
    path: '/lsZfcgxmqk',
    component: () => import('../lsZfcgxmqk.vue'),
    meta: {
      name: '政府采购项目情况历史台账',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
]