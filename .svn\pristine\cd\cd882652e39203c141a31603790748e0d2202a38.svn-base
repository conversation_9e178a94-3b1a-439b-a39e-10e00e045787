import db from "./adapter/zczpAdaptor";

//保密制度-----------------------------------保密制度初始化列表********
export const getRyxz = (params) => {
  console.log(params.sgsj);
  let sgsj
  if (params.sgsj != null && new Date(params.sgsj[0]) != 'Invalid Date') {
    var Y0 = params.sgsj[0].getFullYear() + '年';
    var M0 = (params.sgsj[0].getMonth() + 1 < 10 ? '0' + (params.sgsj[0].getMonth() + 1) : params.sgsj[0].getMonth() + 1) + '月';
    var D0 = (params.sgsj[0].getDate() < 10 ? '0' + (params.sgsj[0].getDate()) : params.sgsj[0].getDate()) + '日';
    var Y1 = params.sgsj[1].getFullYear() + '年';
    var M1 = (params.sgsj[1].getMonth() + 1 < 10 ? '0' + (params.sgsj[1].getMonth() + 1) : params.sgsj[1].getMonth() + 1) + '月';
    var D1 = (params.sgsj[1].getDate() < 10 ? '0' + (params.sgsj[1].getDate()) : params.sgsj[1].getDate()) + '日';
    sgsj = [Y0 + M0 + D0, Y1 + M1 + D1];
  }else{
    sgsj = params.sgsj
  }
  
  // console.log(Y+M+D);
  let page = params.page;
  let pagesize = params.pageSize;

  let list_total = db
    .get("Smry_list")
    .sortBy("cjsj")
    .filter(function (item) {
      //没有时间
      if(item.zgzt == 1){
        if (sgsj === undefined || sgsj == null) {
          return item;
        } else if (sgsj) {
          if (item.sgsj) {
            console.log(item.sgsj >= sgsj[0] && item.sgsj <= sgsj[1]);
            if (item.sgsj >= sgsj[0] && item.sgsj <= sgsj[1]) {
              console.log(1);
              return item;
            }
          }
        }
      }
      
    })
    .cloneDeep()
    .value();

  // 手动分页
  console.log(pagesize * (page - 1), pagesize * (page - 1) + pagesize);
  let pageList = list_total.slice(
    pagesize * (page - 1),
    pagesize * (page - 1) + pagesize
  );

  let resList = {
    list: pageList,
    list_total: list_total,
    total: list_total.length,
  };
  console.log("保密制度", resList);
  return resList;
};
//保密制度-----------------------------------保密制度添加成员********
export const addRyxz = (params) => {
  let sjc = new Date().getTime();
  params.cjsj = sjc;
  params.gxsj = sjc;
  db.read().get("Smry_list").push(params).write();
  db.read().get("Ryxz_list").push(params).write();
};
export const jxsfzhm = (params) => {
  let sfzhm = params.sfzhm;
  let message = 0;
  let pd = db
    .read()
    .get("Smry_list")
    .find({ sfzhm: sfzhm })
    .cloneDeep()
    .value();
  console.log(pd);
  if (pd != undefined) {
    message = 1
  }
  return message
}
//保密制度-----------------------------------保密制度删除成员********
export const deleteRyxz = (params) => {
  db.read().get("Ryxz_list").remove(params).write();
};
