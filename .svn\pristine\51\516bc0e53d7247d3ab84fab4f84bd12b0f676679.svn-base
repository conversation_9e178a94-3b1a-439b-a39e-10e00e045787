/**
 * 确定系统标识，再执行系统数据迁移
 * 迁移逻辑分为固定路径和动态路径
 * 动态路径作用于windows，跟随系统安装路径
 * 固定路径作用域linux，主要由于linux国产化适配权限问题导致，故只能放在有权限的目录下才能保证迁移正常
*/
var OSEnum = ['Win32', 'linux or mac', 'Linux aarch64']
var environmentEnum = ['development', 'production']

import { dataMigration } from './dataMigrationUtil'
import { dataMigrationArrch64 } from './dataMigrationUtilArrch64'

export function getOS() {
    console.log('dataMigrationCheckUtils.js OSEnum', OSEnum, navigator.platform)
    if (OSEnum.indexOf(navigator.platform) != -1) {
      return navigator.platform
    }
    throw new Error('未确定系统标识['+navigator.platform+']')
}

export function getEnvironment() {
    console.log('准备获取当前运行环境', process.env.NODE_ENV)
    if (process.env.NODE_ENV === environmentEnum[0]) {
      return environmentEnum[0]
    }
    return environmentEnum[1]
}

export function checkMigrationFun() {
    let os = getOS()
    if(getEnvironment() == environmentEnum[0]) {
        // 开发环境锁定为windows环境，这里直接执行windows的迁移逻辑
        dataMigration()
    } else {
        if(os == OSEnum[0]) {
            // windows生产环境
            dataMigration()
        } else {
            // linux生产环境
            dataMigrationArrch64()
        }
    }
}
