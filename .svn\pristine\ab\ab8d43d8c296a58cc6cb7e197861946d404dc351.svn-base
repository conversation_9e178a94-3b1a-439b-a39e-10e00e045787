import db from "./adapter/zczpAdaptor";

//保密制度-----------------------------------保密制度初始化列表********
export const getCsbg = (params) => {
  let page = params.page;
  let pagesize = params.pageSize;
  let csmc = params.csmc;
  let sqrq = params.sqrq;
  let list_total = db
    .get("Csbg_list")
    .sortBy("cjsj")
    .filter(function (item) {
      // 1、试卷名称和创建时间都没有
      if (
        (csmc === undefined || csmc == "") &&
        (sqrq === undefined || sqrq == null)
      ) {
        return item;
        console.log("全都没有", item);
      }
      // 2、试卷名称有，创建时间没有
      else if (csmc && (sqrq === undefined || sqrq == null)) {
        if (item.csmc) {
          if (item.csmc.indexOf(csmc) != -1) {
            console.log("ccc", item);
            return item;
          }
        } else {
          console.log("item.csmc", item.csmc);
        }
      }
      // 3、试卷名称没有，创建时间有
      else if ((csmc === undefined || csmc == "") && sqrq) {
        if (item.sqrq) {
          if (item.sqrq >= sqrq[0] && item.sqrq <= sqrq[1]) {
            return item;
          }
        } else {
          console.log("item.sqrq", item.sqrq);
        }
      }
      // 4、试卷名称有，创建时间有
      else if (csmc && sqrq) {
        if (item.csmc && item.sqrq) {
          if (
            item.csmc.indexOf(csmc) != -1 &&
            item.sqrq >= sqrq[0] &&
            item.sqrq <= sqrq[1]
          ) {
            return item;
          }
        } else {
          console.log("item.csmc", item.csmc, "item.sqrq", item.sqrq);
        }
      }
    })
    .cloneDeep()
    .value();
  
  // 手动分页
  console.log(pagesize * (page - 1), pagesize * (page - 1) + pagesize);
  let pageList = list_total.slice(
    pagesize * (page - 1),
    pagesize * (page - 1) + pagesize
  );

  let resList = {
    list: pageList,
    list_total: list_total,
    total: list_total.length,
  };
  console.log("保密制度", resList);
  return resList;
};
//保密制度-----------------------------------保密制度添加成员********
export const addCsbg = (params) => {
  let sjc = new Date().getTime();
  params.cjsj = sjc;
  params.gxsj = sjc;
  db.read().get("Csbg_list").push(params).write();
};
//保密制度-----------------------------------保密制度删除成员********
export const deleteCsbg = (params) => {
  db.read().get("Csbg_list").remove(params).write();
};

export const updataCsbg = (params) => {
  // 数据校验
  // 校验ID
  let csbgid = params.csbgid;
  console.log("csbgid", csbgid);
  if (!csbgid || csbgid == "") {
    return;
  }
  params.gxsj = new Date().getTime();
  // 全字段更新方法(传进来的字段都会更新)
  db.read().get("Csbg_list").find({ csbgid: csbgid }).assign(params).write();
  // // 更新部分字段方法
  // db.read().get('Bmzd_list').find({csbgid:csbgid}).assign({
  // 	zdmc: params.zdmc,
  // 	...
  // }).write()
};

export const getCsgl = () => {
  let list_total = db
    .get("Csgl_list")
    .sortBy("cjsj")
    .filter(function (item) {
      return item;
    })
    .cloneDeep()
    .value();

  console.log("场所管理", list_total);
  return list_total;
};

//场所管理-----------------------------------场所管理********
export const addCsgl = (params) => {
  db.read().get("Csgl_list").push(params).write();
};
//场所管理-----------------------------------场所管理********
export const updataCsgl = (params) => {
  // 数据校验
  // 校验ID
  let csglid = params.csglid;
  console.log("csglid", csglid);
  if (!csglid || csglid == "") {
    return;
  }
  params.gxsj = new Date().getTime();
  params.bgls = undefined
  // 全字段更新方法(传进来的字段都会更新)
  db.read().get("Csgl_list").find({ csglid: csglid }).assign(params).write();
  // // 更新部分字段方法
  // db.read().get('Bmzd_list').find({csglid:csglid}).assign({
  // 	zdmc: params.zdmc,
  // 	...
  // }).write()
};

export const getSmryList = (params) => {
  let smry = db.get("Smry_list").cloneDeep().value();
  console.log(smry);
  return smry;
};