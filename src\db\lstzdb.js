import db from './adapter/zczpHistoryAdaptor'
// 历史台账-涉密岗位管理
export const getSmgwgl = (params) => {
	let page = params.page
	let pagesize = params.pageSize
	let list_total = db.get('lsSmgw_list').sortBy('cjsj').filter(function (item) {
		return item
	}).cloneDeep().value()
	// list_total.forEach((item,lable) =>{
	// 	let bm = db.read().get("Smry_list").filter({ bm: item.bm }).cloneDeep().value()
	// 	bm.forEach(item1 =>{
	// 		if (item.gwmc == item1.gwmc) {
	// 			rs.push(item1)
	// 		}
	// 	})
	// 	item.rs = rs.length
	// 	rs = []
	// })
	// 手动分页
	console.log(pagesize * (page - 1), pagesize * (page - 1) + pagesize)
	let pageList = list_total.slice(pagesize * (page - 1), pagesize * (page - 1) + pagesize)

	let resList = {
		"list": pageList,
		"list_total": list_total,
		"total": list_total.length
	}
	console.log('涉密岗位管理', resList)
	return resList
}
// 历史台账-在岗涉密人员历史台账
export const getsmry = (params) => {
	let page = params.page;
	let pagesize = params.pageSize;
	let list_total = db.get("lsSmry_list").sortBy("cjsj").filter(function (item) {
		return item;
	}).cloneDeep().value();
	// 手动分页
	console.log(pagesize * (page - 1), pagesize * (page - 1) + pagesize);
	let pageList = list_total.slice(
		pagesize * (page - 1),
		pagesize * (page - 1) + pagesize
	);
	let resList = {
		list: pageList,
		list_total: list_total,
		total: list_total.length,
	};
	return resList;
};
// 历史台账-新增汇总历史台账
export const getRyxz = (params) => {
	let sgsj
	if (params.sgsj != null && new Date(params.sgsj[0]) != 'Invalid Date') {
		var Y0 = params.sgsj[0].getFullYear() + '年';
		var M0 = (params.sgsj[0].getMonth() + 1 < 10 ? '0' + (params.sgsj[0].getMonth() + 1) : params.sgsj[0].getMonth() + 1) + '月';
		var D0 = (params.sgsj[0].getDate() < 10 ? '0' + (params.sgsj[0].getDate()) : params.sgsj[0].getDate()) + '日';
		var Y1 = params.sgsj[1].getFullYear() + '年';
		var M1 = (params.sgsj[1].getMonth() + 1 < 10 ? '0' + (params.sgsj[1].getMonth() + 1) : params.sgsj[1].getMonth() + 1) + '月';
		var D1 = (params.sgsj[1].getDate() < 10 ? '0' + (params.sgsj[1].getDate()) : params.sgsj[1].getDate()) + '日';
		sgsj = [Y0 + M0 + D0, Y1 + M1 + D1];
	} else {
		sgsj = params.sgsj
	}
	let page = params.page;
	let pagesize = params.pageSize;

	let list_total = db
		.get("lsryxzhz_list")
		.sortBy("cjsj")
		.filter(function (item) {
			//没有时间
			if (sgsj === undefined || sgsj == null) {
				return item;
			} else if (sgsj) {
				if (item.sgsj) {
					console.log(item.sgsj >= sgsj[0] && item.sgsj <= sgsj[1]);
					if (item.sgsj >= sgsj[0] && item.sgsj <= sgsj[1]) {
						console.log(1);
						return item;
					}
				}
			}
		})
		.cloneDeep()
		.value();

	// 手动分页
	console.log(pagesize * (page - 1), pagesize * (page - 1) + pagesize);
	let pageList = list_total.slice(
		pagesize * (page - 1),
		pagesize * (page - 1) + pagesize
	);

	let resList = {
		list: pageList,
		list_total: list_total,
		total: list_total.length,
	};
	console.log("保密制度", resList);
	return resList;
};
// 历史台账-涉密岗位变更历史台账
export const getGwbg = (params) => {
	let page = params.page;
	let pagesize = params.pageSize;
	let sgsj = params.sgsj;
	let list_total = db
		.get("lsrymjbg_list")
		.sortBy("cjsj")
		.filter(function (item) {
			//没有时间
			if (sgsj === undefined || sgsj == null) {
				return item;
				console.log("全都没有", item);
			} else if (sgsj) {
				if (item.sgsj) {
					if (
						new Date(item.sgsj) >= new Date(sgsj[0]) &&
						new Date(item.sgsj) <= new Date(sgsj[1])
					) {
						return item;
					}
				}
			}
		})
		.cloneDeep()
		.value();


	// 手动分页
	console.log(pagesize * (page - 1), pagesize * (page - 1) + pagesize);
	let pageList = list_total.slice(
		pagesize * (page - 1),
		pagesize * (page - 1) + pagesize
	);

	let resList = {
		list: pageList,
		list_total: list_total,
		total: list_total.length,
	};
	console.log("保密制度", resList);
	return resList;
};
// 历史台账-离岗离职历史台账 
export const getLglz = (params) => {
	let page = params.page;
	let pagesize = params.pageSize;
	let list_total = db
		.get("lslzlg_list")
		.sortBy("cjsj")
		.filter(function (item) {
			return item;
		})
		.cloneDeep()
		.value();

	// 手动分页
	console.log(pagesize * (page - 1), pagesize * (page - 1) + pagesize);
	let pageList = list_total.slice(
		pagesize * (page - 1),
		pagesize * (page - 1) + pagesize
	);

	let resList = {
		list: pageList,
		list_total: list_total,
		total: list_total.length,
	};
	console.log("保密制度", resList);
	return resList;
};
// 历史台账-场所管理历史台账 
export const getCsgl = (params) => {
	let page = params.page;
	let pagesize = params.pageSize;
	let list_total = db
	  .get("lssmcs_list")
	  .sortBy("cjsj")
	  .filter(function (item) {
		return item;
	  })
	  .cloneDeep()
	  .value();
   
	// 手动分页
	let pageList = list_total.slice(
	  pagesize * (page - 1),
	  pagesize * (page - 1) + pagesize
	);
  
	let resList = {
	  list: pageList,
	  list_total: list_total,
	  total: list_total.length,
	};
	return resList;
  };
  // 历史台账-场所变更历史台账 
  export const getCsbg = (params) => {
	let page = params.page;
	let pagesize = params.pageSize;
	let list_total = db
	  .get("lscsbg_list")
	  .sortBy("cjsj")
	  .filter(function (item) {
		return item;
	  })
	  .cloneDeep()
	  .value();
	
	// 手动分页
	let pageList = list_total.slice(
	  pagesize * (page - 1),
	  pagesize * (page - 1) + pagesize
	);
  
	let resList = {
	  list: pageList,
	  list_total: list_total,
	  total: list_total.length,
	};
	return resList;
  };
  // 历史台账-涉密计算机历史台账 
  export const getSmjsj = (params) => {
	let page = params.page;
	let pagesize = params.pageSize;
	let list_total = db
	  .get("lssmjsj_list")
	  .sortBy("cjsj")
	  .filter(function (item) {
		return item;
	  })
	  .cloneDeep()
	  .value();
  
	// 手动分页
	let pageList = list_total.slice(
	  pagesize * (page - 1),
	  pagesize * (page - 1) + pagesize
	);
  
	let resList = {
	  list: pageList,
	  list_total: list_total,
	  total: list_total.length,
	};
	console.log("保密制度", resList);
	return resList;
  };
  // 历史台账-非涉密计算机历史台账 
  export const getFsmjsj = (params) => {
	let page = params.page;
	let pagesize = params.pageSize;
	let list_total = db
	  .get("lsfsmjsj_list")
	  .sortBy("cjsj")
	  .filter(function (item) {
		return item;
	  })
	  .cloneDeep()
	  .value();
	// 手动分页
	let pageList = list_total.slice(
	  pagesize * (page - 1),
	  pagesize * (page - 1) + pagesize
	);
	let resList = {
	  list: pageList,
	  list_total: list_total,
	  total: list_total.length,
	};
	return resList;
  };
  // 历史台账-涉密移动存储介质历史台账 
  export const getSmydccjz = (params) => {
	let page = params.page;
	let pagesize = params.pageSize;
	let list_total = db
	  .get("lsSmydccjz_list")
	  .sortBy("cjsj")
	  .filter(function (item) {
		return item;
	  })
	  .cloneDeep()
	  .value();
	// 手动分页
	let pageList = list_total.slice(
	  pagesize * (page - 1),
	  pagesize * (page - 1) + pagesize
	);
	let resList = {
	  list: pageList,
	  list_total: list_total,
	  total: list_total.length,
	};
	return resList;
  };
  // 历史台账-涉密办公自动化设备历史台账 
  export const getSmbgzdhsb = (params) => {
	let page = params.page;
	let pagesize = params.pageSize;
	let list_total = db
	  .get("lsBgzdhsb_list")
	  .sortBy("cjsj")
	  .filter(function (item) {
		return item;
	  })
	  .cloneDeep()
	  .value();
	// 手动分页
	let pageList = list_total.slice(
	  pagesize * (page - 1),
	  pagesize * (page - 1) + pagesize
	);
  
	let resList = {
	  list: pageList,
	  list_total: list_total,
	  total: list_total.length,
	};
	return resList;
  };
  // 历史台账-非涉密办公自动化设备历史台账 
  export const getFsmbgzdhsb = (params) => {
	let page = params.page;
	let pagesize = params.pageSize;
	let list_total = db
	  .get("lsfBgzdhsb_list")
	  .sortBy("cjsj")
	  .filter(function (item) {
		return item;
	  })
	  .cloneDeep()
	  .value();
	// 手动分页
	let pageList = list_total.slice(
	  pagesize * (page - 1),
	  pagesize * (page - 1) + pagesize
	);
	let resList = {
	  list: pageList,
	  list_total: list_total,
	  total: list_total.length,
	};
	return resList;
  };
  // 历史台账-涉密网络设备历史台账 
  export const getSmwlsb = (params) => {
	let page = params.page;
	let pagesize = params.pageSize;
	let list_total = db
	  .get("lssmwlsb_list")
	  .sortBy("cjsj")
	  .filter(function (item) {
		return item;
	  })
	  .cloneDeep()
	  .value();
	// 手动分页
	let pageList = list_total.slice(
	  pagesize * (page - 1),
	  pagesize * (page - 1) + pagesize
	);
	let resList = {
	  list: pageList,
	  list_total: list_total,
	  total: list_total.length,
	};
	return resList;
  };
  // 历史台账-非涉密网络设备历史台账 
  export const getFmwlsb = (params) => {
	let page = params.page;
	let pagesize = params.pageSize;
	let list_total = db
	  .get("lsfsmwlsb_list")
	  .sortBy("cjsj")
	  .filter(function (item) {
		return item;
	  })
	  .cloneDeep()
	  .value();
	// 手动分页
	let pageList = list_total.slice(
	  pagesize * (page - 1),
	  pagesize * (page - 1) + pagesize
	);
  
	let resList = {
	  list: pageList,
	  list_total: list_total,
	  total: list_total.length,
	};
	return resList;
  };
  // 历史台账-安全产品历史台账 
  export const getAqcp = (params) => {
	let page = params.page;
	let pagesize = params.pageSize;
	let list_total = db
	  .get("lsaqcp_list")
	  .sortBy("cjsj")
	  .filter(function (item) {
		return item;
	  })
	  .cloneDeep()
	  .value();
	// 手动分页
	let pageList = list_total.slice(
	  pagesize * (page - 1),
	  pagesize * (page - 1) + pagesize
	);
  
	let resList = {
	  list: pageList,
	  list_total: list_total,
	  total: list_total.length,
	};
	return resList;
  };
  // 历史台账-涉密载体历史台账 
  export const getSmzttz = (params) => {
	let page = params.page;
	let pagesize = params.pageSize;
	let list_total = db
	  .get("lssmzt_list")
	  .sortBy("cjsj")
	  .filter(function (item) {
		return item;
	  })
	  .cloneDeep()
	  .value();
  
	// 手动分页
	let pageList = list_total.slice(
	  pagesize * (page - 1),
	  pagesize * (page - 1) + pagesize
	);
  
	let resList = {
	  list: pageList,
	  list_total: list_total,
	  total: list_total.length,
	};
	return resList;
  };
  // 历史台账-定密责任人历史台账 
  export const getdmzrr = (params) => {
	let page = params.page
	let pagesize = params.pageSize
	let list_total = db.get('lsdmzrrt_list').sortBy('cjsj').filter(function(item) {
			return item
	}).cloneDeep().value()
	
	// 手动分页
	let pageList = list_total.slice(pagesize * (page - 1), pagesize * (page - 1) + pagesize)
	
	let resList = {
		"list": pageList,
		"list_total": list_total,
		"total": list_total.length
	}
	return resList
}
// 历史台账-定密授权历史台账 
export const getdmsq = (params) => {
	let page = params.page;
	let pagesize = params.pageSize;
	let list_total = db.get("lsdmsq_list").sortBy("cjsj").filter(function(item) {
			return item;
	}).cloneDeep().value();

	// 手动分页
	let pageList = list_total.slice(pagesize * (page - 1),pagesize * (page - 1) + pagesize);

	let resList = {
		list: pageList,
		list_total: list_total,
		total: list_total.length,
	};
	return resList;
};
// 历史台账-国家秘密事项历史台账 
export const getGjmmsx = (params) => {
	let page = params.page;
	let pagesize = params.pageSize;
	let nd = params.nd;
	let gjmmsxmc = params.gjmmsxmc;
	let mj = params.mj;
	let cxnd = params.nd;
	let list_total = db
	  .get("lsgjmmsx_list")
	  .sortBy("cjsj")
	  .filter(function (item) {
		if (nd == item.nd) {
		  if (
			(gjmmsxmc === undefined || gjmmsxmc == "") &&
			(mj === undefined || mj == "")
		  ) {
			console.log("全都没有", item);
			return item;
		  } else if (gjmmsxmc && (mj === undefined || mj == "")) {
			if (item.gjmmsxmc) {
			  if (item.gjmmsxmc.indexOf(gjmmsxmc) != -1) {
				console.log("定密事项内容", item);
				return item;
			  }
			} else {
			  console.log("item.gjmmsxmc", item.gjmmsxmc);
			}
		  } else if ((gjmmsxmc === undefined || gjmmsxmc == "") && mj) {
			if (item.mj) {
			  if (item.mj.indexOf(mj) != -1) {
				console.log("密级", item);
				return item;
			  }
			} else {
			  console.log("item.mj", item.mj);
			}
		  } else if (gjmmsxmc && mj) {
			if (item.gjmmsxmc && mj) {
			  if (
				item.gjmmsxmc.indexOf(gjmmsxmc) != -1 &&
				item.mj.indexOf(mj) != -1
			  ) {
				console.log("定密事项内容和密级", item);
				return item;
			  }
			} else {
			  console.log("item.gjmmsxmc", item.gjmmsxmc, "item.mj", item.mj);
			}
		  }
		}
	  })
	  .cloneDeep()
	  .value();
  
	// 手动分页
	console.log(pagesize * (page - 1), pagesize * (page - 1) + pagesize);
	let pageList = list_total.slice(
	  pagesize * (page - 1),
	  pagesize * (page - 1) + pagesize
	);
  
	let resList = {
	  list: pageList,
	  list_total: list_total,
	  total: list_total.length,
	};
	console.log("国家秘密事项", resList);
	return resList;
  };
  // 历史台账-定密培训历史台账 
  export const getdmpx = (params) => {
	let page = params.page;
	let pagesize = params.pageSize;
	let list_total = db.get("lsdmpx_list").sortBy("cjsj").filter(function(item) {
			return item;
	}).cloneDeep().value();

	// 手动分页
	let pageList = list_total.slice(pagesize * (page - 1),pagesize * (page - 1) + pagesize);

	let resList = {
		list: pageList,
		list_total: list_total,
		total: list_total.length,
	};
	return resList;
};
// 历史台账-定密情况年度统计历史台账 
export const getDmqkndtj = (params) => {
	let page = params.page;
	let pagesize = params.pageSize;
	let list_total = db
	  .get("lsdmqkndtj_list")
	  .sortBy("cjsj")
	  .filter(function (item) {
		return item;
		console.log("全都没有", item);
	  })
	  .cloneDeep()
	  .value();
  
	// 手动分页
	let pageList = list_total.slice(
	  pagesize * (page - 1),
	  pagesize * (page - 1) + pagesize
	);
  
	let resList = {
	  list: pageList,
	  list_total: list_total,
	  total: list_total.length,
	};
	return resList;
  };
  // 历史台账-不明确事项确定情况历史台账 
  export const getDmqsxqdqk = (params) => {
	let page = params.page;
	let pagesize = params.pageSize;
	let list_total = db
	  .get("lsbmqsxqdqk_list")
	  .sortBy("cjsj")
	  .filter(function (item) {
		// 1、bm型号，类型，密级，日期都没有
		// if ((bmbh === undefined || bmbh == "") && (lx === undefined || lx == "") && (mj === undefined || mj == '') && (qyrq === undefined || qyrq == '' ||qyrq == null)) {
		return item;
		console.log("全都没有", item);
	  })
	  .cloneDeep()
	  .value();
  
	// 手动分页
	let pageList = list_total.slice(
	  pagesize * (page - 1),
	  pagesize * (page - 1) + pagesize
	);
  
	let resList = {
	  list: pageList,
	  list_total: list_total,
	  total: list_total.length,
	};
	return resList;
  };
  // 历史台账-政府采购项目情况历史台账 
  export const getZfcgxmqk = (params) => {
	let page = params.page;
	let pagesize = params.pageSize;
	let list_total = db
	  .get("lszfcgxmqk_list")
	  .sortBy("cjsj")
	  .filter(function (item) {
		return item;
	  })
	  .cloneDeep()
	  .value();
  
	// 手动分页
	let pageList = list_total.slice(
	  pagesize * (page - 1),
	  pagesize * (page - 1) + pagesize
	);
  
	let resList = {
	  list: pageList,
	  list_total: list_total,
	  total: list_total.length,
	};
	return resList;
  };