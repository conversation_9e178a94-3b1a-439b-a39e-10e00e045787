import db from './adapter/zczpAdaptor'

// 获取所有的保密制度
export const getAllBmzd = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('Bmzd_list').filter(function (item) {
			return new Date(item.cjsj).getFullYear() == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('Bmzd_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 涉密岗位管理列表获取
export const getSmgwglList = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('Smgwgl_list').filter(function (item) {
			return new Date(item.cjsj).getFullYear() == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('Smgwgl_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 在岗涉密人员列表获取
export const getZgsmrylList = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('Smry_list').filter(function (item) {
			if (item.zgzt == 1) {
				return new Date(item.cjsj).getFullYear() == nowYear
			}

		}).cloneDeep().value()
	} else {
		resList = db.get('Smry_list').filter(function (item) {
			if (item.zgzt == 1) {
				return item
			}

		}).cloneDeep().value()
	}
	return resList
}
// 人员新增汇总列表获取
export const getRyxzhzList = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('Ryxz_list').filter(function (item) {
			return new Date(item.cjsj).getFullYear() == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('Ryxz_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 岗位变更列表数据
export const getGwbgList = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('Gwbg_list').filter(function (item) {
			return new Date(item.cjsj).getFullYear() == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('Gwbg_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 离岗离职列表数据
export const getLglzList = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('Lglz_list').filter(function (item) {
			return new Date(item.cjsj).getFullYear() == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('Lglz_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 教育培训列表数据
export const getJypxList = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('Pxqd_list').filter(function (item) {
			return new Date(item.cjsj).getFullYear() == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('Pxqd_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 场所管理
export const getCsglList = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('Csgl_list').filter(function (item) {
			return new Date(item.cjsj).getFullYear() == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('Csgl_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 场所变更
export const getCsbgList = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('Csbg_list').filter(function (item) {
			return new Date(item.cjsj).getFullYear() == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('Csbg_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 涉密计算机台账
export const getSmjsjList = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('Smjsj_list').filter(function (item) {
			return new Date(item.cjsj).getFullYear() == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('Smjsj_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 非涉密计算机台账
export const getFsmjsjList = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('Fsmjsj_list').filter(function (item) {
			return new Date(item.cjsj).getFullYear() == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('Fsmjsj_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 涉密移动存储介质台账
export const getSmydccjzList = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('Smydccjz_list').filter(function (item) {
			return new Date(item.cjsj).getFullYear() == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('Smydccjz_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 涉密办公自动化设备台账
export const getSmbgzdhsbList = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('Smbgzdhsb_list').filter(function (item) {
			return new Date(item.cjsj).getFullYear() == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('Smbgzdhsb_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 非密办公自动化设备台账
export const getFsmbgzdhsbList = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('Fsmbgzdhsb_list').filter(function (item) {
			return new Date(item.cjsj).getFullYear() == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('Fsmbgzdhsb_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 涉密网络设备台账
export const getSmwlsbList = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('Smwlsb_list').filter(function (item) {
			return new Date(item.cjsj).getFullYear() == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('Smwlsb_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 非密网络设备台账
export const getFmwlsbList = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('Fmwlsb_list').filter(function (item) {
			return new Date(item.cjsj).getFullYear() == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('Fmwlsb_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 安全产品台账
export const getAqcpList = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('Aqcp_list').filter(function (item) {
			return new Date(item.cjsj).getFullYear() == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('Aqcp_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 涉密载体台账
export const getSmzttzList = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('Smzttz_list').filter(function (item) {
			return new Date(item.cjsj).getFullYear() == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('Smzttz_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 定密责任人
export const getDmzrrYear = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('dmzrr_list').filter(function (item) {
			return new Date(item.cjsj).getFullYear() == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('dmzrr_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 定密授权
export const getDmsqYear = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('dmsq_list').filter(function (item) {
			return new Date(item.cjsj).getFullYear() == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('dmsq_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 国家秘密事项
export const getGjmmsxYear = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('Gjmmsx_list').filter(function (item) {
			return new Date(item.cjsj).getFullYear() == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('Gjmmsx_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 定密培训
export const getDmpxYear = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('dmpx_list').filter(function (item) {
			return new Date(item.cjsj).getFullYear() == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('dmpx_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 定密情况年度统计
export const getDmqkndtjYear = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('dmqkndtj_list').filter(function (item) {
			return new Date(item.cjsj).getFullYear() == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('dmqkndtj_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 不明确事项确定情况
export const getBmqsxqdqkYear = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('dmqsxqdqk_list').filter(function (item) {
			return new Date(item.cjsj).getFullYear() == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('dmqsxqdqk_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 涉密政府采购项目情况
export const getZfcgxmqkYear = (nowYear) => {
	let resList
	if (nowYear != undefined) {
		resList = db.get('zfcgxmqk_list').filter(function (item) {
			return new Date(item.cjsj).getFullYear() == nowYear
		}).cloneDeep().value()
	} else {
		resList = db.get('zfcgxmqk_list').filter(function (item) {
			return item
		}).cloneDeep().value()
	}
	return resList
}
// 自查自评数据获取
export const getZczpRiskDatas = (jcjdid) => {
	let nowYear = new Date()
	const resList = db.get('scrw_list').filter(function (item) {
		if (item.jcjdid == jcjdid && new Date(item.gxsj).getFullYear() == nowYear.getFullYear()) {
			return item
		}
	}).cloneDeep().value()
	return resList
}

// 获取单位信息
export const getDwxxDatas = () => {
	const resList = db.get('dwxx_List').cloneDeep().value()
	return resList
}