import db from './adapter/zczpAdaptor'
// 日志工具类
import { writeTrajectoryLog } from '../utils/logUtils'

//保密制度-----------------------------------保密制度初始化列表********
export const getSmbgzdhsb = (params) => {
  let page = params.page
  let pagesize = params.pageSize
  //
  let bmbh = params.bmbh
  let zrr = params.zrr
  let sybm = params.sybm
  let lx = params.lx
  let mj = params.mj
  let qyrq = params.qyrq
  //
  let list_total = db
    .get('Smbgzdhsb_list')
    .sortBy('cjsj')
    .filter(function (item) {
      return item
    })
    .cloneDeep()
    .value()

  // 模糊查询过滤
  if (bmbh) {
    list_total = list_total.filter((item) => {
      if (item.bmbh.toString().indexOf(bmbh) != -1) {
        return item
      }
    })
  }
  if (zrr) {
    list_total = list_total.filter((item) => {
      if (item.zrr.indexOf(zrr) != -1) {
        return item
      }
    })
  }
  if (sybm) {
    list_total = list_total.filter((item) => {
      if (item.sybm.indexOf(sybm.join('/')) != -1) {
        return item
      }
    })
  }
  if (lx) {
    list_total = list_total.filter((item) => {
      if (item.lx == lx) {
        return item
      }
    })
  }
  if (mj) {
    list_total = list_total.filter((item) => {
      if (item.mj == mj) {
        return item
      }
    })
  }
  if (qyrq) {
    list_total = list_total.filter((item) => {
      if (item.qyrq >= qyrq[0] && item.qyrq <= qyrq[1]) {
        return item
      }
    })
  }

  // 手动分页
  console.log(pagesize * (page - 1), pagesize * (page - 1) + pagesize)
  let pageList = list_total.slice(
    pagesize * (page - 1),
    pagesize * (page - 1) + pagesize
  )

  let resList = {
    list: pageList,
    list_total: list_total,
    total: list_total.length,
  }
  console.log('保密制度', resList)
  return resList
}
//保密制度-----------------------------------保密制度添加成员********
export const addSmbgzdhsb = (params) => {
  let sjc = new Date().getTime()
  params.cjsj = sjc
  params.gxsj = sjc
  db.read().get('Smbgzdhsb_list').push(params).write()
  // 添加日志
  let paramsLog = {
    xyybs: 'mk_smbgzdhsb',
    id: params.smbgzghsbid,
    ymngnmc: '新增',
    extraParams: {
      zrr: params.zrr,
    },
  }
  writeTrajectoryLog(paramsLog)
}
//添加校验
export const jyaddSmbgzdhsb = (params) => {
  let bmbh = params.bmbh
  let zcbh = params.zcbh
  let zjxlh = params.zjxlh
  let message = 0
  let pdbmbh = db
    .read()
    .get('Smbgzdhsb_list')
    .find({ bmbh: bmbh })
    .cloneDeep()
    .value()
  let pdzcbh = db
    .read()
    .get('Smbgzdhsb_list')
    .find({ zcbh: zcbh })
    .cloneDeep()
    .value()
  let pdzjxlh = db
    .read()
    .get('Smbgzdhsb_list')
    .find({ zjxlh: zjxlh })
    .cloneDeep()
    .value()
  if (pdbmbh) {
    console.log(1)
    message = 1
  } else if (pdzcbh) {
    console.log(2)
    message = 2
  } else if (pdzjxlh) {
    message = 3
  }
  return message
}
//修改校验
export const jyreviseSmbgzdhbmbh = (params) => {
  let bmbh = params.bmbh
  let message = 0
  let pdbmbh = db
    .read()
    .get('Smbgzdhsb_list')
    .find({ bmbh: bmbh })
    .cloneDeep()
    .value()
  if (pdbmbh) {
    console.log(1)
    message = 1
  }
  return message
}
export const jyreviseSmbgzdhzcbh = (params) => {
  let zcbh = params.zcbh
  let message = 0
  let pdzcbh = db
    .read()
    .get('Smbgzdhsb_list')
    .find({ zcbh: zcbh })
    .cloneDeep()
    .value()
  if (pdzcbh) {
    console.log(1)
    message = 2
  }
  return message
}
export const jyreviseSmbgzdhzjxlh = (params) => {
  let zjxlh = params.zjxlh
  let message = 0
  let pdzjxlh = db
    .read()
    .get('Smbgzdhsb_list')
    .find({ zjxlh: zjxlh })
    .cloneDeep()
    .value()
  if (pdzjxlh) {
    console.log(1)
    message = 3
  }
  return message
}
//修改
export const reviseSmbgzdh = (params) => {
  let sybm = params.sybm.join('/')
  params.sybm = sybm
  let glbm = params.glbm.join('/')
  params.glbm = glbm
  let smbgzghsbid = params.smbgzghsbid
  console.log('smbgzghsbid', smbgzghsbid)
  if (!smbgzghsbid || smbgzghsbid == '') {
    return
  }
  params.gxsj = new Date().getTime()
  // 全字段更新方法(传进来的字段都会更新)
  db.read()
    .get('Smbgzdhsb_list')
    .find({ smbgzghsbid: smbgzghsbid })
    .assign(params)
    .write()
}
//保密制度-----------------------------------保密制度删除成员********
export const deleteSmbgzdhsb = (params) => {
  db.read().get('Smbgzdhsb_list').remove(params).write()
}
//修改
export const xgSmbgzdhsb = (params) => {
  console.log(params)
  params.select.forEach((item) => {
    // 使用情况旧值（使用情况改变才插入轨迹日志）
    let itemSyqkOld = item.syqk
    db.get('Smbgzdhsb_list')
      .find({ zcbh: item.zcbh })
      .assign({
        syqk: params.syzt,
      })
      .write()
    // 添加日志
    if (itemSyqkOld != params.syzt) {
      let paramsLog = {
        xyybs: 'mk_smbgzdhsb',
        id: item.smbgzghsbid,
        ymngnmc: params.syzt,
        extraParams: {
          zrr: item.zrr,
        },
      }
      writeTrajectoryLog(paramsLog)
    }
  })
}
export const getPpxh = () => {
  let ppxh = db.get('Smbgzdhsb_list').cloneDeep().value()
  console.log()
  return ppxh
}
