import db from "./adapter/zczpAdaptor";

// 获取所有dm培训信息(上报数据自选模式专用)
export const getAllDmpxZxms = () => {
	return db.get('dmpx_list').cloneDeep().value()
}

//定密培训-----------------------------------定密培训初始化列表********
export const getdmpx = (params) => {
	let page = params.page;
	let pagesize = params.pageSize;
  let nd = params.nd
  
	let list_total = db.get("dmpx_list").sortBy("cjsj").filter(function(item) {
			return item;
	}).cloneDeep().value();

  // 模糊查询过滤
  if (nd) {
    list_total = list_total.filter((item) => {
      if (item.nd == nd) {
        return item
      }
    })
  }

	// 手动分页
	console.log(pagesize * (page - 1), pagesize * (page - 1) + pagesize);
	let pageList = list_total.slice(pagesize * (page - 1),pagesize * (page - 1) + pagesize);

	let resList = {
		list: pageList,
		list_total: list_total,
		total: list_total.length,
	};
	console.log("定密培训", resList);
	return resList;
};
//定密培训-----------------------------------定密培训添加成员********
export const adddmpx = (params) => {
	let sjc = new Date().getTime();
	params.cjsj = sjc;
	params.gxsj = sjc;
	db.read().get("dmpx_list").push(params).write();
};
//定密培训-----------------------------------定密培训删除成员********
export const deletedmpx = (params) => {
	db.read().get("dmpx_list").remove(params).write();
};

//修改
export const revisedmpx = (params) => {
	let dmpxid = params.dmpxid;
	console.log("dmpxid", dmpxid);
	if (!dmpxid || dmpxid == "") {
		return;
	}
	params.gxsj = new Date().getTime();
	// 全字段更新方法(传进来的字段都会更新)
	db.read().get("dmpx_list").find({
		dmpxid: dmpxid
	}).assign(params).write();
};
