<template>
  <div class="bg_con" style="height: calc(100% - 38px);">
    <div style="width: 100%; position: relative; overflow: hidden; height: 100%;">

      <div class="dabg" style="height: 100%;">
        <div class="content" style="height: 100%;">
          <div class="table" style="height: 100%;">
            <!-- -----------------操作区域--------------------------- -->
            <div class="mhcx">
              <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline" style="float:left">
                <el-form-item label="" style="font-weight: 700;">
                    <!-- <el-input v-model="formInline.tzsj" clearable placeholder="台账时间" class="widths">
                    </el-input> -->
									<el-select v-model="formInline.tzsj" class="widths" placeholder="台账时间">
										<el-option
										v-for="item in yearSelect"
										:key="item.value"
										:label="item.label"
										:value="item.value">
										</el-option>
									</el-select>
                </el-form-item>
                <el-form-item style="font-weight: 700;">
                  <el-input v-model="formInline.nd" clearable placeholder="年度" oninput="value=value.replace(/[^\d.]/g,'')" @blur="nd=$event.target.value" class="widths">
                  </el-input>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
                </el-form-item>
              </el-form>
              <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline" style="float:right">
                <!-- <el-form-item style="float: right;">
                  <el-button type="danger" size="medium" @click="shanchu" icon="el-icon-delete-solid">
                    删除
                  </el-button>
                </el-form-item> -->
                <el-form-item style="float: right;">
                  <el-button type="primary" size="medium" icon="el-icon-download" @click="exportList">
                    导出
                  </el-button>
                </el-form-item>
                <!-- <el-form-item style="float: right;">
                  <input type="file" ref="upload"
                    style="display: none;position: absolute;top:10px; right:0;opacity:0; cursor: pointer;height: 32px;width: 56px;z-index:1;"
                    accept=".xls,.xlsx">
                  <el-button type="primary" icon="el-icon-upload2" size="medium" @click="dr_dialog = true">
                    导入
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <el-button type="success" size="medium" @click="dialogVisible = true" icon="el-icon-plus">
                    新增
                  </el-button>
                </el-form-item> -->
              </el-form>
            </div>


            <!-- -----------------审查组人员列表--------------------------- -->
            <div class="table_content_padding" style="height: 100%;">
              <div class="table_content" style="height: 100%;">
                <el-table :data="bmqsxqdqlList" border @selection-change="selectRow"
                  :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
                  style="width: 100%;border:1px solid #EBEEF5;" height="calc(100% - 34px - 41px - 3px)" stripe>
                  <el-table-column type="selection" width="55" align="center"> </el-table-column>
                  <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                  <!-- <el-table-column prop="mc" label="名称"></el-table-column> -->
                  <el-table-column prop="nd" label="年度" width="60" align="center"></el-table-column>
                  <el-table-column prop="gjmmzshj" label="国家秘密数量"></el-table-column>
                  <el-table-column prop="dmzrrhj" label="定密责任人数量"></el-table-column>
                  <el-table-column prop="dmsqxzshj" label="定密授权数量"></el-table-column>
                  <el-table-column prop="gjmmsxylbxzzsylbs" label="国家秘密事项数量"></el-table-column>
                  <el-table-column prop="dmzdsxczs" label="定密制度数量"></el-table-column>
                  <el-table-column prop="pxcs" label="定密培训次数"></el-table-column>
                  <el-table-column prop="gzmmqds" label="工作秘密数量"></el-table-column>
                  <el-table-column prop="tzsj" label="台账时间"></el-table-column>
                  <el-table-column prop="" label="操作" width="120">
                    <template slot-scope="scoped">
                      <el-button size="medium" type="text" @click="xqyl(scoped.row)">详情
                      </el-button>
                      <!-- <el-button size="medium" type="text" @click="updateItem(scoped.row)">修改
                      </el-button> -->
                    </template>
                  </el-table-column>

                </el-table>

                <!-- -------------------------分页区域---------------------------- -->
                <div style="border: 1px solid #ebeef5;">
                  <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange"
                    :pager-count="5" :current-page="page" :page-sizes="[5, 10, 20, 30]" :page-size="pageSize"
                    layout="total, prev, pager, sizes,next, jumper" :total="total">
                  </el-pagination>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 模板下载 -->
        <el-dialog title="开始导入" class="scbg-dialog" width="600px" @close="mbxzgb" :visible.sync="dr_dialog" show-close>
          <div style="padding: 20px;">
            <div class="daochu">
              <div>一、请点击“导出模板”，并参照模板填写信息。</div>
              <el-button type="primary" size="mini" @click="mbdc">
                模板导出
              </el-button>
            </div>
            <div class="daochu">
              <div>二、数据导入方式：</div>
              <el-radio-group v-model="sjdrfs">
                <el-radio label="追加（导入时已有的记录信息不变，只添加新的记录）"></el-radio>
                <el-radio label="覆盖（导入时更新已有的记录信息，并添加新的记录"></el-radio>
              </el-radio-group>
            </div>
            <div class="daochu">
              <div>三、将按模板填写的文件，导入到系统中。</div>
              <el-button type="primary" size="mini" @click="chooseFile">
                上传导入
              </el-button>
            </div>
          </div>
        </el-dialog>

        <!-- -----------------涉密人员离岗离职信息-弹窗--------------------------- -->
        <el-dialog title="定密情况年度统计信息" :close-on-click-modal="false" :visible.sync="dialogVisible" width="50%" class="xg"
          :before-close="handleClose" @close="close('formName')">
          <el-form ref="formName" :model="tjlist" :rules="rules" size="mini">
            <el-tabs v-model="activeName" @tab-click="handleClick">
              <el-tab-pane label="国家秘密统计情况" name="gjmmtjqk">
                <div style="margin-bottom:10px">一，国家秘密统计情况</div>
                <el-form-item label="年度" label-width="40px">
                  <el-input placeholder="年度" v-model="tjlist.nd" clearable style="width:100px" disabled></el-input>
                </el-form-item>
                <div style="display:flex;justify-content: space-between;">
                  <div>
                    <div class="input-tou">原始国家秘密数</div>

                    <el-form-item label="绝密" label-width="51px" prop="ysgjmmsjm">
                      <el-input placeholder="绝密" v-model="tjlist.ysgjmmsjm" clearable style="width:170px"
                        type="number" @blur="jszs(1)"></el-input>
                    </el-form-item>
                    <el-form-item label="机密" label-width="51px" prop="ysgjmmsjjm">
                      <el-input placeholder="机密" v-model="tjlist.ysgjmmsjjm" clearable style="width:170px"
                        type="number" @blur="jszs(1)"></el-input>
                    </el-form-item>
                    <el-form-item label="秘密" label-width="51px" prop="ysgjmmsmm">
                      <el-input placeholder="秘密" v-model="tjlist.ysgjmmsmm" clearable style="width:170px"
                        type="number" @blur="jszs(1)"></el-input>
                    </el-form-item>
                    <el-form-item label="合计" label-width="51px">
                      <el-input placeholder="合计" v-model="tjlist.ysgjmmshj" clearable style="width:170px"
                        type="number" disabled></el-input>
                    </el-form-item>
                  </div>
                  <div>
                    <div class="input-tou">派生国家秘密数</div>

                    <el-form-item label="绝密" label-width="51px" prop="psgjmmsjm">
                      <el-input placeholder="绝密" v-model="tjlist.psgjmmsjm" clearable style="width:170px"
                        type="number" @blur="jszs(1)"></el-input>
                    </el-form-item>
                    <el-form-item label="机密" label-width="51px" prop="psgjmmsjjm">
                      <el-input placeholder="机密" v-model="tjlist.psgjmmsjjm" clearable style="width:170px"
                        type="number" @blur="jszs(1)"></el-input>
                    </el-form-item>
                    <el-form-item label="秘密" label-width="51px" prop="psgjmmsmm">
                      <el-input placeholder="秘密" v-model="tjlist.psgjmmsmm" clearable style="width:170px"
                        type="number" @blur="jszs(1)"></el-input>
                    </el-form-item>
                    <el-form-item label="合计" label-width="51px">
                      <el-input placeholder="合计" v-model="tjlist.psgjmmshj" clearable style="width:170px"
                        type="number" disabled></el-input>
                    </el-form-item>

                  </div>
                  <div>
                    <div class="input-tou">国家秘密总数</div>

                    <el-form-item label="绝密" label-width="51px">
                      <el-input placeholder="绝密" v-model="tjlist.gjmmzsjm" clearable style="width:170px"
                        type="number" disabled></el-input>
                    </el-form-item>
                    <el-form-item label="机密" label-width="51px">
                      <el-input placeholder="机密" v-model="tjlist.gjmmzsjjm" clearable style="width:170px"
                        type="number" disabled></el-input>
                    </el-form-item>
                    <el-form-item label="秘密" label-width="51px">
                      <el-input placeholder="秘密" v-model="tjlist.gjmmzsmm" clearable style="width:170px"
                        type="number" disabled></el-input>
                    </el-form-item>
                    <el-form-item label="合计" label-width="51px">
                      <el-input placeholder="合计" v-model="tjlist.gjmmzshj" clearable style="width:170px"
                        type="number" disabled></el-input>
                    </el-form-item>

                  </div>

                </div>

                <div style="display:flex">
                  <el-form-item label="变更数" label-width="65px" prop="bgs">
                    <el-input placeholder="变更数" v-model="tjlist.bgs" clearable style="width:150px"
                      type="number"></el-input>
                  </el-form-item>
                  <el-form-item label="解密数" label-width="65px" style="margin-left:40px" prop="jms">
                    <el-input placeholder="解密数" v-model="tjlist.jms" clearable style="width:150px"
                      type="number"></el-input>
                  </el-form-item>
                </div>

              </el-tab-pane>
              <el-tab-pane label="定密责任人数" name="dmzrrs">
                <div style="display:flex;justify-content: space-between;align-items: center;">
                  <div style="margin-bottom:10px">二，定密责任人数</div>
                  <el-button size="small" @click="$router.push('/dmzrr')">查看详情</el-button>
                </div>
                <div style="display:flex;justify-content: space-between;">
                  <div>
                    <div class="input-tou">法定定密责任人数</div>

                    <el-form-item label="绝、机、秘" label-width="93px" prop="fddmzrrjjm">
                      <el-input placeholder="绝、机、秘" v-model="tjlist.fddmzrrjjm" clearable style="width:130px"
                        type="number" @blur="jszs(2)">
                      </el-input>
                    </el-form-item>
                    <el-form-item label="机、秘" label-width="93px" prop="fddmzrrjm">
                      <el-input placeholder="机、秘" v-model="tjlist.fddmzrrjm" clearable style="width:130px"
                        type="number" @blur="jszs(2)"></el-input>
                    </el-form-item>
                    <el-form-item label="秘" label-width="93px" prop="fddmzrrm">
                      <el-input placeholder="秘" v-model="tjlist.fddmzrrm" clearable style="width:130px"
                        type="number" @blur="jszs(2)"></el-input>
                    </el-form-item>
                    <el-form-item label="合计" label-width="93px">
                      <el-input placeholder="合计" v-model="tjlist.fddmzrrhj" clearable style="width:130px"
                        type="number" disabled></el-input>
                    </el-form-item>

                  </div>
                  <div>
                    <div class="input-tou">指定定密责任人数</div>

                    <el-form-item label="绝、机、秘" label-width="93px" prop="zddmzrrjjm">
                      <el-input placeholder="绝、机、秘" v-model="tjlist.zddmzrrjjm" clearable style="width:130px"
                        type="number" @blur="jszs(2)">
                      </el-input>
                    </el-form-item>
                    <el-form-item label="机、秘" label-width="93px" prop="zddmzrrjm">
                      <el-input placeholder="机、秘" v-model="tjlist.zddmzrrjm" clearable style="width:130px"
                        type="number" @blur="jszs(2)"></el-input>
                    </el-form-item>
                    <el-form-item label="秘" label-width="93px" prop="zddmzrrm">
                      <el-input placeholder="秘" v-model="tjlist.zddmzrrm" clearable style="width:130px"
                        type="number" @blur="jszs(2)"></el-input>
                    </el-form-item>
                    <el-form-item label="合计" label-width="93px">
                      <el-input placeholder="合计" v-model="tjlist.zddmzrrhj" clearable style="width:130px"
                        type="number" disabled></el-input>
                    </el-form-item>

                  </div>
                  <div>
                    <div class="input-tou">定密责任人数</div>

                    <el-form-item label="绝、机、秘" label-width="93px">
                      <el-input placeholder="绝、机、秘" v-model="tjlist.dmzrrjjm" clearable style="width:130px"
                        type="number" disabled></el-input>
                    </el-form-item>
                    <el-form-item label="机、秘" label-width="93px">
                      <el-input placeholder="机、秘" v-model="tjlist.dmzrrjm" clearable style="width:130px"
                        type="number" disabled></el-input>
                    </el-form-item>
                    <el-form-item label="秘" label-width="93px">
                      <el-input placeholder="秘" v-model="tjlist.dmzrrm" clearable style="width:130px"
                        type="number" disabled></el-input>
                    </el-form-item>
                    <el-form-item label="合计" label-width="93px">
                      <el-input placeholder="合计" v-model="tjlist.dmzrrhj" clearable style="width:130px"
                        type="number" disabled></el-input>
                    </el-form-item>

                  </div>

                </div>
              </el-tab-pane>
              <el-tab-pane label="定密授权数" name="dmsqs">
                <div style="display:flex;justify-content: space-between;align-items: center;">
                  <div style="margin-bottom:10px">三、定密授权数</div>
                  <el-button size="small" @click="$router.push('/dmsq')">查看详情</el-button>
                </div>
                <div style="display:flex;justify-content: space-between;">
                  <div>
                    <div class="input-tou">现在总数</div>

                    <el-form-item label="绝、机、秘" label-width="93px" prop="dmsqxczsjjm">
                      <el-input placeholder="绝、机、秘" v-model="tjlist.dmsqxczsjjm" clearable style="width:270px"
                        type="number" @blur="jszs(3)">
                      </el-input>
                    </el-form-item>
                    <el-form-item label="机、秘" label-width="93px" prop="dmsqxczsjm">
                      <el-input placeholder="机、秘" v-model="tjlist.dmsqxczsjm" clearable style="width:270px"
                        type="number" @blur="jszs(3)"></el-input>
                    </el-form-item>
                    <el-form-item label="秘" label-width="93px" prop="dmsqxczsm">
                      <el-input placeholder="秘" v-model="tjlist.dmsqxczsm" clearable style="width:270px"
                        type="number" @blur="jszs(3)"></el-input>
                    </el-form-item>
                    <el-form-item label="合计" label-width="93px">
                      <el-input placeholder="合计" v-model="tjlist.dmsqxczshj" clearable style="width:270px"
                        type="number" disabled></el-input>
                    </el-form-item>

                  </div>
                  <div>
                    <div class="input-tou">新增数</div>

                    <el-form-item label="绝、机、秘" label-width="93px" prop="dmsqxzsjjm">
                      <el-input placeholder="绝、机、秘" v-model="tjlist.dmsqxzsjjm" clearable style="width:270px"
                        type="number" @blur="jszs(3)">
                      </el-input>
                    </el-form-item>
                    <el-form-item label="机、秘" label-width="93px" prop="dmsqxzsjm">
                      <el-input placeholder="机、秘" v-model="tjlist.dmsqxzsjm" clearable style="width:270px"
                        type="number" @blur="jszs(3)"></el-input>
                    </el-form-item>
                    <el-form-item label="秘" label-width="93px" prop="dmsqxzsm">
                      <el-input placeholder="秘" v-model="tjlist.dmsqxzsm" clearable style="width:270px"
                        type="number" @blur="jszs(3)"></el-input>
                    </el-form-item>
                    <el-form-item label="合计" label-width="93px">
                      <el-input placeholder="合计" v-model="tjlist.dmsqxzshj" clearable style="width:270px"
                        type="number" disabled></el-input>
                    </el-form-item>

                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane label="国家秘密事项一栏表（细目）数" name="gjmmsxylbs">
                <div style="display:flex;justify-content: space-between;align-items: center;">
                  <div style="margin-bottom:10px">四、国家秘密事项一栏表（细目）数</div>
                  <el-button size="small" @click="$router.push('/gjmmsx')">查看详情</el-button>
                </div>
                <div style="display:flex;justify-content: space-between;">
                  <div>
                    <div class="input-tou">现在总数</div>

                    <el-form-item label="一览表数" label-width="107px" prop="gjmmsxylbxzzsylbs">
                      <el-input placeholder="一览表数" v-model="tjlist.gjmmsxylbxzzsylbs" clearable style="width:230px"
                        type="number">
                      </el-input>
                    </el-form-item>
                    <el-form-item label="一栏表条目数" label-width="107px" prop="gjmmsxylbxzzsylblms">
                      <el-input placeholder="一栏表条目数" v-model="tjlist.gjmmsxylbxzzsylblms" clearable style="width:230px"
                        type="number">
                      </el-input>
                    </el-form-item>

                  </div>
                  <div>
                    <div class="input-tou">新增数</div>

                    <el-form-item label="一览表数" label-width="107px" prop="gjmmsxylbxzsylbs">
                      <el-input placeholder="一览表数" v-model="tjlist.gjmmsxylbxzsylbs" clearable style="width:230px"
                        type="number">
                      </el-input>
                    </el-form-item>
                    <el-form-item label="一栏表条目数" label-width="107px" prop="gjmmsxylbxzsylblms">
                      <el-input placeholder="一栏表条目数" v-model="tjlist.gjmmsxylbxzsylblms" clearable style="width:230px"
                        type="number">
                      </el-input>
                    </el-form-item>

                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane label="定密制度数" name="dmzds">
                <div style="display:flex;justify-content: space-between;align-items: center;">
                  <div style="margin-bottom:10px">五、定密制度数</div>
                </div>
                <div style="display:flex;justify-content: space-between;">
                  <div>

                    <el-form-item label="现在总数" prop="dmzdsxczs" label-width="107px">
                      <el-input placeholder="现在总数" v-model="tjlist.dmzdsxczs" clearable style=""
                        type="number">
                      </el-input>
                    </el-form-item>
                    <el-form-item label="新制定修订数" prop="dmzdsxzdxds" label-width="107px">
                      <el-input placeholder="新制定修订数" v-model="tjlist.dmzdsxzdxds" clearable
                        type="number">
                      </el-input>
                    </el-form-item>

                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane label="定密培训数" name="dmpxs">
                <div style="display:flex;justify-content: space-between;align-items: center;">
                  <div style="margin-bottom:10px">六、定密培训数</div>
                  <el-button size="small" @click="$router.push('/dmpx')">查看详情</el-button>
                </div>
                <div style="display:flex;justify-content: space-between;">
                  <div>

                    <el-form-item label="培训次数" prop="pxcs" label-width="79px">
                      <el-input placeholder="培训次数" v-model="tjlist.pxcs" clearable style=""
                        type="number">
                      </el-input>
                    </el-form-item>
                    <el-form-item label="总学时数" prop="zxss" label-width="79px">
                      <el-input placeholder="总学时数" v-model="tjlist.zxss" clearable
                        type="number">
                      </el-input>
                    </el-form-item>
                    <el-form-item label="总人数" prop="zrs" label-width="79px">
                      <el-input placeholder="总人数" v-model="tjlist.zrs" clearable
                        type="number">
                      </el-input>
                    </el-form-item>

                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane label="工作秘密数" name="gzmms">
                <div style="display:flex;justify-content: space-between;align-items: center;">
                  <div style="margin-bottom:10px">七、工作秘密数</div>
                </div>
                <div style="display:flex;justify-content: space-between;">
                  <div>

                    <el-form-item label="工作秘密确定数" prop="gzmmqds" label-width="163px">
                      <el-input placeholder="工作秘密确定数" v-model="tjlist.gzmmqds" clearable style=""
                        type="number">
                      </el-input>
                    </el-form-item>
                    <el-form-item label="工作秘密清单应制定数" prop="gzmmqdyzds" label-width="163px">
                      <el-input placeholder="工作秘密清单应制定数" v-model="tjlist.gzmmqdyzds" clearable
                        type="number">
                      </el-input>
                    </el-form-item>
                    <el-form-item label="工作秘密清单实制定数" prop="gzmmqdszds" label-width="163px">
                      <el-input placeholder="工作秘密清单实制定数" v-model="tjlist.gzmmqdszds" clearable
                        type="number">
                      </el-input>
                    </el-form-item>

                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>

            <el-form-item label="备注" prop="bz" label-width="40px">
              <el-input type="textarea" v-model="tjlist.bz"></el-input>
            </el-form-item>
          </el-form>

          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitTj('formName')">保 存</el-button>
            <el-button type="warning" @click="dialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>

        <el-dialog title="修改定密情况年度统计信息" :close-on-click-modal="false" :visible.sync="xgdialogVisible" width="50%"
          class="xg" @close="close1('form')">
          <el-form ref="form" :model="xglist" :rules="rules" size="mini">
            <el-tabs v-model="activeName" @tab-click="handleClick">
              <el-tab-pane label="国家秘密统计情况" name="gjmmtjqk">
                <div style="margin-bottom:10px">一，国家秘密统计情况</div>

                <el-form-item label="年度" label-width="40px">
                  <el-input placeholder="年度" v-model="xglist.nd" clearable style="width:100px" disabled></el-input>
                </el-form-item>

                <div style="display:flex;justify-content: space-between;">
                  <div>
                    <div class="input-tou">原始国家秘密数</div>

                    <el-form-item label="绝密" label-width="51px" prop="ysgjmmsjm">
                      <el-input placeholder="绝密" v-model="xglist.ysgjmmsjm" clearable style="width:170px"
                        type="number" @blur="jszs(4)"></el-input>
                    </el-form-item>
                    <el-form-item label="机密" label-width="51px" prop="ysgjmmsjjm">
                      <el-input placeholder="机密" v-model="xglist.ysgjmmsjjm" clearable style="width:170px"
                        type="number" @blur="jszs(4)"></el-input>
                    </el-form-item>
                    <el-form-item label="秘密" label-width="51px" prop="ysgjmmsmm">
                      <el-input placeholder="秘密" v-model="xglist.ysgjmmsmm" clearable style="width:170px"
                        type="number" @blur="jszs(4)"></el-input>
                    </el-form-item>
                    <el-form-item label="合计" label-width="51px">
                      <el-input placeholder="合计" v-model="xglist.ysgjmmshj" clearable style="width:170px"
                        type="number" disabled></el-input>
                    </el-form-item>

                  </div>
                  <div>
                    <div class="input-tou">派生国家秘密数</div>

                    <el-form-item label="绝密" label-width="51px" prop="psgjmmsjm">
                      <el-input placeholder="绝密" v-model="xglist.psgjmmsjm" clearable style="width:170px"
                        type="number" @blur="jszs(4)"></el-input>
                    </el-form-item>
                    <el-form-item label="机密" label-width="51px" prop="psgjmmsjjm">
                      <el-input placeholder="机密" v-model="xglist.psgjmmsjjm" clearable style="width:170px"
                        type="number" @blur="jszs(4)"></el-input>
                    </el-form-item>
                    <el-form-item label="秘密" label-width="51px" prop="psgjmmsmm">
                      <el-input placeholder="秘密" v-model="xglist.psgjmmsmm" clearable style="width:170px"
                        type="number" @blur="jszs(4)"></el-input>
                    </el-form-item>
                    <el-form-item label="合计" label-width="51px">
                      <el-input placeholder="合计" v-model="xglist.psgjmmshj" clearable style="width:170px"
                        type="number" disabled></el-input>
                    </el-form-item>

                  </div>
                  <div>
                    <div class="input-tou">国家秘密总数</div>

                    <el-form-item label="绝密" label-width="51px">
                      <el-input placeholder="绝密" v-model="xglist.gjmmzsjm" clearable style="width:170px"
                        type="number" disabled></el-input>
                    </el-form-item>
                    <el-form-item label="机密" label-width="51px">
                      <el-input placeholder="机密" v-model="xglist.gjmmzsjjm" clearable style="width:170px"
                        type="number" disabled></el-input>
                    </el-form-item>
                    <el-form-item label="秘密" label-width="51px">
                      <el-input placeholder="秘密" v-model="xglist.gjmmzsmm" clearable style="width:170px"
                        type="number" disabled></el-input>
                    </el-form-item>
                    <el-form-item label="合计" label-width="51px">
                      <el-input placeholder="合计" v-model="xglist.gjmmzshj" clearable style="width:170px"
                        type="number" disabled></el-input>
                    </el-form-item>

                  </div>

                </div>

                <div style="display:flex">
                  <el-form-item label="变更数" label-width="65px" prop="bgs">
                    <el-input placeholder="变更数" v-model="xglist.bgs" clearable style="width:150px"
                      type="number"></el-input>
                  </el-form-item>
                  <el-form-item label="解密数" label-width="65px" style="margin-left:40px" prop="jms">
                    <el-input placeholder="解密数" v-model="xglist.jms" clearable style="width:150px"
                      type="number"></el-input>
                  </el-form-item>
                </div>

              </el-tab-pane>
              <el-tab-pane label="定密责任人数" name="dmzrrs">
                <div style="display:flex;justify-content: space-between;align-items: center;">
                  <div style="margin-bottom:10px">二，定密责任人数</div>
                  <el-button size="small" @click="$router.push('/dmzrr')">查看详情</el-button>
                </div>
                <div style="display:flex;justify-content: space-between;">
                  <div>
                    <div class="input-tou">法定定密责任人数</div>

                    <el-form-item label="绝、机、秘" label-width="93px" prop="fddmzrrjjm">
                      <el-input placeholder="绝、机、秘" v-model="xglist.fddmzrrjjm" clearable style="width:130px"
                        type="number" @blur="jszs(5)">
                      </el-input>
                    </el-form-item>
                    <el-form-item label="机、秘" label-width="93px" prop="fddmzrrjm">
                      <el-input placeholder="机、秘" v-model="xglist.fddmzrrjm" clearable style="width:130px"
                        type="number" @blur="jszs(5)"></el-input>
                    </el-form-item>
                    <el-form-item label="秘" label-width="93px" prop="fddmzrrm">
                      <el-input placeholder="秘" v-model="xglist.fddmzrrm" clearable style="width:130px"
                        type="number" @blur="jszs(5)"></el-input>
                    </el-form-item>
                    <el-form-item label="合计" label-width="93px">
                      <el-input placeholder="合计" v-model="xglist.fddmzrrhj" clearable style="width:130px"
                        type="number" disabled></el-input>
                    </el-form-item>

                  </div>
                  <div>
                    <div class="input-tou">指定定密责任人数</div>

                    <el-form-item label="绝、机、秘" label-width="93px" prop="zddmzrrjjm">
                      <el-input placeholder="绝、机、秘" v-model="xglist.zddmzrrjjm" clearable style="width:130px"
                        type="number" @blur="jszs(5)">
                      </el-input>
                    </el-form-item>
                    <el-form-item label="机、秘" label-width="93px" prop="zddmzrrjm">
                      <el-input placeholder="机、秘" v-model="xglist.zddmzrrjm" clearable style="width:130px"
                        type="number" @blur="jszs(5)"></el-input>
                    </el-form-item>
                    <el-form-item label="秘" label-width="93px" prop="zddmzrrm">
                      <el-input placeholder="秘" v-model="xglist.zddmzrrm" clearable style="width:130px"
                        type="number" @blur="jszs(5)"></el-input>
                    </el-form-item>
                    <el-form-item label="合计" label-width="93px">
                      <el-input placeholder="合计" v-model="xglist.zddmzrrhj" clearable style="width:130px"
                        type="number" disabled></el-input>
                    </el-form-item>

                  </div>
                  <div>
                    <div class="input-tou">定密责任人数</div>

                    <el-form-item label="绝、机、秘" label-width="93px">
                      <el-input placeholder="绝、机、秘" v-model="xglist.dmzrrjjm" clearable style="width:130px"
                        type="number" disabled></el-input>
                    </el-form-item>
                    <el-form-item label="机、秘" label-width="93px">
                      <el-input placeholder="机、秘" v-model="xglist.dmzrrjm" clearable style="width:130px"
                        type="number" disabled></el-input>
                    </el-form-item>
                    <el-form-item label="秘" label-width="93px">
                      <el-input placeholder="秘" v-model="xglist.dmzrrm" clearable style="width:130px"
                        type="number" disabled></el-input>
                    </el-form-item>
                    <el-form-item label="合计" label-width="93px">
                      <el-input placeholder="合计" v-model="xglist.dmzrrhj" clearable style="width:130px"
                        type="number" disabled></el-input>
                    </el-form-item>

                  </div>

                </div>
              </el-tab-pane>
              <el-tab-pane label="定密授权数" name="dmsqs">
                <div style="display:flex;justify-content: space-between;align-items: center;">
                  <div style="margin-bottom:10px">三、定密授权数</div>
                  <el-button size="small" @click="$router.push('/dmsq')">查看详情</el-button>
                </div>
                <div style="display:flex;justify-content: space-between;">
                  <div>
                    <div class="input-tou">现在总数</div>

                    <el-form-item label="绝、机、秘" label-width="93px" prop="dmsqxczsjjm">
                      <el-input placeholder="绝、机、秘" v-model="xglist.dmsqxczsjjm" clearable style="width:270px"
                        type="number" @blur="jszs(6)">
                      </el-input>
                    </el-form-item>
                    <el-form-item label="机、秘" label-width="93px" prop="dmsqxczsjm">
                      <el-input placeholder="机、秘" v-model="xglist.dmsqxczsjm" clearable style="width:270px"
                        type="number" @blur="jszs(6)"></el-input>
                    </el-form-item>
                    <el-form-item label="秘" label-width="93px" prop="dmsqxczsm">
                      <el-input placeholder="秘" v-model="xglist.dmsqxczsm" clearable style="width:270px"
                        type="number" @blur="jszs(6)"></el-input>
                    </el-form-item>
                    <el-form-item label="合计" label-width="93px">
                      <el-input placeholder="合计" v-model="xglist.dmsqxczshj" clearable style="width:270px"
                        type="number" disabled></el-input>
                    </el-form-item>

                  </div>
                  <div>
                    <div class="input-tou">新增数</div>

                    <el-form-item label="绝、机、秘" label-width="93px" prop="dmsqxzsjjm">
                      <el-input placeholder="绝、机、秘" v-model="xglist.dmsqxzsjjm" clearable style="width:270px"
                        type="number" @blur="jszs(6)">
                      </el-input>
                    </el-form-item>
                    <el-form-item label="机、秘" label-width="93px" prop="dmsqxzsjm">
                      <el-input placeholder="机、秘" v-model="xglist.dmsqxzsjm" clearable style="width:270px"
                        type="number" @blur="jszs(6)"></el-input>
                    </el-form-item>
                    <el-form-item label="秘" label-width="93px" prop="dmsqxzsm">
                      <el-input placeholder="秘" v-model="xglist.dmsqxzsm" clearable style="width:270px"
                        type="number" @blur="jszs(6)"></el-input>
                    </el-form-item>
                    <el-form-item label="合计" label-width="93px">
                      <el-input placeholder="合计" v-model="xglist.dmsqxzshj" clearable style="width:270px"
                        type="number" disabled></el-input>
                    </el-form-item>

                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane label="国家秘密事项一栏表（细目）数" name="gjmmsxylbs">
                <div style="display:flex;justify-content: space-between;align-items: center;">
                  <div style="margin-bottom:10px">四、国家秘密事项一栏表（细目）数</div>
                  <el-button size="small" @click="$router.push('/gjmmsx')">查看详情</el-button>
                </div>
                <div style="display:flex;justify-content: space-between;">
                  <div>
                    <div class="input-tou">现在总数</div>

                    <el-form-item label="一览表数" label-width="107px" prop="gjmmsxylbxzzsylbs">
                      <el-input placeholder="一览表数" v-model="xglist.gjmmsxylbxzzsylbs" clearable style="width:230px"
                        type="number">
                      </el-input>
                    </el-form-item>
                    <el-form-item label="一栏表条目数" label-width="107px" prop="gjmmsxylbxzzsylblms">
                      <el-input placeholder="一栏表条目数" v-model="xglist.gjmmsxylbxzzsylblms" clearable style="width:230px"
                        type="number">
                      </el-input>
                    </el-form-item>

                  </div>
                  <div>
                    <div class="input-tou">新增数</div>

                    <el-form-item label="一览表数" label-width="107px" prop="gjmmsxylbxzsylbs">
                      <el-input placeholder="一览表数" v-model="xglist.gjmmsxylbxzsylbs" clearable style="width:230px"
                        type="number">
                      </el-input>
                    </el-form-item>
                    <el-form-item label="一栏表条目数" label-width="107px" prop="gjmmsxylbxzsylblms">
                      <el-input placeholder="一栏表条目数" v-model="xglist.gjmmsxylbxzsylblms" clearable style="width:230px"
                        type="number">
                      </el-input>
                    </el-form-item>

                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane label="定密制度数" name="dmzds">
                <div style="display:flex;justify-content: space-between;align-items: center;">
                  <div style="margin-bottom:10px">五、定密制度数</div>
                </div>
                <div style="display:flex;justify-content: space-between;">
                  <div>

                    <el-form-item label="现在总数" prop="dmzdsxczs" label-width="107px">
                      <el-input placeholder="现在总数" v-model="xglist.dmzdsxczs" clearable style=""
                        type="number">
                      </el-input>
                    </el-form-item>
                    <el-form-item label="新制定修订数" prop="dmzdsxzdxds" label-width="107px">
                      <el-input placeholder="新制定修订数" v-model="xglist.dmzdsxzdxds" clearable
                        type="number">
                      </el-input>
                    </el-form-item>

                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane label="定密培训数" name="dmpxs">
                <div style="display:flex;justify-content: space-between;align-items: center;">
                  <div style="margin-bottom:10px">六、定密培训数</div>
                  <el-button size="small" @click="$router.push('/dmpx')">查看详情</el-button>
                </div>
                <div style="display:flex;justify-content: space-between;">
                  <div>

                    <el-form-item label="培训次数" prop="pxcs" label-width="79px">
                      <el-input placeholder="培训次数" v-model="xglist.pxcs" clearable style=""
                        type="number">
                      </el-input>
                    </el-form-item>
                    <el-form-item label="总学时数" prop="zxss" label-width="79px">
                      <el-input placeholder="总学时数" v-model="xglist.zxss" clearable
                        type="number">
                      </el-input>
                    </el-form-item>
                    <el-form-item label="总人数" prop="zrs" label-width="79px">
                      <el-input placeholder="总人数" v-model="xglist.zrs" clearable
                        type="number">
                      </el-input>
                    </el-form-item>

                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane label="工作秘密数" name="gzmms">
                <div style="display:flex;justify-content: space-between;align-items: center;">
                  <div style="margin-bottom:10px">七、工作秘密数</div>
                  
                </div>
                <div style="display:flex;justify-content: space-between;">
                  <div>

                    <el-form-item label="工作秘密确定数" prop="gzmmqds" label-width="163px">
                      <el-input placeholder="工作秘密确定数" v-model="xglist.gzmmqds" clearable style=""
                        type="number">
                      </el-input>
                    </el-form-item>
                    <el-form-item label="工作秘密清单应制定数" prop="gzmmqdyzds" label-width="163px">
                      <el-input placeholder="工作秘密清单应制定数" v-model="xglist.gzmmqdyzds" clearable
                        type="number">
                      </el-input>
                    </el-form-item>
                    <el-form-item label="工作秘密清单实制定数" prop="gzmmqdszds" label-width="163px">
                      <el-input placeholder="工作秘密清单实制定数" v-model="xglist.gzmmqdszds" clearable
                        type="number">
                      </el-input>
                    </el-form-item>

                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>

            <el-form-item label="备注" prop="bz" label-width="40px">
              <el-input type="textarea" v-model="xglist.bz"></el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="updataDialog('form')">保 存</el-button>
            <el-button type="warning" @click="xgdialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>
        <!-- 详情 -->
        <el-dialog title="定密情况年度统计信息详情" :close-on-click-modal="false" :visible.sync="xqdialogVisible" width="50%"
          class="xg">
          <el-form ref="form" :model="xglist" :rules="rules" size="mini">
            <div class="div-tabs">
            <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="国家秘密统计情况" name="gjmmtjqk" style="padding: 0 10px;">
              <div style="margin-bottom:10px">一，国家秘密统计情况</div>
              <el-form-item label="年度" class="one-line">
                  <el-input placeholder="年度" v-model="xglist.nd" clearable style="width:100px" disabled></el-input>
                </el-form-item>
              <div style="display:flex;justify-content: space-between;">
                <div>
                  <div class="input-tou">原始国家秘密数</div>
                    <el-form-item label="绝密" class="one-line">
                      <el-input placeholder="绝密" v-model="xglist.ysgjmmsjm" clearable style="width: 100%;"
                        type="number"></el-input>
                    </el-form-item>
                    <el-form-item label="机密" class="one-line">
                      <el-input placeholder="机密" v-model="xglist.ysgjmmsjjm" clearable style="width: 100%;"
                        type="number"></el-input>
                    </el-form-item>
                    <el-form-item label="秘密" class="one-line">
                      <el-input placeholder="秘密" v-model="xglist.ysgjmmsmm" clearable style="width: 100%;"
                        type="number"></el-input>
                    </el-form-item>
                    <el-form-item label="合计" class="one-line">
                      <el-input placeholder="合计" v-model="xglist.ysgjmmshj" clearable style="width: 100%;"
                        type="number"></el-input>
                    </el-form-item>
                </div>
                <div>
                  <div class="input-tou">派生国家秘密数</div>
                    <el-form-item label="绝密" class="one-line">
                      <el-input placeholder="绝密" v-model="xglist.psgjmmsjm" clearable style="width: 100%;"
                        type="number"></el-input>
                    </el-form-item>
                    <el-form-item label="机密" class="one-line">
                      <el-input placeholder="机密" v-model="xglist.psgjmmsjjm" clearable style="width: 100%;"
                        type="number"></el-input>
                    </el-form-item>
                    <el-form-item label="秘密" class="one-line">
                      <el-input placeholder="秘密" v-model="xglist.psgjmmsmm" clearable style="width: 100%;"
                        type="number"></el-input>
                    </el-form-item>
                    <el-form-item label="合计" class="one-line">
                      <el-input placeholder="合计" v-model="xglist.psgjmmshj" clearable style="width: 100%;"
                        type="number"></el-input>
                    </el-form-item>
                </div>
                <div>
                  <div class="input-tou">国家秘密总数</div>
                    <el-form-item label="绝密" class="one-line">
                      <el-input placeholder="绝密" v-model="xglist.gjmmzsjm" clearable style="width: 100%;"
                        type="number"></el-input>
                    </el-form-item>
                    <el-form-item label="机密" class="one-line">
                      <el-input placeholder="机密" v-model="xglist.gjmmzsjjm" clearable style="width: 100%;"
                        type="number"></el-input>
                    </el-form-item>
                    <el-form-item label="秘密" class="one-line">
                      <el-input placeholder="秘密" v-model="xglist.gjmmzsmm" clearable style="width: 100%;"
                        type="number"></el-input>
                    </el-form-item>
                    <el-form-item label="合计" class="one-line">
                      <el-input placeholder="合计" v-model="xglist.gjmmzshj" clearable style="width: 100%;"
                        type="number"></el-input>
                    </el-form-item>
                </div>

              </div>
                <div style="display:flex">
                  <el-form-item label="变更数" class="one-line">
                    <el-input placeholder="变更数" v-model="xglist.bgs" clearable style="width: 100%;"
                      type="number"></el-input>
                  </el-form-item>
                  <el-form-item label="解密数" class="one-line">
                    <el-input placeholder="解密数" v-model="xglist.jms" clearable style="width: 100%;"
                      type="number"></el-input>
                  </el-form-item>
                  <div class="one-line" style="border-width: 0;"></div>
                </div>
            </el-tab-pane>
            <el-tab-pane label="定密责任人数" name="dmzrrs" style="padding: 0 10px;">
              <div style="display:flex;justify-content: space-between;align-items: center;">
                <div style="margin-bottom:10px">二，定密责任人数</div>
                <el-button size="small" @click="$router.push('/dmzrr')">查看详情</el-button>
              </div>
              <div style="display:flex;justify-content: space-between;">
                <div>
                  <div class="input-tou">法定定密责任人数</div>
                    <el-form-item label="绝、机、秘" class="one-line">
                      <el-input placeholder="绝、机、秘" v-model="xglist.fddmzrrjjm" clearable style="width: 100%;"
                        type="number">
                      </el-input>
                    </el-form-item>
                    <el-form-item label="机、秘" class="one-line">
                      <el-input placeholder="机、秘" v-model="xglist.fddmzrrjm" clearable style="width: 100%;"
                        type="number"></el-input>
                    </el-form-item>
                    <el-form-item label="秘" class="one-line">
                      <el-input placeholder="秘" v-model="xglist.fddmzrrm" clearable style="width: 100%;"
                        type="number"></el-input>
                    </el-form-item>
                    <el-form-item label="合计" class="one-line">
                      <el-input placeholder="合计" v-model="xglist.fddmzrrhj" clearable style="width: 100%;"
                        type="number"></el-input>
                    </el-form-item>
                </div>
                <div>
                  <div class="input-tou">指定定密责任人数</div>
                    <el-form-item label="绝、机、秘" class="one-line">
                      <el-input placeholder="绝、机、秘" v-model="xglist.zddmzrrjjm" clearable style="width: 100%;"
                        type="number">
                      </el-input>
                    </el-form-item>
                    <el-form-item label="机、秘" class="one-line">
                      <el-input placeholder="机、秘" v-model="xglist.zddmzrrjm" clearable style="width: 100%;"
                        type="number"></el-input>
                    </el-form-item>
                    <el-form-item label="秘" class="one-line">
                      <el-input placeholder="秘" v-model="xglist.zddmzrrm" clearable style="width: 100%;"
                        type="number"></el-input>
                    </el-form-item>
                    <el-form-item label="合计" class="one-line">
                      <el-input placeholder="合计" v-model="xglist.zddmzrrhj" clearable style="width: 100%;"
                        type="number"></el-input>
                    </el-form-item>
                </div>
                <div>
                  <div class="input-tou">定密责任人数</div>
                    <el-form-item label="绝、机、秘" class="one-line">
                      <el-input placeholder="绝、机、秘" v-model="xglist.dmzrrjjm" clearable style="width: 100%;"
                        type="number"></el-input>
                    </el-form-item>
                    <el-form-item label="机、秘" class="one-line">
                      <el-input placeholder="机、秘" v-model="xglist.dmzrrjm" clearable style="width: 100%;"
                        type="number"></el-input>
                    </el-form-item>
                    <el-form-item label="秘" class="one-line">
                      <el-input placeholder="秘" v-model="xglist.dmzrrm" clearable style="width: 100%;"
                        type="number"></el-input>
                    </el-form-item>
                    <el-form-item label="合计" class="one-line">
                      <el-input placeholder="合计" v-model="xglist.dmzrrhj" clearable style="width: 100%;"
                        type="number"></el-input>
                    </el-form-item>
                </div>

              </div>
            </el-tab-pane>
            <el-tab-pane label="定密授权数" name="dmsqs" style="padding: 0 10px;">
              <div style="display:flex;justify-content: space-between;align-items: center;">
                <div style="margin-bottom:10px">三、定密授权数</div>
                <el-button size="small" @click="$router.push('/dmsq')">查看详情</el-button>
              </div>
              <div style="display:flex;justify-content: space-between;">
                <div>
                  <div class="input-tou">现在总数</div>
                    <el-form-item label="绝、机、秘" class="one-line">
                      <el-input placeholder="绝、机、秘" v-model="xglist.dmsqxczsjjm" clearable style="width: 100%;"
                        type="number">
                      </el-input>
                    </el-form-item>
                    <el-form-item label="机、秘" class="one-line">
                      <el-input placeholder="机、秘" v-model="xglist.dmsqxczsjm" clearable style="width: 100%;"
                        type="number"></el-input>
                    </el-form-item>
                    <el-form-item label="秘" class="one-line">
                      <el-input placeholder="秘" v-model="xglist.dmsqxczsm" clearable style="width: 100%;"
                        type="number"></el-input>
                    </el-form-item>
                    <el-form-item label="合计" class="one-line">
                      <el-input placeholder="合计" v-model="xglist.dmsqxczshj" clearable style="width: 100%;"
                        type="number"></el-input>
                    </el-form-item>
                </div>
                <div>
                  <div class="input-tou">新增数</div>
                    <el-form-item label="绝、机、秘" class="one-line">
                      <el-input placeholder="绝、机、秘" v-model="xglist.dmsqxzsjjm" clearable style="width: 100%;"
                        type="number">
                      </el-input>
                    </el-form-item>
                    <el-form-item label="机、秘" class="one-line">
                      <el-input placeholder="机、秘" v-model="xglist.dmsqxzsjm" clearable style="width: 100%;"
                        type="number"></el-input>
                    </el-form-item>
                    <el-form-item label="秘" class="one-line">
                      <el-input placeholder="秘" v-model="xglist.dmsqxzsm" clearable style="width: 100%;"
                        type="number"></el-input>
                    </el-form-item>
                    <el-form-item label="合计" class="one-line">
                      <el-input placeholder="合计" v-model="xglist.dmsqxzshj" clearable style="width: 100%;"
                        type="number"></el-input>
                    </el-form-item>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="国家秘密事项一栏表（细目）数" name="gjmmsxylbs" style="padding: 0 10px;">
              <div style="display:flex;justify-content: space-between;align-items: center;">
                <div style="margin-bottom:10px">四、国家秘密事项一栏表（细目）数</div>
                <el-button size="small" @click="$router.push('/gjmmsx')">查看详情</el-button>
              </div>
              <div style="display:flex;justify-content: space-between;">
                <div>
                  <div class="input-tou">现在总数</div>
                    <el-form-item label="一览表数" class="one-line">
                      <el-input placeholder="一览表数" v-model="xglist.gjmmsxylbxzzsylbs" clearable style="width: 100%;"
                        type="number">
                      </el-input>
                    </el-form-item>
                    <el-form-item label="一栏表条目数" class="one-line">
                      <el-input placeholder="一栏表条目数" v-model="xglist.gjmmsxylbxzzsylblms" clearable style="width: 100%;"
                        type="number">
                      </el-input>
                    </el-form-item>
                </div>
                <div>
                  <div class="input-tou">新增数</div>
                    <el-form-item label="一览表数" class="one-line">
                      <el-input placeholder="一览表数" v-model="xglist.gjmmsxylbxzsylbs" clearable style="width: 100%;"
                        type="number">
                      </el-input>
                    </el-form-item>
                    <el-form-item label="一栏表条目数" class="one-line">
                      <el-input placeholder="一栏表条目数" v-model="xglist.gjmmsxylbxzsylblms" clearable style="width: 100%;"
                        type="number">
                      </el-input>
                    </el-form-item>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="定密制度数" name="dmzds">
              <div style="display:flex;justify-content: space-between;align-items: center;">
                <div style="margin-bottom:10px">五、定密制度数</div>
               
              </div>
              <div style="display:flex;justify-content: space-between;">
                <div>
                    <el-form-item label="现在总数" class="one-line">
                      <el-input placeholder="现在总数" v-model="xglist.dmzdsxczs" clearable style="width: 100%;"
                        type="number">
                      </el-input>
                    </el-form-item>
                    <el-form-item label="新制定修订数" class="one-line">
                      <el-input placeholder="新制定修订数" v-model="xglist.dmzdsxzdxds" clearable style="width: 100%;"
                        type="number">
                      </el-input>
                    </el-form-item>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="定密培训数" name="dmpxs" style="padding: 0 10px;">
              <div style="display:flex;justify-content: space-between;align-items: center;">
                <div style="margin-bottom:10px">六、定密培训数</div>
                <el-button size="small" @click="$router.push('/dmpx')">查看详情</el-button>
              </div>
              <div style="display:flex;justify-content: space-between;">
                <div>
                    <el-form-item label="培训次数" class="one-line">
                      <el-input placeholder="培训次数" v-model="xglist.pxcs" clearable style="width: 100%;"
                        type="number">
                      </el-input>
                    </el-form-item>
                    <el-form-item label="总学时数" class="one-line">
                      <el-input placeholder="总学时数" v-model="xglist.zxss" clearable style="width: 100%;"
                        type="number">
                      </el-input>
                    </el-form-item>
                    <el-form-item label="总人数" class="one-line">
                      <el-input placeholder="总人数" v-model="xglist.zrs" clearable style="width: 100%;"
                        type="number">
                      </el-input>
                    </el-form-item>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="工作秘密数" name="gzmms">
              <div style="display:flex;justify-content: space-between;align-items: center;">
                <div style="margin-bottom:10px">七、工作秘密数</div>
                
              </div>
              <div style="display:flex;justify-content: space-between;">
                <div>
                    <el-form-item label="工作秘密确定数" class="one-line larger-title">
                      <el-input placeholder="工作秘密确定数" v-model="xglist.gzmmqds" clearable style="width: 100%;"
                        type="number">
                      </el-input>
                    </el-form-item>
                    <el-form-item label="工作秘密清单应制定数" class="one-line larger-title">
                      <el-input placeholder="工作秘密清单应制定数" v-model="xglist.gzmmqdyzds" clearable style="width: 100%;"
                        type="number">
                      </el-input>
                    </el-form-item>
                    <el-form-item label="工作秘密清单实制定数" class="one-line larger-title">
                      <el-input placeholder="工作秘密清单实制定数" v-model="xglist.gzmmqdszds" clearable style="width: 100%;"
                        type="number">
                      </el-input>
                    </el-form-item>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
          </div>
          </el-form>
          <el-form ref="formName" :model="xglist" :rules="rules" label-width="40px" size="mini" disabled>
            <el-form-item label="备注" prop="bz" class="one-line-textarea">
              <el-input type="textarea" v-model="xglist.bz"></el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">

            <el-button type="warning" @click="xqdialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>


      </div>
    </div>
  </div>

</template>
<script>
import {
  getdmmj
} from "../../../db/xzdb"
import {
	getDmqkndtj
} from "../../../db/lstzdb";
import {
  //内容管理初始化成员列表
  // getDmqkndtj,
  //添加内容管理
  addDmqkndtj,
  //删除内容管理
  deleteDmqkndtj,
  //修改
  reviseDmqkndtj,
} from "../../../db/dmqkndtjdb";
import {
  getlogin
} from "../../../db/loginyhdb";
import {
  exportExcel
} from "../../../utils/exportExcel"; //excel导出工具
import {
  getUuid
} from "../../../utils/getUuid"; //获取uuid
export default {
  components: {},
  props: {},
  data() {
    return {
      excelList: [],
      pdaqcp: 0, //提示信息判断
      sblxxz: [], //下拉框数据
      bmqsxqdqlList: [], //列表数据
      tableDataCopy: [], //查询备份数据
      xglist: {}, //修改与详情数据
      updateItemOld: {},
      xgdialogVisible: false, //修改弹框
      xqdialogVisible: false, //详情弹框
      formInline: {}, //查询区域数据
			yearSelect: [],
      tjlist: {
        nd: "2022",
        ysgjmmsjm: 0,
        ysgjmmsjjm: 0,
        ysgjmmsmm: 0,
        ysgjmmshj: 0,
        psgjmmsjm: 0,
        psgjmmsjjm: 0,
        psgjmmsmm: 0,
        psgjmmshj: 0,
        gjmmzsjm: 0,
        gjmmzsjjm: 0,
        gjmmzsmm: 0,
        gjmmzshj: 0,
        bgs: 0,
        jms: 0,
        fddmzrrjjm: 0,
        fddmzrrjm: 0,
        fddmzrrm: 0,
        fddmzrrhj: 0,
        zddmzrrjjm: 0,
        zddmzrrjm: 0,
        zddmzrrm: 0,
        zddmzrrhj: 0,
        dmzrrjjm: 0,
        dmzrrjm: 0,
        dmzrrm: 0,
        dmzrrhj: 0,
        dmsqxczsjjm: 0,
        dmsqxczsjm: 0,
        dmsqxczsm: 0,
        dmsqxczshj: 0,
        dmsqxzsjjm: 0,
        dmsqxzsjm: 0,
        dmsqxzsm: 0,
        dmsqxzshj: 0,
        gjmmsxylbxzzsylbs: 0,
        gjmmsxylbxzzsylblms: 0,
        gjmmsxylbxzsylbs: 0,
        gjmmsxylbxzsylblms: 0,
        dmzdsxczs: 0,
        dmzdsxzdxds: 0,
        pxcs: 0,
        zxss: 0,
        zrs: 0,
        gzmmqds: 0,
        gzmmqdyzds: 0,
        gzmmqdszds: 0,
        bz: '',
      }, //添加数据
      rules: {
        ysgjmmsjm: [{
          required: true,
          message: '请输入绝密数量',
          trigger: 'blur'
        },],
        ysgjmmsjjm: [{
          required: true,
          message: '请输入机密数量',
          trigger: 'blur'
        },],
        ysgjmmsmm: [{
          required: true,
          message: '请输入秘密数量',
          trigger: 'blur'
        },],
        psgjmmsjm: [{
          required: true,
          message: '请输入绝密数量',
          trigger: 'blur'
        },],
        psgjmmsjjm: [{
          required: true,
          message: '请输入机密数量',
          trigger: 'blur'
        },],
        psgjmmsmm: [{
          required: true,
          message: '请输入秘密数量',
          trigger: 'blur'
        },],
        bgs: [{
          required: true,
          message: '请输入变更数数量',
          trigger: 'blur'
        },],
        jms: [{
          required: true,
          message: '请输入解密数数量',
          trigger: 'blur'
        },],
        fddmzrrjjm: [{
          required: true,
          message: '请输入绝、机、秘数量',
          trigger: 'blur'
        },],
        fddmzrrjm: [{
          required: true,
          message: '请输入机、秘数量',
          trigger: 'blur'
        },],
        fddmzrrm: [{
          required: true,
          message: '请输入秘数量',
          trigger: 'blur'
        },],
        zddmzrrjjm: [{
          required: true,
          message: '请输入绝、机、秘数量',
          trigger: 'blur'
        },],
        zddmzrrjm: [{
          required: true,
          message: '请输入机、秘数量',
          trigger: 'blur'
        },],
        zddmzrrm: [{
          required: true,
          message: '请输入秘数量',
          trigger: 'blur'
        },],
        dmsqxczsjjm: [{
          required: true,
          message: '请输入绝、机、秘数量',
          trigger: 'blur'
        },],
        dmsqxczsjm: [{
          required: true,
          message: '请输入机、秘数量',
          trigger: 'blur'
        },],
        dmsqxczsm: [{
          required: true,
          message: '请输入秘数量',
          trigger: 'blur'
        },],
        dmsqxzsjjm: [{
          required: true,
          message: '请输入绝、机、秘数量',
          trigger: 'blur'
        },],
        dmsqxzsjm: [{
          required: true,
          message: '请输入机、秘数量',
          trigger: 'blur'
        },],
        dmsqxzsm: [{
          required: true,
          message: '请输入秘数量',
          trigger: 'blur'
        },],
        gjmmsxylbxzzsylbs: [{
          required: true,
          message: '请输入一览表数数量',
          trigger: 'blur'
        },],
        gjmmsxylbxzzsylblms: [{
          required: true,
          message: '请输入一栏表条目数数量',
          trigger: 'blur'
        },],
        gjmmsxylbxzsylbs: [{
          required: true,
          message: '请输入一览表数数量',
          trigger: 'blur'
        },],
        gjmmsxylbxzsylblms: [{
          required: true,
          message: '请输入一栏表条目数数量',
          trigger: 'blur'
        },],
        dmzdsxczs: [{
          required: true,
          message: '请输入现在总数数量',
          trigger: 'blur'
        },],
        dmzdsxzdxds: [{
          required: true,
          message: '请输入新制定修订数数量',
          trigger: 'blur'
        },],
        pxcs: [{
          required: true,
          message: '请输入培训次数',
          trigger: 'blur'
        },],
        zxss: [{
          required: true,
          message: '请输入总学时数',
          trigger: 'blur'
        },],
        zrs: [{
          required: true,
          message: '请输入总人数',
          trigger: 'blur'
        },],
        gzmmqds: [{
          required: true,
          message: '请输入工作秘密确定数',
          trigger: 'blur'
        },],
        gzmmqdyzds: [{
          required: true,
          message: '请输入工作秘密清单应制定数',
          trigger: 'blur'
        },],
        gzmmqdszds: [{
          required: true,
          message: '请输入工作秘密清单实制定数',
          trigger: 'blur'
        },],

      }, //校验
      page: 1, //当前页
      pageSize: 10, //每页条数
      total: 0, //总共数据数
      selectlistRow: [], //列表的值
      dialogVisible: false, //添加弹窗状态
      //导入
      dialogVisible_dr: false, //导入成员组弹窗状态
      dr_cyz_list: [], //待选择导入成员组列表
      multipleTable: [], //已选择导入成员组列表
      activeName: 'gjmmtjqk',
      dwmc: "",
      dwdm: "",
      xh: [],
      dr_dialog: false,
      //数据导入方式
      sjdrfs: ''
    };
  },
  computed: {},
  mounted() {
    this.dwmc = getlogin()[0].dwmc
    this.dwdm = getlogin()[0].dwzch
		//获取最近十年的年份
		let yearArr = []
		for (let i = new Date().getFullYear(); i > new Date().getFullYear() - 10; i--) {
			yearArr.push(
				{
					label: i.toString(),
					value: i.toString()
				})
		}
		yearArr.unshift({
			label: "全部",
			value: ""
		})
		this.yearSelect = yearArr
    //下拉框数据
    this.sblxxz = getdmmj()
    //列表初始化
    this.bmqsxqdqk();

  },
  methods: {
    mbxzgb() {
      this.sjdrfs = ''
    },
    mbdc() { },

    chooseFile() { },
    //导出
    exportList() {
      let filename = "不明确事项确定情况历史台账" + getUuid() + ".xlsx"
      const {
        dialog
      } = require('electron').remote;
      //弹窗title
      let options = {
        title: "保存文件",
        defaultPath: filename,
      };
      console.log(dialog)
      //导出文件夹选择弹窗
      dialog.showSaveDialog(options, result => {
        console.log('result', result)
        if (result == null || result == "") {
          console.log("取消导出")
          return
        }
        let list = []
        list.push(["定密事项综合统计表"])

        list.push(["填报单位:", this.dwmc, "", "", "","", "单位代码", this.dwdm, "", "", "所属层级", "", "", "", "省", "",
          "市（地、州、盟）", "", "县（市、区、旗）", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "",
          "", "", "", "", "", "", "", "", "", "",
        ])
        list.push(["机关、单位/地方名称", "1国家秘密统计情况", "","", "", "", "", "", "", "", "", "", "", "", "", "", "2定密责任人数", "",
          "", "", "", "", "", "", "", "", "", "", "3定密授权数", "", "", "", "", "", "", "", "4国家秘密事项一览表（细目）数", "",
          "", "", "5定密制度", "", "定密培训数", "", "", "工作秘密数", "", "", "备注"
        ])
        list.push(["", "国家秘密总数", "", "", "","", "原始国家秘密数", "", "", "", "派生国家秘密数", "", "", "", "变更数", "解密数", "定密责任人数",
          "", "", "", "法定定密责任人数", "", "", "", "指定定密责任人数", "", "", "", "现有总数", "", "", "", "新增数", "", "", "",
          "现有总数", "", "新增数", "", "", "", "", "", "", "", "", "",
        ])
        list.push(["", "台账时间", "总数", "绝密", "机密", "秘密", "合计", "绝密", "机密", "秘密", "合计", "绝密", "机密", "秘密", "", "", "总数",
          "绝、机、秘", "机、秘", "秘", "合计", "绝、机、秘", "机、秘", "秘", "合计", "绝、机、秘", "机、秘", "秘", "合计", "绝、机、秘", "机、秘",
          "秘", "新增数合计", "绝、机、秘", "机、秘", "秘", "一览表数", "一览表条目数", "一览表数", "一览表条目数", "现在数", "新制定修订数", "培训次数",
          "总学时数", "总人数", "工作秘密确定数", "工作秘密清单应制定数", "工作秘密清单实制定数"
        ])
        //对导出的数据进行格式，js导出excel要求传入的参数格式为[["列1名称","列2名称","列3名称"],["列11","值21","值31"],["列12","值22","值32"]]
        for (var i in this.excelList) { //每一行的值
          let item = this.excelList[i] //{"name":"XXX","dw":"XXX","zw":"XXX"}

          console.log("导出值:", this.excelList);
          let column = ["", item["tzsj"], item["gjmmzshj"], item["gjmmzsjm"], item["gjmmzsjjm"], item["gjmmzsmm"], item[
            "ysgjmmshj"], item["ysgjmmsjm"], item["ysgjmmsjjm"], item["ysgjmmsmm"],
            item["psgjmmshj"], item["psgjmmsjm"], item["psgjmmsjjm"], item["psgjmmsmm"], item["bgs"], item[
            "jms"], item["dmzrrhj"], item["dmzrrjjm"], item["dmzrrjm"], item["dmzrrm"], item["fddmzrrhj"],
            item["fddmzrrjjm"], item["fddmzrrjm"], item["fddmzrrm"], item[
            "zddmzrrhj"], item["zddmzrrjjm"], item["zddmzrrjm"], item["zddmzrrm"], item["dmsqxczshj"], item[
            "dmsqxczsjjm"], item["dmsqxczsjm"], item["dmsqxczsm"], item["dmsqxzshj"], item["dmsqxzsjjm"],
            item["dmsqxzsjm"], item["dmsqxzsm"], item["gjmmsxylbxzzsylbs"], item["gjmmsxylbxzzsylblms"], item[
            "gjmmsxylbxzsylbs"], item["gjmmsxylbxzsylblms"], item["dmzdsxczs"], item["dmzdsxzdxds"], item["pxcs"], item["zxss"], item["zrs"], item["gzmmqds"], item["gzmmqdyzds"], item["gzmmqdyzds"], item["bz"],
          ]
          console.log(column);
          list.push(column)
        }
        // list.push(["填报人",  "", "联系方式", "", "", "", "填报时间", ""
        // ])
        list.push(["填报人", "", "", "", "", "", "", "", "联系方式", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "填报时间", ""])
        let merges = [{
          s: { //s为开始
            c: 0, //开始列
            r: 0 //开始取值范围
          },
          e: { //e结束
            c: 11, //结束列
            r: 0 //结束范围
          }
        },
        {
          s: {
            c: 1,
            r: 1
          },
          e: {
            c: 4,
            r: 1
          }
        },
        {
          s: {
            c: 6,
            r: 1
          },
          e: {
            c: 8,
            r: 1
          }
        },
        {
          s: {
            c: 10,
            r: 1
          },
          e: {
            c: 12,
            r: 1
          }
        },
        {
          s: {
            c: 0,
            r: 2
          },
          e: {
            c: 0,
            r: 4
          }
        },
        {
          s: {
            c: 1,
            r: 2
          },
          e: {
            c: 14,
            r: 2
          }
        },
        {
          s: {
            c: 15,
            r: 2
          },
          e: {
            c: 26,
            r: 2
          }
        },
        {
          s: {
            c: 27,
            r: 2
          },
          e: {
            c: 34,
            r: 2
          }
        },
        {
          s: {
            c: 35,
            r: 2
          },
          e: {
            c: 38,
            r: 2
          }
        },
        {
          s: {
            c: 39,
            r: 2
          },
          e: {
            c: 40,
            r: 2
          }
        },
        {
          s: {
            c: 41,
            r: 2
          },
          e: {
            c: 43,
            r: 2
          }
        },
        {
          s: {
            c: 44,
            r: 2
          },
          e: {
            c: 46,
            r: 2
          }
        },
        {
          s: {
            c: 1,
            r: 3
          },
          e: {
            c: 4,
            r: 3
          }
        },
        {
          s: {
            c: 5,
            r: 3
          },
          e: {
            c: 8,
            r: 3
          }
        },
        {
          s: {
            c: 9,
            r: 3
          },
          e: {
            c: 12,
            r: 3
          }
        },
        {
          s: {
            c: 13,
            r: 3
          },
          e: {
            c: 13,
            r: 4
          }
        },
        {
          s: {
            c: 14,
            r: 3
          },
          e: {
            c: 14,
            r: 4
          }
        },
        {
          s: {
            c: 15,
            r: 3
          },
          e: {
            c: 18,
            r: 3
          }
        },
        {
          s: {
            c: 19,
            r: 3
          },
          e: {
            c: 22,
            r: 3
          }
        },
        {
          s: {
            c: 23,
            r: 3
          },
          e: {
            c: 26,
            r: 3
          }
        },
        {
          s: {
            c: 27,
            r: 3
          },
          e: {
            c: 30,
            r: 3
          }
        },
        {
          s: {
            c: 31,
            r: 3
          },
          e: {
            c: 34,
            r: 3
          }
        },
        {
          s: {
            c: 35,
            r: 3
          },
          e: {
            c: 36,
            r: 3
          }
        },
        {
          s: {
            c: 37,
            r: 3
          },
          e: {
            c: 38,
            r: 3
          }
        },
        {
          s: {
            c: 39,
            r: 3
          },
          e: {
            c: 40,
            r: 3
          }
        },
        {
          s: {
            c: 41,
            r: 3
          },
          e: {
            c: 43,
            r: 3
          }
        },
        {
          s: {
            c: 44,
            r: 3
          },
          e: {
            c: 46,
            r: 3
          }
        },
        {
          s: {
            c: 39,
            r: 2
          },
          e: {
            c: 39,
            r: 3
          }
        },
        {
          s: {
            c: 40,
            r: 2
          },
          e: {
            c: 40,
            r: 3
          }
        },
        {
          s: {
            c: 41,
            r: 2
          },
          e: {
            c: 41,
            r: 3
          }
        },
        {
          s: {
            c: 42,
            r: 2
          },
          e: {
            c: 42,
            r: 3
          }
        },
        {
          s: {
            c: 43,
            r: 2
          },
          e: {
            c: 43,
            r: 3
          }
        },
        {
          s: {
            c: 44,
            r: 2
          },
          e: {
            c: 44,
            r: 3
          }
        },
        {
          s: {
            c: 45,
            r: 2
          },
          e: {
            c: 45,
            r: 3
          }
        },
        {
          s: {
            c: 46,
            r: 2
          },
          e: {
            c: 46,
            r: 3
          }
        },
        {
          s: {
            c: 47,
            r: 2
          },
          e: {
            c: 47,
            r: 4
          }
        },


        ]
        exportExcel(result, list, merges) //list 要求为二维数组
        this.$message('导出成功:' + result)
      })
    },
    //修改
    updataDialog(form) {
      this.$refs[form].validate((valid) => {
        if (valid) {

          reviseDmqkndtj(this.xglist)
          this.bmqsxqdqk();
          // 关闭dialog
          this.$message.success("修改成功");
          this.xgdialogVisible = false;


        } else {
          console.log('error submit!!');
          return false;
        }
      });

    },
    //详情弹框
    xqyl(row) {
      this.updateItemOld = JSON.parse(JSON.stringify(row));

      this.xglist = JSON.parse(JSON.stringify(row));
      // this.form1.ywlx = row.ywlx
      console.log("old", row);
      console.log("this.xglist.ywlx", this.xglist);
      this.xqdialogVisible = true;
    },
    //修改弹框
    updateItem(row) {
      this.updateItemOld = JSON.parse(JSON.stringify(row));

      this.xglist = JSON.parse(JSON.stringify(row));
      // this.form1.ywlx = row.ywlx
      console.log("old", row);
      console.log("this.xglist.ywlx", this.xglist);
      this.xgdialogVisible = true;
    },
    //查询
    onSubmit() {
      //  form是查询条件
      console.log(this.formInline);
      // 备份了一下数据
      let arr = this.tableDataCopy
      // 通过遍历key值来循环处理
      Object.keys(this.formInline).forEach(e => {
        // 调用自己定义好的筛选方法
        console.log(this.formInline[e]);
        arr = this.filterFunc(this.formInline[e], e, arr)
      })
      // 为表格赋值
      this.bmqsxqdqlList = arr
    },
    //查询方法
    filterFunc(val, target, filterArr) {
      // 参数不存在或为空时，就相当于查询全部
      if (val == undefined || val == '') {
        console.log(2);
        return filterArr
      }
      return filterArr.filter(p => {
        console.log(p);
        let bool
        let resP
        if (Object.prototype.toString.call(val) == '[object Array]') {
          console.log('是数组')
          if (val.length > 1) {
            let timeArr1 = val[1].replace(/[\u4e00-\u9fa5]/g, '/')
            let date = new Date(timeArr1)
            if ('Invalid Date' != date) {
              // 时间
              if (new Date(p[target].replace(/[\u4e00-\u9fa5]/g, '/')).getTime() <= date.getTime()) {
                console.log('找到小于范围内是记录')
                resP = p
                let timeArr0 = val[0].replace(/[\u4e00-\u9fa5]/g, '/')
                if (new Date(p[target].replace(/[\u4e00-\u9fa5]/g, '/')).getTime() >= new Date(timeArr0)
                  .getTime()) {
                  console.log('找到大于范围内是记录')
                  resP = p
                } else {
                  resP = undefined
                }
              }
            } else {
              console.log('非法时间')
            }
            if (resP) {
              console.log('不是时间，通过时间校验')
              bool = true
            }
          } else {
            if (new Date(p[target]).getTime() <= new Date(timeArr0).getTime()) {
              resP = p
            } else {
              resP = undefined
            }
            if (resP) {
              bool = true
            }
          }
          return bool
        }
        return p[target].indexOf(val) > -1
        // return bool
      }) // 可以自己加一个.toLowerCase()来兼容一下大小
    },

    returnSy() {
      this.$router.push("/tzglsy");
    },
    //获取列表的值
    bmqsxqdqk() {
      let params = {
        page: this.page,
        pageSize: this.pageSize,
      };
      Object.assign(params, this.formInline);
      let resList = getDmqkndtj(params);
      console.log("params", params);
      this.tableDataCopy = resList.list
      this.excelList = resList.list_total
      this.bmqsxqdqlList = resList.list;
      this.total = resList.total;
    },
    //删除
    shanchu(id) {
      if (this.selectlistRow != '') {
        this.$confirm("是否继续删除?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            let valArr = this.selectlistRow;
            // console.log("....", val);
            valArr.forEach(function (item) {
              deleteDmqkndtj(item);
              console.log("删除：", item);
              console.log("删除：", item);
            });
            let params = valArr;
            this.$message({
              message: "删除成功",
              type: "success",
            });
            this.bmqsxqdqk();
          })
          .catch(() => {
            this.$message("已取消删除");
          });
      } else {
        this.$message({
          message: '未选择删除记录，请选择下列列表',
          type: 'warning'
        });
      }
    },
    //添加
    showDialog() {
      this.resetForm();
      this.dialogVisible = true;
    },
    //确定添加成员组
    submitTj(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let params = {
            nd: this.tjlist.nd,
            ysgjmmsjm: this.tjlist.ysgjmmsjm,
            ysgjmmsjjm: this.tjlist.ysgjmmsjjm,
            ysgjmmsmm: this.tjlist.ysgjmmsmm,
            ysgjmmshj: this.tjlist.ysgjmmshj,
            psgjmmsjm: this.tjlist.psgjmmsjm,
            psgjmmsjjm: this.tjlist.psgjmmsjjm,
            psgjmmsmm: this.tjlist.psgjmmsmm,
            psgjmmshj: this.tjlist.psgjmmshj,
            gjmmzsjm: this.tjlist.gjmmzsjm,
            gjmmzsjjm: this.tjlist.gjmmzsjjm,
            gjmmzsmm: this.tjlist.gjmmzsmm,
            gjmmzshj: this.tjlist.gjmmzshj,
            bgs: this.tjlist.bgs,
            jms: this.tjlist.jms,
            fddmzrrjjm: this.tjlist.fddmzrrjjm,
            fddmzrrjm: this.tjlist.fddmzrrjm,
            fddmzrrm: this.tjlist.fddmzrrm,
            fddmzrrhj: this.tjlist.fddmzrrhj,
            zddmzrrjjm: this.tjlist.zddmzrrjjm,
            zddmzrrjm: this.tjlist.zddmzrrjm,
            zddmzrrm: this.tjlist.zddmzrrm,
            zddmzrrhj: this.tjlist.zddmzrrhj,
            dmzrrjjm: this.tjlist.dmzrrjjm,
            dmzrrjm: this.tjlist.dmzrrjm,
            dmzrrm: this.tjlist.dmzrrm,
            dmzrrhj: this.tjlist.dmzrrhj,
            dmsqxczsjjm: this.tjlist.dmsqxczsjjm,
            dmsqxczsjm: this.tjlist.dmsqxczsjm,
            dmsqxczsm: this.tjlist.dmsqxczsm,
            dmsqxczshj: this.tjlist.dmsqxczshj,
            dmsqxzsjjm: this.tjlist.dmsqxzsjjm,
            dmsqxzsjm: this.tjlist.dmsqxzsjm,
            dmsqxzsm: this.tjlist.dmsqxzsm,
            dmsqxzshj: this.tjlist.dmsqxzshj,
            gjmmsxylbxzzsylbs: this.tjlist.gjmmsxylbxzzsylbs,
            gjmmsxylbxzzsylblms: this.tjlist.gjmmsxylbxzzsylblms,
            gjmmsxylbxzsylbs: this.tjlist.gjmmsxylbxzsylbs,
            gjmmsxylbxzsylblms: this.tjlist.gjmmsxylbxzsylblms,
            dmzdsxczs: this.tjlist.dmzdsxczs,
            dmzdsxzdxds: this.tjlist.dmzdsxzdxds,
            pxcs: this.tjlist.pxcs,
            zxss: this.tjlist.zxss,
            zrs: this.tjlist.zrs,
            gzmmqds: this.tjlist.gzmmqds,
            gzmmqdyzds: this.tjlist.gzmmqdyzds,
            gzmmqdszds: this.tjlist.gzmmqdszds,
            bz: this.tjlist.bz,
            dmqkndtjid: getUuid()
          };

          addDmqkndtj(params);
          this.dialogVisible = false;

          this.$message({
            message: '添加成功',
            type: 'success'
          });
          this.resetForm();
          this.bmqsxqdqk();


        } else {
          console.log('error submit!!');
          return false;
        }
      });

    },

    deleteTkglBtn() { },
    //选中列表的数据
    selectRow(val) {
      console.log(val);
      this.selectlistRow = val;
    },
    //列表分页--跳转页数
    handleCurrentChange(val) {
      this.page = val;
      this.bmqsxqdqk();
    },
    //列表分页--更改每页显示个数
    handleSizeChange(val) {
      this.page = 1;
      this.pageSize = val;
      this.bmqsxqdqk();
    },
    //添加重置
    resetForm() {
      this.tjlist.ysgjmmsjm = 0
      this.tjlist.ysgjmmsjjm = 0
      this.tjlist.ysgjmmsmm = 0
      this.tjlist.ysgjmmshj = 0
      this.tjlist.psgjmmsjm = 0
      this.tjlist.psgjmmsjjm = 0
      this.tjlist.psgjmmsmm = 0
      this.tjlist.psgjmmshj = 0
      this.tjlist.gjmmzsjm = 0
      this.tjlist.gjmmzsjjm = 0
      this.tjlist.gjmmzsmm = 0
      this.tjlist.gjmmzshj = 0
      this.tjlist.bgs = 0
      this.tjlist.jms = 0
      this.tjlist.fddmzrrjjm = 0
      this.tjlist.fddmzrrjm = 0
      this.tjlist.fddmzrrm = 0
      this.tjlist.fddmzrrhj = 0
      this.tjlist.zddmzrrjjm = 0
      this.tjlist.zddmzrrjm = 0
      this.tjlist.zddmzrrm = 0
      this.tjlist.zddmzrrhj = 0
      this.tjlist.dmzrrjjm = 0
      this.tjlist.dmzrrjm = 0
      this.tjlist.dmzrrm = 0
      this.tjlist.dmzrrhj = 0
      this.tjlist.dmsqxczsjjm = 0
      this.tjlist.dmsqxczsjm = 0
      this.tjlist.dmsqxczsm = 0
      this.tjlist.dmsqxczshj = 0
      this.tjlist.dmsqxzsjjm = 0
      this.tjlist.dmsqxzsjm = 0
      this.tjlist.dmsqxzsm = 0
      this.tjlist.dmsqxzshj = 0
      this.tjlist.gjmmsxylbxzzsylbs = 0
      this.tjlist.gjmmsxylbxzzsylblms = 0
      this.tjlist.gjmmsxylbxzsylbs = 0
      this.tjlist.gjmmsxylbxzsylblms = 0
      this.tjlist.dmzdsxczs = 0
      this.tjlist.dmzdsxzdxds = 0
      this.tjlist.pxcs = 0
      this.tjlist.zxss = 0
      this.tjlist.zrs = 0
      this.tjlist.gzmmqds = 0
      this.tjlist.gzmmqdyzds = 0
      this.tjlist.gzmmqdszds = 0
      this.tjlist.bz = ''
    },
    handleClose(done) {
      this.resetForm();
      this.dialogVisible = false;
    },
    // 弹框关闭触发
    close(formName) {
      console.log(1);
      // 清空表单校验，避免再次进来会出现上次校验的记录
      this.$refs[formName].resetFields();
    },
    //取消校验
    close1(form) {
      // 清空表单校验，避免再次进来会出现上次校验的记录
      this.$refs[form].resetFields();
    },
    handleClick(tab, event) {
      console.log(tab, event);
    },
    jszs(index) {
      if (index == 1) {
        this.tjlist.gjmmzsjm = this.tjlist.ysgjmmsjm * 1 + this.tjlist.psgjmmsjm * 1;
        this.tjlist.gjmmzsjjm = this.tjlist.ysgjmmsjjm * 1 + this.tjlist.psgjmmsjjm * 1;
        this.tjlist.gjmmzsmm = this.tjlist.ysgjmmsmm * 1 + this.tjlist.psgjmmsmm * 1;
        this.tjlist.ysgjmmshj = this.tjlist.ysgjmmsjm * 1 + this.tjlist.ysgjmmsjjm * 1 + this.tjlist.ysgjmmsmm * 1;
        this.tjlist.psgjmmshj = this.tjlist.psgjmmsjm * 1 + this.tjlist.psgjmmsjjm * 1 + this.tjlist.psgjmmsmm * 1;
        this.tjlist.gjmmzshj = this.tjlist.psgjmmshj * 1 + this.tjlist.ysgjmmshj * 1;
      } else if (index == 2) {
        this.tjlist.dmzrrjjm = this.tjlist.fddmzrrjjm * 1 + this.tjlist.zddmzrrjjm * 1;
        this.tjlist.dmzrrjm = this.tjlist.fddmzrrjm * 1 + this.tjlist.zddmzrrjm * 1;
        this.tjlist.dmzrrm = this.tjlist.fddmzrrm * 1 + this.tjlist.zddmzrrm * 1;
        this.tjlist.fddmzrrhj = this.tjlist.fddmzrrjjm * 1 + this.tjlist.fddmzrrjm * 1 + this.tjlist.fddmzrrm * 1;
        this.tjlist.zddmzrrhj = this.tjlist.zddmzrrjjm * 1 + this.tjlist.zddmzrrjm * 1 + this.tjlist.zddmzrrm * 1;
        this.tjlist.dmzrrhj = this.tjlist.fddmzrrhj * 1 + this.tjlist.zddmzrrhj * 1;
      } else if (index == 3) {
        this.tjlist.dmsqxczshj = this.tjlist.dmsqxczsjjm * 1 + this.tjlist.dmsqxczsjm * 1 + this.tjlist.dmsqxczsm * 1;
        this.tjlist.dmsqxzshj = this.tjlist.dmsqxzsjjm * 1 + this.tjlist.dmsqxzsjm * 1 + this.tjlist.dmsqxzsm * 1;
      } else if (index == 4) {
        this.xglist.gjmmzsjm = this.xglist.ysgjmmsjm * 1 + this.xglist.psgjmmsjm * 1;
        this.xglist.gjmmzsjjm = this.xglist.ysgjmmsjjm * 1 + this.xglist.psgjmmsjjm * 1;
        this.xglist.gjmmzsmm = this.xglist.ysgjmmsmm * 1 + this.xglist.psgjmmsmm * 1;
        this.xglist.ysgjmmshj = this.xglist.ysgjmmsjm * 1 + this.xglist.ysgjmmsjjm * 1 + this.xglist.ysgjmmsmm * 1;
        this.xglist.psgjmmshj = this.xglist.psgjmmsjm * 1 + this.xglist.psgjmmsjjm * 1 + this.xglist.psgjmmsmm * 1;
        this.xglist.gjmmzshj = this.xglist.psgjmmshj * 1 + this.xglist.ysgjmmshj * 1;
      } else if (index == 5) {
        this.xglist.dmzrrjjm = this.xglist.fddmzrrjjm * 1 + this.xglist.zddmzrrjjm * 1;
        this.xglist.dmzrrjm = this.xglist.fddmzrrjm * 1 + this.xglist.zddmzrrjm * 1;
        this.xglist.dmzrrm = this.xglist.fddmzrrm * 1 + this.xglist.zddmzrrm * 1;
        this.xglist.fddmzrrhj = this.xglist.fddmzrrjjm * 1 + this.xglist.fddmzrrjm * 1 + this.xglist.fddmzrrm * 1;
        this.xglist.zddmzrrhj = this.xglist.zddmzrrjjm * 1 + this.xglist.zddmzrrjm * 1 + this.xglist.zddmzrrm * 1;
        this.xglist.dmzrrhj = this.xglist.fddmzrrhj * 1 + this.xglist.zddmzrrhj * 1;
      } else if (index == 6) {
        this.xglist.dmsqxczshj = this.xglist.dmsqxczsjjm * 1 + this.xglist.dmsqxczsjm * 1 + this.xglist.dmsqxczsm * 1;
        this.xglist.dmsqxzshj = this.xglist.dmsqxzsjjm * 1 + this.xglist.dmsqxzsjm * 1 + this.xglist.dmsqxzsm * 1;
      }
    },
  },
  watch: {},
};
</script>

<style scoped>
.bg_con {
  width: 100%;
}

.daochu {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.dabg {
  /* margin-top: 10px; */
  box-shadow: 0px 2px 12px 0px rgba(9, 70, 255, 0.13);
  border-radius: 8px;
  width: 100%;
}

.xmlb-title {
  line-height: 60px;
  width: 100%;
  padding-left: 10px;
  height: 60px;
  background: url(../../assets/background/bg-02.png) no-repeat left;
  background-size: 100% 100%;
  text-indent: 10px;
  /* margin: 0 20px; */
  color: #0646bf;
  font-weight: 700;
}

.fhsy {
  display: inline-block;
  width: 120px;
  margin-top: 10px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-left: 30px;
  padding-top: 4px;
  float: right;
  background: url(../../assets/icons/zzjg_icon1.png) no-repeat center;
  background-size: 100% 100%;
}

.item_button {
  height: 100%;
  float: left;
  padding-left: 10px;
  line-height: 50px;
}

.select_wrap {
  /* //padding: 5px; */

  .select_wrap_content {
    float: left;
    width: 100%;
    line-height: 50px;
    /* // padding-left: 20px; */
    /* // padding-right: 20px; */
    height: 100%;
    background: rgba(255, 255, 255, 0.7);

    .item_label {
      padding-left: 10px;
      height: 100%;
      float: left;
      line-height: 50px;
      font-size: 1em;
    }
  }
}

.mhcx1 {
  margin-top: 0px;
}

.widths {
  width: 6vw;
}

.widthx {
  width: 8vw;
}

.cd {
  width: 184px;
}

.input-tou {
  margin-bottom: 10px;
}

:deep(.el-form--inline) .el-form-item {
  margin-right: 9px;
}

.mhcx :deep(.el-form-item) {
  /* margin-top: 5px; */
  margin-bottom: 5px;
}
</style>