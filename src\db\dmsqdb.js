import db from "./adapter/zczpAdaptor";

// 获取所有dm授权信息(上报数据自选模式专用)
export const getAllDmsqZxms = () => {
	return db.get('dmsq_list').cloneDeep().value()
}

//定密授权-----------------------------------定密授权初始化列表********
export const getdmsq = (params) => {
  console.log('getdmsq', params)
	let page = params.page;
	let pagesize = params.pageSize;
  //
	let bsqjg = params.bsqjg
	let dmqx = params.dmqx
	let nd = params.nd
  //
	let list_total = db.get("dmsq_list").sortBy("cjsj").filter(function(item) {
			return item;
	}).cloneDeep().value();

  // 模糊查询过滤
  if(bsqjg) {
    list_total = list_total.filter(item => {
      if(item.bsqjg.indexOf(bsqjg) != -1) {
        return item
      }
    })
  }
  if(dmqx) {
    list_total = list_total.filter(item => {
      if(item.dmqx == dmqx) {
        return item
      }
    })
  }
  if(nd) {
    list_total = list_total.filter(item => {
      if(item.nd == nd) {
        return item
      }
    })
  }
	// 手动分页
	console.log(pagesize * (page - 1), pagesize * (page - 1) + pagesize);
	let pageList = list_total.slice(pagesize * (page - 1),pagesize * (page - 1) + pagesize);

	let resList = {
		list: pageList,
		list_total: list_total,
		total: list_total.length,
	};
	console.log("定密授权", resList);
	return resList;
};
//定密授权-----------------------------------定密授权添加成员********
export const adddmsq = (params) => {
	let sjc = new Date().getTime();
	params.cjsj = sjc;
	params.gxsj = sjc;
	db.read().get("dmsq_list").push(params).write();
};
//定密授权-----------------------------------定密授权删除成员********
export const deletedmsq = (params) => {
	db.read().get("dmsq_list").remove(params).write();
};

//修改
export const revisedmsq = (params) => {
	let dmsqid = params.dmsqid;
	console.log("dmsqid", dmsqid);
	if (!dmsqid || dmsqid == "") {
		return;
	}
	params.gxsj = new Date().getTime();
	// 全字段更新方法(传进来的字段都会更新)
	db.read().get("dmsq_list").find({
		dmsqid: dmsqid
	}).assign(params).write();
};
