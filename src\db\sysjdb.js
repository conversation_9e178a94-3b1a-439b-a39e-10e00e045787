import db from './adapter/zczpAdaptor'

//保密制度-----------------------------------保密制度初始化列表********
export const getSysj = () => {
	let bmzd = db.get('Bmzd_list').sortBy('cjsj').filter(function(item) {
			return item
	}).cloneDeep().value()
	let zzjg = db.get('zzjg_list').sortBy('cjsj').filter(function(item) {
			return item
	}).cloneDeep().value()
	let smgw = db.get('Smgwgl_list').sortBy('cjsj').filter(function(item) {
			return item
	}).cloneDeep().value()
	let smry = db.get('Smry_list').sortBy('cjsj').filter(function(item) {
		if (item.zgzt == 1) {
			return item
		}
			
	}).cloneDeep().value()
	let smcs = db.get('Csgl_list').sortBy('cjsj').filter(function(item) {
			return item
	}).cloneDeep().value()
	let smsb = db.get('Smjsj_list').sortBy('cjsj').filter(function(item) {
			return item
	}).cloneDeep().value()
	let jypx = db.get('Pxqd_list').sortBy('cjsj').filter(function(item) {
			return item
	}).cloneDeep().value()
	let smzt = db.get('Smzttz_list').sortBy('cjsj').filter(function(item) {
			return item
	}).cloneDeep().value()
	
	// 手动分页
	let resList = {
		"bmzd": bmzd.length,
		"zzjg": zzjg.length,
		"smgw": smgw.length,
		"smry": smry.length,
		"smcs": smcs.length,
		"smsb": smsb.length,
		"jypx": jypx.length,
		"smzt": smzt.length,
	}
	return resList
}