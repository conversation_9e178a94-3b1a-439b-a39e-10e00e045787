export default [
  {
    name: 'xggj',
    path: '/xggj',
    component: () => import('../xggj.vue'),
    meta: {
      name: '相关工具',
      icon: 'aaa',
      hidden: false,
      showHeaderMenu: true,
      showAsideMenu: true,
      menuList: ['/xggj1', '/xggj2'],
    },
  },
  {
    name: 'xggj1',
    path: '/xggj1',
    component: () => import('../xggj1.vue'),
    meta: {
      name: '相关工具1',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true,
    },
  },
  {
    name: 'xggj2',
    path: '/xggj2',
    component: () => import('../xggj2.vue'),
    meta: {
      name: '相关工具2',
      icon: 'aaa',
      hidden: true,
      showHeaderMenu: true,
      showAsideMenu: true,
    },
  }
]
