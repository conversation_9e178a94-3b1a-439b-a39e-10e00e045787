import db from "./adapter/zczpAdaptor";

//保密制度-----------------------------------保密制度初始化列表********
export const getRyxz = (params) => {
  let page = params.page;
  let pagesize = params.pageSize;
  let sgsj = params.sgsj;
  let list_total = db
    .get("Ryxz_list")
    .sortBy("cjsj")
    .filter(function (item) {
      //没有时间
      if (sgsj === undefined || sgsj == null) {
        console.log("时间为空");
        return item;
        console.log("全都没有", item);
      } else if (sgsj) {
        if (item.sgsj) {
          // console.log('时间存在'+item.sgsj);
          // console.log('时间存在'+sgsj[0]);
          // console.log('时间存在'+sgsj[1]);
          if (item.sgsj >= sgsj[0] && item.sgsj <= sgsj[1]) {
            return item;
          }
        }
      }
    })
    .cloneDeep()
    .value();

  // 手动分页
  console.log(pagesize * (page - 1), pagesize * (page - 1) + pagesize);
  let pageList = list_total.slice(
    pagesize * (page - 1),
    pagesize * (page - 1) + pagesize
  );

  let resList = {
    list: pageList,
    list_total: list_total,
    total: list_total.length,
  };
  console.log("保密制度", resList);
  return resList;
};
//保密制度-----------------------------------保密制度添加成员********
export const addRyxz = (params) => {
  let sfzhm = params.sfzhm;
  let message = 0;
  console.log(sfzhm);
  let sjc = new Date().getTime();
  params.cjsj = sjc;
  params.gxsj = sjc;
  let pd = db
    .read()
    .get("Smry_list")
    .find({ sfzhm: sfzhm })
    .cloneDeep()
    .value();
  if (pd == undefined) {
    db.read().get("Smry_list").push(params).write();
    db.read().get("Ryxz_list").push(params).write();
    message = 1;
  } else {
    message = 2;
  }
  return message;
};
//保密制度-----------------------------------保密制度删除成员********
export const deleteRyxz = (params) => {
  db.read().get("Ryxz_list").remove(params).write();
};
