import db from './adapter/zczpAdaptor'

//登录信息-------------------------------------------------------------------
export const getpxqdry = (params) => {
	console.log(params);
	let bm
	if (params.bm != undefined && params.bm != '') {
		bm = params.bm.join('/')
	}
	
	// let page = params.page
	// let pagesize = params.pageSize
	let list_total = db.get('Smry_list').sortBy('cjsj').filter(function(item) {
		// console.log(item);
		if (item.zgzt == 1) {
			if(bm === undefined || bm ==''){
				return item
			}else if(bm){
				if(item.bm){
					if(item.bm.indexOf(bm) != -1){
						return item
					}
				}
			}
		}
		
	}).cloneDeep().value()
	// let pageList = list_total.slice(pagesize * (page - 1), pagesize * (page - 1) + pagesize)
	
	let resList = {
		// "list": pageList,
		"list_total": list_total,
		"total": list_total.length
	}
	console.log('保密制度', resList)
	return resList
	
}
