import DB from './adapter/zczpAdaptor'
import SystemDB from './adapter/zczpSystemAdaptor'

import { getUuid } from '../utils/getUuid'

import { getScrw, getDwpfjlListByDxXx, getDwpfjlId } from './zczpCommunal'

// 组织机构
import { selectAllZzjgByZzjgid } from './zzjgdb'

// 单位信息
import { selectDwxxByDwid } from './dwxxDb'

// 涉密人员
import { getsmry } from './smrydb'

/*****
 * 部门
 * ******/
// 获取部门详细自查记录字典
export const getBmxxzcjlZD = () => {
  const dxList = DB.get('bmzcdx_list').cloneDeep().value()
  const resDxList = []
  dxList.forEach((dx) => {
    const scnrList = DB.get('bmzcnr_list')
      .filter({ dxid: dx.dxid })
      .cloneDeep()
      .value()
    scnrList.forEach((scnr) => {
      scnr.dxmc = dx.dxmc
      scnr.sfsynr = true
      resDxList.push(scnr)
    })
  })
  return resDxList
}

// //通过历史id获取部门详细自查记录，如果没有部门详细自查记录则返回空数据
// export const getBmxxzcjlByLsid = (ccdnsjgid) => {
//   const zdList = getBmxxzcjlZD()
//   const zcjlList = db
//     .get('bmxxzcjl_list')
//     .filter({ ccdnsjgid: ccdnsjgid })
//     .cloneDeep()
//     .value()
//   if (zcjlList === undefined || zcjlList.length < 1) {
//     return []
//   }
//   zdList.forEach((zd) => {
//     zcjlList.forEach((zcjl) => {
//       if (zd.nrid == zcjl.nrid) {
//         zd.nsjgpfjlid = zcjl.nsjgpfjlid
//         zd.ccdnsjgid = zcjl.ccdnsjgid
//         zd.ykf = zcjl.ykf
//         zd.sfsynr = zcjl.sfsynr
//         zd.synr = zcjl.synr
//         zd.kfsm = zcjl.kfsm
//       }
//     })
//   })
//   // console.log(zdList)
//   return zdList
// }

// 插入或更新内设机构评分记录表
export const insertUpdateNsjgpfjlByCcdnsjgid = (dxNrList, ccdnsjgid) => {
  let nsjgpfjlid
  let nsjgpfjl
  dxNrList.forEach((item) => {
    nsjgpfjlid = item.nsjgpfjlid
    if (nsjgpfjlid) {
      nsjgpfjl = DB.get('nsjgpfjl_list')
        .find({ nsjgpfjlid: nsjgpfjlid })
        .cloneDeep()
        .value()
      if (nsjgpfjl) {
        // 更新
        DB.get('nsjgpfjl_list')
          .find({ nsjgpfjlid: nsjgpfjlid })
          .assign({
            ykf: item.ykf,
            sfsynr: item.sfsynr,
            synr: item.synr,
            kfsm: item.kfsm,
          })
          .write()
        return
      }
    }
    // 新增
    DB.get('nsjgpfjl_list')
      .push({
        nsjgpfjlid: getUuid(),
        ccdnsjgid: ccdnsjgid,
        nrid: item.nrid,
        ykf: item.ykf,
        sfsynr: item.sfsynr,
        synr: item.synr,
        kfsm: item.kfsm,
      })
      .write()
  })
  return true
}

// 更新 抽查的内设机构表(ccdnsjg_list)
export const updateCcdnsjgById = (params) => {
  DB.get('ccdnsjg_list')
    .find({ ccdnsjgid: params.ccdnsjgid })
    .assign({
      zt: params.zt,
    })
    .write()
  return true
}

// 通过[抽查内设机构流水ID]获取 内设机构评分记录历史信息
export const selectNsjgpfjlListByCcdnsjgid = (ccdnsjgid) => {
  console.log('准备获取内设机构评分记录历史信息', ccdnsjgid)
  // 获取字典信息
  let zdxxList = getBmxxzcjlZD()
  // 获取历史信息
  let oldList = DB.get('nsjgpfjl_list')
    .filter({ ccdnsjgid: ccdnsjgid })
    .cloneDeep()
    .value()
  //
  zdxxList.forEach((item) => {
    oldList.forEach((oldItem) => {
      if (item.nrid == oldItem.nrid) {
        item.nsjgpfjlid = oldItem.nsjgpfjlid
        item.ykf = oldItem.ykf
        item.sfsynr = oldItem.sfsynr
        item.synr = oldItem.synr
        item.kfsm = oldItem.kfsm
      }
    })
  })
  return zdxxList
}

/*****
 * 单位检查
 * ******/
//获取单位检查字典
export const getDwxxzcjlZD = () => {
  const dwdxList = DB.get('dwzcdx_list').cloneDeep().value()
  dwdxList.forEach((dx) => {
    dx.xx = []
    const dwxxList = DB.get('dwzcxx_list')
      .filter({ dxid: dx.dxid })
      .cloneDeep()
      .value()
    dwxxList.forEach((xx) => {
      const dwnrList = DB.get('dwzcnr_list')
        .filter({ xxid: xx.xxid })
        .cloneDeep()
        .value()
      dwnrList.forEach((dwnr) => {
        dwnr.dxid = xx.dxid
        dwnr.xxmc = xx.xxmc
        dx.xx.push(dwnr)
      })
    })
  })
  return dwdxList
}

/*****
 * 审查任务表
 * ******/
// 插入审查任务表
export const insertScrwList = (params) => {
  params.rwid = getUuid()
  let time = new Date().getTime()
  params.cjsj = time
  params.gxsj = time
  try {
    DB.get('scrw_list').push(params).write()
  } catch (error) {
    console.log(error)
    return undefined
  }
  return params.rwid
}

// 通过ID获取审查任务表
export const selectScrwByRwid = (rwid) => {
  console.log('rwid', rwid)
  let rwxx = DB.get('scrw_list').find({ rwid: rwid }).cloneDeep().value()
  return rwxx
}

// 更新审查任务状态 zt
export const updateScrwListZt = (params) => {
  if (!params) {
    console.log('[updateScrwListZt]更新审查任务失败，参数为空')
    return
  }
  if (!params.rwid) {
    console.log('[updateScrwListZt]更新审查任务失败，任务ID为空')
    return
  }
  // 加入截至时间
  params.jzsj = new Date().getTime()
  // 加入更新时间
  params.gxsj = new Date().getTime()
  //
  let scrw = DB.get('scrw_list')
    .filter((item) => {
      if (item.rwid == params.rwid) {
        return item
      }
    })
    .cloneDeep()
    .value()
  if (!scrw || scrw == []) {
    console.log('[updateScrwListZt]更新审查任务失败，任务ID查询结果为空')
    return
  }
  try {
    DB.get('scrw_list')
      .find({ rwid: params.rwid })
      .assign(getScrw(params))
      .write()
  } catch (error) {
    console.log('[updateScrwListZt]更新审查任务失败', error)
    return false
  }
  return true
}

// 更新审查任务(审查报告页使用)
export const updateScrwList = (params) => {
  if (!params) {
    console.log('[updateScrwListZt]更新审查任务失败，参数为空')
    return
  }
  if (!params.rwid) {
    console.log('[updateScrwListZt]更新审查任务失败，任务ID为空')
    return
  }
  // 加入截至时间
  params.jzsj = new Date().getTime()
  //
  let scrw = DB.get('scrw_list')
    .filter((item) => {
      if (item.rwid == params.rwid) {
        return item
      }
    })
    .cloneDeep()
    .value()
  if (!scrw || scrw == []) {
    console.log('[updateScrwListZt]更新审查任务失败，任务ID查询结果为空')
    return
  }
  try {
    DB.get('scrw_list')
      .find({ rwid: params.rwid })
      .assign(getScrw(params))
      .write()
  } catch (error) {
    console.log('[updateScrwListZt]更新审查任务失败', error)
    return false
  }
  return true
}

/*****
 * 单位评分记录表
 * ******/
// 插入或更新单位评分记录表
export const insertUpdateDwpfjlListByRwid = (dxXxList, rwid) => {
  // console.log('插入或更新单位评分记录表 rwid', rwid,'dxXxList', dxXxList)
  // return
  // 获取二维的评分记录列表list（入参dxXxList是三维的）
  const resDxXxList = getDwpfjlListByDxXx(dxXxList, rwid)

  console.log(resDxXxList)
  // 循环二维的评分记录列表，找到记录则更新，未找到则插入
  let dwpfjl
  try {
    resDxXxList.forEach((item) => {
      // console.log('[insertUpdateDwpfjlListByRwid]', item)
      if (item.dwpfjlid) {
        // 为历史记录，查询库确认库里是否有该条记录信息，查到则更新，否则删除
        dwpfjl = DB.get('dwpfjl_list')
          .find({ dwpfjlid: item.dwpfjlid })
          .cloneDeep()
          .value()
        if (dwpfjl) {
          // 更新
          dwpfjl = DB.get('dwpfjl_list')
            .find({ dwpfjlid: item.dwpfjlid })
            .assign(item)
            .write()
        } else {
          // 插入
          item.dwpfjlid = getDwpfjlId()
          item.rwid = rwid
          DB.get('dwpfjl_list').push(item).write()
        }
      } else {
        // 为新建记录，此时直接插入
        item.dwpfjlid = getDwpfjlId()
        item.rwid = rwid
        DB.get('dwpfjl_list').push(item).write()
      }
    })
  } catch (error) {
    console.log(
      '[insertUpdateDwpfjlListByRwid]单位评分记录表更新或插入失败',
      error
    )
    return false
  }
  return true
}

// 通过任务ID获取单位评分记录(组合)
export const selectDwpfjlListByRwid = (rwid) => {
  const dwpfjlList = DB.get('dwpfjl_list')
    .filter(function (item) {
      if (item.rwid == rwid) {
        return item
      }
    })
    .cloneDeep()
    .value()
  console.log('dwpfjlList', dwpfjlList)
  const dwxxzcjlZDList = getDwxxzcjlZD()
  dwxxzcjlZDList.forEach((dx) => {
    dx.xx.forEach((xx) => {
      dwpfjlList.forEach((dwxxzcjl) => {
        // console.log(dwxxzcjl.nrid==xx.nrid)
        if (dwxxzcjl.nrid == xx.nrid) {
          xx.dwpfjlid = dwxxzcjl.dwpfjlid
          xx.rwid = dwxxzcjl.rwid
          xx.ykf = dwxxzcjl.ykf
          xx.sfsynr = dwxxzcjl.sfsynr
          xx.synr = dwxxzcjl.synr
          xx.kfsm = dwxxzcjl.kfsm
        }
      })
      // console.log(xx)
    })
  })
  // console.log(dwxxzcjlZDList)
  return dwxxzcjlZDList
}

// 通过任务ID获取单位评分记录(非组合)
export const selectDwpfjlListByRwidFzh = (rwid) => {
  const dwpfjlList = DB.get('dwpfjl_list')
    .filter(function (item) {
      if (item.rwid == rwid) {
        return item
      }
    })
    .cloneDeep()
    .value()
  console.log('dwpfjlList非组合', dwpfjlList)
  return dwpfjlList
}

/*****
 * 继续自查自评
 * ******/
export const selectScrwJxzczpPage = (params) => {
  console.log('继续自查自评入参', params)
  let page = undefined
  let pageSize = undefined
  let zt = undefined
  if (params) {
    page = params.page
    pageSize = params.pageSize
    zt = params.zt
  }
  let list_total = DB.get('scrw_list')
    .filter((item) => {
      if (item.zt != zt) {
        return item
      }
    })
    .cloneDeep()
    .orderBy('kssj', 'desc')
    .value()
  // 手动分页
  let pageList = list_total
  if (page && pageSize && list_total) {
    pageList = list_total.slice(
      pageSize * (page - 1),
      pageSize * (page - 1) + pageSize
    )
  }
  // 分页完成，获取单位信息
  let dwxx
  let jcjd
  pageList.forEach((item) => {
    // 单位名称
    dwxx = DB.get('dwxx_List').find({ dwid: item.dwid }).cloneDeep().value()
    if (dwxx) {
      item.dwmc = dwxx.dwmc
    }
  })
  //
  let resList = {
    list: pageList,
    list_total: list_total,
    total: list_total.length,
  }
  console.log('继续自查自评', resList)
  return resList
}

/*****
 * 自查自评历史
 * ******/
export const selectScrwZczplsPage = (params) => {
  console.log('自查自评历史入参', params)
  let page = undefined
  let pageSize = undefined
  let zt = undefined
  let cxsj = undefined
  if (params) {
    page = params.page
    pageSize = params.pageSize
    zt = params.zt
    cxsj = params.cxsj
  }
  let list_total = DB.get('scrw_list')
    .filter({ zt: zt })
    .filter((item) => {
      // console.log('时间过滤', item)
      if (!cxsj) {
        return item
      }
      if (item.kssj > cxsj[0] && item.kssj < cxsj[1]) {
        return item
      }
    })
    .cloneDeep()
    .orderBy('kssj', 'desc')
    .value()
  // 手动分页
  let pageList = list_total
  if (page && pageSize && list_total) {
    pageList = list_total.slice(
      pageSize * (page - 1),
      pageSize * (page - 1) + pageSize
    )
  }
  // 分页完成，获取单位信息
  let dwxx
  let jcjd
  pageList.forEach((item) => {
    dwxx = DB.get('dwxx_List').find({ dwid: item.dwid }).cloneDeep().value()
    if (dwxx) {
      item.dwmc = dwxx.dwmc
    }
  })
  //
  let resList = {
    list: pageList,
    list_total: list_total,
    total: list_total.length,
  }
  console.log('自查自评历史', resList)
  return resList
}

/*****
 * 抽查的内设机构表
 * ******/
export const selectCcdnsjgListByRwid = (params) => {
  console.log('抽查的内设机构入参', params)
  let page = undefined
  let pageSize = undefined
  if (params) {
    page = params.page
    pageSize = params.pageSize
  }
  let list_total = DB.get('ccdnsjg_list')
    .filter({ rwid: params.rwid })
    .cloneDeep()
    .sortBy('zt')
    .value()
  // 手动分页
  let pageList = list_total
  if (page && pageSize && list_total) {
    pageList = list_total.slice(
      pageSize * (page - 1),
      pageSize * (page - 1) + pageSize
    )
  }
  // 获取组织机构的详细信息
  let zzjg
  pageList.forEach((item) => {
    zzjg = selectAllZzjgByZzjgid(item.zzjgid)
    if (zzjg) {
      item.zzjgmc = zzjg.label
    }
  })
  //
  let resList = {
    list: pageList,
    list_total: list_total,
    total: list_total.length,
  }
  console.log('抽查的内设机构', resList)
  return resList
}

// 插入抽查的内设机构列表中(批量)
export const insertCcdnsjgList = (list, rwid) => {
  if (!list || list.length < 0) {
    console.log('插入抽查的内设机构列表(批量)失败，组织机构集合为空', list)
    return
  }
  if (!rwid) {
    console.log('插入抽查的内设机构列表(批量)失败，任务ID为空', rwid)
    return
  }
  list.forEach((item) => {
    item.rwid = rwid
    insertCcdnsjg(item)
  })
  return true
}

// 插入抽查的内设机构列表中(单个)
export const insertCcdnsjg = (params) => {
  let rwid = params.rwid
  let zzjgid = params.bmm
  if (!rwid) {
    console.log('内设机构表插入失败，任务ID为空', params)
    return
  }
  if (!zzjgid) {
    console.log('内设机构表插入失败，组织机构ID为空', params)
    return
  }
  let list = DB.get('ccdnsjg_list')
    .filter((item) => {
      if (item.rwid == rwid && item.zzjgid == zzjgid) {
        return item
      }
    })
    .cloneDeep()
    .value()
  if (list && list.length > 0) {
    // 找到该记录，不需要任何操作
    return
  }
  DB.get('ccdnsjg_list')
    .push({
      ccdnsjgid: getUuid(),
      rwid: rwid,
      zzjgid: zzjgid,
      zt: 0,
    })
    .write()
}
// 通过抽查的内设机构流水ID移除抽查的内设机构表
export const deleteCcdnsjgListByID = (ccdnsjgid) => {
  console.log('deleteCcdnsjgListByID params', ccdnsjgid)
  let bool = false
  if (!ccdnsjgid) {
    console.log(
      '通过抽查的内设机构流水ID移除抽查的内设机构表[ID为空]',
      ccdnsjgid
    )
    return bool
  }
  try {
    DB.get('ccdnsjg_list').remove({ ccdnsjgid: ccdnsjgid }).write()
    bool = true
  } catch (error) {
    bool = false
  }
  // 通过抽查的内设机构流水ID移除内设机构评分记录表
  if (bool) {
    bool = deleteNsjgpfjlByCcdnsjgid(ccdnsjgid)
  }
  return bool
}
// 通过抽查的内设机构流水ID获取抽查的组织机构信息
export const selectZzjgByCcdnsjgid = (ccdnsjgid) => {
  console.log('ccdnsjgid', ccdnsjgid)
  const ccdnsjg = DB.get('ccdnsjg_list')
    .find({ ccdnsjgid: ccdnsjgid })
    .cloneDeep()
    .value()
  return DB.get('zzjg_list').find({ bmm: ccdnsjg.zzjgid }).cloneDeep().value()
}

// 通过任务ID获取抽查的内设机构抽查结果
export const selectCcdsjgListCcjg = (rwid) => {
  console.log('selectCcdsjgListCcjg rwid', rwid)
  let ccdnsjgList = DB.get('ccdnsjg_list')
    .filter({ rwid: rwid })
    .cloneDeep()
    .value()
  console.log('selectCcdsjgListCcjg ccdnsjgList', ccdnsjgList)
  let nsjgpfjlList
  let zzjg
  ccdnsjgList.forEach((item) => {
    // 默认100
    item.df = 100
    // 获取内设机构评分记录
    nsjgpfjlList = DB.get('nsjgpfjl_list')
      .filter({ ccdnsjgid: item.ccdnsjgid })
      .cloneDeep()
      .value()
    nsjgpfjlList.forEach((nsjg) => {
      if (nsjg.ykf) {
        item.df -= nsjg.ykf
      }
    })
    // 获取组织机构名称
    zzjg = DB.get('zzjg_list').find({ bmm: item.zzjgid }).cloneDeep().value()
    Object.assign(item, zzjg)
  })
  return ccdnsjgList
}

/*****
 * 内设机构评分表
 * ******/
export const deleteNsjgpfjlByCcdnsjgid = (ccdnsjgid) => {
  try {
    DB.get('nsjgpfjl_list').remove({ ccdnsjgid: ccdnsjgid }).write()
  } catch (error) {
    console.log(error)
    return false
  }
  return true
}

/*****
 * 其他
 * ******/
export const selectRwxxDwxxCcnsjgxxByRwidCcdnsjgid = (params) => {
  // console.log('params', params)
  let rwxx = selectScrwByRwid(params.rwid)
  // console.log('rwxx', rwxx)
  let dwxx = selectDwxxByDwid(rwxx.dwid)
  // console.log('dwxx', dwxx)
  let zzjgxx = selectZzjgByCcdnsjgid(params.ccdnsjgid)
  // console.log('zzjgxx', zzjgxx)
  return {
    rwmc: rwxx.rwmc,
    dwmc: dwxx.dwmc,
    zzjgmc: zzjgxx.label,
  }
}

/*****
 * 抽查的人员
 * ******/
// 获取抽查的部门集合（分页）
export const selectCcdryListPage = (params) => {
  let page = undefined
  let pageSize = undefined
  let rwid = undefined
  if (params) {
    page = params.page
    pageSize = params.pageSize
    rwid = params.rwid
  }
  let list_total = DB.get('ccdry_list')
    .filter((item) => {
      // console.log('item', item)
      if (item.rwid == rwid) {
        return item
      }
    })
    .cloneDeep()
    .orderBy('zt', 'desc')
    .value()
  // 手动分页
  let pageList = list_total
  if (page && pageSize && list_total) {
    pageList = list_total.slice(
      pageSize * (page - 1),
      pageSize * (page - 1) + pageSize
    )
  }
  // 分页完成，获取人员信息
  let ryxx
  pageList.forEach((item) => {
    // console.log('获取人员信息', item)
    ryxx = DB.get('Smry_list').find({ smryid: item.smryid }).cloneDeep().value()
    if (ryxx) {
      item.xm = ryxx.xm
      item.bm = ryxx.bm
      item.zw = ryxx.zw
    }
  })
  //
  let resList = {
    list: pageList,
    list_total: list_total,
    total: list_total.length,
  }
  console.log('获取抽查的部门集合（分页）', resList)
  return resList
}

// 插入抽查的人员列表中(批量)
export const insertCcdryList = (list, rwid) => {
  if (!list || list.length < 0) {
    console.log('插入抽查的人员列表(批量)失败，涉密人员集合为空', list)
    return
  }
  if (!rwid) {
    console.log('插入抽查的人员列表(批量)失败，任务ID为空', rwid)
    return
  }
  list.forEach((item) => {
    item.rwid = rwid
    insertCcdnry(item)
  })
  return true
}

// 插入抽查的人员列表中(单个)
export const insertCcdnry = (params) => {
  console.log('插入抽查的人员列表中(单个)', params)
  let rwid = params.rwid
  let smryid = params.smryid
  if (!rwid) {
    console.log('抽查的人员表插入失败，任务ID为空', params)
    return
  }
  if (!smryid) {
    console.log('抽查的人员表插入失败，涉密人员ID为空', params)
    return
  }
  let list = DB.get('ccdry_list')
    .filter((item) => {
      if (item.rwid == rwid && item.smryid == smryid) {
        return item
      }
    })
    .cloneDeep()
    .value()
  if (list && list.length > 0) {
    // 找到该记录，不需要任何操作
    return
  }
  DB.get('ccdry_list')
    .push({
      ccdryid: getUuid(),
      rwid: rwid,
      smryid: smryid,
      zt: 0,
    })
    .write()
}
// 通过抽查的人员流水ID移除抽查的人员表
export const deleteCcdryListByID = (ccdryid) => {
  console.log('deleteCcdryListByID params', ccdryid)
  let bool = false
  if (!ccdryid) {
    console.log('通过抽查的人员流水ID移除抽查的人员表[ID为空]', ccdryid)
    return bool
  }
  try {
    DB.get('ccdry_list').remove({ ccdryid: ccdryid }).write()
    bool = true
  } catch (error) {
    bool = false
  }
  // 通过抽查的人员流水ID移除人员评分记录表
  if (bool) {
    bool = deleteRyfjlByCcdryid(ccdryid)
  }
  return bool
}

// 通过抽查的人员流水ID获取抽查的人员信息
export const selectCcdryxxByCcdryid = (params) => {
  let ccdryid = params.ccdryid
  let ccdryxx = DB.get('ccdry_list')
    .find({ ccdryid: ccdryid })
    .cloneDeep()
    .value()
  let smryxx = DB.get('Smry_list')
    .find({ smryid: ccdryxx.smryid })
    .cloneDeep()
    .value()
  Object.assign(ccdryxx, smryxx)
  return ccdryxx
}

// 通过任务ID获取抽查的人员抽查结果
export const selectCcdryListCcjg = (rwid) => {
  // 获取抽查的人员列表
  let ryList = DB.get('ccdry_list').filter({ rwid: rwid }).cloneDeep().value()
  console.log('ryList', ryList)
  // 涉密人员查询结果
  let smry
  // 组织机构查询结果
  let zzjg
  // 职务查询结果
  let zw
  // 人员评分记录集合
  let rypfjlList
  // 判断人员是否全部符合要求（临时变量）
  let rysfhg
  //
  ryList.forEach((item) => {
    // 获取人员姓名
    smry = DB.get('Smry_list').find({ smryid: item.smryid }).cloneDeep().value()
    if (smry) {
      item.xm = smry.xm
      // 获取部门
      item.zzjgmc = smry.bm
      // if (smry.bmid) {
      //   zzjg = DB.get('zzjg_list')
      //     .find({ zzjgid: smry.bmid })
      //     .cloneDeep()
      //     .value()
      //   if (zzjg) {
      //     item.zzjgmc = zzjg.zzjgmc
      //   }
      // }
      // 获取职务
      item.zwmc = smry.zw
      // if (smry.zwid) {
      //   zw = DB.get('zw_list').find({ zwid: smry.zwid }).cloneDeep().value()
      //   if (zw) {
      //     item.zwmc = zw.zwmc
      //   }
      // }
    }
    // 计算结果（全部符合要求为合格，否则不合格）
    rysfhg = true
    rypfjlList = DB.get('rypfjl_list')
      .filter({ ccdryid: item.ccdryid })
      .cloneDeep()
      .value()
    rypfjlList.forEach((rypfjl) => {
      if (!rypfjl.sffhyq) {
        rysfhg = false
      }
    })
    item.jg = '合格'
    if (!rysfhg) {
      item.jg = '不合格'
    }
  })
  return ryList
}

/*****
 * 人员评分记录表
 * ******/
export const deleteRyfjlByCcdryid = (ccdryid) => {
  try {
    DB.get('rypfjl_list').remove({ ccdryid: ccdryid }).write()
  } catch (error) {
    console.log(error)
    return false
  }
  return true
}

//获取人员详细自查记录字典
export const getRyzcxxjlZD = () => {
  const dxList = DB.get('ryzcdx_list').cloneDeep().value()
  const resDxList = []
  dxList.forEach((dx) => {
    const scnrList = DB.get('ryzcnr_list')
      .filter({ dxid: dx.dxid })
      .cloneDeep()
      .value()
    scnrList.forEach((scnr) => {
      scnr.dxmc = dx.dxmc
      resDxList.push(scnr)
    })
  })
  return resDxList
}
// 插入或更新人员评分记录表
export const insertUpdateRyjlByCcdnryid = (dxNrList, ccdryid) => {
  let rypfjlid
  let rypfjl
  dxNrList.forEach((item) => {
    rypfjlid = item.rypfjlid
    // console.log(item)
    if (rypfjlid) {
      rypfjl = DB.get('rypfjl_list')
        .find({ rypfjlid: rypfjlid })
        .cloneDeep()
        .value()
      if (rypfjl) {
        // 更新
        DB.get('rypfjl_list')
          .find({ rypfjlid: rypfjlid })
          .assign({
            sffhyq: item.sffhyq,
            bzsm: item.bzsm,
          })
          .write()
        return
      }
    }
    // 新增
    DB.get('rypfjl_list')
      .push({
        rypfjlid: getUuid(),
        ccdryid: ccdryid,
        nrid: item.nrid,
        sffhyq: item.sffhyq,
        bzsm: item.bzsm,
      })
      .write()
  })
  return true
}

// 通过[抽查人员流水ID]获取 人员评分记录历史信息
export const selectRypfjlListByCcdryid = (ccdryid) => {
  // 获取字典信息
  let zdxxList = getRyzcxxjlZD()
  // 获取历史信息
  let oldList = DB.get('rypfjl_list')
    .filter({ ccdryid: ccdryid })
    .cloneDeep()
    .value()
  //
  // console.log('oldList', oldList, ccdryid)
  zdxxList.forEach((item) => {
    oldList.forEach((oldItem) => {
      if (item.nrid == oldItem.nrid) {
        item.rypfjlid = oldItem.rypfjlid
        item.sffhyq = oldItem.sffhyq
        item.bzsm = oldItem.bzsm
      }
    })
    // console.log(item)
  })
  return zdxxList
}

// 更新 抽查的人员表(ccdry_list)
export const updateCcdryById = (params) => {
  DB.get('ccdry_list')
    .find({ ccdryid: params.ccdryid })
    .assign({
      zt: params.zt,
    })
    .write()
  return true
}
