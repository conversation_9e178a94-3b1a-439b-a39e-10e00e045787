/**
 * log文件操作工具类
 */
const FS = require('fs')
const readline = require('readline')

//
import { Notification } from 'element-ui'
//
import {
  dateFormatNYR,
  dateFormat,
  dateFormatNY,
  dateFormatChinese,
} from '../utils/moment'

import { getWindowLocation } from '../utils/windowLocation'

import {
  // 获取log文件保存路径
  getLogFileSavePath,
  // 创建目录（如果给的文件路径，则自动忽略文件，只创建路径）
  createDirectory,
} from './pathUtil'

import { checkArr, checkString } from './utils'

import { selectScrwByRwid } from '../db/zczpdb'

// //
// import { getWindowLocation } from '../utils/windowLocation'

// // log日志监测代理白名单
// const logMonitorProxyKeyWhiteList = [
//   'constructor',
//   'length',
//   'get',
//   '__chain__',
//   '__wrapped__',
//   '__actions__'
// ]
// /**
//  * 写入日志 （配合代理专用）
//  * target: 表数组对象(整个表的内容)
//  * key: 字段名称
//  * options: 操作方式，0 获取，1 修改
//  * value: 修改后的值(value只有修改的时候)
// */
// export const writeLog = (target, key, options, value) => {
//   let time = new Date().getTime()
//   let local = getWindowLocation()
//   if (Object.prototype.toString.call(key) != '[object String]' || logMonitorProxyKeyWhiteList.indexOf(key) != -1) {
//     return
//   }
//   // console.log('options', options)
//   if(options == 0) {
//     console.log(time,local.yhm,'获取', key, target[key])
//   } else {
//     console.log(time,local.yhm,'修改', key, target, value)
//   }
// }

// /**
//  * 参数说明
//  * obj = {
//  * // 业务数据
//  * bussData: {},
//  * // 系统数据
//  * sysData: {}
//  * }
//  */
// export const writeLog = (obj) => {
//   // 获取日志文件路径
//   let logSavePath
//   try {
//     logSavePath = getLogFileSavePath()
//   } catch (error) {
//     Notification.error({
//       title: '系统异常',
//       message: '系统无法获取或解析日志目录，请确认系统日志目录是否正确填写',
//     })
//     return
//   }
//   console.log('logSavePath', logSavePath)
//   // 创建日志文件路径
//   try {
//     createDirectory(logSavePath)
//   } catch (error) {
//     console.log('创建日志所属目录失败', error)
//     Notification.error({
//       title: '系统异常',
//       message: '系统无法创建日志目录，请确认系统日志目录是否具有读写权限',
//     })
//     return
//   }
//   // 解析数据生成日志文件格式
//   let logData = ''
//   let bussData = obj.bussData
//   logData +=
//     '\n[' +
//     dateFormat(new Date()) +
//     ']' +
//     '[' +
//     bussData.account +
//     ']' +
//     '[' +
//     bussData.type +
//     ']' +
//     '[' +
//     bussData.page +
//     ']' +
//     '[' +
//     bussData.action +
//     ']' +
//     '\n'
//   let sysData = obj.sysData
//   Object.keys(sysData).forEach((item) => {
//     console.log(item)
//     logData += '[系统参数]' + '[' + item + ']' + sysData[item] + '\n'
//   })
//   try {
//     // 生成日志文件完整路径（按天生成）并追加内容
//     logSavePath += 'zczp-log-' + dateFormatNYR(new Date()) + '.log'
//     FS.appendFileSync(logSavePath, logData)
//   } catch (error) {
//     Notification.error({
//       title: '系统异常',
//       message: '系统日志写入异常，请确认系统日志目录是否具有读写权限',
//     })
//     return
//   }
// }

/**
 * 写入操作日志(按天生成)
 * xyybs: 页面标识，详情参见应用标识参照表
 * ymngnmc: 页面内功能名称（新增、删除、修改）
 * cznr: 操作内容（即当前操作的数据，只接收JSON对象或数组）
 */
export const writeOptionsLog = (xyybs, ymngnmc, cznr) => {
  // 获取日志文件路径
  let logSavePath
  try {
    logSavePath = getLogFileSavePath()
  } catch (error) {
    Notification.error({
      title: '系统异常[操作日志]',
      message: '系统无法获取或解析日志目录，请确认系统日志目录是否正确填写',
    })
    return
  }
  console.log('logSavePath', logSavePath)
  // 创建日志文件路径
  try {
    createDirectory(logSavePath)
  } catch (error) {
    console.log('创建日志所属目录失败', error)
    Notification.error({
      title: '系统异常[操作日志]',
      message: '系统无法创建日志目录，请确认系统日志目录是否具有读写权限',
    })
    return
  }
  // 获取当前用户信息
  let localObj = getWindowLocation()
  if (!localObj) {
    Notification.error({
      title: '系统异常[操作日志]',
      message: '未能检测到登录用户信息',
    })
    return
  }
  // 解析数据生成日志文件格式
  let logData = ''
  logData +=
    '[' +
    dateFormat(new Date()) +
    ']' +
    '[' +
    localObj.yhm +
    ']' +
    '[' +
    localObj.xm +
    ']' +
    '[' +
    xyybs +
    ']' +
    '[' +
    ymngnmc +
    ']'
  if (Object.prototype.toString.call(cznr) == '[object String]') {
    logData += '>>>' + cznr + '\n'
  } else {
    logData += '>>>' + JSON.stringify(cznr) + '\n'
  }
  try {
    // 生成日志文件完整路径（按天生成）并追加内容
    logSavePath +=
      'secret keeper-log-options-' + dateFormatNYR(new Date()) + '.log'
    FS.appendFileSync(logSavePath, logData)
  } catch (error) {
    Notification.error({
      title: '系统异常[操作日志]',
      message: '系统日志写入异常，请确认系统日志目录是否具有读写权限',
    })
    return
  }
}

/**
 * 写入登录日志(按天生成)
 * loginType: 0-登录 1-登出
 * yhlx: 0-超级管理员 1-三员中的其他二员 3-普通用户
 * extraParams: 额外参数只接收对象，且不能包含中括号[]
 */
const loginTypeArr = ['登录', '登出']
const yhlxArr = ['超级管理员', '三员中的其他二员', '', '普通用户']
export const writeLoginLog = (loginType) => {
  // 获取日志文件路径
  let logSavePath
  try {
    logSavePath = getLogFileSavePath()
  } catch (error) {
    console.log(error)
    Notification.error({
      title: '系统异常[登录日志]',
      message: '系统无法获取或解析日志目录，请确认系统日志目录是否正确填写',
    })
    return
  }
  console.log('logSavePath', logSavePath)
  // 创建日志文件路径
  try {
    createDirectory(logSavePath)
  } catch (error) {
    console.log('创建日志所属目录失败', error)
    Notification.error({
      title: '系统异常[登录日志]',
      message: '系统无法创建日志目录，请确认系统日志目录是否具有读写权限',
    })
    return
  }
  // 获取当前用户信息
  let localObj = getWindowLocation()
  if (!localObj) {
    Notification.error({
      title: '系统异常[登录日志]',
      message: '未能检测到登录用户信息',
    })
    return
  }
  // 解析数据生成日志文件格式
  let logData = ''
  logData +=
    '[' +
    dateFormat(new Date()) +
    ']' +
    '[' +
    localObj.yhid +
    ']' +
    '[' +
    localObj.yhm +
    ']' +
    '[' +
    localObj.xm +
    ']'
  if (yhlxArr[localObj.yhlx]) {
    logData += '[' + yhlxArr[localObj.yhlx] + ']'
  } else {
    logData += '[' + '未知用户类型' + ']'
  }
  //
  logData += '\n'
  try {
    // 生成日志文件完整路径（按天生成）并追加内容
    logSavePath +=
      'secret keeper-log-login-' + dateFormatNYR(new Date()) + '.log'
    FS.appendFileSync(logSavePath, logData)
  } catch (error) {
    Notification.error({
      title: '系统异常[登录日志]',
      message: '系统日志写入异常，请确认系统日志目录是否具有读写权限',
    })
    return
  }
}

/**
 * 写入轨迹日志(按月生成)
 */
export const writeTrajectoryLog = (params) => {
  if (!params) {
    console.log('写入轨迹日志失败，参数为空', params)
    return
  }
  let xyybs = params.xyybs
  let id = params.id
  let ymngnmc = params.ymngnmc
  let extraParams = params.extraParams
  if (!xyybs) {
    console.log('写入轨迹日志失败，参数小应用标识为空', xyybs)
    return
  }
  if (!id) {
    console.log('写入轨迹日志失败，参数数据ID为空', id)
    return
  }
  if (!ymngnmc) {
    console.log('写入轨迹日志失败，参数用户功能名称为空', ymngnmc)
    return
  }
  // 获取日志文件路径
  let logSavePath
  try {
    logSavePath = getLogFileSavePath()
  } catch (error) {
    Notification.error({
      title: '系统异常[轨迹日志]',
      message: '系统无法获取或解析日志目录，请确认系统日志目录是否正确填写',
    })
    return
  }
  console.log('logSavePath', logSavePath)
  // 创建日志文件路径
  try {
    createDirectory(logSavePath)
  } catch (error) {
    console.log('创建日志所属目录失败', error)
    Notification.error({
      title: '系统异常[轨迹日志]',
      message: '系统无法创建日志目录，请确认系统日志目录是否具有读写权限',
    })
    return
  }
  // 获取当前用户信息
  let localObj = getWindowLocation()
  if (!localObj) {
    Notification.error({
      title: '系统异常[轨迹日志]',
      message: '未能检测到登录用户信息',
    })
    return
  }
  // 解析数据生成日志文件格式
  let logData = ''
  logData +=
    '[' +
    dateFormat(new Date()) +
    ']' +
    '[' +
    xyybs +
    ']' +
    '[' +
    id +
    ']' +
    '[' +
    ymngnmc +
    ']' +
    '[' +
    localObj.yhm +
    ']' +
    '[' +
    localObj.xm +
    ']'
  if (extraParams) {
    // 校验额外的参数extraParams是否为json对象或json数组
    let extraParamsStr = extraParams
    try {
      if (checkString(extraParamsStr)) {
        extraParamsStr = JSON.parse(extraParamsStr)
      }
      // //
      // if(checkArr(extraParamsStr)) {
      //   Notification.error({
      //     title: '系统异常[轨迹日志]',
      //     message: '额外参数异常，只接收对象',
      //   })
      //   return
      // }
      // //
      // extraParamsStr = JSON.stringify(extraParamsStr)
      // if(extraParamsStr.indexOf('[') != -1 || extraParamsStr.indexOf(']') != -1) {
      //   Notification.error({
      //     title: '系统异常[轨迹日志]',
      //     message: '额外参数异常，只接收对象且不能包含中括号[]',
      //   })
      //   return
      // }
      //
      extraParamsStr = JSON.stringify(extraParamsStr)
    } catch (error) {
      console.error(error)
      Notification.error({
        title: '系统异常[轨迹日志]',
        message: '额外参数异常，不是一个JSON对象或JSON字符串',
      })
      return
    }
    logData += '>>>' + extraParamsStr
  }
  logData += '\n'
  try {
    // 生成日志文件完整路径（按天生成）并追加内容
    logSavePath +=
      'secret keeper-log-trajectory-' + dateFormatNY(new Date()) + '.log'
    FS.appendFileSync(logSavePath, logData)
  } catch (error) {
    Notification.error({
      title: '系统异常[轨迹日志]',
      message: '系统日志写入异常，请确认系统日志目录是否具有读写权限',
    })
    return
  }
}

/**
 * 写入系统操作日志(按月生成)
 */
export const writeSystemOptionsLog = (params) => {
  if (!params) {
    console.log('写入系统操作日志失败，参数为空', params)
    return
  }
  let xyybs = params.xyybs
  let ymngnmc = params.ymngnmc
  let extraParams = params.extraParams
  if (!xyybs) {
    console.log('写入系统操作日志失败，参数小应用标识为空', xyybs)
    return
  }
  if (!ymngnmc) {
    console.log('写入系统操作日志失败，参数用户功能名称为空', ymngnmc)
    return
  }
  // 获取日志文件路径
  let logSavePath
  try {
    logSavePath = getLogFileSavePath()
  } catch (error) {
    Notification.error({
      title: '系统异常[系统操作日志]',
      message: '系统无法获取或解析日志目录，请确认系统日志目录是否正确填写',
    })
    return
  }
  console.log('logSavePath', logSavePath)
  // 创建日志文件路径
  try {
    createDirectory(logSavePath)
  } catch (error) {
    console.log('创建日志所属目录失败', error)
    Notification.error({
      title: '系统异常[系统操作日志]',
      message: '系统无法创建日志目录，请确认系统日志目录是否具有读写权限',
    })
    return
  }
  // 获取当前用户信息
  let localObj = getWindowLocation()
  if (!localObj) {
    Notification.error({
      title: '系统异常[系统操作日志]',
      message: '未能检测到登录用户信息',
    })
    return
  }
  // 解析数据生成日志文件格式
  let logData = ''
  logData +=
    '[' +
    dateFormat(new Date()) +
    ']' +
    '[' +
    localObj.yhm +
    ']' +
    '[' +
    localObj.xm +
    ']' +
    '[' +
    xyybs +
    ']' +
    '[' +
    ymngnmc +
    ']'
  if (extraParams) {
    // 校验额外的参数extraParams是否为json对象或json数组
    let extraParamsStr = extraParams
    try {
      if (checkString(extraParamsStr)) {
        extraParamsStr = JSON.parse(extraParamsStr)
      }
      // //
      // if(checkArr(extraParamsStr)) {
      //   Notification.error({
      //     title: '系统异常[轨迹日志]',
      //     message: '额外参数异常，只接收对象',
      //   })
      //   return
      // }
      // //
      // extraParamsStr = JSON.stringify(extraParamsStr)
      // if(extraParamsStr.indexOf('[') != -1 || extraParamsStr.indexOf(']') != -1) {
      //   Notification.error({
      //     title: '系统异常[轨迹日志]',
      //     message: '额外参数异常，只接收对象且不能包含中括号[]',
      //   })
      //   return
      // }
      //
      extraParamsStr = JSON.stringify(extraParamsStr)
    } catch (error) {
      console.error(error)
      Notification.error({
        title: '系统异常[系统操作日志]',
        message: '额外参数异常，不是一个JSON对象或JSON字符串',
      })
      return
    }
    logData += '>>>' + extraParamsStr
  }
  logData += '\n'
  try {
    // 生成日志文件完整路径（按天生成）并追加内容
    logSavePath +=
      'secret keeper-log-system-options-' + dateFormatNY(new Date()) + '.log'
    FS.appendFileSync(logSavePath, logData)
  } catch (error) {
    Notification.error({
      title: '系统异常[系统操作日志]',
      message: '系统日志写入异常，请确认系统日志目录是否具有读写权限',
    })
    return
  }
}

/**
 * =============================================================
 * =============================================================
 * =============================================================
 * =============================================================
 * =============================================================
 */

// 解析轨迹日志(台账轨迹使用)
export const parseTrajectoryLogs = (params, callback) => {
  if (!params) {
    console.log('解析轨迹日志文件失败，参数为空', param)
    return
  }
  let id = params.id
  let xyybs = params.xyybs
  if (!id) {
    console.log('解析轨迹日志文件失败，参数ID为空', id)
    return
  }
  // 获取日志文件路径
  let logSavePath
  try {
    logSavePath = getLogFileSavePath()
  } catch (error) {
    Notification.error({
      title: '系统异常[轨迹日志]',
      message: '系统无法获取或解析日志目录，请确认系统日志目录是否正确填写',
    })
    return
  }
  console.log('logSavePath', logSavePath)
  let files = FS.readdirSync(logSavePath, { encoding: 'utf8' })
  // console.log('files', files)
  files = files.sort()
  // console.log('files sort', files)
  let rl
  let matchArr
  //
  let resArr = []
  //
  files.forEach((fileName) => {
    if (fileName.indexOf('trajectory') != -1) {
      // 打开日志文件并按行筛选需要的日志
      console.log('准备解析轨迹日志文件', fileName)
      rl = readline.createInterface({
        input: FS.createReadStream(logSavePath + fileName),
        crlfDelay: Infinity,
      })
      rl.on('line', (line) => {
        let lineMarkIndex = line.indexOf('>>>')
        let linePrefix = line
        if (lineMarkIndex != -1) {
          linePrefix = line.substring(0, lineMarkIndex + 1)
        }
        matchArr = linePrefix.match(/\[(.+?)(?=\])/g)
        // console.log('matchArr', matchArr)
        if (matchArr.length < 6) {
          console.error('非法日志[忽略]>>', line)
          return true
        }
        if (
          matchArr[2].substring(1) == id &&
          matchArr[1].substring(1) == xyybs
        ) {
          // console.log(`文件的单行内容：${line}`)
          let resObj = {
            // 日期格式解析
            time: dateFormatChinese(new Date(matchArr[0].substring(1))),
            xyybs: matchArr[1].substring(1),
            id: matchArr[2].substring(1),
            ymngnmc: matchArr[3].substring(1),
            yhm: matchArr[4].substring(1),
            xm: matchArr[5].substring(1),
          }
          // // 适配旧版日志（旧版第7个[]没有，新版该位置为状态变化时间）
          // if(matchArr.length > 6 && matchArr[6]) {
          //   resObj.ztbhsj = matchArr[6].substring(1)
          // }
          // // 涉密人员特殊处理
          // if (xyybs == 'mk_smry') {
          //   let extraParamsStr = line.substring(line.indexOf('>>>') + 3)
          //   // console.log('extraParamsStr', extraParamsStr)
          //   try {
          //     resObj.extraParams = JSON.parse(extraParamsStr)
          //   } catch (error) {
          //     console.error(error)
          //     Notification.error({
          //       title: '系统异常[轨迹日志]',
          //       message: '日志额外参数解析异常，额外参数不是一个JSON对象或数组',
          //     })
          //     return
          //   }
          // }
          let extraIndex = line.indexOf('>>>')
          if (extraIndex != -1) {
            let extraParamsStr = line.substring(extraIndex + 3)
            // console.log('extraParamsStr', extraParamsStr)
            try {
              resObj.extraParams = JSON.parse(extraParamsStr)
            } catch (error) {
              console.error(error)
              // Notification.error({
              //   title: '系统异常[轨迹日志]',
              //   message: '日志额外参数解析异常，额外参数不是一个JSON对象或数组',
              // })
              return
            }
          }
          resArr.push(resObj)
        }
      })
    }
  })
  if (rl) {
    rl.on('close', () => {
      console.log('逐行读取文件流关闭')
      callback(resArr)
    })
  } else {
    callback(resArr)
  }
}
// 解析操作日志(审计使用)
export const parseOperationLogsSj = (callback) => {
  // 获取日志文件路径
  let logSavePath
  try {
    logSavePath = getLogFileSavePath()
  } catch (error) {
    Notification.error({
      title: '系统异常[操作日志]',
      message: '系统无法获取或解析日志目录，请确认系统日志目录是否正确填写',
    })
    return
  }
  console.log('logSavePath', logSavePath)
  let files = FS.readdirSync(logSavePath, { encoding: 'utf8' })
  // console.log('files', files)
  // 日志文件名筛选
  files = files.filter((item) => {
    return item.indexOf('options') != -1
  })
  // 正序排序
  files = files.sort()
  // 倒置
  files.reverse()
  console.log('files sort', files)
  let rl
  let matchArr
  //
  let resArr = []
  //
  files.forEach((fileName) => {
    // 打开日志文件并按行筛选需要的日志
    console.log('准备解析操作日志文件', fileName)
    rl = readline.createInterface({
      input: FS.createReadStream(logSavePath + fileName),
      crlfDelay: Infinity,
    })
    rl.on('line', (line) => {
      if (line != '') {
        let lineMarkIndex = line.indexOf('>>>')
        let linePrefix = line
        if (lineMarkIndex != -1) {
          linePrefix = line.substring(0, lineMarkIndex + 1)
        }
        matchArr = linePrefix.match(/\[(.+?)(?=\])/g)
        if (!matchArr || matchArr.length != 5) {
          // console.error('非法日志[忽略]>>', line)
          return true
        }
        // console.log(`文件的单行内容：${line}`)
        let resObj = {
          // 日期格式解析
          time: new Date(matchArr[0].substring(1)).getTime(),
          yhm: matchArr[1].substring(1),
          xm: matchArr[2].substring(1),
          xyybs: matchArr[3].substring(1),
          ymngnmc: matchArr[4].substring(1),
        }
        // 附加参数
        if (line.indexOf('>>>') != -1) {
          let extraParamsStr = line.substring(line.indexOf('>>>') + 3)
          // console.log('extraParamsStr', extraParamsStr)
          try {
            resObj.extraParams = JSON.parse(extraParamsStr)
            // 直接将附加参数加入到 resObj中，方便显示
            Object.assign(resObj, resObj.extraParams)
          } catch (error) {
            console.error(error)
            Notification.error({
              title: '系统异常[操作日志]',
              message: '日志额外参数解析异常，额外参数不是一个JSON对象或数组',
            })
            return
          }
        }
        // 判断检查季度，有则直接用，没有则数据库查询
        let jcjdmc = resObj.jcjdmc
        if (!jcjdmc || jcjdmc == '') {
          let rwxx = selectScrwByRwid(resObj.rwid)
          if (rwxx) {
            resObj.jcjdid = rwxx.jcjdid
            resObj.jcjdmc = rwxx.jcjdmc
          }
        }
        resArr.push(resObj)
      }
    })
  })
  if (rl) {
    rl.on('close', () => {
      console.log('逐行读取文件流关闭')
      callback(resArr)
    })
  } else {
    callback(resArr)
  }
}
// 解析系统操作日志(审计使用)
export const parseSystemOperationLogsSj = (callback) => {
  // 获取日志文件路径
  let logSavePath
  try {
    logSavePath = getLogFileSavePath()
  } catch (error) {
    Notification.error({
      title: '系统异常[系统操作日志]',
      message: '系统无法获取或解析日志目录，请确认系统日志目录是否正确填写',
    })
    return
  }
  console.log('logSavePath', logSavePath)
  let files = FS.readdirSync(logSavePath, { encoding: 'utf8' })
  // console.log('files', files)
  // 日志文件名筛选
  files = files.filter((item) => {
    return item.indexOf('system-options') != -1
  })
  // 正序排序
  files = files.sort()
  // 倒置
  files.reverse()
  console.log('files sort', files)
  let rl
  let matchArr
  //
  let resArr = []
  //
  files.forEach((fileName) => {
    // 打开日志文件并按行筛选需要的日志
    console.log('准备解析操作日志文件', fileName)
    rl = readline.createInterface({
      input: FS.createReadStream(logSavePath + fileName),
      crlfDelay: Infinity,
    })
    rl.on('line', (line) => {
      if (line != '') {
        let lineMarkIndex = line.indexOf('>>>')
        let linePrefix = line
        if (lineMarkIndex != -1) {
          linePrefix = line.substring(0, lineMarkIndex + 1)
        }
        matchArr = linePrefix.match(/\[(.+?)(?=\])/g)
        if (!matchArr || matchArr.length != 5) {
          // console.error('非法日志[忽略]>>', line)
          return true
        }
        // console.log(`文件的单行内容：${line}`)
        let resObj = {
          // 日期格式解析
          time: new Date(matchArr[0].substring(1)).getTime(),
          yhm: matchArr[1].substring(1),
          xm: matchArr[2].substring(1),
          xyybs: matchArr[3].substring(1),
          ymngnmc: matchArr[4].substring(1),
        }
        // 附加参数
        if (line.indexOf('>>>') != -1) {
          let extraParamsStr = line.substring(line.indexOf('>>>') + 3)
          // console.log('extraParamsStr', extraParamsStr)
          try {
            resObj.extraParams = JSON.parse(extraParamsStr)
            // 直接将附加参数加入到 resObj中，方便显示
            Object.assign(resObj, resObj.extraParams)
          } catch (error) {
            console.error(error)
            Notification.error({
              title: '系统异常[系统操作日志]',
              message: '日志额外参数解析异常，额外参数不是一个JSON对象或数组',
            })
            return
          }
        }
        resArr.push(resObj)
      }
    })
  })
  if (rl) {
    rl.on('close', () => {
      console.log('逐行读取文件流关闭')
      callback(resArr)
    })
  } else {
    callback(resArr)
  }
}
// 解析轨迹日志(审计使用)
export const parseTrajectoryLogsSj = (callback) => {
  // 获取日志文件路径
  let logSavePath
  try {
    logSavePath = getLogFileSavePath()
  } catch (error) {
    Notification.error({
      title: '系统异常[轨迹日志]',
      message: '系统无法获取或解析日志目录，请确认系统日志目录是否正确填写',
    })
    return
  }
  console.log('logSavePath', logSavePath)
  let files = FS.readdirSync(logSavePath, { encoding: 'utf8' })
  // console.log('files', files)
  // 日志文件名筛选
  files = files.filter((item) => {
    return item.indexOf('trajectory') != -1
  })
  // 正序排序
  files = files.sort()
  // 倒置
  files.reverse()
  // console.log('files sort', files)
  let rl
  let matchArr
  //
  let resArr = []
  //
  files.forEach((fileName) => {
    // 打开日志文件并按行筛选需要的日志
    console.log('准备解析轨迹日志文件', fileName)
    rl = readline.createInterface({
      input: FS.createReadStream(logSavePath + fileName),
      crlfDelay: Infinity,
    })
    rl.on('line', (line) => {
      if (line != '') {
        let lineMarkIndex = line.indexOf('>>>')
        let linePrefix = line
        if (lineMarkIndex != -1) {
          linePrefix = line.substring(0, lineMarkIndex + 1)
        }
        matchArr = linePrefix.match(/\[(.+?)(?=\])/g)
        // console.log('matchArr', matchArr)
        if (matchArr.length != 6) {
          console.error('非法日志[忽略]>>', line)
          return true
        }
        // console.log(`文件的单行内容：${line}`)
        let resObj = {
          // 日期格式解析
          time: new Date(matchArr[0].substring(1)).getTime(),
          xyybs: matchArr[1].substring(1),
          id: matchArr[2].substring(1),
          ymngnmc: matchArr[3].substring(1),
          yhm: matchArr[4].substring(1),
          xm: matchArr[5].substring(1),
        }
        // 附加参数
        if (line.indexOf('>>>') != -1) {
          let extraParamsStr = line.substring(line.indexOf('>>>') + 3)
          // console.log('extraParamsStr', extraParamsStr)
          try {
            resObj.extraParams = JSON.parse(extraParamsStr)
          } catch (error) {
            console.error(error)
            Notification.error({
              title: '系统异常[轨迹日志]',
              message: '日志额外参数解析异常，额外参数不是一个JSON对象或数组',
            })
            return
          }
        }
        resArr.push(resObj)
      }
    })
  })
  if (rl) {
    rl.on('close', () => {
      console.log('逐行读取文件流关闭')
      callback(resArr)
    })
  } else {
    callback(resArr)
  }
}
// 解析登录日志(审计使用)
export const parseLoginLogs = (callback) => {
  // 获取日志文件路径
  let logSavePath
  try {
    logSavePath = getLogFileSavePath()
  } catch (error) {
    Notification.error({
      title: '系统异常[登录日志]',
      message: '系统无法获取或解析日志目录，请确认系统日志目录是否正确填写',
    })
    return
  }
  console.log('logSavePath', logSavePath)
  let files = FS.readdirSync(logSavePath, { encoding: 'utf8' })
  // console.log('files', files)
  // 日志文件名筛选
  files = files.filter((item) => {
    return item.indexOf('login') != -1
  })
  // 正序排序
  files = files.sort()
  // 倒置
  files.reverse()
  let rl
  let matchArr
  //
  let resArr = []
  //
  files.forEach((fileName) => {
    // 打开日志文件并按行筛选需要的日志
    console.log('准备解析登录日志文件', fileName)
    rl = readline.createInterface({
      input: FS.createReadStream(logSavePath + fileName),
      crlfDelay: Infinity,
    })
    rl.on('line', (line) => {
      if (line != '') {
        let lineMarkIndex = line.indexOf('>>>')
        let linePrefix = line
        if (lineMarkIndex != -1) {
          linePrefix = line.substring(0, lineMarkIndex + 1)
        }
        matchArr = linePrefix.match(/\[(.+?)(?=\])/g)
        // console.log('matchArr', matchArr)
        if (matchArr.length != 5) {
          console.error('非法日志[忽略]>>', line)
          return true
        }
        let resObj = {
          // 日期格式解析
          time: dateFormatChinese(new Date(matchArr[0].substring(1))),
          id: matchArr[1].substring(1),
          yhm: matchArr[2].substring(1),
          xm: matchArr[3].substring(1),
          yhlx: matchArr[4].substring(1),
        }
        resArr.push(resObj)
      }
    })
  })
  if (rl) {
    rl.on('close', () => {
      console.log('逐行读取文件流关闭')
      callback(resArr)
    })
  } else {
    callback(resArr)
  }
}

/**
 * =============================================================
 * =============================================================
 * =============================================================
 * =============================================================
 * =============================================================
 */

// 轨迹日志icon图标设置
export const setTrajectoryIcons = (arrList) => {
  arrList.forEach((item) => {
    switch (item.ymngnmc) {
      case '新增':
        item.icon = 'el-icon-plus'
        item.color = '#67C23A'
        break
      case '在用':
        item.icon = 'el-icon-circle-check'
        item.color = '#67C23A'
        break
      case '在管':
        item.icon = 'el-icon-circle-check'
        item.color = '#67C23A'
        break
      case '停用':
        item.icon = 'el-icon-remove-outline'
        item.color = '#E6A23C'
        break
      case '报废':
        item.icon = 'el-icon-circle-close'
        item.color = '#F56C6C'
        break
      case '借出':
        item.icon = 'el-icon-position'
        item.color = '#409EFF'
        break
      case '外借':
        item.icon = 'el-icon-position'
        item.color = '#409EFF'
        break
      case '修改':
        item.icon = 'el-icon-position'
        item.color = '#409EFF'
        break
      case '销毁':
        item.icon = 'el-icon-delete'
        item.color = '#F56C6C'
        break
      case '外发':
        item.icon = 'el-icon-delete'
        item.color = '#F56C6C'
        break
    }
  })
}
