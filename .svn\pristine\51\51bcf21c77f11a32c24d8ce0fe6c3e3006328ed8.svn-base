<template>
	<div>
		
	</div>
</template>
<script>
	export default {
		data() {
			return {

			};
		},
		computed: {},
		methods: {
			tabsCode() {
                // 比较结果activeName的值
                if (this.$route.query.activeName) {
               		this.$router.push(this.$route.query.activeName)
                } else {
                  this.$router.push('/lsSmgwgl')
                }
            },
		},
		watch: {},
		mounted() {
			this.tabsCode()
		}
	};
</script>
<style scoped>

</style>
