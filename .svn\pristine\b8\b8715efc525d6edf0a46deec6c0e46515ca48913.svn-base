import db from './adapter/zczpAdaptor'

// 日志工具类
import { writeTrajectoryLog } from '../utils/logUtils'

//保密制度-----------------------------------保密制度初始化列表********
export const getFsmjsj = (params) => {
  let page = params.page
  let pagesize = params.pageSize
  // let zcbh = params.zcbh
  // let lx = params.lx
  // let qyrq = params.qyrq
  let list_total = db
    .get('Fsmjsj_list')
    .sortBy('cjsj')
    .filter(function (item) {
      // 	// 1、bm型号，类型，密级，日期都没有
      // if ((zcbh === undefined || zcbh == "") && (lx === undefined || lx == "") && (qyrq === undefined || qyrq == '' ||qyrq == null)) {
      return item
    })
    .cloneDeep()
    .value()

  // 手动分页
  console.log(pagesize * (page - 1), pagesize * (page - 1) + pagesize)
  let pageList = list_total.slice(
    pagesize * (page - 1),
    pagesize * (page - 1) + pagesize
  )

  let resList = {
    list: pageList,
    list_total: list_total,
    total: list_total.length,
  }
  console.log('保密制度', resList)
  return resList
}
//保密制度-----------------------------------保密制度添加成员********
export const addFsmjsj = (params) => {
  let sjc = new Date().getTime()
  params.cjsj = sjc
  params.gxsj = sjc
  db.read().get('Fsmjsj_list').push(params).write()
  // 添加日志
  let paramsLog = {
    xyybs: 'mk_fsmjsj',
    id: params.fsmjsjid,
    ymngnmc: '新增',
    extraParams: {
      zrr: params.zrr,
    },
  }
  writeTrajectoryLog(paramsLog)
}
export const jyaddFsmjsj = (params) => {
  let zcbh = params.zcbh
  let zjxlh = params.zjxlh
  let message = 0
  let pdzcbh = db
    .read()
    .get('Fsmjsj_list')
    .find({ zcbh: zcbh })
    .cloneDeep()
    .value()
  let pdzjxlh = db
    .read()
    .get('Fsmjsj_list')
    .find({ zjxlh: zjxlh })
    .cloneDeep()
    .value()
  if (pdzcbh) {
    console.log(2)
    message = 2
  } else if (pdzjxlh) {
    message = 3
  }
  return message
}
export const reviseFsmjsjzcbh = (params) => {
  let zcbh = params.zcbh
  let message = 0
  let pdzcbh = db
    .read()
    .get('Fsmjsj_list')
    .find({ zcbh: zcbh })
    .cloneDeep()
    .value()
  if (pdzcbh) {
    console.log(2)
    message = 2
  }
  return message
}
export const reviseFsmjsjzjxlh = (params) => {
  let zjxlh = params.zjxlh
  let message = 0
  let pdzjxlh = db
    .read()
    .get('Fsmjsj_list')
    .find({ zjxlh: zjxlh })
    .cloneDeep()
    .value()
  if (pdzjxlh) {
    console.log(2)
    message = 3
  }
  return message
}
export const reviseFsmjsj = (params) => {
  let sybm = params.sybm.join('/')
  params.sybm = sybm
  let glbm = params.glbm.join('/')
  params.glbm = glbm
  let fsmjsjid = params.fsmjsjid
  console.log('fsmjsjid', fsmjsjid)
  if (!fsmjsjid || fsmjsjid == '') {
    return
  }
  params.gxsj = new Date().getTime()
  // 全字段更新方法(传进来的字段都会更新)
  db.read()
    .get('Fsmjsj_list')
    .find({ fsmjsjid: fsmjsjid })
    .assign(params)
    .write()
}
//保密制度-----------------------------------保密制度删除成员********
export const deleteFsmjsj = (params) => {
  db.read().get('Fsmjsj_list').remove(params).write()
}
//非是秘密计算机修改
export const xgfsmjsjsyzt_zy = (params) => {
  // console.log(params)
  params.forEach((item) => {
    // 使用情况旧值（使用情况改变才插入轨迹日志）
    let itemSyqkOld = item.syqk
    db.get('Fsmjsj_list')
      .find({ zcbh: item.zcbh })
      .assign({
        syqk: '在用',
      })
      .write()
    // 添加日志
    if (itemSyqkOld != '在用') {
      let paramsLog = {
        xyybs: 'mk_fsmjsj',
        id: item.fsmjsjid,
        ymngnmc: '在用',
        extraParams: {
          zrr: item.zrr,
        },
      }
      writeTrajectoryLog(paramsLog)
    }
  })
}
//非是秘密计算机修改
export const xgfsmjsjsyzt_ty = (params) => {
  // console.log(params);
  params.forEach((item) => {
    // 使用情况旧值（使用情况改变才插入轨迹日志）
    let itemSyqkOld = item.syqk
    db.get('Fsmjsj_list')
      .find({ zcbh: item.zcbh })
      .assign({
        syqk: '停用',
      })
      .write()
    // 添加日志
    if (itemSyqkOld != '停用') {
      let paramsLog = {
        xyybs: 'mk_fsmjsj',
        id: item.fsmjsjid,
        ymngnmc: '停用',
        extraParams: {
          zrr: item.zrr,
        },
      }
      writeTrajectoryLog(paramsLog)
    }
  })
}
//非是秘密计算机修改
export const xgfsmjsjsyzt_bf = (params) => {
  // console.log(params);
  params.forEach((item) => {
    // 使用情况旧值（使用情况改变才插入轨迹日志）
    let itemSyqkOld = item.syqk
    db.get('Fsmjsj_list')
      .find({ zcbh: item.zcbh })
      .assign({
        syqk: '报废',
      })
      .write()
    // 添加日志
    if (itemSyqkOld != '报废') {
      let paramsLog = {
        xyybs: 'mk_fsmjsj',
        id: item.fsmjsjid,
        ymngnmc: '报废',
        extraParams: {
          zrr: item.zrr,
        },
      }
      writeTrajectoryLog(paramsLog)
    }
  })
}
//非是秘密计算机修改
export const xgfsmjsjsyzt_jc = (params) => {
  // console.log(params);
  params.forEach((item) => {
    // 使用情况旧值（使用情况改变才插入轨迹日志）
    let itemSyqkOld = item.syqk
    db.get('Fsmjsj_list')
      .find({ zcbh: item.zcbh })
      .assign({
        syqk: '借出',
      })
      .write()
    // 添加日志
    if (itemSyqkOld != '借出') {
      let paramsLog = {
        xyybs: 'mk_fsmjsj',
        id: item.fsmjsjid,
        ymngnmc: '借出',
        extraParams: {
          zrr: item.zrr,
        },
      }
      writeTrajectoryLog(paramsLog)
    }
  })
}
//非是秘密计算机修改
export const xgfsmjsjsyzt_xh = (params) => {
  console.log(params)
  params.forEach((item) => {
    // 使用情况旧值（使用情况改变才插入轨迹日志）
    let itemSyqkOld = item.syqk
    db.get('Fsmjsj_list')
      .find({ zcbh: item.zcbh })
      .assign({
        syqk: '销毁',
      })
      .write()
    // 添加日志
    if (itemSyqkOld != '销毁') {
      let paramsLog = {
        xyybs: 'mk_fsmjsj',
        id: item.fsmjsjid,
        ymngnmc: '销毁',
        extraParams: {
          zrr: item.zrr,
        },
      }
      writeTrajectoryLog(paramsLog)
    }
  })
}
export const getPpxh = () => {
  let ppxh = db.get('Fsmjsj_list').cloneDeep().value()
  console.log()
  return ppxh
}
