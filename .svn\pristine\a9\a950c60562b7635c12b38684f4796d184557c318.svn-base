import db from "./adapter/zczpAdaptor";

//培训清单-----------------------------------培训清单初始化列表********
export const getPxqd = (params) => {
  let page = params.page;
  let pagesize = params.pageSize;
  let pxxs = params.pxxs;
  let pxrq = params.pxrq;
  let list_total = db
    .get("Pxqd_list")
    .sortBy("cjsj")
    .filter(function (item) {
      // 1、试卷名称和创建时间都没有
      if (
        (pxxs === undefined || pxxs == "") &&
        (pxrq === undefined || pxrq == null)
      ) {
        return item;
        console.log("全都没有", item);
      }
      // 2、试卷名称有，创建时间没有
      else if (pxxs && (pxrq === undefined || pxrq == null)) {
        if (item.pxxs) {
          if (item.pxxs.indexOf(pxxs) != -1) {
            console.log("ccc", item);
            return item;
          }
        } else {
          console.log("item.pxxs", item.pxxs);
        }
      }
      // 3、试卷名称没有，创建时间有
      else if ((pxxs === undefined || pxxs == "") && pxrq) {
        if (item.pxrq) {
          if (item.pxrq >= pxrq[0] && item.pxrq <= pxrq[1]) {
            return item;
          }
        } else {
          console.log("item.pxrq", item.pxrq);
        }
      }
      // 4、试卷名称有，创建时间有
      else if (pxxs && pxrq) {
        if (item.pxxs && item.pxrq) {
          if (
            item.pxxs.indexOf(pxxs) != -1 &&
            item.pxrq >= pxrq[0] &&
            item.pxrq <= pxrq[1]
          ) {
            return item;
          }
        } else {
          console.log("item.pxxs", item.pxxs, "item.pxrq", item.pxrq);
        }
      }
    })
    .cloneDeep()
    .value();

  // 手动分页
  console.log(pagesize * (page - 1), pagesize * (page - 1) + pagesize);
  let pageList = list_total.slice(
    pagesize * (page - 1),
    pagesize * (page - 1) + pagesize
  );

  let resList = {
    list: pageList,
    list_total: list_total,
    total: list_total.length,
  };
  console.log("培训清单", resList);
  return resList;
};
//培训清单-----------------------------------培训清单添加成员********
export const addPxqd = (params) => {
  let sjc = new Date().getTime();
  params.cjsj = sjc;
  params.gxsj = sjc;

  db.read().get("Pxqd_list").push(params).write();

  // db.read().get('Pxqd_list').push(params).write()
};
export const jxpxzt = (params) => {
  let pxzt = params.pxzt;
  let message = 0;
  let pxrq = params.pxrq;
  let pd = db
    .read()
    .get("Pxqd_list")
    .filter({ pxrq: pxrq })
    .cloneDeep()
    .value();
  if (pd != undefined) {
    for (let i = 0; i < pd.length; i++) {
      if (pxzt == pd[i].pxzt) {
        message = 1;
        break;
      }
    }
  }
  console.log(message);
  return message;
};
//培训清单-----------------------------------培训清单删除成员********
export const deletePxqd = (params) => {
  db.read().get("Pxqd_list").remove(params).write();
};

//培训清单-----------------------------------培训清单修改单条********
export const updatePxqd = (params) => {
  // 数据校验
  // 校验ID
  let pxqdid = params.pxqdid;
  console.log("pxqdid", pxqdid);
  if (!pxqdid || pxqdid == "") {
    return;
  }
  params.gxsj = new Date().getTime();
  // 全字段更新方法(传进来的字段都会更新)
  db.read().get("Pxqd_list").find({ pxqdid: pxqdid }).assign(params).write();
  // // 更新部分字段方法
  // db.read().get('Bmzd_list').find({pxqdid:pxqdid}).assign({
  // 	zdmc: params.zdmc,
  // 	...
  // }).write()
};
export const getPxzt = () => {
	let pxzt = db.get('Pxqd_list').cloneDeep().value()
	console.log()
	return pxzt
}
