<template>
	<div class="bg_con" style="height: calc(100% - 38px);">
		<div style="width: 100%; position: relative; overflow: hidden; height: 100%;">

			<div class="dabg" style="height: 100%;">
				<div class="content" style="height: 100%;">
					<div class="table" style="height: 100%;">
						<!-- -----------------操作区域--------------------------- -->
						<div class="mhcx">
							<el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline"
								style="float:left">
								<el-form-item style="font-weight: 700;">
									<el-input v-model="formInline.zcbh" clearable placeholder="编号" class="widths">
									</el-input>
								</el-form-item>
								<el-form-item style="font-weight: 700;">
									<el-date-picker v-model="formInline.qyrq" type="daterange" range-separator="至"
										start-placeholder="启用起始日期" end-placeholder="启用结束日期" format="yyyy年MM月dd日"
										value-format="yyyy年MM月dd日">
									</el-date-picker>
								</el-form-item>
								<el-form-item>
									<el-button type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
								</el-form-item>
								<el-form-item>
									<el-button type="warning" icon="el-icon-circle-close" @click="cz">重置</el-button>
								</el-form-item>
							</el-form>
							<el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline"
								style="float:right">
								<el-form-item style="float: right;">
									<el-button type="danger" size="medium" @click="shanchu" icon="el-icon-delete-solid">
										删除
									</el-button>
								</el-form-item>
								<el-form-item style="float: right;">
									<el-button type="primary" size="medium" icon="el-icon-download"
										@click="exportList()">导出
									</el-button>
								</el-form-item>
								<el-form-item style="float: right;">
									<input type="file" ref="upload"
										style="display: none;position: absolute;top:10px; right:0;opacity:0; cursor: pointer;height: 32px;width: 56px;z-index:1;"
										accept=".xls,.xlsx">
									<el-button type="primary" icon="el-icon-upload2" size="medium"
										@click="dr_dialog = true">
										导入
									</el-button>
								</el-form-item>
								<el-form-item style="float: right;">
									<el-button type="success" size="medium" @click="xzaqcp"
										icon="el-icon-plus">
										新增
									</el-button>
								</el-form-item>
							</el-form>
						</div>


						<!-- -----------------审查组人员列表--------------------------- -->
						<div class="table_content_padding" style="height: 100%;">
							<div class="table_content" style="height: 100%;">
								<el-table :data="aqcpList" border @selection-change="selectRow"
									:header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
									style="width: 100%;border:1px solid #EBEEF5;"
									height="calc(100% - 34px - 41px - 3px)" stripe>
									<el-table-column type="selection" width="55" align="center"> </el-table-column>
									<el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
									<!-- <el-table-column prop="mc" label="名称"></el-table-column> -->
									<el-table-column prop="ppxh" label="品牌型号"></el-table-column>
									<el-table-column prop="lx" label="类型"></el-table-column>
									<el-table-column prop="zcbh" label="固定资产编号"></el-table-column>
									<el-table-column prop="qyrq" label="启用日期"></el-table-column>
									<el-table-column prop="sl" label="数量"></el-table-column>
									<el-table-column prop="yt" label="用途"></el-table-column>
									<el-table-column prop="" label="操作" width="120">
										<template slot-scope="scoped">
											<el-button size="medium" type="text" @click="xqyl(scoped.row)">详情
											</el-button>
											<el-button size="medium" type="text" @click="updateItem(scoped.row)">修改
											</el-button>
										</template>
									</el-table-column>

								</el-table>

								<!-- -------------------------分页区域---------------------------- -->
								<div style="border: 1px solid #ebeef5;">
									<el-pagination background @current-change="handleCurrentChange"
										@size-change="handleSizeChange" :pager-count="5" :current-page="page"
										:page-sizes="[5, 10, 20, 30]" :page-size="pageSize"
										layout="total, prev, pager, sizes,next, jumper" :total="total">
									</el-pagination>
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- 模板下载 -->
				<el-dialog title="开始导入" class="scbg-dialog" width="600px" @close="mbxzgb" :visible.sync="dr_dialog"
					show-close>
					<div style="padding: 20px;">
						<div class="daochu">
							<div>一、请点击“导出模板”，并参照模板填写信息。</div>
							<el-button type="primary" size="mini" @click="mbdc">
								模板导出
							</el-button>
						</div>
						<div class="daochu">
							<div>二、数据导入方式：</div>
							<el-radio-group v-model="sjdrfs" @change="Radio($event)">
								<el-radio label="1">追加（导入时已有的记录信息不变，只添加新的记录）</el-radio>
								<el-radio label="2">覆盖（导入时更新已有的记录信息，并添加新的记录）</el-radio>
							</el-radio-group>
						</div>
						<div class="daochu">
							<div>三、将按模板填写的文件，导入到系统中。</div>
							<el-button type="primary" size="mini" @click="chooseFile">
								上传导入
							</el-button>
						</div>
					</div>
				</el-dialog>
				<!-- -----------------导入-弹窗--------------------------- -->
				<el-dialog width="1000px" height="800px" title="导入安全产品" class="scbg-dialog"
					:visible.sync="dialogVisible_dr" show-close>
					<div style="height: 600px;">
						<el-table :data="dr_cyz_list" ref="multipleTable" @selection-change="handleSelectionChange"
							style="width: 100%;border:1px solid #EBEEF5;" height="100%" stripe>
							<el-table-column type="selection" width="55"> </el-table-column>
							<el-table-column prop="类型" label="类型"></el-table-column>
							<el-table-column prop="品牌型号" label="品牌型号"></el-table-column>
							<el-table-column prop="资产编号" label="资产编号"></el-table-column>
							<el-table-column prop="用途" label="用途"></el-table-column>
							<el-table-column prop="安装位置" label="安装位置"></el-table-column>
							<el-table-column prop="数量" label="数量"></el-table-column>

						</el-table>
					</div>

					<div style="height :30px;display: flex;align-items: center;justify-content: center;margin: 10px 0;">
						<el-button type="primary" @click="drcy" size="mini">导 入</el-button>
						<el-button type="warning" @click="dialogVisible_dr = false" size="mini">关 闭</el-button>
					</div>
				</el-dialog>

				<!-- -----------------涉密人员离岗离职信息-弹窗--------------------------- -->
				<el-dialog title="安全产品详细信息" :close-on-click-modal="false" :visible.sync="dialogVisible" width="47%"
					class="xg" :before-close="handleClose" @close="close('formName')">
					<el-form ref="formName" :model="tjlist" :rules="rules" label-width="120px" size="mini">
						<el-form-item label="资产编号" prop="zcbh" class="one-line aqcp">
							<el-input placeholder="资产编号" v-model="tjlist.zcbh" clearable @blur="onInputBlur(1)">
							</el-input>
						</el-form-item>
						<div style="display:flex">
							<el-form-item label="启用日期" prop="qyrq">
								<!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
								<el-date-picker v-model="tjlist.qyrq" clearable type="date" style="width: 100%;"
									placeholder="选择日期" format="yyyy年MM月dd日" value-format="yyyy年MM月dd日">
								</el-date-picker>
							</el-form-item>
							<el-form-item label="类型" prop="lx">
								<el-select v-model="tjlist.lx" placeholder="请选择类型" style="width: 100%;">
									<el-option v-for="item in sblxxz" :label="item.sblxmc" :value="item.sblxmc"
										:key="item.sblxid">
									</el-option>
								</el-select>
							</el-form-item>
						</div>
						<div style="display:flex">
							<el-form-item label="品牌型号" prop="ppxh">
								<el-autocomplete class="inline-input" value-key="ppxh" v-model.trim="tjlist.ppxh"
									style="width: 100%;" :fetch-suggestions="querySearchppxh" placeholder="品牌型号">
								</el-autocomplete>
							</el-form-item>
							<el-form-item label="用途" prop="yt">
								<el-autocomplete class="inline-input" value-key="yt" v-model.trim="tjlist.yt"
									style="width: 100%;" :fetch-suggestions="querySearchyt" placeholder="用途">
								</el-autocomplete>
							</el-form-item>
						</div>
						<div style="display:flex">
							<el-form-item label="安装位置" prop="azwz">
								<el-autocomplete class="inline-input" value-key="azwz" v-model.trim="tjlist.azwz"
									style="width: 100%;" :fetch-suggestions="querySearchazwz" placeholder="安装位置">
								</el-autocomplete>
							</el-form-item>
							<el-form-item label="数量" prop="sl">
								<el-input type="number" placeholder="数量" v-model="tjlist.sl" clearable></el-input>
							</el-form-item>
						</div>
					</el-form>
					<span slot="footer" class="dialog-footer">
						<el-button type="primary" @click="submitTj('formName')">保 存</el-button>
						<el-button type="warning" @click="handleClose">关 闭</el-button>
					</span>
				</el-dialog>

				<el-dialog title="修改安全产品详细信息" :close-on-click-modal="false" :visible.sync="xgdialogVisible" width="47%"
					class="xg" @close="close1('form')">
					<el-form ref="form" :model="xglist" :rules="rules" label-width="120px" size="mini">
						<el-form-item label="资产编号" prop="zcbh" class="one-line aqcp">
							<el-input placeholder="资产编号" v-model="xglist.zcbh" clearable @blur="onInputBlur(2)"
								disabled>
							</el-input>
						</el-form-item>
						<div style="display:flex">
							<el-form-item label="启用日期" prop="qyrq">
								<!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
								<el-date-picker v-model="xglist.qyrq" style="width: 100%;" clearable type="date"
									placeholder="选择日期" format="yyyy年MM月dd日" value-format="yyyy年MM月dd日">
								</el-date-picker>
							</el-form-item>
							<el-form-item label="类型" prop="lx">
								<el-select v-model="xglist.lx" placeholder="请选择类型" style="width: 100%;">
									<el-option v-for="item in sblxxz" :label="item.sblxmc" :value="item.sblxmc"
										:key="item.sblxid">
									</el-option>
								</el-select>
							</el-form-item>
						</div>
						<div style="display:flex">
							<el-form-item label="品牌型号" prop="ppxh">
								<el-autocomplete class="inline-input" value-key="ppxh" v-model.trim="xglist.ppxh"
									style="width: 100%;" :fetch-suggestions="querySearchppxh" placeholder="品牌型号">
								</el-autocomplete>
							</el-form-item>
							<el-form-item label="用途" prop="yt">
								<el-autocomplete class="inline-input" value-key="yt" v-model.trim="xglist.yt"
									style="width: 100%;" :fetch-suggestions="querySearchyt" placeholder="用途">
								</el-autocomplete>
							</el-form-item>
						</div>
						<div style="display:flex">
							<el-form-item label="安装位置" prop="azwz">
								<el-autocomplete class="inline-input" value-key="azwz" v-model.trim="xglist.azwz"
									style="width: 100%;" :fetch-suggestions="querySearchazwz" placeholder="安装位置">
								</el-autocomplete>
							</el-form-item>
							<el-form-item label="数量" prop="sl">
								<el-input type="number" placeholder="数量" v-model="xglist.sl" clearable></el-input>
							</el-form-item>
						</div>
					</el-form>
					<span slot="footer" class="dialog-footer">
						<el-button type="primary" @click="updataDialog('form')">保 存</el-button>
						<el-button type="warning" @click="xgdialogVisible = false">关 闭</el-button>
					</span>
				</el-dialog>
				<!-- 详情 -->
				<el-dialog title="安全产品详细信息详情" :close-on-click-modal="false" :visible.sync="xqdialogVisible" width="47%"
					class="xg">
					<el-form ref="form" :model="xglist" label-width="120px" size="mini" disabled>
						<el-form-item label="资产编号" prop="zcbh" class="one-line">
							<el-input placeholder="资产编号" v-model="xglist.zcbh" clearable></el-input>
						</el-form-item>
						<div style="display:flex">
							<el-form-item label="启用日期" prop="qyrq">
								<!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
								<el-date-picker v-model="xglist.qyrq" clearable type="date" style="width: 100%;"
									placeholder="选择日期" format="yyyy年MM月dd日" value-format="yyyy年MM月dd日">
								</el-date-picker>
							</el-form-item>
							<el-form-item label="类型" prop="lx">
								<el-select v-model="xglist.lx" placeholder="请选择类型" style="width: 100%;">
									<el-option v-for="item in sblxxz" :label="item.sblxmc" :value="item.sblxmc"
										:key="item.sblxid">
									</el-option>
								</el-select>
							</el-form-item>
						</div>
						<div style="display:flex">
							<el-form-item label="品牌型号" prop="ppxh">
								<el-input placeholder="品牌型号" v-model="xglist.ppxh" clearable></el-input>
							</el-form-item>
							<el-form-item label="用途" prop="yt">
								<el-input placeholder="用途" v-model="xglist.yt" clearable></el-input>
							</el-form-item>
						</div>
						<div style="display:flex">
							<el-form-item label="安装位置" prop="azwz">
								<el-input placeholder="安装位置" v-model="xglist.azwz" clearable></el-input>
							</el-form-item>
							<el-form-item label="数量" prop="sl">
								<el-input type="number" placeholder="数量" v-model="xglist.sl" clearable></el-input>
							</el-form-item>
						</div>
					</el-form>
					<span slot="footer" class="dialog-footer">

						<el-button type="warning" @click="xqdialogVisible = false">关 闭</el-button>
					</span>
				</el-dialog>


			</div>
		</div>
	</div>

</template>
<script>
//获取下拉框数据
import {
	getaqcplx
} from "../../../db/xzdb"
//对数据操作
import {
	//内容管理初始化成员列表
	getAqcp,
	//添加内容管理
	addAqcp,
	//删除内容管理
	deleteAqcp,
	//修改
	reviseAqcp,
	//添加时校验
	jyaddAqcp,
	getPpxh,
} from "../../../db/aqcpdb";
//登录信息
import {
	getlogin
} from "../../../db/loginyhdb";
//时间格式转换
import {
	dateFormatNYRChinese
} from "../../../utils/moment"
import {
	exportExcel
} from "../../../utils/exportExcel"; //excel导出工具

import {
	getUuid
} from "../../../utils/getUuid"; //获取uuid
import XLSX from "xlsx";

export default {
	components: {},
	props: {},
	data() {
		return {
			zcbh: '',//资产编号
			pdaqcp: 0, //提示信息判断
			sblxxz: [], //下拉框数据
			aqcpList: [], //列表数据
			tableDataCopy: [], //查询备份数据
			xglist: {}, //修改与详情数据
			updateItemOld: {},//修改，详情弹框信息
			xgdialogVisible: false, //修改弹框
			xqdialogVisible: false, //详情弹框
			formInline: {}, //查询区域数据
			tjlist: {
				zcbh: '',
				qyrq: '',
				lx: '',
				ppxh: '',
				yt: '',
				azwz: '',
				sl: '',
			}, //添加数据
			rules: {
				zcbh: [{
					required: true,
					message: '请输入资产编号',
					trigger: 'blur'
				},],
				qyrq: [{
					required: true,
					message: '请选择启用日期',
					trigger: 'blur'
				},],
				lx: [{
					required: true,
					message: '请选择类型',
					trigger: 'blur'
				},],
				ppxh: [{
					required: true,
					message: '请输入品牌型号',
					trigger: ['blur', 'change'],
				},],
				yt: [{
					required: true,
					message: '请输入用途',
					trigger: ['blur', 'change'],
				},],
				azwz: [{
					required: true,
					message: '请输入安装位置',
					trigger: ['blur', 'change'],
				},],
				sl: [{
					required: true,
					message: '请输入数量',
					trigger: 'blur'
				},],
			}, //校验
			page: 1, //当前页
			pageSize: 10, //每页条数
			total: 0, //总共数据数
			selectlistRow: [], //列表的值
			dialogVisible: false, //添加弹窗状态
			//导入
			dialogVisible_dr: false, //导入成员组弹窗状态
			dr_cyz_list: [], //待选择导入成员组列表
			multipleTable: [], //已选择导入成员组列表
			dwmc: '',//单位名称
			year: '',//年
			yue: '',//月
			ri: '',//日
			Date: '',//时间
			xh: [],//导出列表序号
			dclist: [],//复制列表数据
			dr_dialog: false,
			//数据导入方式
			sjdrfs: ''
		};
	},
	computed: {},
	mounted() {
		this.dwmc = getlogin()[0].dwmc
		let date = new Date()
		this.year = date.getFullYear() + '年'; //获取完整的年份(4位)
		this.yue = (date.getMonth() + 1) + '月'; //获取当前月份(0-11,0代表1月)
		this.ri = date.getDate() + '日'; //获取当前日(1-31)
		this.Date = this.year + this.yue + this.ri
		//下拉框数据
		this.sblxxz = getaqcplx()
		//列表初始化
		this.aqcp();
		this.$refs.upload.addEventListener("change", (e) => {
			//绑定监听表格导入事件
			this.readExcel(e);
		});
		this.ppxhlist()
	},
	methods: {
		//新增数据按钮事件
		xzaqcp(){
			this.dialogVisible = true
			this.aqcp()
			this.tjlist.qyrq = this.Date
		},
		//数据导入方式按钮事件
		Radio(val) {
			this.sjdrfs = val
			console.log("当前选中的值", val)
		},
		mbxzgb() {
			this.sjdrfs = ''
		},
		mbdc() {
			console.log("----导出涉密人员----");
			// console.log(this.selectlistRow);
			// if (this.selectlistRow.length > 0) {
			let filename = "安全产品汇总模板" + getUuid() + ".xlsx";

			const {
				dialog
			} = require("electron").remote;
			//弹窗title
			let options = {
				title: "保存文件",
				defaultPath: filename,
			};
			console.log(dialog);
			//导出文件夹选择弹窗
			dialog.showSaveDialog(options, (result) => {
				console.log("result", result);
				if (result == null || result == "") {
					console.log("取消导出");
					return;
				}
				let list = [];

				//对导出的数据进行格式，js导出excel要求传入的参数格式为[["列1名称","列2名称","列3名称"],["列11","值21","值31"],["列12","值22","值32"]]
				list.push([
					"序号",
					"类型",
					"品牌型号",
					"资产编号",
					"启用日期",
					"用途",
					"安装位置",
					"数量",
				]); //确定列名

        
        let styles = {
          // 列样式
          cols: {
            // 作用sheet页索引（0开始）（-1全sheet页生效）
            scoped: -1,
            style: [
              { wpx: 100 },
              { wpx: 100 },
              { wpx: 100 },
              { wpx: 100 },
              { wpx: 100 },
              { wpx: 100 },
              { wpx: 100 },
              { wpx: 100 }
            ]
          },
          // 全局样式
          all: {
            alignment: {
              horizontal: 'center', // 水平居中
              vertical: 'center' // 垂直居中
            },
            font: {
              sz: 11, // 字号
              name: '宋体' // 字体
            },
            border: { // 边框
              top: {
                style: 'thin'
              },
              bottom: {
                style: 'thin'
              },
              left: {
                style: 'thin'
              },
              right: {
                style: 'thin'
              }
            }
          },
          // 单元格样式
          cell: []
        }

				exportExcel(result, list, undefined, styles) //list 要求为二维数组
				this.dr_dialog = false
				this.$message("导出成功:" + result);
			});
		},
		//导入
		chooseFile() {
			if (this.sjdrfs != '') {
				if (this.sjdrfs == 1) {
					this.$refs.upload.click()
				}
				else if (this.sjdrfs == 2) {
					let valArr = this.dclist
					valArr.forEach(function (item) {
						deleteAqcp(item);
					})
					this.$refs.upload.click()
				}
			} else {
				this.$message.warning('请选择导入方式')
			}
		},
		//----成员组选择
		handleSelectionChange(val) {
			this.multipleTable = val;
			console.log("选中：", this.multipleTable);
		},
		//---确定导入成员组
		drcy() {
			//遍历已选择导入的成员，进行格式化，然后添加到数据库
			for (var i in this.multipleTable) {
				var cy = {
					lx: this.multipleTable[i]["类型"],
					ppxh: this.multipleTable[i]["品牌型号"],
					zcbh: this.multipleTable[i]["资产编号"],
					qyrq: dateFormatNYRChinese(this.multipleTable[i]["启用日期"]),
					yt: this.multipleTable[i]["用途"],
					azwz: this.multipleTable[i]["安装位置"],
					sl: this.multipleTable[i]["数量"],
					aqcpid: getUuid()
				};
				addAqcp(cy);
			}
			this.dialogVisible_dr = false;
			this.aqcp();
		},
		//----表格导入方法
		readExcel(e) {
			var that = this;
			const files = e.target.files;
			console.log("files", files);
			var vali = /\.(xls|xlsx)$/;
			if (files.length <= 0) {
				//如果没有文件名
				return false;
			} else if (!vali.test(files[0].name.toLowerCase())) {
				this.$Message.error("上传格式不正确，请上传xls或者xlsx格式");
				return false;
			}
			const fileReader = new FileReader();
			fileReader.onload = (e) => {
				try {
					const data = e.target.result;
					const workdata = XLSX.read(data, {
						type: "binary",
						cellDates: true,
					});
					console.log("文件的内容：", workdata); // 文件的内容
					//查看打印能看到 workdata的sheetNames里是文件中每个表的有值的集合；
					const wsname = workdata.SheetNames[0]; //取第一张表
					console.log("wsname", wsname);
					const ws = XLSX.utils.sheet_to_json(workdata.Sheets[wsname]); //生成json表格内容
					console.log(ws); //自第二行开始的内容
					this.dialogVisible_dr = true;
					this.dr_cyz_list = ws;
					console.log("列表的值:", this.dr_cyz_list);
					// console.log("列表的值:", this.dr_cyz_list[0]['业务类型'])
					// 加工excel读取业务类型为数组
					// this.dr_cyz_list.forEach(function(item) {
					// 	console.log(item[0]['业务类型'].splite(','))
					// })
					this.$refs.upload.value = ""; // 处理完成 清空表单值
					this.dr_dialog = false
				} catch (e) {
					return false;
				}
			};
			fileReader.readAsBinaryString(files[0]);
		},
		//修改
		updataDialog(form) {
			this.$refs[form].validate((valid) => {
				if (valid) {
					if (this.pdaqcp == 0) {
						reviseAqcp(this.xglist)
						this.aqcp();
						this.ppxhlist()
						// 关闭dialog
						this.$message.success("修改成功");
						this.xgdialogVisible = false;
					} else if (this.pdaqcp == 2) {
						this.$message.error('资产编号已存在');
					}

				} else {
					console.log('error submit!!');
					return false;
				}
			});

		},
		//详情弹框
		xqyl(row) {
			this.updateItemOld = JSON.parse(JSON.stringify(row));

			this.xglist = JSON.parse(JSON.stringify(row));
			// this.form1.ywlx = row.ywlx
			console.log("old", row);
			console.log("this.xglist.ywlx", this.xglist);
			this.xqdialogVisible = true;
		},
		//修改弹框
		updateItem(row) {
			this.updateItemOld = JSON.parse(JSON.stringify(row));

			this.xglist = JSON.parse(JSON.stringify(row));
			this.zcbh = this.xglist.zcbh
			// this.form1.ywlx = row.ywlx
			console.log("old", row);
			console.log("this.xglist.ywlx", this.xglist);
			this.xgdialogVisible = true;
		},
		//查询
		onSubmit() {
			// //  form是查询条件
			// console.log(this.formInline);
			// // 备份了一下数据
			// let arr = this.tableDataCopy
			// // 通过遍历key值来循环处理
			// Object.keys(this.formInline).forEach(e => {
			// 	// 调用自己定义好的筛选方法
			// 	console.log(this.formInline[e]);
			// 	arr = this.filterFunc(this.formInline[e], e, arr)
			// })
			// // 为表格赋值
			// this.aqcpList = arr
      this.aqcp()
		},
		//查询方法
		filterFunc(val, target, filterArr) {
			// 参数不存在或为空时，就相当于查询全部
			console.log(1);
			if (val == undefined || val == '') {
				console.log(2);
				return filterArr
			}
			return filterArr.filter(p => {
				console.log(p);
				let bool
				let resP
				if (Object.prototype.toString.call(val) == '[object Array]') {
					console.log('是数组')
					if (val.length > 1) {
						let timeArr1 = val[1].replace(/[\u4e00-\u9fa5]/g, '/')
						let date = new Date(timeArr1)
						if ('Invalid Date' != date) {
							// 时间
							if (new Date(p[target].replace(/[\u4e00-\u9fa5]/g, '/')).getTime() <= date.getTime()) {
								console.log('找到小于范围内是记录')
								resP = p
								let timeArr0 = val[0].replace(/[\u4e00-\u9fa5]/g, '/')
								if (new Date(p[target].replace(/[\u4e00-\u9fa5]/g, '/')).getTime() >= new Date(timeArr0)
									.getTime()) {
									console.log('找到大于范围内是记录')
									resP = p
								} else {
									resP = undefined
								}
							}
						} else {
							console.log('非法时间')
						}
						if (resP) {
							console.log('不是时间，通过时间校验')
							bool = true
						}
					} else {
						if (new Date(p[target]).getTime() <= new Date(timeArr0).getTime()) {
							resP = p
						} else {
							resP = undefined
						}
						if (resP) {
							bool = true
						}
					}
					return bool
				}
				return p[target].indexOf(val) > -1
				// return bool
			}) // 可以自己加一个.toLowerCase()来兼容一下大小
		},

		returnSy() {
			this.$router.push("/tzglsy");
		},
		//获取列表的值
		aqcp() {
			let params = {
				page: this.page,
				pageSize: this.pageSize,
			};
			Object.assign(params, this.formInline);
			let resList = getAqcp(params);
			console.log("params", params);
			this.tableDataCopy = resList.list

			this.aqcpList = resList.list;
			this.dclist = resList.list_total
			if (resList.list_total.length != 0) {
				this.tjlist = resList.list_total[resList.list_total.length - 1]
			}
			this.dclist.forEach((item, label) => {
				this.xh.push(label + 1)
			})
			this.total = resList.total;
		},
		//删除
		shanchu(id) {
			if (this.selectlistRow != '') {
				this.$confirm("是否继续删除?", "提示", {
					confirmButtonText: "确定",
					cancelButtonText: "取消",
					type: "warning",
				})
					.then(() => {
						let valArr = this.selectlistRow;
						// console.log("....", val);
						valArr.forEach(function (item) {
						
							deleteAqcp(item);
							console.log("删除：", item);
							console.log("删除：", item);
						});
						let params = valArr;
						this.$message({
							message: "删除成功",
							type: "success",
						});
						this.aqcp();
						this.ppxhlist()
					})
					.catch(() => {
						this.$message("已取消删除");
					});
			} else {
				this.$message({
					message: '未选择删除记录，请选择下列列表',
					type: 'warning'
				});
			}
		},
		//添加
		showDialog() {
			this.resetForm();
			this.dialogVisible = true;
		},

		//导出
		exportList() {
			console.log("----导出涉密人员----");
			// console.log(this.selectlistRow);
			// if (this.selectlistRow.length > 0) {
			let filename = "安全产品汇总表" + getUuid() + ".xlsx";

			const {
				dialog
			} = require("electron").remote;
			//弹窗title
			let options = {
				title: "保存文件",
				defaultPath: filename,
			};
			console.log(dialog);
			//导出文件夹选择弹窗
			dialog.showSaveDialog(options, (result) => {
				console.log("result", result);
				if (result == null || result == "") {
					console.log("取消导出");
					return;
				}
				let list = [];
				list.push(["安全产品台账"])
				list.push(["上报单位:", this.dwmc, "", "", "", "", "", ""])
				list.push(["统计年度:", this.year, "", "", "", "", "填报时间:", this.Date])
				//对导出的数据进行格式，js导出excel要求传入的参数格式为[["列1名称","列2名称","列3名称"],["列11","值21","值31"],["列12","值22","值32"]]
				list.push([
					"序号",
					"类型",
					"品牌型号",
					"固定资产编号",
					"启用日期",
					"用途",
					"安装位置",
					"数量",
				]); //确定列名

				for (var i in this.dclist) {
					//每一行的值
					let item = this.dclist[i]; //{"name":"XXX","dw":"XXX","zw":"XXX"}

					console.log("导出值:", this.dclist);
					let column = [
						(parseInt(i) + 1),
						item["lx"],
						item["ppxh"],
						item["zcbh"],
						item["qyrq"],
						item["yt"],
						item["azwz"],
						item["sl"],
					];
					list.push(column);
				}
				let merges = [{
					s: { //s为开始
						c: 0, //开始列
						r: 0 //开始取值范围
					},
					e: { //e结束
						c: 7, //结束列
						r: 0 //结束范围
					}
				}]
				let styles = {
					// 列样式
					cols: {
						// 作用sheet页索引（0开始）（-1全sheet页生效）
						scoped: -1,
						style: [
							{ wpx: 100 },
							{ wpx: 300 },
							{ wpx: 150 },
							{ wpx: 150 },
							{ wpx: 100 },
							{ wpx: 200 },
							{ wpx: 200 },
							{ wpx: 120 },
						]
					},
					// 全局样式
					all: {
						alignment: {
							horizontal: 'center', // 水平居中
							vertical: 'center', // 垂直居中
              wrapText: true, // 文字换行
						},
						font: {
							sz: 11, // 字号
							name: '宋体' // 字体
						},
						border: {  // 边框
							top: {
								style: 'thin'
							},
							bottom: {
								style: 'thin'
							},
							left: {
								style: 'thin'
							},
							right: {
								style: 'thin'
							}
						}
					},
					// 单元格样式
					cell: [
						{
							// 生效sheet页索引（值为 -1 时所有sheet页都生效）
							scoped: -1,
							// 索引
							index: 'A1',
							style: {
								font: {
									name: '宋体',
									sz: 16, // 字号
									bold: true,
								},
								alignment: {
									horizontal: 'center', // 水平居中
									vertical: 'center' // 垂直居中
								}
							}
						}
					]
				}
        // excel导出方法自定义配置
        let config = {
          // 忽略行索引(从1开始)
          rowIndexArrIgnore: [1]
        }
				exportExcel(result, list, merges, styles, config) //list 要求为二维数组
				this.$message("导出成功:" + result);
			});
		},
		//确定添加成员组
		submitTj(formName) {
			this.$refs[formName].validate((valid) => {
				if (valid) {
					let params = {
						zcbh: this.tjlist.zcbh,
						qyrq: this.tjlist.qyrq,
						lx: this.tjlist.lx,
						ppxh: this.tjlist.ppxh,
						yt: this.tjlist.yt,
						azwz: this.tjlist.azwz,
						sl: this.tjlist.sl,
						aqcpid: getUuid()
					};
					this.onInputBlur(1)
					if (this.pdaqcp == 0) {
						addAqcp(params);
						this.dialogVisible = false;

						this.$message({
							message: '添加成功',
							type: 'success'
						});
						this.resetForm();
						this.aqcp();
						this.ppxhlist()
					}

				} else {
					console.log('error submit!!');
					return false;
				}
			});

		},

		deleteTkglBtn() { },
		//选中列表的数据
		selectRow(val) {
			console.log(val);
			this.selectlistRow = val;
		},
		//列表分页--跳转页数
		handleCurrentChange(val) {
			this.page = val;
			this.aqcp();
		},
		//列表分页--更改每页显示个数
		handleSizeChange(val) {
			this.page = 1;
			this.pageSize = val;
			this.aqcp();
		},
		//添加重置
		resetForm() {
			this.tjlist.mc = "";
			this.tjlist.zcbh = "";
			this.tjlist.qyrq = "";
			this.tjlist.lx = "";
			this.tjlist.ppxh = "";
			this.tjlist.yt = "";
			this.tjlist.azwz = "";
			this.tjlist.sl = "";
		},
		handleClose(done) {
			// this.resetForm();
			this.dialogVisible = false;
			this.aqcp()
		},
		// 弹框关闭触发
		close(formName) {
			// 清空表单校验，避免再次进来会出现上次校验的记录
			this.$refs[formName].resetFields();
		},
		//取消校验
		close1(form) {
			// 清空表单校验，避免再次进来会出现上次校验的记录
			this.$refs[form].resetFields();
		},
		//添加时的校验
		onInputBlur(index) {
			if (index == 1) {
				this.pdaqcp = jyaddAqcp(this.tjlist)
				if (this.pdaqcp == 2) {
					this.$message.error('资产编号已存在');
				}
			} else if (index == 2) {
				this.pdaqcp = 0
				if (this.zcbh != this.xglist.zcbh) {
					console.log(1);
					this.pdaqcp = jyaddAqcp(this.xglist)
					if (this.pdaqcp == 2) {
						this.$message.error('资产编号已存在');
					}
				}

			}
		},
		//模糊查询品牌型号
		querySearchppxh(queryString, cb) {
			var restaurants = this.restaurantsppxh;
			console.log("restaurants", restaurants);
			var results = queryString ? restaurants.filter(this.createFilterppxh(queryString)) : restaurants;
			console.log("results", results);
			// 调用 callback 返回建议列表的数据
			for (var i = 0; i < results.length; i++) {
				for (var j = i + 1; j < results.length; j++) {
					if (results[i].ppxh === results[j].ppxh) {
						results.splice(j, 1);
						j--;
					}
				}
			}
			cb(results);
			console.log("cb(results.zw)", results);
		},
		createFilterppxh(queryString) {
			return (restaurant) => {
				return (restaurant.ppxh.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
			};
		},
		//模糊查询用途
		querySearchyt(queryString, cb) {
			var restaurants = this.restaurantsppxh;
			console.log("restaurants", restaurants);
			var results = queryString ? restaurants.filter(this.createFilteryt(queryString)) : restaurants;
			console.log("results", results);
			// 调用 callback 返回建议列表的数据
			for (var i = 0; i < results.length; i++) {
				for (var j = i + 1; j < results.length; j++) {
					if (results[i].yt === results[j].yt) {
						results.splice(j, 1);
						j--;
					}
				}
			}
			cb(results);
			console.log("cb(results.zw)", results);
		},
		createFilteryt(queryString) {
			return (restaurant) => {
				return (restaurant.yt.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
			};
		},
		//模糊查询安装位置
		querySearchazwz(queryString, cb) {
			var restaurants = this.restaurantsppxh;
			console.log("restaurants", restaurants);
			var results = queryString ? restaurants.filter(this.createFilterazwz(queryString)) : restaurants;
			console.log("results", results);
			// 调用 callback 返回建议列表的数据
			for (var i = 0; i < results.length; i++) {
				for (var j = i + 1; j < results.length; j++) {
					if (results[i].azwz === results[j].azwz) {
						results.splice(j, 1);
						j--;
					}
				}
			}
			cb(results);
			console.log("cb(results.zw)", results);
		},
		createFilterazwz(queryString) {
			return (restaurant) => {
				return (restaurant.azwz.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
			};
		},
		ppxhlist() {
			let resList = getPpxh()
			this.restaurantsppxh = resList;
			console.log("this.restaurants", this.restaurantsppxh);
			console.log(resList)
		},
		cz() {
			this.formInline = {}
		},
	},
	watch: {},
};
</script>

<style scoped>
.bg_con {
	width: 100%;
}

.dabg {
	/* margin-top: 10px; */
	box-shadow: 0px 2px 12px 0px rgba(9, 70, 255, 0.13);
	border-radius: 8px;
	width: 100%;
}

.daochu {
	display: flex;
	align-items: center;
	margin-bottom: 10px;
}

/* /deep/.el-radio {
	display: block;
	margin-top: 10px;
	margin-bottom: 10px;
} */

.xmlb-title {
	line-height: 60px;
	width: 100%;
	padding-left: 10px;
	height: 60px;
	background: url(../../assets/background/bg-02.png) no-repeat left;
	background-size: 100% 100%;
	text-indent: 10px;
	/* margin: 0 20px; */
	color: #0646bf;
	font-weight: 700;
}

.fhsy {
	display: inline-block;
	width: 120px;
	margin-top: 10px;
	height: 40px;
	display: flex;
	justify-content: center;
	align-items: center;
	padding-left: 30px;
	padding-top: 4px;
	float: right;
	background: url(../../assets/icons/zzjg_icon1.png) no-repeat center;
	background-size: 100% 100%;
}

.item_button {
	height: 100%;
	float: left;
	padding-left: 10px;
	line-height: 50px;
}

.select_wrap {
	/* //padding: 5px; */

	.select_wrap_content {
		float: left;
		width: 100%;
		line-height: 50px;
		/* // padding-left: 20px; */
		/* // padding-right: 20px; */
		height: 100%;
		background: rgba(255, 255, 255, 0.7);

		.item_label {
			padding-left: 10px;
			height: 100%;
			float: left;
			line-height: 50px;
			font-size: 1em;
		}
	}
}

.mhcx1 {
	margin-top: 0px;
}

.widths {
	width: 6vw;
}

.cd {
	width: 191px;
}

/deep/.el-form--inline .el-form-item {
	margin-right: 9px;
}

/deep/.mhcx .el-form-item {
	/* margin-top: 5px; */
	margin-bottom: 5px;
}

.dialog-footer {
	display: block;
	margin-top: 10px;
}


</style>