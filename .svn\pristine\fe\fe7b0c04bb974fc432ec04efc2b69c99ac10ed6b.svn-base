<template>
  <div class="bg_con" style="height: calc(100% - 32px);">
    <div style="width: 100%; position: relative; overflow: hidden; height: 100%;">

      <div class="dabg" style="height: 100%;">
        <div class="content" style="height: 100%;">
          <div class="table" style="height: 100%;">
            <!-- -----------------操作区域--------------------------- -->
            <div class="select_wrap">
              <div class="select_wrap_content">
                <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline" style="float:left;">
                  <el-form-item label="部门" style="font-weight: 700;">
                    <!-- <el-input v-model="formInline.bm" clearable placeholder="部门" class="widthw">
									</el-input> -->
                    <el-cascader v-model="formInline.bm" :options="regionOption" :props="regionParams" filterable
                      clearable ref="cascaderArr"></el-cascader>
                  </el-form-item>
                  <el-form-item label="岗位名称" style="font-weight: 700;">
                    <el-input v-model="formInline.gwmc" clearable placeholder="岗位名称" class="widthw">
                    </el-input>
                  </el-form-item>

                  <el-form-item>
                    <el-button type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="warning" icon="el-icon-circle-close" @click="cz">重置</el-button>
                  </el-form-item>
                </el-form>
                <div class="item_button" style="float:right">
                  <el-button type="danger" icon="el-icon-delete-solid" size="medium" @click="shanchu()">删除
                  </el-button>
                </div>
                <div class="item_button" style="float:right">
                  <el-button type="primary" icon="el-icon-download" size="medium" @click="exportList">
                    导出
                  </el-button>
                </div>
                <div class="item_button" style="float:right">
                  <input type="file" ref="upload"
                    style="display: none;position: absolute;top:10px; right:0;opacity:0; cursor: pointer;height: 32px;width: 56px;z-index:1;"
                    accept=".xls,.xlsx">
                  <el-button type="primary" icon="el-icon-upload2" size="medium" @click="dr_dialog = true">
                    导入
                  </el-button>
                </div>
                <div class="item_button" style="float:right">
                  <el-button type="success" icon="el-icon-plus" size="medium" @click="showDialog">添加
                  </el-button>
                </div>
              </div>
            </div>

            <!-- -----------------审查组人员列表--------------------------- -->
            <div class="table_content_padding" style="height: 100%;">
              <div class="table_content" style="height: 100%;">
                <el-table :data="smgwglList" border @selection-change="selectRow"
                  :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
                  style="width: 100%;border:1px solid #EBEEF5;" height="calc(100% - 34px - 57.6px - 10px)" stripe>
                  <el-table-column type="selection" width="55" align="center"> </el-table-column>
                  <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                  <el-table-column prop="bm" label="部门"></el-table-column>
                  <el-table-column prop="gwmc" label="岗位名称"></el-table-column>
                  <el-table-column prop="smdj" label="涉密等级"></el-table-column>
                  <!-- <el-table-column prop="gwqdyj" label="岗位确定依据"></el-table-column> -->
                  <!-- <el-table-column prop="gwlb" label="岗位类别"></el-table-column> -->

                  <!-- <el-table-column prop="gwdyjb" label="岗位对应级别"></el-table-column> -->
                  <!-- <el-table-column prop="bz" label="备注"></el-table-column> -->
                  <el-table-column prop="" label="操作" width="120">
                    <template slot-scope="scoped">
                      <el-button size="medium" type="text" @click="xqyl(scoped.row)">详情
                      </el-button>
                      <el-button size="medium" type="text" @click="updateItem(scoped.row)">修改
                      </el-button>
                    </template>
                  </el-table-column>

                </el-table>

                <!-- -------------------------分页区域---------------------------- -->
                <div style="border: 1px solid #ebeef5;">
                  <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange"
                    :pager-count="5" :current-page="page" :page-sizes="[5, 10, 20, 30]" :page-size="pageSize"
                    layout="total, prev, pager, sizes,next, jumper" :total="total">
                  </el-pagination>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 模板下载 -->
        <el-dialog title="开始导入" class="scbg-dialog" width="600px" @close="mbxzgb" :visible.sync="dr_dialog" show-close>
          <div style="padding: 20px;">
            <div class="daochu">
              <div>一、请点击“导出模板”，并参照模板填写信息。</div>
              <el-button type="primary" size="mini" @click="mbdc">
                模板导出
              </el-button>
            </div>
            <div class="daochu">
              <div>二、数据导入方式：</div>
              <el-radio-group v-model="sjdrfs" @change="Radio($event)">
                <el-radio label="1">追加（导入时已有的记录信息不变，只添加新的记录）</el-radio>
                <el-radio label="2">覆盖（导入时更新已有的记录信息，并添加新的记录）</el-radio>
              </el-radio-group>
            </div>
            <div class="daochu">
              <div>三、将按模板填写的文件，导入到系统中。</div>
              <el-button type="primary" size="mini" @click="chooseFile">
                上传导入
              </el-button>
            </div>
          </div>
        </el-dialog>

        <!-- -----------------导入-弹窗--------------------------- -->
        <el-dialog width="1000px" height="800px" title="导入涉密岗位管理" class="scbg-dialog" :visible.sync="dialogVisible_dr"
          show-close>
          <div style="height: 600px;">
            <el-table :data="dr_cyz_list" ref="multipleTable" @selection-change="handleSelectionChange"
              style="width: 100%;border:1px solid #EBEEF5;" height="100%" stripe>
              <el-table-column type="selection" width="55"> </el-table-column>
              <el-table-column prop="__EMPTY" label="部门"></el-table-column>
              <el-table-column prop="__EMPTY_1" label="岗位名称"></el-table-column>
              <el-table-column prop="__EMPTY_2" label="涉密等级" width="300px"></el-table-column>
              <el-table-column prop="__EMPTY_3" label="岗位确定依据"></el-table-column>
              <el-table-column prop="__EMPTY_4" label="备注">
              </el-table-column>
            </el-table>
          </div>

          <div style="height :30px;display: flex;align-items: center;justify-content: center;margin: 10px 0;">
            <el-button type="primary" @click="drcy" size="mini">导 入</el-button>
            <el-button @click="dialogVisible_dr = false" size="mini">关 闭</el-button>
          </div>
        </el-dialog>

        <!-- -----------------涉密岗位-弹窗--------------------------- -->

        <el-dialog title="添加涉密岗位" :close-on-click-modal="false" :visible.sync="dialogVisible" width="47%" class="xg"
          :before-close="handleClose" @close="close('formName')">
          <el-form ref="formName" :model="tjlist" :rules="rules" size="mini" label-width="120px">
            <div style="display:flex">
              <el-form-item label="部门" prop="bm">
                <!-- <el-input v-model="tjlist.bm" placeholder="部门" clearable></el-input> -->
                <el-cascader v-model="tjlist.bm" :options="regionOption" :props="regionParams" style="width: 100%;"
                  filterable @change="onInputBlur(1)"></el-cascader>
              </el-form-item>
              <el-form-item label="岗位名称" prop="gwmc">
                <el-input v-model="tjlist.gwmc" clearable placeholder="岗位名称" @blur="onInputBlur(1)"
                  style="width: calc(100% - 20px);">
                </el-input>
                <el-popover placement="right" width="200" trigger="hover">
                  <div>
                    <div style="display:flex;margin-bottom:10px">
                      <i class="el-icon-info" style="color:#409eef;position: relative;top: 2px;"></i>
                      <div class="tszt">提示</div>
                    </div>
                    <div class="smzt">
                      一般按照机关、单位设置的岗位名称填写。有明确行政职务的涉密岗位，其涉密岗位名称按照行政职务名称填写，没有明确行政职务的涉密岗位，以具体岗位职责命名，如案件查办岗，市场监管岗，执法检查岗等，请勿填写如岗位1、岗位A等不清晰的岗位名称。
                    </div>
                  </div>
                  <i class="el-icon-info" style="color:#409eef;position: absolute;    right: 10px;top: 20px;"
                    slot="reference"></i>

                </el-popover>
              </el-form-item>

            </div>
            <div style="display:flex">
              <el-form-item label="涉密等级" prop="smdj">
                <el-select v-model="tjlist.smdj" @change="onInputBlur(1)" placeholder="请选择涉密等级" style="width: 100%;">
                  <el-option v-for="item in smdj" :label="item.smdjmc" :value="item.smdjmc" :key="item.smdjid">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="岗位确定依据" prop="gwqdyj">
                <el-select v-model="tjlist.gwqdyj" placeholder="请选择岗位确定依据" style="width: 100%;">
                  <el-option v-for="item in gwqdyj" :label="item.gwqdyjmc" :value="item.gwqdyjmc" :key="item.gwqdyjid">
                  </el-option>
                </el-select>
              </el-form-item>
            </div>

            <el-form-item label="备注" prop="bz" class="one-line-textarea">
              <el-input type="textarea" v-model="tjlist.bz" style="width:100%;"></el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitTj('formName')">保 存</el-button>
            <el-button type="warning" @click="dialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>

        <el-dialog title="修改涉密岗位" :close-on-click-modal="false" :visible.sync="xgdialogVisible" width="47%" class="xg"
          @close="close1('form')">
          <el-form ref="form" :model="xglist" :rules="rules" label-width="120px" size="mini">
            <div style="display:flex">
              <el-form-item label="部门" prop="bm">
                <!-- <el-input v-model="xglist.bm" placeholder="部门" clearable></el-input> -->
                <el-cascader v-model="xglist.bm" :options="regionOption" :props="regionParams" style="width: 100%;"
                  filterable @change="onInputBlur(1)"></el-cascader>
              </el-form-item>
              <el-form-item label="岗位名称" prop="gwmc">
                <el-input v-model="xglist.gwmc" clearable placeholder="岗位名称" @blur="onInputBlurXg(1)"
                  style="width:calc(100% - 20px);">
                </el-input>
                <el-popover placement="right" width="200" trigger="hover">
                  <div>
                    <div style="display:flex;margin-bottom:10px">
                      <i class="el-icon-info" style="color:#409eef;position: relative;top: 2px;"></i>
                      <div class="tszt">提示</div>
                    </div>
                    <div class="smzt">
                      一般按照机关、单位设置的岗位名称填写。有明确行政职务的涉密岗位，其涉密岗位名称按照行政职务名称填写，没有明确行政职务的涉密岗位，以具体岗位职责命名，如案件查办岗，市场监管岗，执法检查岗等，请勿填写如岗位1、岗位A等不清晰的岗位名称。
                    </div>
                  </div>
                  <i class="el-icon-info" style="color:#409eef;position: absolute;    right: 10px;top: 20px;"
                    slot="reference"></i>

                </el-popover>
              </el-form-item>

            </div>
            <div style="display:flex">
              <el-form-item label="涉密等级" prop="smdj">
                <el-select v-model="xglist.smdj" @change="onInputBlurXg(1)" placeholder="请选择涉密等级" style="width: 100%;">
                  <el-option v-for="item in smdj" :label="item.smdjmc" :value="item.smdjmc" :key="item.smdjid">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="岗位确定依据" prop="gwqdyj">
                <el-select v-model="xglist.gwqdyj" placeholder="请选择岗位确定依据" style="width: 100%;">
                  <el-option v-for="item in gwqdyj" :label="item.gwqdyjmc" :value="item.gwqdyjmc" :key="item.gwqdyjid">
                  </el-option>
                </el-select>
              </el-form-item>
            </div>

            <el-form-item label="备注" prop="bz" class="one-line-textarea">
              <el-input type="textarea" v-model="xglist.bz"></el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="updataDialog('form')">保 存</el-button>
            <el-button type="warning" @click="xgdialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>

        <el-dialog title="涉密岗位详情" :close-on-click-modal="false" :visible.sync="xqdialogVisible" width="47%" class="xg">
          <el-form ref="form" :model="xglist" size="mini" disabled label-width="120px">
            <div style="display:flex">
              <el-form-item label="部门" prop="bm">
                <!-- <el-input v-model="xglist.bm" placeholder="部门" clearable></el-input> -->
                <el-cascader v-model="xglist.bm" :options="regionOption" :props="regionParams" style="width: 100%;"
                  filterable @change="onInputBlur(1)"></el-cascader>
              </el-form-item>
              <el-form-item label="岗位名称" prop="gwmc">
                <el-input v-model="xglist.gwmc" clearable placeholder="岗位名称" @blur="onInputBlur(1)"
                  style="width: calc(100% - 20px);">
                </el-input>
                <el-popover placement="right" width="200" trigger="hover">
                  <div>
                    <div style="display:flex;margin-bottom:10px">
                      <i class="el-icon-info" style="color:#409eef;position: relative;top: 2px;"></i>
                      <div class="tszt">提示</div>
                    </div>
                    <div class="smzt">
                      一般按照机关、单位设置的岗位名称填写。有明确行政职务的涉密岗位，其涉密岗位名称按照行政职务名称填写，没有明确行政职务的涉密岗位，以具体岗位职责命名，如案件查办岗，市场监管岗，执法检查岗等，请勿填写如岗位1、岗位A等不清晰的岗位名称。
                    </div>
                  </div>
                  <i class="el-icon-info" style="color:#409eef;position: absolute;    right: 10px;top: 20px;"
                    slot="reference"></i>

                </el-popover>
              </el-form-item>

            </div>
            <div style="display:flex">
              <el-form-item label="涉密等级" prop="smdj">
                <el-select v-model="xglist.smdj" placeholder="请选择涉密等级" style="width: 100%;">
                  <el-option v-for="item in smdj" :label="item.smdjmc" :value="item.smdjmc" :key="item.smdjid">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="岗位确定依据" prop="gwqdyj">
                <el-select v-model="xglist.gwqdyj" placeholder="请选择岗位确定依据" style="width: 100%;">
                  <el-option v-for="item in gwqdyj" :label="item.gwqdyjmc" :value="item.gwqdyjmc" :key="item.gwqdyjid">
                  </el-option>
                </el-select>
              </el-form-item>
            </div>

            <el-form-item label="备注" prop="bz" class="one-line-textarea">
              <el-input type="textarea" v-model="xglist.bz"></el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="warning" @click="xqdialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>
      </div>
    </div>
  </div>

</template>
<script>
import {
  //内容管理初始化成员列表
  getSmgwgl,
  //添加内容管理
  addSmgwgl,
  //删除内容管理
  deleteSmgwgl,
  getbmmc,
  reviseSmgwgl,
  deleteSmgwgltsxx,
  jxbmgw,
  addDrSmgwgl
} from "../../../db/smgwgldb";
import {
  getlogin
} from "../../../db/loginyhdb";
import { getDocsmgwMbPath } from "../../../utils/pathUtil";
import {
  exportExcel
} from "../../../utils/exportExcel"; //excel导出工具
import {
  getsmdj,
  getgwqdyj,
  getjbzc
} from "../../../db/xzdb"
import {
  getUuid
} from "../../../utils/getUuid"; //获取uuid
import XLSX from "xlsx";
export default {
  components: {},
  props: {},
  data() {
    return {
      pdgwbm: 0,
      gwmc: '',
      smdj: [],
      gwqdyj: [],
      jbzc: [],
      regionOption: [], //地域信息
      regionParams: {
        label: 'label', //这里可以配置你们后端返回的属性
        value: 'label',
        children: 'childrenRegionVo',
        expandTrigger: 'click',
        checkStrictly: true,
      }, //地域信息配置参数
      formInline: {
        gwmc: undefined,
        bm: undefined
      },
      smgwglList: [],
      tjlist: {
        bm: '',
        gwmc: '',
        smdj: '',
        gwqdyj: '',
        zw: '',
        zj: '',
        zc: '',
        gwdyjb: '',
        bz: '',
      },
      xglist: {},
      updateItemOld: {},
      xgdialogVisible: false,
      //导入
      dialogVisible_dr: false, //导入成员组弹窗状态
      dr_cyz_list: [], //待选择导入成员组列表
      multipleTable: [], //已选择导入成员组列表
      xqdialogVisible: false,
      page: 1,
      pageSize: 10,
      total: 0,
      selectlistRow: [], //列表的值
      dialogVisible: false, //添加弹窗状态
      tsxx: '',
      //表单验证
      rules: {
        bm: [{
          required: true,
          message: '请输入部门',
          trigger: 'change'
        },],
        gwmc: [{
          required: true,
          message: '请输入岗位名称',
          trigger: 'blur'
        },],
        smdj: [{
          required: true,
          message: '请选择涉密等级',
          trigger: 'blur'
        },],
        gwqdyj: [{
          required: true,
          message: '请选择岗位确定依据',
          trigger: 'blur'
        },],
        zw: [{
          required: true,
          message: '请输入职务',
          trigger: 'blur'
        },],
        zj: [{
          required: true,
          message: '请输入职级',
          trigger: 'blur'
        },],
        zc: [{
          required: true,
          message: '请选择级别职称',
          trigger: 'blur'
        },],
        // gwdyjb: [{
        // 	required: true,
        // 	message: '请选择岗位对应级别',
        // 	trigger: 'blur'
        // },],
        // bz: [{
        // 	required: true,
        // 	message: '请输入备注',
        // 	trigger: 'blur'
        // },],
      },
      dwmc: '',
      year: '',
      yue: '',
      ri: '',
      Date: '',
      xh: [],
      dclist: [],
      dr_dialog: false,
      //数据导入方式
      sjdrfs: ''
    }
  },
  computed: {},
  mounted() {
    this.dwmc = getlogin()[0].dwmc
    let date = new Date()
    this.year = date.getFullYear() + '年'; //获取完整的年份(4位)
    this.yearwn = date.getFullYear(); //获取完整的年份(4位)
    this.yue = (date.getMonth() + 1) + '月'; //获取当前月份(0-11,0代表1月)
    this.ri = date.getDate() + '日'; //获取当前日(1-31)
    this.Date = this.year + this.yue + this.ri
    console.log('年', this.year, '月', this.yue, '日', this.ri, '时间', this.Date);
    this.smdj = getsmdj()
    this.gwqdyj = getgwqdyj()
    this.jbzc = getjbzc()
    console.log(this.smdj);
    console.log(this.gwqdyj);
    console.log(this.jbzc);
    let bmmc = getbmmc()
    let shu = []
    // console.log(bmmc);
    bmmc.forEach(item => {
      let childrenRegionVo = []
      bmmc.forEach(item1 => {
        if (item.bmm == item1.fbmm) {
          // console.log(item, item1);
          childrenRegionVo.push(item1)
          // console.log(childrenRegionVo);
          item.childrenRegionVo = childrenRegionVo
        }
      });
      // console.log(item);
      shu.push(item)
    })
    console.log(shu[0]);
    console.log(shu[0].childrenRegionVo);

    if (shu[0].childrenRegionVo) {
      shu[0].childrenRegionVo.forEach(item => {
      this.regionOption.push(item)
    })
    }
    console.log(this.regionOption);
    //列表初始化
    this.smgwgl()
    this.$refs.upload.addEventListener('change', e => { //绑定监听表格导入事件
      this.readExcel(e);
    })
  },
  methods: {
    Radio(val) {
      this.sjdrfs = val
      console.log("当前选中的值", val)
    },
    mbxzgb() {
      this.sjdrfs = ''
    },
    //导出模板
    mbdc(){
      let filename = "涉密岗位模板" + ".xlsx";

      const { dialog } = require("electron").remote;

      const FS = require("fs");

      //弹窗title
      let options = {
        title: "保存文件",
        defaultPath: filename,
      };
      console.log(dialog);
      //导出文件夹选择弹窗
      dialog.showSaveDialog(options, (result) => {
        console.log("result", result);
        if (result == null || result == "") {
          console.log("取消导出");
          return;
        }
        // 直接读取模板下载
        // FS.copyFileSync(getDocDmsxzhtjbMbPath(), result, FS.constants.COPYFILE_EXCL)
        FS.writeFileSync(result, FS.readFileSync(getDocsmgwMbPath()));
        this.dr_dialog = false;
        this.$message("导出成功:" + result);
        this.dialogVisibleSjdc = false;
      });
    },
    //导入
    chooseFile() {
      if (this.sjdrfs != '') {
        if (this.sjdrfs == 1) {
          this.$refs.upload.click()
        } else if (this.sjdrfs == 2) {
          let valArr = this.dclist
          valArr.forEach(function (item) {
            deleteSmgwgl(item)
          })
          this.$refs.upload.click()
        }
      } else {
        this.$message.warning('请选择导入方式')
      }
    },
    //----成员组选择
    handleSelectionChange(val) {
      this.multipleTable = val
      console.log("选中：", this.multipleTable);
    },
    //---确定导入成员组
    drcy() {
      //遍历已选择导入的成员，进行格式化，然后添加到数据库
      for (var i in this.multipleTable) {
        var params = {
          bm: this.multipleTable[i]["__EMPTY"],
          gwmc: this.multipleTable[i]["__EMPTY_1"],
          smdj: this.multipleTable[i]["__EMPTY_2"],
          gwqdyj: this.multipleTable[i]["__EMPTY_3"],
          // rs: this.multipleTable[i]["__EMPTY_4"],
          // gwdyjb: this.multipleTable[i]["岗位对应级别"],
          bz: this.multipleTable[i]["__EMPTY_4"],
          smgwid: getUuid()
        }
        addDrSmgwgl(params)
        console.log("params", params);
      }
      this.dialogVisible_dr = false
      this.smgwgl()
    },
    //----表格导入方法
    readExcel(e) {
      var that = this;
      const files = e.target.files;
      console.log("files", files);
      var vali = /\.(xls|xlsx)$/
      if (files.length <= 0) { //如果没有文件名
        return false;
      } else if (!vali.test(files[0].name.toLowerCase())) {
        this.$Message.error('上传格式不正确，请上传xls或者xlsx格式');
        return false;
      }
      const fileReader = new FileReader();
      fileReader.onload = (e) => {
        try {
          const data = e.target.result;
          const workdata = XLSX.read(data, {
            type: 'binary'
          });
          console.log("文件的内容：", workdata) // 文件的内容
          //查看打印能看到 workdata的sheetNames里是文件中每个表的有值的集合；
          const wsname = workdata.SheetNames[0]; //取第一张表
          console.log('wsname', wsname)
          const ws = XLSX.utils.sheet_to_json(workdata.Sheets[wsname]); //生成json表格内容
          console.log(ws); //自第二行开始的内容
          this.dialogVisible_dr = true
          this.dr_cyz_list = ws.slice(4)
          console.log("列表的值:", this.dr_cyz_list)
          // 加工excel读取业务类型为数组
          // this.dr_cyz_list.forEach(function(item) {
          // 	console.log(item[0]['业务类型'].splite(','))
          // })
          this.$refs.upload.value = ''; // 处理完成 清空表单值
          this.dr_dialog = false
        } catch (e) {
          return false;
        }
      };
      fileReader.readAsBinaryString(files[0]);

    },
    onSubmit() {
      this.smgwgl()
    },
    returnSy() {
      this.$router.push("/tzglsy");
    },
    smgwgl() {
      let params = {
        page: this.page,
        pageSize: this.pageSize
      }
      Object.assign(params, this.formInline)
      let resList = getSmgwgl(params)
      console.log("params", params);
      console.log(resList.list);

      this.smgwglList = resList.list
      this.dclist = resList.list_total
      this.dclist.forEach((item, label) => {
        this.xh.push(label + 1)
      })
      this.total = resList.total
    },
    //删除
    shanchu(id) {
      if (this.selectlistRow != '') {
        this.$confirm(this.tsxx, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let valArr = this.selectlistRow
          valArr.forEach(function (item) {
            deleteSmgwgl(item)
          })
          let params = valArr
          this.$message({
            message: '删除成功',
            type: 'success'
          });
          this.smgwgl()
        }).catch(() => {
          this.$message('已取消删除')
        })
      } else {
        this.$message({
          message: '未选择删除记录，请选择下列列表',
          type: 'warning'
        });
      }
    },
    //添加
    showDialog() {
      this.dialogVisible = true
    },
    //添加重置
    resetForm() {
      // this.tjlist.bm = ""
      this.tjlist.gwmc = ""
      this.tjlist.smdj = ""
      this.tjlist.gwqdyj = ""
      // this.tjlist.zw = ""
      // this.tjlist.zj = ""
      // this.tjlist.zc = ""
      // this.tjlist.gwdyjb = ""
      this.tjlist.bz = ""
    },
    //确定添加成员组
    submitTj(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let params = {
            bm: this.tjlist.bm,
            gwmc: this.tjlist.gwmc,
            smdj: this.tjlist.smdj,
            gwqdyj: this.tjlist.gwqdyj,
            // zw: this.tjlist.zw,
            // zj: this.tjlist.zj,
            // zc: this.tjlist.zc,
            // gwdyjb: this.tjlist.gwdyjb,
            bz: this.tjlist.bz,
            smgwid: getUuid()
          }
          if (this.pdgwbm == 0) {
            addSmgwgl(params)
            this.dialogVisible = false

            this.$message({
              message: '添加成功',
              type: 'success'
            });
            this.resetForm()
            this.smgwgl()
          } else {
            this.$message.error('本部门已包含本涉密等级对应岗位');
          }

        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    // 弹框关闭触发
    close(formName) {
      // 清空表单校验，避免再次进来会出现上次校验的记录
      this.$refs[formName].clearValidate();
    },

    close1(form) {
      // 清空表单校验，避免再次进来会出现上次校验的记录
      this.$refs[form].resetFields();
    },
    //修改
    updataDialog(form) {
      this.$refs[form].validate((valid) => {
        if (valid) { // 删除旧的
          /**
           * 修改前部门+岗位名称+涉密等级校验
           * 1、判断部门、岗位名称、涉密等级是否相同
           * 相同：直接修改
           * 不同：判断部门+岗位名称+涉密等级是否重复
           *    a、重复提示
           *    b、不重复修改
          */
          let bm = this.xglist.bm
          let gwmc = this.xglist.gwmc
          let smdj = this.xglist.smdj
          // 旧数据
          let bmOld = this.updateItemOld.bm
          let gwmcOld = this.updateItemOld.gwmc
          let smdjOld = this.updateItemOld.smdj
          console.log(bm, bmOld)
          if (bmOld == bm.join('/') && gwmcOld == gwmc && smdjOld == smdj) {
            // '校验数据相同，直接执行修改'
          } else {
            // '校验数据不同，执行重复验证，通过后修改'
            this.onInputBlurXg(1)
          }
          if (this.pdgwbm == 0) {
            reviseSmgwgl(this.xglist)
            // 刷新页面表格数据
            this.smgwgl()
            // 关闭dialog
            this.$message.success('修改成功')
            this.xgdialogVisible = false
          } else {
            this.$message.error('本部门已包含本涉密等级对应岗位')
          }
        } else {
          console.log('error submit!!');
          return false;
        }
      });

    },
    xqyl(row) {
      this.updateItemOld = JSON.parse(JSON.stringify(row))

      this.xglist = JSON.parse(JSON.stringify(row))
      // this.form1.ywlx = row.ywlx
      console.log('old', row)
      console.log("this.xglist.ywlx", this.xglist);

      this.xglist.bm = this.xglist.bm.split('/')
      this.xqdialogVisible = true
    },

    updateItem(row) {
      this.updateItemOld = JSON.parse(JSON.stringify(row))

      this.xglist = JSON.parse(JSON.stringify(row))
      this.gwmc = this.xglist.gwmc
      // this.form1.ywlx = row.ywlx
      console.log('old', row)
      console.log("this.xglist.ywlx", this.xglist);
      this.xglist.bm = this.xglist.bm.split('/')
      this.xgdialogVisible = true
    },

    exportList() {
      console.log("----导出涉密岗位----")
      // console.log(this.selectlistRow);
      // if (this.selectlistRow.length > 0) {
      let filename = "涉密岗位管理表" + getUuid() + ".xlsx"

      const {
        dialog
      } = require('electron').remote;
      //弹窗title
      let options = {
        title: "保存文件",
        defaultPath: filename,
      };
      console.log(dialog)
      //导出文件夹选择弹窗
      dialog.showSaveDialog(options, result => {
        console.log('result', result)
        if (result == null || result == "") {
          console.log("取消导出")
          return
        }
        let list = []
        list.push(["附件5-2"])
        list.push(["基础表2 涉密岗位统计情况"])
        let dwmc = "(填报单位名称、印章)" + this.dwmc
        let year = "统计年度：" + this.yearwn
        let date = "填报时间：" + this.Date
        list.push([dwmc, "", "", "", "",  ""])
        list.push([year, "", "", date, "", ""])
        //对导出的数据进行格式，js导出excel要求传入的参数格式为[["列1名称","列2名称","列3名称"],["列11","值21","值31"],["列12","值22","值32"]]
        list.push(["序号", "部门", "岗位名称", "涉密等级", "岗位确定依据",  "备注"]) //确定列名

        for (var i in this.dclist) { //每一行的值
          let item = this.dclist[i] //{"name":"XXX","dw":"XXX","zw":"XXX"}

          console.log("导出值:", this.dclist);
          let column = [(parseInt(i) + 1), item["bm"], item["gwmc"], item["smdj"], item["gwqdyj"], item["bz"]]
          list.push(column)
        }
        console.log(list)
        // 单元格合并规则(列索引从0开始)
        let merges = [
          {
            s: { //s为开始
              c: 0, //开始列
              r: 1 //开始取值范围
            },
            e: { //e结束
              c: 6, //结束列
              r: 1 //结束范围
            }
          },
          {
            s: { //s为开始
              c: 0, //开始列
              r: 2 //开始取值范围
            },
            e: { //e结束
              c: 1, //结束列
              r: 2 //结束范围
            }
          },
          {
            s: { //s为开始
              c: 0, //开始列
              r: 3 //开始取值范围
            },
            e: { //e结束
              c: 2, //结束列
              r: 3 //结束范围
            }
          },
          {
            s: { //s为开始
              c: 3, //开始列
              r: 3 //开始取值范围
            },
            e: { //e结束
              c: 6, //结束列
              r: 3 //结束范围
            }
          },
        ]
        let styles = {
          // 列样式
          cols: {
            // 作用sheet页索引（0开始）（-1全sheet页生效）
            scoped: -1,
            style: [
              { wpx: 100 },
              { wpx: 300 },
              { wpx: 100 },
              { wpx: 100 },
              { wpx: 100 },
              { wpx: 100 },
              { wpx: 300 },
            ]
          },
          // 全局样式
          all: {
            alignment: {
              horizontal: 'center', // 水平居中
              vertical: 'center', // 垂直居中
              wrapText: true, // 文字换行
            },
            font: {
              sz: 11, // 字号
              name: '宋体', // 字体
              bold: true,
            },
            border: {  // 边框
              top: {
                style: 'thin'
              },
              bottom: {
                style: 'thin'
              },
              left: {
                style: 'thin'
              },
              right: {
                style: 'thin'
              }
            }
          },
          // 单元格样式
          cell: [
            {
              // 生效sheet页索引（值为 -1 时所有sheet页都生效）
              scoped: -1,
              // 索引
              index: 'A2',
              style: {
                font: {
                  name: '宋体',
                  sz: 16, // 字号
                  bold: true,
                },
                alignment: {
                  horizontal: 'center', // 水平居中
                  vertical: 'center' // 垂直居中
                }
              }
            }
          ]
        }
        // excel导出方法自定义配置
        let config = {
          // 忽略行索引(从1开始)
          rowIndexArrIgnore: [1]
        }
        exportExcel(result, list, merges, styles, config) //list 要求为二维数组
        this.$message('导出成功:' + result)
      })
      // dialog.showSaveDialog(options).then(result => {
      // 	if (result.filePath == null || result.filePath == "") {
      // 		console.log("取消导出")
      // 		return
      // 	}
      // 	let list = []
      // 	//对导出的数据进行格式，js导出excel要求传入的参数格式为[["列1名称","列2名称","列3名称"],["列11","值21","值31"],["列12","值22","值32"]]
      // 	list.push(["部门", "岗位名称", "涉密等级", "岗位确定依据", "岗位类别", "职务", "职级", "职称", "岗位对应级别", "备注"]) //确定列名
      // 	// for (var i in this.selectlistRow) { //每一行的值
      // 	// 	let item = this.selectlistRow[i] //{"name":"XXX","dw":"XXX","zw":"XXX"}

      // 	// 	console.log("导出值:", this.selectlistRow);
      // 	for (var i in this.smgwglList) { //每一行的值
      // 		let item = this.smgwglList[i] //{"name":"XXX","dw":"XXX","zw":"XXX"}

      // 		console.log("导出值:", this.smgwglList);
      // 		let column = [item["bm"], item["gwmc"], item["smdj"], item["gwqdyj"],
      // 			item["gwlb"], item["zw"], item["zj"], item["zc"], item["gwdyjb"], item["bz"]
      // 		]
      // 		list.push(column)
      // 	}
      // 	exportExcel(result.filePath, list) //list 要求为二维数组
      // 	this.$message('导出成功:' + result.filePath)
      // }).catch(error => {
      // 	console.log(error);
      // });

    },

    selectRow(val) {
      console.log(val);
      this.selectlistRow = val;
      let tsxx = ''
      val.forEach(function (item) {
        tsxx = deleteSmgwgltsxx(item)
      })
      this.tsxx = tsxx
      console.log(this.tsxx);
    },
    //列表分页--跳转页数
    handleCurrentChange(val) {
      this.page = val
      this.smgwgl()
    },
    //列表分页--更改每页显示个数
    handleSizeChange(val) {
      this.page = 1
      this.pageSize = val
      this.smgwgl()
    },

    handleClose(done) {
      this.resetForm()
      this.dialogVisible = false
    },
    handleChange() { },
    /**
     * 添加dialog中涉密等级和岗位名称绑定的事件
     * 校验 部门+岗位名称+涉密等级 是否已存在
     * index == 2逻辑未调整，后续有调整需注意
    */
    onInputBlur(index) {
      if (index == 1) {
        this.pdgwbm = jxbmgw(this.tjlist)
        if (this.pdgwbm == 1) {
          this.$message.error('该部门已有本涉密等级对应岗位')
        }
      } else if (index == 2) {
        console.log(this.xglist.gwmc);
        console.log(this.gwmc);
        this.pdgwbm = 0
        if (this.xglist.gwmc != this.gwmc) {
          this.pdgwbm = jxbmgw(this.xglist)
          if (this.pdgwbm == 1) {
            this.$message.error('该部门已有本岗位')
          }
        }

      }
    },
    /**
     * 修改dialog中保存按钮绑定的事件
     * 校验 部门+岗位名称+涉密等级 是否已存在
     * index == 2逻辑已删除，需注意
    */
    onInputBlurXg(index) {
      if (index == 1) {
        this.pdgwbm = jxbmgw(this.xglist)
        if (this.pdgwbm == 1) {
          this.$message.error('该部门已有本涉密等级对应岗位')
        }
      }
    },
    cz() {
      this.formInline = {}
    },
  },
  watch: {

  }
}
</script>

<style scoped>
.bg_con {
  width: 100%;
  background-size: cover;
}

.dabg {
  /* margin-top: 10px; */
  box-shadow: 0px 2px 12px 0px rgba(9, 70, 255, 0.13);
  border-radius: 8px;
  /* padding: 20px 20px; */
  width: 100%;
}

.xmlb-title {
  line-height: 60px;
  width: 100%;
  padding-left: 10px;
  height: 60px;
  background: url(../../assets/background/bg-02.png) no-repeat left;
  background-size: 100% 100%;
  text-indent: 10px;
  /* margin: 0 20px; */
  color: #0646bf;
  font-weight: 700;
}

.fhsy {
  display: inline-block;
  width: 120px;
  margin-top: 10px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-left: 30px;
  padding-top: 4px;
  float: right;
  background: url(../../assets/icons/zzjg_icon1.png) no-repeat center;
  background-size: 100% 100%;
}

.item_button {
  height: 100%;
  float: left;
  padding-left: 10px;
  /* line-height: 50px; */
}

.daochu {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

/* /deep/.el-radio {
	display: block;
	margin-top: 10px;
	margin-bottom: 10px;
} */

.select_wrap {
  /* //padding: 5px; */

  .select_wrap_content {
    float: left;
    width: 100%;
    line-height: 50px;
    /* // padding-left: 20px; */
    /* // padding-right: 20px; */
    height: 100%;
    background: rgba(255, 255, 255, 0.7);

    .item_label {
      padding-left: 10px;
      height: 100%;
      float: left;
      line-height: 50px;
      font-size: 1em;
    }
  }
}

/deep/.el-form-item {
  margin-bottom: 20px;
}

.bz {
  height: 72px !important;
}

/deep/.el-dialog__body .el-form>div>div {
  /* width: auto; */
  max-width: 100%;
}

.dialog-footer {
  display: block;
  margin-top: 10px;
}
</style>