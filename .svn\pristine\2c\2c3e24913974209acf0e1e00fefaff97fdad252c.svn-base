<template>
  <div style="height: 100%;width: 100%;">
    <!---->
    <div class="mhcx">
      <el-form :inline="true" size="medium" class="demo-form-inline" style="float:left">
        <el-form-item style="float: right;">
          <div>当前审查任务：{{dialogObj.rwmc}}</div>
        </el-form-item>
      </el-form>
      <!---->
      <el-form :inline="true" size="medium" class="demo-form-inline" style="float:right">
        <el-form-item style="float: right;">
          <el-button type="danger" size="medium" icon="el-icon-caret-left" @click="fhsyb">
            返回上一步
          </el-button>
        </el-form-item>
        <el-form-item style="float: right;">
          <el-button type="primary" size="medium" icon="el-icon-document-add" @click="saveToNext">
            保存至下一步
          </el-button>
        </el-form-item>
        <el-form-item style="float: right;">
          <el-button type="warning" size="medium" icon="el-icon-document-add" @click="tjnsjg">
            添加内设机构
          </el-button>
        </el-form-item>
      </el-form>
      <div style="clear: both;"></div>
    </div>
    <!---->
    <!-- 表格区域 -->
    <div style="height: calc(100% - 58px - 34px - 42px);">
      <el-table :data="scList" border :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" style="width: 100%;border:1px solid #EBEEF5;" height="100%" stripe>
        <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
        <el-table-column prop="zzjgmc" label="内设机构"></el-table-column>
        <el-table-column label="登记状态" width="250">
          <template slot-scope="scoped">
            <div>
              <span v-if="scoped.row.zt == 0">待登记</span>
              <span v-if="scoped.row.zt == 1">继续登记</span>
              <span v-if="scoped.row.zt == 2">完成登记</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="" label="操作" width="120">
          <template slot-scope="scoped">
            <el-button v-if="scoped.row.zt == 0" size="small" type="text" @click="dj(scoped.row)">登记</el-button>
            <el-button v-if="scoped.row.zt == 1" size="small" type="text" @click="dj(scoped.row)">继续登记</el-button>
            <el-button v-if="scoped.row.zt == 2" size="small" type="text" @click="toXqxx(scoped.row)">详情</el-button>
            <el-button size="small" type="text" @click="yc(scoped.row)">移除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!---->
    <!-- 分页组件区域 -->
    <div style="border: 1px solid #ebeef5">
      <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange" :pager-count="5" :current-page="pageInfo.page" :page-sizes="[5, 10, 20, 30]" :page-size="pageInfo.pageSize" layout="total, prev, pager, sizes,next, jumper" :total="pageInfo.total">
      </el-pagination>
    </div>
    <!--添加内设机构dialog-->
    <el-dialog title="添加内设机构" :visible.sync="dialogVisibleTjnsjg" width="50%">
      <div>
        <el-table :data="zzjgAllList" ref="zzjgAllTable" border :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" style="width: 100%;border:1px solid #EBEEF5;" height="50vh" stripe>
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column type="index" width="60" label="序号"></el-table-column>
          <el-table-column prop="label" label="内设机构"></el-table-column>
          <el-table-column prop="bmflag" label="是否保密部门"></el-table-column>
        </el-table>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" size="medium" @click="dialogZzjgQdxz">保 存</el-button>
        <el-button type="warning" size="medium" @click="dialogVisibleTjnsjg = false">关 闭</el-button>
      </span>
    </el-dialog>
    <!---->
  </div>
</template>

<script>

// 组织机构
import { selectAllZzjg } from '../../../../db/zzjgdb'

import { setZczpIdsObj, getZczpIdsObj, removeZczpIdsObjField } from '../../../../utils/windowLocation'

import {
  // 通过任务ID获取审查任务信息
  selectScrwByRwid,
  // 通过任务ID获取抽查的内设机构集合
  selectCcdnsjgListByRwid,
  // 插入抽查的内设机构列表中(批量)
  insertCcdnsjgList,
  // 通过抽查的内设机构流水ID移除抽查的内设机构表
  deleteCcdnsjgListByID,
  // 更新审查任务
  updateScrwListZt
} from '../../../../db/zczpdb'

import { writeOptionsLog } from '../../../../utils/logUtils'

export default {
  data () {
    return {
      dialogObj: {
        // rwid: 'D66215EC-1B9A-408A-BEE8-7A9396165EA7'
      },
      pageInfo: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      scList: [],
      // dialog
      dialogVisibleTjnsjg: false,
      // dialog内所有的内设机构
      zzjgAllList: []
      //
    }
  },
  computed: {},
  components: {
  },
  methods: {
    // 调转到详情信息页面（不可编辑的页面）
    toXqxx (row) {
      setZczpIdsObj('ccdnsjgid', row.ccdnsjgid)
      this.$router.push({
        path: '/ccdnsjgDjXqxx'
      })
    },
    // 返回上一步
    fhsyb () {
      // 更新任务状态码
      let updateScrwListParams = {
        rwid: this.dialogObj.rwid,
        zt: 1
      }
      let bool = updateScrwListZt(updateScrwListParams)
      if (bool) {
        this.$message.success('抽查的内设机构记录录入成功')
        // 写入操作日志
        writeOptionsLog('yybs-ccdnsjg', '返回上一步', this.dialogObj)
        this.$router.push({
          path: '/xjzczp',
          // query: {
          //   // 任务ID
          //   rwid: this.dialogObj.rwid
          // }
        })
      }
    },
    // 保存至下一步
    saveToNext () {
      // 更新任务状态码
      let updateScrwListParams = {
        rwid: this.dialogObj.rwid,
        zt: 3
      }
      let bool = updateScrwListZt(updateScrwListParams)
      if (bool) {
        this.$message.success('抽查的内设机构记录录入成功')
        // 写入操作日志
        writeOptionsLog('yybs-ccdnsjg', '抽查的内设机构保存完成', this.dialogObj)
        this.$router.push({
          path: '/ccdry',
          // query: {
          //   // 任务ID
          //   rwid: this.dialogObj.rwid
          // }
        })
      }
    },
    // 添加内设机构
    tjnsjg () {
      this.dialogVisibleTjnsjg = true
    },
    // 登记
    dj (row) {
      setZczpIdsObj('ccdnsjgid', row.ccdnsjgid)
      this.$router.push({
        path: '/ccdnsjgDj',
        // query: {
        //   rwid: this.dialogObj.rwid,
        //   ccdnsjgid: row.ccdnsjgid
        // }
      })
    },
    // 移除，使用[抽查的内设机构流水ID]移除该记录的[抽查的内设机构表]和[内设机构评分记录表]
    yc (row) {
      console.log(row)
      let bool = deleteCcdnsjgListByID(row.ccdnsjgid)
      if (bool) {
        this.$message.success('移除成功')
        // local自查自评对象中移除该属性
        removeZczpIdsObjField('ccdnsjgid')
        // 写入操作日志
        writeOptionsLog('yybs-ccdnsjgDj', '内设机构'+row.zzjgmc+'登记信息移除成功', row)
        // 刷新数据
        this.getCcdnsjgListByRwid()
      }
    },
    handleSizeChange () { },
    handleCurrentChange (val) { },
    returnSy () {
      this.$router.go(-1)
    },
    // 获取任务下抽查的内设机构信息
    getCcdnsjgListByRwid () {
      let params = {
        rwid: this.dialogObj.rwid
      }
      Object.assign(params, this.pageInfo)
      let ccdnsjgListPage = selectCcdnsjgListByRwid(params)
      console.log(ccdnsjgListPage)
      this.scList = ccdnsjgListPage.list
    },
    // dialog组织机构确认选择事件触发
    dialogZzjgQdxz () {
      let selection = this.$refs.zzjgAllTable.selection
      console.log('selection', selection)
      // 加入到抽查的内设机构表中
      let bool = insertCcdnsjgList(selection, this.dialogObj.rwid)
      if (bool) {
        // 将任务状态码拨到2-抽查的内设机构临时保存
        // 更新任务状态码
        let updateScrwListParams = {
          rwid: this.dialogObj.rwid,
          zt: 2
        }
        bool = updateScrwListZt(updateScrwListParams)
        if (bool) {
          // 写入操作日志
          writeOptionsLog('yybs-ccdnsjg', '添加抽查的内设机构', this.dialogObj)
          // 获取抽查的的内设机构
          this.getCcdnsjgListByRwid()
          this.dialogVisibleTjnsjg = false
        }
      }
    }
  },
  watch: {
    'dialogObj.rwid' (newVal, oldVal) {
      console.log('内设机构 rwid', newVal, oldVal)
      // 获取审查任务表历史信息
      let scrw = selectScrwByRwid(newVal)
      console.log('scrw历史信息', scrw)
      this.dialogObj = scrw
    }
  },
  mounted () {
    // let params = this.$route.query
    let params = getZczpIdsObj()
    console.log('内设机构 params', params)
    if (params) {
      this.dialogObj = params
    }
    // 获取所有的组织机构
    this.zzjgAllList = selectAllZzjg()
    console.log('获取所有的组织机构', this.zzjgAllList)
    ////////////
    this.getCcdnsjgListByRwid()
  }
};
</script>

<style scoped></style>
