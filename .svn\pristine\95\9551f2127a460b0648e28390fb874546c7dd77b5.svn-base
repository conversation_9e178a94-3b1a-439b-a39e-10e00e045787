<template>
  <div style="height: 100%;">
    <hsoft_top_title>
      <template #left>文件路径设置</template>
    </hsoft_top_title>
    <!---->
    <div style="padding: 10px 0;text-align: right;">
      <el-button type="success" @click="dialogVisibleSetting = true">添加</el-button>
      <!-- <el-button type="primary" @click="getSettingList()">查询</el-button> -->
    </div>
    <el-table :data="settingList" border :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" style="width: 100%;border:1px solid #EBEEF5;" height="calc(100% - 32px - 60px - 32px - 10px - 10px)" stripe>
      <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
      <el-table-column prop="csbs" label="路径标识" width="100"></el-table-column>
      <el-table-column prop="cssm" label="路径说明"></el-table-column>
      <el-table-column prop="csz" label="路径" width="" align="left"></el-table-column>
      <el-table-column prop="gxsj" label="修改时间" width="200" align="left">
        <template slot-scope="scoped">
          <span>{{formatTime(scoped.row.gxsj)}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="" label="操作" width="100">
        <template slot-scope="scoped">
          <el-button size="small" type="text" @click="modifySetting(scoped.row)">修改</el-button>
          <el-button size="small" type="text" @click="deleteSetting(scoped.row)" style="color:#F56C6C;">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange" :pager-count="5" :current-page="pageInfo.page" :page-sizes="[5, 10, 20, 30]" :page-size="pageInfo.pageSize" layout="total, prev, pager, sizes,next, jumper" :total="pageInfo.total" style="padding-top: 10px;">
    </el-pagination>
    <!---->
    <!-- 添加文件相关 -->
    <el-dialog title="添加文件路径" :visible.sync="dialogVisibleSetting" width="35%">
      <div>
        <el-form :label-position="'right'" label-width="120px" size="mini">
          <div style="display:flex">
            <el-form-item label="路径标识" class="one-line">
              <el-input v-model="settingForm.csbs"></el-input>
            </el-form-item>
          </div>
          <el-form-item label="路径说明" class="one-line-textarea">
            <el-input type="textarea" v-model="settingForm.cssm"></el-input>
          </el-form-item>
          <div style="display:flex">
            <el-form-item label="路径" class="one-line">
              <el-button type="primary" @click="choosePath()">选择路径</el-button>
              <div>{{settingForm.csz}}</div>
            </el-form-item>
          </div>
          <div style="display:flex">
            <el-form-item label="分组号" class="one-line">
              <el-input v-model="settingForm.fzh"></el-input>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="addSetting()">保 存</el-button>
        <el-button type="warning" @click="dialogVisibleSetting = false">取 消</el-button>
      </span>
    </el-dialog>
    <!---->
    <!---->
    <!---->
  </div>
</template>

<script>
import hsoft_top_title from '../../components/hsoft-top-title/hsoft-top-title.vue'

const { dialog } = require('electron').remote

import { getWindowLocation } from '../../../utils/windowLocation'

import { dateFormatChinese } from '../../../utils/moment'

import { writeSystemOptionsLog } from '../../../utils/logUtils'

import { moveFilesByDirectory, deleteFilesByDirectory } from '../../../utils/pathUtil'

// import { decideChange } from '../../../utils'

// 系统参数设置表
import {
  // 插入文件相关表
  insertWjxgList,
  // 查询文件相关表
  selectWjxgList,
  // 删除文件相关设置
  deleteWjxgList,
  // 修改文件相关设置
  updateWjxgList
} from '../../../db/zczpSystem/zczpSysyemDb'

export default {
  data () {
    return {
      // 分页信息
      pageInfo: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      // 添加文件路径dialog
      dialogVisibleSetting: false,
      settingForm: {},
      // 文件路径设置表格数据
      settingList: []
    }
  },
  components: {
    hsoft_top_title
  },
  methods: {
    // 选择路径
    choosePath () {
      const _this = this
      // const { dialog } = require('electron').remote
      let options = {
        title: '请选择路径',
        properties: ['openDirectory']
      }
      dialog.showOpenDialog(options, result => {
        console.log('result', result)
        _this.settingForm.csz = result[0]
        _this.settingForm = JSON.parse(JSON.stringify(_this.settingForm))
      })
    },
    // 格式化时间
    formatTime (time) {
      return dateFormatChinese(new Date(time))
    },
    handleCurrentChange (val) {
      this.pageInfo.page = val
      this.getSettingList()
    },
    handleSizeChange (val) {
      this.pageInfo.pageSize = val
      this.getSettingList()
    },
    // 修改(表格)
    modifySetting (row) {
      const _this = this
      let oldRow = JSON.parse(JSON.stringify(row))
      let params = JSON.parse(JSON.stringify(row))
      // 校验旧路径
      let oldCsz = oldRow.csz
      if (!oldCsz) {
        this.$message.warning('[系统异常]未检测到旧路径')
        return
      }
      // 弹出路径选择器
      let options = {
        title: '请选择路径',
        properties: ['openDirectory']
      }
      dialog.showOpenDialog(options, result => {
        console.log('result', result)
        let csz = result[0]
        if (csz) {
          // 先将该目录下的文件挪到新目录下
          try {
            moveFilesByDirectory(oldCsz, csz)
          } catch (error) {
            console.error(error)
            if (!error.code) {
              // 手动new出来的异常对象或无法翻译的异常对象
              this.$notify({
                title: '操作异常',
                message: error.message,
                type: 'error',
                offset: 100,
                duration: 0
              })
              return
            }
            let errObj = JSON.parse(error.message)
            console.log('errObj', errObj)
            this.$notify({
              title: '系统异常',
              message: '[' + errObj.mark + ']\n',
              type: 'error',
              offset: 100,
              duration: 0
            })
            return
          }
          params.csz = csz
          // 更新数据库
          updateWjxgList(params)
          // 写入日志
          row.cszNew = params.csz
          let logParams = {
            xyybs: 'yybs_wjljsz',
            ymngnmc: '修改',
            extraParams: row
          }
          writeSystemOptionsLog(logParams)
          //
          _this.getSettingList()
          // 提示修改成功并询问是否需要清除就路径下文件
          _this.$confirm('路径修改成功，路径下文件已迁移到新路径下，是否需要清理旧路径下的文件？[' + oldCsz + ']', '是否文件清理？', {
            cancelButtonClass: "btn-custom-cancel",
            confirmButtonText: '立即清理',
            cancelButtonText: '稍后自行清理',
            type: 'warning',
            // center: true
          }).then(() => {
            // 确认清理
            try {
              deleteFilesByDirectory(oldCsz)
            } catch (error) {
              if (!error.code) {
                // 手动new出来的异常对象或无法翻译的异常对象
                this.$notify({
                  title: '系统异常',
                  message: error.message,
                  type: 'error',
                  offset: 100,
                  duration: 0
                })
                return
              }
              let errObj = JSON.parse(error.message)
              console.log('errObj', errObj)
              this.$notify({
                title: '系统异常',
                message: '[' + errObj.mark + ']\n' + errObj.solvtion,
                type: 'error',
                offset: 100,
                duration: 0
              })
              return
            }
            this.$message.success('文件清理成功')
          }).catch(() => {
            // 稍后自行清理
          })
        }
      })
    },
    // 删除文件相关设置
    deleteSetting (row) {
      let params = {
        filesettingid: row.filesettingid
      }
      deleteWjxgList(params)
      this.getSettingList()
      // 写入日志
      let logParams = {
        xyybs: 'yybs_wjljsz',
        ymngnmc: '删除',
        extraParams: row
      }
      writeSystemOptionsLog(logParams)
    },
    // 获取文件路径设置集合
    getSettingList () {
      this.settingForm = {}
      let params = {}
      Object.assign(params, this.pageInfo)
      let wjxgPage = selectWjxgList(params)
      this.settingList = wjxgPage.list
      this.pageInfo.total = wjxgPage.total
    },
    // 添加文件路径设置
    addSetting () {
      console.log('表单数据', this.settingForm)
      insertWjxgList(this.settingForm)
      // 写入日志
      let logParams = {
        xyybs: 'yybs_wjljsz',
        ymngnmc: '添加',
        extraParams: this.settingForm
      }
      writeSystemOptionsLog(logParams)
      //
      this.getSettingList()
      this.dialogVisibleSetting = false
    }
  },
  mounted () {
    //
    this.getSettingList()
  }
}
</script>

<style scoped>
.out-card {
  /* margin-bottom: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04); */
}
/**单位信息区域**/
.out-card .out-card-div {
  font-size: 13px;
  padding: 5px 20px;
}
.out-card .out-card-div div {
  padding: 10px 5px;
  display: flex;
}
.out-card .dwxx div:hover {
  background: #f4f4f5;
  border-radius: 20px;
}
.out-card .dwxx div label {
  /* background-color: red; */
  width: 125px;
  display: inline-block;
  text-align: right;
  font-weight: 600;
  color: #909399;
}
.out-card .dwxx div span {
  /* background-color: rgb(33, 92, 79); */
  flex: 1;
  display: inline-block;
  padding-left: 20px;
}
</style>