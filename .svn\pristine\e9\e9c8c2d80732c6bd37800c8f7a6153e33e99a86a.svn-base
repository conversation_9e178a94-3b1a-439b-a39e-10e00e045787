<template>
	<div style="height: calc(100% - 32px);">
		<div class="zdwb">
			<div class="zzDialogContainer">
				<div id="zzjgt" ref="zzjgpic" style="width:100%; height:100%"> </div>
			</div>
		</div>
	</div>
</template>

<script>
import {
	getDwxxDB
} from "../../../db/dwxxDb"
import {
	getJgxx
} from "../../../db/zzjgdb";
export default {
	data() {
		return {
			oldArr: [],
			newArr: [],
			zzjgList: [], // 组织机构树
		};
	},
	mounted() {
		this.getZzjgPic()
	},
	methods: {
        mathMax(arr) {
            var max = arr[0];
            for(var i = 1; i < arr.length; i++) {
                if(arr[i] > max) {
                max = arr[i];
                }
            }
            return max;
        },
		// 组织结构树
		filters(arr) {
			let newArrUpdate = this.oldArr.map(item => ({
				...item,
				name: item.label,
			}))
            let lengthArr = []
            newArrUpdate.forEach((oldItem, oldIndex) => {
                lengthArr.push(oldItem.name.length)
            })
            let paddingTopCount
            lengthArr.splice(0,1)
			this.newArr = arr.filter((item, itemIndex) => {
				newArrUpdate.forEach((oldItem, oldIndex) => {
                    oldItem.collapsed = false
                    if(((this.mathMax(lengthArr)) * 25 - oldItem.name.length * 25) / 2 == 0){
                        paddingTopCount = 15
                    }else{
                        paddingTopCount = ((this.mathMax(lengthArr)) * 25 - oldItem.name.length * 25) / 2 + 15
                    }
                    if(oldItem.bmflag == '是'){
                        oldItem.label = {
                            padding: [paddingTopCount ,15,15,15],
                            fontSize: 14,
                            height: (this.mathMax(lengthArr)) * 25 - ((this.mathMax(lengthArr)) * 25 - oldItem.name.length * 25) / 2,
                            lineHeight: 25,
                            formatter(value) {
                                return `{b|${value.name.split('').join('\n')}}`
                            },
                            backgroundColor: '#2196F3',
                            borderColor: '#2196F3',
                            borderWidth: 0.5,
                            rich: {
                                b: {
                                    fontSize: 14,
                                    color: 'yellow'
                                }
                            },
                            borderRadius: 4,
                        }
                    }else{
                        oldItem.label = {
                            padding: [paddingTopCount ,15,15,15],
                            fontSize: 14,
                            height: (this.mathMax(lengthArr)) * 25 - ((this.mathMax(lengthArr)) * 25 - oldItem.name.length * 25) / 2,
                            lineHeight: 25,
                            formatter(value) {
                                return `{b|${value.name.split('').join('\n')}}`
                            },
                            backgroundColor: '#2196F3',
                            borderColor: '#2196F3',
                            borderWidth: 0.5,
                            rich: {
                                b: {
                                    fontSize: 14,
                                    color: '#ffffff',
                                }
                            },
                            borderRadius: 4,
                        }
                    }
                    if (oldItem.fbmm == item.bmm) { //有子节点，oldItem是item的子项
                        item.children.push(oldItem)
                    }
                    if (oldIndex == this.oldArr.length - 1) { //内层循环最后一项处理完毕
                        if (item.children && item.children.length) {//当前层级有子项，子项不为空
                            this.filters(item.children); //调用递归过滤函数
                        }
                    }
				});
				return true //返回过滤后的新数组赋值给this.newArr
			})
			return this.newArr
		},
		// 获取组织结构图echarts
		getZzjgPic(type) {
			this.oldArr = getJgxx()
			let arr = []
			this.oldArr.forEach((item1) => {
				item1.children = []
				if (item1.bmjb == 0) {
					arr.push(item1)
				}
			});
			this.zzjgList = this.filters(arr)[0].children
			let list = getDwxxDB()
			let dwmc = list.list[0].dwmc
			let lastZzjgList = []
			lastZzjgList[0] = {
				name: dwmc,
				collapsed: false,
				label: {
					// position: [45, 90],
                    padding: 15,
                    fontSize: 20,
                    backgroundColor: '#2196F3',
                    borderColor: '#2196F3',
                    borderWidth: 0.5,
                    borderRadius: 4,
                    color:'#ffffff'
                },
				children: this.zzjgList
			}
            let myChartContainer = document.getElementById("zzjgt");
            myChartContainer.style.height = window.innerHeight + 'px';
            myChartContainer.style.width = window.innerWidth + 'px';
			let myChart

			myChart = this.$echarts.init(
				document.getElementById("zzjgt")
			)
			let option = {
				tooltip: {
					trigger: 'item',
					triggerOn: 'mousemove'
				},
                silent:true,
				series: [
					{
						type: 'tree',
                        edgeShape: 'polyline', // 链接线是折现还是曲线
                        orient: 'TB',
                        roam: true,
                        data: lastZzjgList,
                        top: '5%',
                        // left: '5%',
                        // right: '5%',
                        // bottom: '10%',
                        symbol: 'none',
                        initialTreeDepth: 10,
						label: {
							position: [0, 0],
							verticalAlign: 'middle',
							align: 'middle',
						},
						lineStyle: {
                            color:'rgba(0,0,0,0.2)',
                            type:'solid',
                            width:3
						},
						leaves: {
							label: {
								position: [0, 0],
								verticalAlign: 'middle',
								align: 'middle'
							}
						},
						emphasis: {
							focus: 'descendant'
						},
						expandAndCollapse: true,
						animationDuration: 550,
						animationDurationUpdate: 750
					}
				]
			}
			myChart.setOption(option)
            myChartContainer.style.height = window.innerHeight + 'px';
            myChartContainer.style.width = window.innerWidth + 'px';
			myChart.resize()
		},
	},
};
</script>

<style scoped>
.zdwb {
	width: 100%;
	height: 100%;
	/* box-shadow: 0px 1px 12px 0px rgba(0, 0, 0, 0.1); */
	/* background: url(../../assets/background/table_bg.png) no-repeat center; */
	/* background-size: 100% 100%; */
}

.zzDialogContainer {
	width: 100%;
    /* height: calc(100% - 120px); */
    height: 100%;
}


</style>
