import db from "./adapter/zczpAdaptor";
// 获取文档列表数据
export const getWdInfoDatas = (params) => {
	let page = params.page;
	let pagesize = params.pageSize;
	let list_total = db.get("bggl_wdlb").filter(function(item) {
			return item;
	}).cloneDeep().value();

	// 手动分页
	let pageList = list_total.slice(pagesize * (page - 1),pagesize * (page - 1) + pagesize);

	let resList = {
		list: pageList,
		list_total: list_total,
		total: list_total.length,
	};
	return resList;
};
// 文件上传
export const addFileInfo = (params) => {
	db.read().get("bggl_wdlb").push(params).write();
};
// 删除文件
export const deleteFileItem = (params) => {
	db.read().get("bggl_wdlb").remove(params).write();
};
// 获取会员口令
export const getHykl = () => {
	let resList = db.get("aqs_list").filter(function(item) {
			return item;
	}).cloneDeep().value();
	return resList;
};
// 更新会员口令
export const addHykl = (params) => {
	db.read().get("bggl_hykl").push(params).write();
};
// 更新会员口令
export const reviseHyKl = (params) => {
	let id = params.id
	if (!id || id == '') {
		return
	}
	// params.gxsj = new Date().getTime()
	// params.dqnf = new Date().getFullYear().toString()
	// 全字段更新方法(传进来的字段都会更新)
	db.read().get('aqs_list').find({ id }).assign(params).write()
}